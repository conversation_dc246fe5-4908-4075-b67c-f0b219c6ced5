<?php

namespace App\Service\Admin;

use App\Entity\Task;
use App\Entity\TaskProgress;
use App\Helper\ModalboxHelper;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\QueryBuilder;
use Omines\DataTablesBundle\Adapter\Doctrine\ORMAdapter;
use Omines\DataTablesBundle\Column\DateTimeColumn;
use Omines\DataTablesBundle\Column\TextColumn;
use Omines\DataTablesBundle\DataTable;
use Omines\DataTablesBundle\DataTableFactory;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Request;
use App\Traits\ExceptionLoggerTrait;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Security\Csrf\CsrfTokenManagerInterface;

class TaskService
{
    use ExceptionLoggerTrait;

    public function __construct(
        private readonly DataTableFactory $dataTableFactory,
        private readonly RouterInterface $router,
        ParameterBagInterface $parameterBag,
        private readonly CsrfTokenManagerInterface $csrfTokenManager,
        private readonly ModalboxHelper $modalboxHelper,
        private readonly EntityManagerInterface $entityManager,
        private readonly RequestStack $requestStack,
    ) {
        $this->initializeLogger($parameterBag, 'TaskService');
    }

    public function createTaskTable(Request $request, int $projectId, bool $isAdmin = true): DataTable
    {
        $dataTable = $this->dataTableFactory->create();

        foreach ($this->getColumns($isAdmin, $projectId) as $key => $column) {
            $dataTable->add($key, $column['className'], array_filter($column, fn($k) => $k !== 'className', ARRAY_FILTER_USE_KEY));
        }

        return $dataTable->createAdapter(ORMAdapter::class, [
            'entity' => Task::class,
            'query' => fn(QueryBuilder $qb) => $this->buildQuery($qb, $projectId),
        ])
            ->addOrderBy('assignedAt', 'desc')
            ->handleRequest($request);
    }

    private function getColumns(bool $isAdmin, int $projectId): array
    {
        $columns = [
            'title' => [
                'label' => 'Title',
                'field' => 't.title',
                'className' => TextColumn::class,
                'render' => function ($value, $context) {
                    $title = $context->getTitle() ?? '-';
                    $maxLength = 50;
                    if (strlen($title) > $maxLength) {
                        $truncated = substr($title, 0, $maxLength) . '...';
                        return sprintf('<span title="%s">%s</span>', htmlspecialchars($title), htmlspecialchars($truncated));
                    }
                    return htmlspecialchars($title);
                },
            ],
            'status' => [
                'label' => 'Status',
                'field' => 't.status',
                'className' => TextColumn::class,
                'render' => fn($value) => $this->renderStatusBadge($value),
            ],
            'assignedTo' => [
                'label' => 'Assigned To',
                'field' => 'm.name',
                'className' => TextColumn::class,
                'render' => fn($value, $context) => htmlspecialchars($context->getAssignedTo()?->getName() ?? 'N/A'),
            ],
            'assignedBy' => [
                'label' => 'Assigned By',
                'field' => 'emp.name',
                'className' => TextColumn::class,
                'render' => fn($value, $context) =>
                    $context->getAssignedByAdmin()?->getUsername()
                    ?? $context->getAssignedBy()?->getName()
                    ?? 'N/A',
            ],
            'assignedAt' => [
                'label' => 'Assigned On',
                'field' => 't.assignedAt',
                'className' => DateTimeColumn::class,
                'format' => 'd-m-Y',
                'searchable' => false,
            ],
        ];

        $columns['actions'] = [
            'label' => 'Actions',
            'field' => 't.id',
            'className' => TextColumn::class,
            'orderable' => false,
            'render' => fn($value, $context) => $this->renderActionColumn($context, $projectId, $isAdmin),
        ];

        return $columns;
    }

    private function renderStatusBadge(string $status): string
    {
        return match ($status) {
            'NEW' => '<span class="badge btn-primary">New</span>',
            'IN_PROGRESS' => '<span class="badge btn-primary">In Progress</span>',
            'COMPLETED' => '<span class="badge bg-success">Completed</span>',
            default => '<span class="badge bg-secondary">Unknown</span>',
        };
    }

    private function renderActionColumn(Task $task, int $projectId, bool $isAdmin): string
    {
        $entityId = $task->getId();
        $baseRoute = $isAdmin ? 'task' : 'employee_task';
        $editUrl = $this->router->generate("{$baseRoute}_edit", ['project_id' => $projectId, 'id' => $entityId]);
        $deleteUrl = $this->router->generate("{$baseRoute}_delete", ['project_id' => $projectId, 'id' => $entityId]);
        $viewUrl = $this->router->generate("{$baseRoute}_view", ['project_id' => $projectId, 'id' => $entityId]);
        $progressButton = '';
        $hasProgress = $this->entityManager->getRepository(TaskProgress::class)->findBy(['task' => $entityId]);
        $taskStatus = strtolower($task->getStatus());
        if (!empty($hasProgress)) {
            $progressUrl = $this->router->generate("{$baseRoute}_progress_report", ['project_id' => $projectId, 'task_id' => $entityId]);
            $progressButton = sprintf(
                '<a href="%s" class="bs-btn btn btn-primary btn-sm" style="margin: 2px;" title="View Progress">
                <i class="bs-fa fa fa-bar-chart"></i>
            </a>',
                $progressUrl
            );
        }
        $deleteToken = $this->csrfTokenManager->getToken('delete' . $entityId)->getValue();

        $modalId = 'deleteModal_' . $entityId;
        $modalBody = sprintf(
            'Are you sure you want to delete the task "%s"?<br><br>' .
            '<strong>Warning:</strong> This action cannot be undone.',
            htmlspecialchars($task->getTitle())
        );

        $footerButtons = sprintf(
            '<button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>' .
            '<button type="button" class="btn btn-primary delete-task-btn" data-delete="true" data-url="%s" data-token="%s">Delete</button>',
            $deleteUrl,
            $deleteToken
        );

        $deleteModal = $this->modalboxHelper->renderModal(
            $modalId,
            'Confirm Task Deletion',
            $modalBody,
            $footerButtons
        );

        $deleteButton = '';
        if ($taskStatus !== 'completed') {
            $deleteButton = sprintf(
                '<button type="button" class="bs-btn btn btn-primary btn-sm bs-m" data-toggle="modal" data-target="#%s" title="Delete">
                    <i class="bs-fa fa fa-trash"></i>
                </button>%s',
                $modalId,
                $deleteModal
            );
        }
        return sprintf(
            '<div class="d-inline-flex gap-2">
                <a href="%s" class="bs-btn btn btn-primary btn-sm" title="View" style="margin: 2px;"><i class="bs-fa fa fa-eye"></i></a>
                <a href="%s" class="bs-btn btn btn-primary btn-sm bs-m" title="Edit"><i class="bs-fa fa fa-edit"></i></a>
                %s
               %s
            </div>',
            $viewUrl,
            $editUrl,
            $progressButton,
            $deleteButton
        );
    }

    private function buildQuery(QueryBuilder $qb, int $projectId): QueryBuilder
    {
        $session = $this->requestStack->getSession();
        $filters = $session->get('employee_task_filters_' . $projectId, []);
        $qb->select('t')
            ->addSelect('m', 'emp', 'adm')
            ->from(Task::class, 't')
            ->where('t.project = :projectId')
            ->setParameter('projectId', $projectId)
            ->leftJoin('t.assignedTo', 'm')
            ->leftJoin('t.assignedBy', 'emp')
            ->leftJoin('t.assignedByAdmin', 'adm');
        if (!empty($filters['startDate']) && !empty($filters['endDate'])) {
            try {
                $startDate = new \DateTime($filters['startDate']);
                $endDate = new \DateTime($filters['endDate']);
                $qb->andWhere('t.assignedAt BETWEEN :startDate AND :endDate')
                    ->setParameter('startDate', $startDate->setTime(0, 0))
                    ->setParameter('endDate', $endDate->setTime(23, 59, 59));
            } catch (\Exception $e) {
                $this->logger->warning('Invalid date filter: ' . $e->getMessage());
            }
        }

        return $qb;
    }
}