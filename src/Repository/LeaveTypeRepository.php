<?php

namespace App\Repository;

use App\Entity\LeaveType;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<LeaveType>
 */
class LeaveTypeRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, LeaveType::class);
    }
    public function findOneByFieldActive(string $field, $value): ?LeaveType
    {
        return $this->createQueryBuilder('lt')
            ->where('lt.' . $field . ' = :value')
            ->andWhere('lt.isDelete = :isDelete')
            ->setParameter('value', $value)
            ->setParameter('isDelete', false)
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult();
    }
}
