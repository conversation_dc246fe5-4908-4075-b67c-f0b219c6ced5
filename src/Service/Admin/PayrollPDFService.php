<?php

namespace App\Service\Admin;

use App\Entity\Payroll;
use Doctrine\ORM\EntityManagerInterface;
use O<PERSON>s\DataTablesBundle\Adapter\ArrayAdapter;
use Omines\DataTablesBundle\Column\NumberColumn;
use Omines\DataTablesBundle\Column\TextColumn;
use Omines\DataTablesBundle\DataTable;
use Omines\DataTablesBundle\DataTableFactory;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Request;
use App\Traits\ExceptionLoggerTrait;
use App\Helper\ModalboxHelper;
use Knp\Snappy\Pdf;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Error\SyntaxError;

class PayrollPDFService
{
    use ExceptionLoggerTrait;

    public function __construct(
        private readonly DataTableFactory $dataTableFactory,
        private readonly ParameterBagInterface $parameterBag,
        private readonly EntityManagerInterface $entityManager,
        private readonly ModalboxHelper $modalboxHelper,
        private readonly Pdf $pdf,
        private readonly Environment $twig,
        private readonly UrlGeneratorInterface $urlGenerator,
    ) {
        $this->initializeLogger($parameterBag, 'extra_pay_service');
    }

    public function createExtraPayTable(Request $request): DataTable
    {
        $qb = $this->entityManager->createQueryBuilder();
        $results = $qb
            ->select('DISTINCT p.month, p.year')
            ->from(Payroll::class, 'p')
            ->getQuery()
            ->getArrayResult();

        $data = array_map(static function ($row) {
            return [
                'month' => $row['month'],
                'year' => $row['year'],
            ];
        }, $results);

        $dataTable = $this->dataTableFactory->create();

        foreach ($this->getColumns() as $key => $column) {
            $dataTable->add($key, $column['className'], array_filter($column, fn($k) => $k !== 'className', ARRAY_FILTER_USE_KEY));
        }

        return $dataTable
            ->createAdapter(ArrayAdapter::class, $data)
            ->addOrderBy('year', 'desc')
            ->addOrderBy('month', 'desc')
            ->handleRequest($request);
    }

    private function getColumns(): array
    {
        return [
            'month' => [
                'label' => 'Month',
                'className' => TextColumn::class,
                'field' => 'month',
                'render' => fn($value, $context) => self::MONTH_MAP[$context['month']] ?? '',
            ],
            'year' => [
                'label' => 'Year',
                'field' => 'year',
                'className' => NumberColumn::class,
            ],
            'actions' => [
                'label' => 'Actions',
                'className' => TextColumn::class,
                'orderable' => false,
                'searchable' => false,
                'render' => fn($value, $context) => $this->renderActionColumn($context),
            ],
        ];
    }

    public const MONTH_MAP = [
        1 => 'January',
        2 => 'February',
        3 => 'March',
        4 => 'April',
        5 => 'May',
        6 => 'June',
        7 => 'July',
        8 => 'August',
        9 => 'September',
        10 => 'October',
        11 => 'November',
        12 => 'December',
    ];

    private function renderActionColumn($context): string
    {
        $month = $context['month'];
        $year = $context['year'];
        $modalId = "sharePayslipModal_{$year}_{$month}";

        $modalBody = 'Are you sure you want to share the payslip of all employees via email?';
        $footerButtons = sprintf(
            '<button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>' .
            '<button type="button" class="btn btn-primary share-payslip-confirm" data-dismiss="modal" data-month="%s" data-year="%s">Yes, Share</button>',
            $month,
            $year
        );

        $modal = $this->modalboxHelper->renderModal(
            $modalId,
            'Share Payslip',
            $modalBody,
            $footerButtons
        );
        $bankUrl = $this->urlGenerator->generate('admin_payroll_bank_sheet', [
            'month' => $month,
            'year' => $year,
        ]);

        $button = sprintf(
            '<div class="d-inline-flex gap-2">
        <button type="button" class="bs-btn btn btn-sm btn-primary" style="margin: 2px;" data-toggle="modal" data-target="#%s" title="Share-to-Employees">
            <i class="bs-fa fa fa-share"></i>
        </button>
        <a href="%s" class="bs-btn btn btn-sm btn-primary" style="margin: 2px;" title="Download Bank XLSX">
            <i class="bs-fa fa fa-file-excel-o"></i>
        </a>
    </div>',
            $modalId,
            $bankUrl
        );

        return $button . $modal;
    }

    public function generatePayslipPdf(array $payslipData): string
    {
        try {
            $html = $this->twig->render('admin/payrollPDF/payslip.html.twig', [
                'data' => $payslipData,
            ]);
        } catch (LoaderError|SyntaxError|RuntimeError $e) {
            $this->log('Failed to render payslip template', [
                'error' => $e->getMessage(),
            ]);
        }
        return $this->pdf->getOutputFromHtml($html);
    }


    public function preparePayslipData(Payroll $payroll): array
    {
        $employee = $payroll->getEmployee() ?? throw new \RuntimeException('Employee not found');
        $month = $payroll->getMonth();
        $year = $payroll->getYear();
        $firstDepartment = $employee->getDepartments()->first();
        $department = is_object($firstDepartment) ? $firstDepartment->getDepName() : 'Not Assigned';

        return [
            'monthName' => self::MONTH_MAP[$month],
            'year' => $year,
            'employeeNumber' => $employee->getEmployeeCode(),
            'pfNumber' => $employee->getPfNumber(),
            'employeeName' => $employee->getName(),
            'esiNumber' => $employee->getEsiNumber(),
            'bankName' => $employee->getBankName(),
            'department' => $department,
            'accountNumber' => $employee->getBankAccountNumber(),
            'uan' => $employee->getUan(),
            'workingDetails' => [
                'WD' => $payroll->getWorkingDays(),
                'UL' => $payroll->getDeduction(),
                'PD' => $payroll->getPresentDays(),
                'TOTAL' => $payroll->getWorkingDays(),
            ],
            'earnings' => [
                'BASIC' => $payroll->getBasicDaSalary(),
                'HRA' => $payroll->getHra(),
                'CONV' => $payroll->getConveyanceAllowance(),
                'BONUS' => $payroll->getExtraPay(),
                'SPECIAL' => $payroll->getSpecialAllowance(),
                'Medical' => $payroll->getMedicalAllowance(),
                'Telephone' => $payroll->getTelephoneAllowance(),
            ],
            'deductions' => [
                'PF' => $payroll->getPfEmployee(),
                'ESI' => $payroll->getEsic(),
                'TDS' => $payroll->getTdsDeduction(),
                'OTH' => $payroll->getLoanOtherDeductions(),
                'LEAVE' => $payroll->getTotalLeaveDeduction(),
            ],
            'grossIncome' => $payroll->getSalary(),
            'grossDeduction' => $payroll->getTotalDeductions(),
            'salaryAmount' => $payroll->getNetSalary(),
            'payableAmount' => $payroll->getSalaryPayable(),
        ];
    }

    /**
     * @throws \DateMalformedStringException
     */
    public function export(array $payrolls): StreamedResponse
    {
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        $row = 1;

        foreach ($payrolls as $payroll) {
            $employee = $payroll->getEmployee();
            $A = 'BTPLKMB';
            $B = 'VPAY';
            $ifscCode = $employee->getIfscCode();
            $C = (str_starts_with($ifscCode, 'KKBK')) ? 'IFT' : 'NEFT';
            $D = '';
            $year = $payroll->getYear();
            $month = $payroll->getMonth();
            $payrollDate = \DateTime::createFromFormat('Y-m-d', sprintf('%04d-%02d-01', $year, $month));
            $E = $payrollDate->modify('first day of next month')->setDate(
                (int) $payrollDate->format('Y'),
                (int) $payrollDate->format('m'),
                10
            )->format('d/m/Y');

            $F = '';
            $G = **********;
            $I = 'M';
            $H = $payroll->getSalaryPayable();
            $J = '';
            $K = $payroll->getEmployee()->getName();
            $L = '';
            $M = $payroll->getEmployee()->getIfscCode();
            $N = $payroll->getEmployee()->getBankAccountNumber();
            $O = '';
            $P = '';
            $Q = '';
            $R = '';
            $S = '';
            $T = '';
            $U = '';
            $V = $payroll->getEmployee()->getEmail();
            $W = $payroll->getEmployee()->getPersonalPhoneNumber();
            $employeeName = $employee->getName();
            $nameParts = preg_split('/\s+/', trim($employeeName));

            $firstName = $nameParts[0] ?? 'Employee';
            $lastInitial = isset($nameParts[1]) ? strtoupper($nameParts[count($nameParts) - 1][0]) : '';

            $monthMap = [
                1 => 'Jan', 2 => 'Feb', 3 => 'Mar', 4 => 'Apr', 5 => 'May', 6 => 'Jun',
                7 => 'Jul', 8 => 'Aug', 9 => 'Sep', 10 => 'Oct', 11 => 'Nov', 12 => 'Dec',
            ];

            $monthShort = $monthMap[$month] ?? '';

            $X = sprintf('%s%s-Salary-%s%d', $firstName, $lastInitial, $monthShort, $year);

            $Y = "BS-Salary-$monthShort-$year";

            $sheet->setCellValue("A$row", $A);
            $sheet->setCellValue("B$row", $B);
            $sheet->setCellValue("C$row", $C);
            $sheet->setCellValue("D$row", $D);
            $sheet->setCellValue("E$row", $E);
            $sheet->setCellValue("F$row", $F);
            $sheet->setCellValue("G$row", $G);
            $sheet->setCellValue("H$row", $H);
            $sheet->setCellValue("I$row", $I);
            $sheet->setCellValue("J$row", $J);
            $sheet->setCellValue("K$row", $K);
            $sheet->setCellValue("L$row", $L);
            $sheet->setCellValue("M$row", $M);
            $sheet->setCellValue("N$row", $N);
            $sheet->setCellValue("O$row", $O);
            $sheet->setCellValue("P$row", $P);
            $sheet->setCellValue("Q$row", $Q);
            $sheet->setCellValue("R$row", $R);
            $sheet->setCellValue("S$row", $S);
            $sheet->setCellValue("T$row", $T);
            $sheet->setCellValue("U$row", $U);
            $sheet->setCellValue("V$row", $V);
            $sheet->setCellValue("W$row", $W);
            $sheet->setCellValue("X$row", $X);
            $sheet->setCellValue("Y$row", $Y);

            $row++;
        }
        foreach (range('A', 'Y') as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }
        $writer = new Xlsx($spreadsheet);

        return new StreamedResponse(function () use ($writer) {
            $writer->save('php://output');
        }, 200, [
            'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'Content-Disposition' => sprintf('attachment;filename="BankSalarySheet-%s-%d.xlsx"', $monthShort, $year),
            'Cache-Control' => 'max-age=0',
        ]);
    }
}