<?php

namespace App\Form;

use App\Entity\Announcement;
use App\Entity\Department;
use Doctrine\ORM\EntityRepository;
use FOS\CKEditorBundle\Form\Type\CKEditorType;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\CallbackTransformer;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\DateType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\Choice;
use Symfony\Component\Validator\Constraints\Length;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Form\FormEvents;
use Symfony\Component\Form\FormEvent;
use Symfony\Component\Form\FormError;

class AnnouncementType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('title', TextType::class, [
                'constraints' => [
                    new NotBlank([
                        'message' => 'Field label is required.',
                    ]),
                    new Length([
                        'min' => 2,
                        'max' => 255,
                        'minMessage' => 'Title name must be at least {{ limit }} characters long',
                        'maxMessage' => 'Title name cannot be longer than {{ limit }} characters',
                    ]),
                ],
            ])
            ->add('content', CKEditorType::class, [
                'config' => ['toolbar' => 'standard'],
                'constraints' => [new NotBlank()],
                'row_attr' => [
                    'class' => 'col-md-12'
                ],
                'attr' => ['rows' => '10'],
            ])
            ->add('targetAllEmployees', CheckboxType::class, [
                'required' => false,
                'label' => 'All employees',
            ])
            ->add('departments', EntityType::class, [
                'label' => false,
                'class' => Department::class,
                'choice_label' => 'depName',
                'multiple' => true,
                'expanded' => true,
                'required' => false,
                'query_builder' => function (EntityRepository $er) {
                    return $er->createQueryBuilder('d')
                        ->where('d.isDelete = :isDelete')
                        ->setParameter('isDelete', false);
                },
            ])
            ->add('targetTeamLeadersOnly', CheckboxType::class, [
                'required' => false,
                'label' => 'Team leaders',
            ])
            ->add('startDate', TextType::class, [
                'label' => 'Start Date',
                'attr' => [
                    'class' => 'form-control datepicker',
                    'placeholder' => 'Select start date',
                    'required' => 'required',
                ],
                'constraints' => [new NotBlank()],
            ])
            ->add('endDate', TextType::class, [
                'label' => 'End Date',
                'attr' => [
                    'class' => 'form-control datepicker',
                    'placeholder' => 'Select end date',
                    'required' => 'required',
                ],
                'constraints' => [new NotBlank()],
            ])
            ->add('isImportant', CheckboxType::class, [
                'required' => false,
                'label' => 'Mark as important',
            ])
            ->add('priority', ChoiceType::class, [
                'choices' => [
                    'Normal' => 0,
                    'High' => 1,
                    'Pinned' => 2,
                ],
                'constraints' => [
                    new Choice(['choices' => [0, 1, 2]]),
                ],
            ])
            ->add('sendEmail', CheckboxType::class, [
                'required' => false,
                'label' => 'Email notification',
            ])
            ->add('status', ChoiceType::class, [
                'choices' => [
                    'Draft' => 'draft',
                    'Published' => 'published',
                    'Archived' => 'archived',
                ],
                'constraints' => [
                    new NotBlank(),
                    new Choice(['choices' => ['draft', 'published', 'archived']]),
                ],
            ]);
        foreach (['startDate', 'endDate'] as $field) {
            $builder->get($field)
                ->addModelTransformer(new CallbackTransformer(
                    function ($dateAsObject) {
                        return $dateAsObject instanceof \DateTimeInterface ? $dateAsObject->format('d-m-Y') : '';
                    },
                    function ($dateAsString) {
                        return $dateAsString ? \DateTime::createFromFormat('d-m-Y', $dateAsString) : null;
                    }
                ));
        }

        $builder->addEventListener(
            FormEvents::POST_SUBMIT,
            function (FormEvent $event) {
                $form = $event->getForm();
                $data = $event->getData();

                if (!$data instanceof Announcement) {
                    return;
                }

                $targetAllEmployees = $form->get('targetAllEmployees')->getData();
                $departments = $form->get('departments')->getData();
                $targetTeamLeadersOnly = $form->get('targetTeamLeadersOnly')->getData();
                if (!$targetAllEmployees && $departments->isEmpty() && !$targetTeamLeadersOnly) {
                    $form->get('targetAllEmployees')->addError(
                        new FormError(
                            'You must select at least one audience: all employees, specific departments, or team leaders.'
                        )
                    );
                }

                $startDate = $form->get('startDate')->getData();
                $endDate = $form->get('endDate')->getData();
                if ($startDate && $endDate && $endDate < $startDate) {
                    $form->get('endDate')->addError(
                        new FormError(
                            'End date cannot be before start date.'
                        )
                    );
                }
            }
        );
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => Announcement::class,
        ]);
    }
}