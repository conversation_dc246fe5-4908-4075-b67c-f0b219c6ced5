<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Your Password</title>
</head>
<body style="margin: 0; padding: 0; background-color: #f8f9fa; font-family: Arial, sans-serif; line-height: 1.6;">
<table role="presentation" style="width: 100%; border: none; margin: 0; padding: 40px 20px; background-color: #f8f9fa;">
    <tr>
        <td align="center">
            <table role="presentation" style="width: 100%; max-width: 600px; border: none; margin: 0; padding: 0; background-color: #ffffff; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                <!-- Content -->
                <tr>
                    <td style="padding: 20px 40px;">
                        <h1 style="color: #333333; font-size: 24px; margin: 0 0 20px 0;">Hi!</h1>

                        <p style="color: #666666; font-size: 16px; margin: 0 0 20px 0;">
                            To reset your password, please click the button below:
                        </p>

                        <!-- Button -->
                        <p style="margin: 30px 0;">
                            <a href="{{ url('app_reset_password_employee', {token: resetToken.token}) }}"
                               style="display: inline-block; background-color: #fd7e14; color: #ffffff; text-decoration: none; padding: 12px 30px; border-radius: 4px; font-weight: bold;">
                                Reset Password
                            </a>
                        </p>

                        <!-- Fallback URL -->
                        <p style="color: #666666; font-size: 14px; margin: 0 0 20px 0;">
                            If the button doesn't work, copy and paste this link into your browser:
                            <br>
                            <a href="{{ url('app_reset_password_employee', {token: resetToken.token}) }}"
                               style="color: #fd7e14; text-decoration: underline; word-break: break-all;">
                                {{ url('app_reset_password_employee', {token: resetToken.token}) }}
                            </a>
                        </p>

                        <p style="color: #666666; font-size: 14px; margin: 0 0 20px 0;">
                            This link will expire in {{ resetToken.expirationMessageKey|trans(resetToken.expirationMessageData, 'ResetPasswordBundle') }}.
                        </p>

                        <p style="color: #666666; font-size: 16px; margin: 30px 0 0 0;">
                            Best regards,<br>
                            The BrainStream Team
                        </p>
                    </td>
                </tr>

                <!-- Footer -->
                <tr>
                    <td style="padding: 20px 40px 40px 40px; text-align: center; color: #999999; font-size: 14px; border-top: 1px solid #eeeeee;">
                        <p style="margin: 0;">
                            If you didn't request a password reset, please ignore this email or contact support if you have concerns.
                        </p>
                    </td>
                </tr>
            </table>
        </td>
    </tr>
</table>
</body>
</html>