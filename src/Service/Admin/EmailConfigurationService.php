<?php
namespace App\Service\Admin;

use App\Entity\Setting;
use App\Repository\SettingRepository;
use Doctrine\ORM\EntityManagerInterface;

readonly class EmailConfigurationService
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private SettingRepository      $emailSettingRepository
    ) {
    }

    public function getEmailSettings(): array
    {
        $emailSettings = $this->emailSettingRepository->findAll();
        $settings = [];

        $dsnSetting = $this->emailSettingRepository->findOneBy(['fieldName' => 'MAILER_DSN']);
        if ($dsnSetting) {
            $dsnString = $dsnSetting->getFieldValue();
            if (preg_match('/^smtp:\/\/([^:]+):([^@]+)@([^:]+):(\d+)/', $dsnString, $matches)) {
                $settings['Username'] = $matches[1];
                $settings['Password'] = $matches[2];
                $settings['Host'] = $matches[3];
                $settings['Port'] = $matches[4];
            }
        }

        $senderSetting = $this->emailSettingRepository->findOneBy(['fieldName' => 'MAILER_FROM_ADDRESS']);
        if ($senderSetting) {
            $settings['Sender_Address'] = $senderSetting->getFieldValue();
        }

        foreach ($emailSettings as $setting) {
            if (!in_array($setting->getFieldName(), ['MAILER_DSN', 'MAILER_FROM_ADDRESS'])) {
                $settings[$setting->getFieldName()] = $setting->getFieldValue();
            }
        }

        return $settings;
    }

    public function updateConfiguration(string $fieldName, ?string $fieldValue): void
    {
        $configuration = $this->emailSettingRepository->findOneBy(['fieldName' => $fieldName]);

        if (!$configuration) {
            $configuration = new Setting();
            $configuration->setFieldName($fieldName);
            $configuration->setDateCreated(new \DateTime());
        }

        $configuration->setFieldValue($fieldValue);
        $configuration->setDateUpdated(new \DateTime());

        $this->entityManager->persist($configuration);
        $this->entityManager->flush();
    }
}
