{"name": "brainee-hrm-desktop", "version": "1.0.0", "description": "Brainee HRM Desktop Employee Tracking Application", "main": "src/main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "build-win": "electron-builder --win", "build-mac": "electron-builder --mac", "build-linux": "electron-builder --linux", "pack": "electron-builder --dir", "dist": "electron-builder", "test": "node test-setup.js", "install-app": "node install.js", "postinstall": "echo 'Run npm run test to verify installation'"}, "keywords": ["electron", "hrm", "employee-tracking", "desktop-app"], "author": "Brainee HRM", "license": "MIT", "devDependencies": {"electron": "^28.3.3", "electron-builder": "^24.9.1"}, "dependencies": {"auto-launch": "^5.0.5", "axios": "^1.6.2", "electron-store": "^8.1.0", "form-data": "^4.0.0", "node-machine-id": "^1.1.12", "sqlite3": "^5.1.6"}, "build": {"appId": "com.brainee.hrm.desktop", "productName": "Brainee HRM Desktop", "directories": {"output": "dist"}, "files": ["src/**/*", "assets/**/*", "node_modules/**/*"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createStartMenuShortcut": true, "createDesktopShortcut": true}}}