<?php

namespace App\Service;

use App\Repository\ScreenshotLogRepository;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;

class ScreenshotFileOrganizer
{
    public function __construct(
        private readonly ScreenshotLogRepository $screenshotLogRepository,
        private readonly EntityManagerInterface $entityManager,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * Organize screenshots that have local file paths into server folder structure
     */
    public function organizeScreenshots(): int
    {
        $organized = 0;
        
        // Find screenshots with local file paths (not yet organized)
        $screenshots = $this->screenshotLogRepository->findBy([
            'isProcessed' => false
        ]);

        foreach ($screenshots as $screenshot) {
            if ($this->organizeScreenshot($screenshot)) {
                $organized++;
            }
        }

        if ($organized > 0) {
            $this->logger->info("Organized {$organized} screenshots into server folder structure");
        }

        return $organized;
    }

    /**
     * Organize a single screenshot
     */
    private function organizeScreenshot($screenshot): bool
    {
        try {
            $localFilePath = $screenshot->getFilePath();
            
            // Skip if already organized (server path)
            if (str_starts_with($localFilePath, '/uploads/')) {
                $screenshot->setIsProcessed(true);
                $this->entityManager->flush();
                return false;
            }

            // Check if local file exists
            if (!file_exists($localFilePath)) {
                $this->logger->warning("Local screenshot file not found: {$localFilePath}");
                return false;
            }

            // Get employee name and clean it for folder
            $employee = $screenshot->getEmployee();
            $employeeName = $employee->getName();
            $cleanEmployeeName = preg_replace('/[^a-zA-Z0-9_-]/', '', str_replace(' ', '', $employeeName));
            
            // Get month from captured date
            $capturedDate = $screenshot->getCapturedAt();
            $monthFolder = $capturedDate->format('Y-m');
            
            // Create server folder structure
            $projectDir = dirname(__DIR__, 2); // Go up from src/Service to project root
            $serverDir = $projectDir . '/public/uploads/screenshots/' . $cleanEmployeeName . '/' . $monthFolder;
            if (!is_dir($serverDir)) {
                mkdir($serverDir, 0755, true);
            }

            // Copy file to server location
            $fileName = $screenshot->getFileName();
            $serverFilePath = $serverDir . '/' . $fileName;
            
            if (copy($localFilePath, $serverFilePath)) {
                // Update database with server path
                $serverPath = '/uploads/screenshots/' . $cleanEmployeeName . '/' . $monthFolder . '/' . $fileName;
                $screenshot->setFilePath($serverPath);
                $screenshot->setIsProcessed(true);
                
                $this->entityManager->flush();
                
                $this->logger->info("Organized screenshot: {$fileName} -> {$serverPath}");
                return true;
            } else {
                $this->logger->error("Failed to copy screenshot file: {$localFilePath} -> {$serverFilePath}");
                return false;
            }

        } catch (\Exception $e) {
            $this->logger->error("Error organizing screenshot {$screenshot->getId()}: " . $e->getMessage());
            return false;
        }
    }
}
