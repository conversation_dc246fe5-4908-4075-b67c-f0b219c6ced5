
###> symfony/framework-bundle ###
/.env.local
/.env.local.php
/.env.*.local
/config/secrets/prod/prod.decrypt.private.php
/public/bundles/
/var/
/vendor/
###< symfony/framework-bundle ###

###> symfony/webpack-encore-bundle ###
/node_modules/
/public/build/
npm-debug.log
yarn-error.log
.env
###< symfony/webpack-encore-bundle ###


###> phpunit/phpunit ###
/phpunit.xml

.phpunit.result.cache
###< phpunit/phpunit ###

###> symfony/phpunit-bridge ###
.phpunit.result.cache
/phpunit.xml
###< symfony/phpunit-bridge ###

###> symfony/asset-mapper ###
/assets/vendor/
###< symfony/asset-mapper ###

symfony.lock

public/uploads
public/uploads/*

.DS_Store
**/.DS_Store
.idea/*
###> phpstan/phpstan ###
phpstan.neon
###< phpstan/phpstan ###

# Electron app
electron-app/node_modules/
electron-app/dist/
electron-app/build/
electron-app/out/
electron-app/*.log
electron-app/.env*
electron-app/coverage/
electron-app/.nyc_output/

# Database files
*.sqlite
*.sqlite3
*.db

# Log files
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env.test
.env.production

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/*
!.vscode/extensions.json
*.swp
*.swo
*~

# OS generated files
Thumbs.db
ehthumbs.db
Desktop.ini

# Backup files
*.bak
*.backup
*.old

# Composer
composer.phar

# PHPUnit
/phpunit.xml
.phpunit.result.cache

# Build artifacts
/build/
/dist/
