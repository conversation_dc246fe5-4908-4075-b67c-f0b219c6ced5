<?php

namespace App\Command;

use App\Entity\Announcement;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'app:archive-expired-announcements',
    description: 'Archives announcements whose end date has passed.'
)]
class ArchiveExpiredAnnouncementsCommand extends Command
{
    public function __construct(private readonly EntityManagerInterface $entityManager)
    {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->setDescription('Archives announcements whose end date has passed.')
            ->setHelp('This command checks all published announcements and sets their status to "archived" if their end date is in the past.');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        $now = new \DateTime();
        $announcements = $this->entityManager->getRepository(Announcement::class)->findBy([
            'status' => 'published',
        ]);

        $archivedCount = 0;

        foreach ($announcements as $announcement) {
            if ($announcement->getEndDate() < $now) {
                $announcement->setStatus('archived');
                $this->entityManager->persist($announcement);
                $archivedCount++;
            }
        }

        $this->entityManager->flush();

        if ($archivedCount > 0) {
            $io->success(sprintf('Archived %d announcement(s).', $archivedCount));
        } else {
            $io->info('No announcements needed archiving.');
        }

        return Command::SUCCESS;
    }
}