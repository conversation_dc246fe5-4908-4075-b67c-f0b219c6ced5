<?php

namespace App\Repository;

use App\Entity\EmployeeRemainingHours;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<EmployeeRemainingHours>
 */
class EmployeeRemainingHoursRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, EmployeeRemainingHours::class);
    }

    //    /**
    //     * @return EmployeeRemainingHours[] Returns an array of EmployeeRemainingHours objects
    //     */
    //    public function findByExampleField($value): array
    //    {
    //        return $this->createQueryBuilder('e')
    //            ->andWhere('e.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->orderBy('e.id', 'ASC')
    //            ->setMaxResults(10)
    //            ->getQuery()
    //            ->getResult()
    //        ;
    //    }

    //    public function findOneBySomeField($value): ?EmployeeRemainingHours
    //    {
    //        return $this->createQueryBuilder('e')
    //            ->andWhere('e.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->getQuery()
    //            ->getOneOrNullResult()
    //        ;
    //    }
    public function countUnapprovedRemainingHours(): int
    {
        return $this->createQueryBuilder('erh')
            ->select('COUNT(erh.id)')
            ->where('erh.isApproved = :isApproved')
            ->setParameter('isApproved', false)
            ->getQuery()
            ->getSingleScalarResult();
    }
}
