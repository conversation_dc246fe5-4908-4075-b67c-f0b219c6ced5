<?php

namespace App\Controller\Admin;

use App\Entity\MasterEmployee;
use App\Entity\User;
use App\Service\Common\ReportGeneratorService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Doctrine\ORM\EntityManagerInterface;

#[Route('/admin/employee')]
class AdminReportController extends AbstractController
{
    public function __construct(
        private readonly ReportGeneratorService $reportService,
        private readonly EntityManagerInterface $entityManager,
    ) {
    }

    #[Route('/report/360/form', name: 'admin_generate_360_report_form', methods: ['GET', 'POST'])]
    public function generate360ReportForm(Request $request): Response
    {
        $user = $this->getUser();
        if (!$user instanceof User) {
            throw $this->createAccessDeniedException('This can only be accessed by an admin.');
        }

        $employeeId = $request->request->get('employeeId');
        $employee = null;
        if ($employeeId) {
            $employee = $this->entityManager->getRepository(MasterEmployee::class)->findOneBy([
                'id' => $employeeId,
                'isDelete' => false,
            ]);
            if (!$employee) {
                $this->addFlash('error', 'Selected employee not found.');
            }
        }

        $reportData = null;
        $errors = [];
        $formData = [
            'periodType' => $request->request->get('periodType', 'year'),
            'year' => $request->request->get('year', (int) date('Y')),
            'month' => $request->request->get('month') ? (int) $request->request->get('month') : null,
            'employeeId' => $employeeId,
        ];

        if ($request->isMethod('POST') && $employee) {
            $periodType = $formData['periodType'];
            $year = (int) $formData['year'];
            $month = $formData['month'];

            if (!in_array($periodType, ['year', 'month'])) {
                $errors[] = 'Invalid period type selected.';
            }

            if ($year < 2000 || $year > (int) date('Y')) {
                $errors[] = 'Invalid year. Must be between 2000 and current year.';
            }

            if ($periodType === 'month' && (!$month || $month < 1 || $month > 12)) {
                $errors[] = 'Please select a valid month.';
            }

            if (empty($errors)) {
                try {
                    $reportData = $this->reportService->generate360Report($employee->getId(), $periodType, $year, $month);

                    $reportData = [
                        'employee_name' => $employee->getName() ?? 'Unknown',
                        'employee_id' => $employee->getId(),
                        'report_period' => ($periodType === 'year' ? "Year $year" : \DateTime::createFromFormat('!m', $month)->format('F') . " $year"),
                        'task_score' => $reportData['task_score'],
                        'team_logger_score' => $reportData['team_logger_score'],
                        'performance_feedback_score' => $reportData['performance_feedback_score'],
                        'bug_tracker_score' => $reportData['bug_tracker_score'],
                        'final_score' => $reportData['final_score'],
                        'comments' => 'Generated automatically based on KRA metrics.',
                        'Extra_Hours' => $reportData['weekend_hours'],
                    ];
                } catch (\Exception $e) {
                    $errors[] = 'Error generating report: ' . $e->getMessage();
                }
            }
            foreach ($errors as $error) {
                $this->addFlash('error', $error);
            }
        }

        $employees = $this->entityManager->getRepository(MasterEmployee::class)->findBy(['isDelete' => false], ['name' => 'ASC']);

        return $this->render('admin/Report_360/index.html.twig', [
            'currentYear' => (int) date('Y'),
            'selectedPeriodType' => $formData['periodType'],
            'selectedYear' => $formData['year'],
            'selectedMonth' => $formData['month'],
            'selectedEmployeeId' => $formData['employeeId'],
            'reportData' => $reportData,
            'employees' => $employees,
        ]);
    }
}