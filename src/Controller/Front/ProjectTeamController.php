<?php

namespace App\Controller\Front;

use App\Entity\MasterEmployee;
use App\Entity\Project;
use App\Entity\ProjectTeam;
use App\Form\ProjectTeamFormType;
use App\Service\Common\ProjectTeamService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Csrf\CsrfToken;
use Symfony\Component\Security\Csrf\CsrfTokenManagerInterface;

#[Route('/employee/project')]
class ProjectTeamController extends AbstractController
{
    public function __construct(
        private readonly ProjectTeamService $projectTeamService,
        private readonly EntityManagerInterface $entityManager,
        private readonly CsrfTokenManagerInterface $csrfTokenManager
    ) {
    }

    private function ensureTeamLeaderAccess(MasterEmployee $employee): void
    {
        if (!$employee->isTeamLeader()) {
            throw $this->createAccessDeniedException('This can only be accessed by a team leader.');
        }
    }

    /**
     * @throws \Exception
     */
    #[Route(path: '/{project_id}/team-manage', name: 'employee_project_team_manage', methods: ['GET', 'POST'])]
    public function index(Request $request, int $project_id): Response
    {
        $employee = $this->getUser();
        $this->ensureTeamLeaderAccess($employee);

        $table = $this->projectTeamService->createProjectTeamTable($request, $project_id,$employee);

        if ($table->isCallback()) {
            return $table->getResponse();
        }

        return $this->render('Front/project_team/index.html.twig', [
            'datatable' => $table,
            'project_id' => $project_id,
        ]);
    }

    /**
     * @throws \Exception
     */
    #[Route(path: '/{project_id}/team-manage/new', name: 'employee_project_team_new', methods: ['GET', 'POST'])]
    public function new(Request $request, int $project_id): Response
    {
        $employee = $this->getUser();
        $this->ensureTeamLeaderAccess($employee);

        $project = $this->entityManager->getRepository(Project::class)->find($project_id);
        if (!$project) {
            throw $this->createNotFoundException('Project not found.');
        }

        $projectTeam = new ProjectTeam();
        $projectTeam->setProject($project);

        $form = $this->createForm(ProjectTeamFormType::class, $projectTeam, [
            'project_id' => $project_id,
            'team_leader' => $employee,
            'project' => $project,
        ]);

        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $teamMember = $projectTeam->getTeamMember();
            $projectTeamRepo = $this->entityManager->getRepository(ProjectTeam::class);

            if ($projectTeamRepo->isTeamMemberAlreadyAssigned($project_id, $teamMember->getId())) {
                $this->addFlash('error', 'This team member is already assigned to this project.');
                return $this->render('Front/project_team/new.html.twig', [
                    'form' => $form->createView(),
                    'project_id' => $project_id,
                ]);
            }

            $projectTeam->setAssignedByTeamLeader($employee);
            $this->entityManager->persist($projectTeam);
            $this->entityManager->flush();

            $this->addFlash('success', 'Team member assigned successfully.');
            return $this->redirectToRoute('employee_project_team_manage', ['project_id' => $project_id]);
        }

        return $this->render('Front/project_team/new.html.twig', [
            'form' => $form->createView(),
            'project_id' => $project_id,
        ]);
    }

    #[Route(path: '/team-manage/delete/{id}', name: 'employee_project_team_delete', methods: ['POST'])]
    public function delete(Request $request, ProjectTeam $projectTeam): Response
    {
        $employee = $this->getUser();
        $this->ensureTeamLeaderAccess($employee);

        $token = $request->request->get('_token');
        if (!$this->csrfTokenManager->isTokenValid(new CsrfToken('delete' . $projectTeam->getId(), $token))) {
            return $this->json([
                'success' => false,
                'message' => 'Invalid CSRF token.'
            ], Response::HTTP_FORBIDDEN);
        }

        $this->entityManager->remove($projectTeam);
        $this->entityManager->flush();

        return $this->json([
            'success' => true,
            'message' => 'Team member removed successfully.'
        ], Response::HTTP_OK);
    }
}