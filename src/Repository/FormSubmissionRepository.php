<?php

namespace App\Repository;

use App\Entity\FormSubmission;
use App\Entity\MasterEmployee;
use App\Entity\User;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<FormSubmission>
 */
class FormSubmissionRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, FormSubmission::class);
    }

    //    /**
    //     * @return FormSubmission[] Returns an array of FormSubmission objects
    //     */
    //    public function findByExampleField($value): array
    //    {
    //        return $this->createQueryBuilder('f')
    //            ->andWhere('f.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->orderBy('f.id', 'ASC')
    //            ->setMaxResults(10)
    //            ->getQuery()
    //            ->getResult()
    //        ;
    //    }

    //    public function findOneBySomeField($value): ?FormSubmission
    //    {
    //        return $this->createQueryBuilder('f')
    //            ->andWhere('f.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->getQuery()
    //            ->getOneOrNullResult()
    //        ;
    //    }

    public function findUniqueSubmissionDates(MasterEmployee $employee): array
    {
        $result = $this->createQueryBuilder('s')
            ->select('s.reviewPeriod')
            ->where('s.employee = :employee')
            ->andWhere('s.status = :status')
            ->setParameter('employee', $employee)
            ->setParameter('status', true)
            ->orderBy('s.reviewPeriod', 'DESC')
            ->distinct()
            ->getQuery()
            ->getResult();

        return array_column($result, 'reviewPeriod');
    }

    public function findSubmissionsWithDetailsByDate(MasterEmployee $employee, string $date)
    {
        return $this->createQueryBuilder('s')
            ->select('s', 't', 'r', 'f', 'sec')
            ->leftJoin('s.template', 't')
            ->leftJoin('s.responses', 'r')
            ->leftJoin('r.field', 'f')
            ->leftJoin('r.section', 'sec')
            ->where('s.employee = :employee')
            ->andWhere('s.status = :status')
            ->andWhere('s.reviewPeriod = :date')
            ->andWhere('s.employee != s.employeeReviewer OR s.employeeReviewer IS NULL')
            ->setParameter('employee', $employee)
            ->setParameter('status', true)
            ->setParameter('date', $date)
            ->orderBy('s.submissionDate', 'DESC')
            ->getQuery()
            ->getResult();
    }

    public function findUniqueSubmissionAdminDates(): array
    {
        $result = $this->createQueryBuilder('s')
            ->select('s.reviewPeriod')
            ->where('s.status = :status')
            ->setParameter('status', true)
            ->orderBy('s.reviewPeriod', 'DESC')
            ->distinct()
            ->getQuery()
            ->getResult();

        return array_column($result, 'reviewPeriod');
    }

    public function findSubmissionsWithDetailsByFilters(?MasterEmployee $employee = null, ?string $date = null)
    {
        $qb = $this->createQueryBuilder('s')
            ->select('s', 't', 'r', 'f', 'sec', 'e')
            ->leftJoin('s.template', 't')
            ->leftJoin('s.responses', 'r')
            ->leftJoin('r.field', 'f')
            ->leftJoin('r.section', 'sec')
            ->leftJoin('s.employee', 'e')
            ->andWhere('s.status = :status')
            ->setParameter('status', true);

        // Add employee filter if specified
        if ($employee) {
            $qb->andWhere('s.employee = :employee')
                ->setParameter('employee', $employee);
        }

        // Add date filter if specified
        if ($date) {
            $qb->andWhere('s.reviewPeriod = :date')
                ->setParameter('date', $date);
        }
        // Exclude submissions where employee and employeeReviewer are the same
        $qb->andWhere('s.employee != s.employeeReviewer OR s.employeeReviewer IS NULL');
        return $qb->orderBy('s.submissionDate', 'DESC')
            ->getQuery()
            ->getResult();
    }

    public function findSelfEvaluationsByFilters(?MasterEmployee $employee = null, ?string $date = null)
    {
        $qb = $this->createQueryBuilder('s')
            ->select('s', 't', 'r', 'f', 'sec', 'e')
            ->leftJoin('s.template', 't')
            ->leftJoin('s.responses', 'r')
            ->leftJoin('r.field', 'f')
            ->leftJoin('r.section', 'sec')
            ->leftJoin('s.employee', 'e')
            ->andWhere('s.status = :status')
            ->setParameter('status', true);

        // Filter by employee (if provided)
        if ($employee) {
            $qb->andWhere('s.employee = :employee')
                ->setParameter('employee', $employee);
        }

        // Filter by date (if provided)
        if ($date) {
            $qb->andWhere('s.reviewPeriod = :date')
                ->setParameter('date', $date);
        }

        // Ensure only self-evaluations (employee == reviewer)
        $qb->andWhere('s.employee = s.employeeReviewer');

        return $qb->orderBy('s.submissionDate', 'DESC')
            ->getQuery()
            ->getResult();
    }

    public function findSubmissionsWithDetailsSelfEvaluation(MasterEmployee $employee, string $date)
    {
        return $this->createQueryBuilder('s')
            ->select('s', 't', 'r', 'f', 'sec')
            ->leftJoin('s.template', 't')
            ->leftJoin('s.responses', 'r')
            ->leftJoin('r.field', 'f')
            ->leftJoin('r.section', 'sec')
            ->where('s.employee = :employee')
            ->andWhere('s.status = :status')
            ->andWhere('s.reviewPeriod = :date')
            ->andWhere('s.employee = s.employeeReviewer')
            ->setParameter('employee', $employee)
            ->setParameter('status', true)
            ->setParameter('date', $date)
            ->orderBy('s.submissionDate', 'DESC')
            ->getQuery()
            ->getResult();
    }
}
