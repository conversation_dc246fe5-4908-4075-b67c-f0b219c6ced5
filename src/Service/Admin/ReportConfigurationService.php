<?php
namespace App\Service\Admin;

use App\Entity\Setting;
use App\Repository\SettingRepository;
use Doctrine\ORM\EntityManagerInterface;
use App\Traits\ExceptionLoggerTrait;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;

class ReportConfigurationService
{
    use ExceptionLoggerTrait;
    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly SettingRepository      $reportSettingRepository,
        ParameterBagInterface                   $parameterBag,

    ) {
        $this->initializeLogger($parameterBag, 'ReportConfigurationService');
    }

    public function getReportSettings(array $fields): array
    {
        $settings = [];

        try {
            foreach ($fields as $key => $field) {
                $setting = $this->reportSettingRepository->findOneBy(['fieldName' => $field]);
                if ($setting) {
                    $settings[$key] = $setting->getFieldValue();
                }
            }

            // Fetch additional settings that are not in the predefined fields
            $reportSettings = $this->reportSettingRepository->findAll();
            foreach ($reportSettings as $setting) {
                if (!in_array($setting->getFieldName(), $fields, true)) {
                    $settings[$setting->getFieldName()] = $setting->getFieldValue();
                }
            }
        } catch (\Exception $e) {
            $this->log('Error fetching report settings', ['exception' => $e]);
        }

        return $settings;
    }

    public function updateConfiguration(string $fieldName, ?string $fieldValue): void
    {
        try {
            $configuration = $this->reportSettingRepository->findOneBy(['fieldName' => $fieldName]);

            if (!$configuration) {
                $configuration = new Setting();
                $configuration->setFieldName($fieldName);
                $configuration->setDateCreated(new \DateTime());
            }

            $configuration->setFieldValue($fieldValue);
            $configuration->setDateUpdated(new \DateTime());

            $this->entityManager->persist($configuration);
            $this->entityManager->flush();
        } catch (\Exception $e) {
            $this->log('Error updating configuration', ['fieldName' => $fieldName, 'exception' => $e]);
        }
    }
}
