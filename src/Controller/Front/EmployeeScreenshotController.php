<?php

namespace App\Controller\Front;

use App\Entity\MasterEmployee;
use App\Repository\ScreenshotLogRepository;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/employee/screenshots')]
class EmployeeScreenshotController extends AbstractController
{
    public function __construct(
        private readonly ScreenshotLogRepository $screenshotLogRepository,
    ) {
    }

    #[Route('/', name: 'employee_screenshots', methods: ['GET'])]
    public function index(Request $request): Response
    {
        $employee = $this->getUser();
        if (!$employee instanceof MasterEmployee) {
            throw $this->createAccessDeniedException('This can only be accessed by an employee.');
        }

        // Get date from request or default to today
        $selectedDate = $request->get('date', date('Y-m-d'));
        
        // Get screenshots for the employee and selected date
        $screenshots = $this->screenshotLogRepository->findByEmployeeAndDate(
            $employee,
            new \DateTime($selectedDate)
        );

        // Group screenshots by hour for better organization
        $screenshotsByHour = [];
        foreach ($screenshots as $screenshot) {
            $hour = $screenshot->getCapturedAt()->format('H:00');
            $screenshotsByHour[$hour][] = $screenshot;
        }

        return $this->render('Front/screenshots/index.html.twig', [
            'selectedDate' => $selectedDate,
            'screenshots' => $screenshots,
            'screenshotsByHour' => $screenshotsByHour,
            'totalScreenshots' => count($screenshots),
        ]);
    }

    #[Route('/view/{id}', name: 'employee_screenshot_view', methods: ['GET'])]
    public function viewScreenshot(int $id): Response
    {
        $employee = $this->getUser();
        if (!$employee instanceof MasterEmployee) {
            throw $this->createAccessDeniedException('This can only be accessed by an employee.');
        }

        $screenshot = $this->screenshotLogRepository->find($id);
        
        if (!$screenshot) {
            throw $this->createNotFoundException('Screenshot not found.');
        }

        // Ensure employee can only view their own screenshots
        if ($screenshot->getEmployee()->getId() !== $employee->getId()) {
            throw $this->createAccessDeniedException('You can only view your own screenshots.');
        }

        return $this->render('Front/screenshots/view.html.twig', [
            'screenshot' => $screenshot,
        ]);
    }

    #[Route('/daily-report/{date}', name: 'employee_screenshot_daily_report', methods: ['GET'])]
    public function dailyReport(string $date): Response
    {
        $employee = $this->getUser();
        if (!$employee instanceof MasterEmployee) {
            throw $this->createAccessDeniedException('This can only be accessed by an employee.');
        }

        $screenshots = $this->screenshotLogRepository->findByEmployeeAndDate(
            $employee,
            new \DateTime($date)
        );

        // Group screenshots by hour for better organization
        $screenshotsByHour = [];
        foreach ($screenshots as $screenshot) {
            $hour = $screenshot->getCapturedAt()->format('H:00');
            $screenshotsByHour[$hour][] = $screenshot;
        }

        return $this->render('Front/screenshots/daily_report.html.twig', [
            'employee' => $employee,
            'date' => $date,
            'screenshots' => $screenshots,
            'screenshotsByHour' => $screenshotsByHour,
            'totalScreenshots' => count($screenshots),
        ]);
    }
}
