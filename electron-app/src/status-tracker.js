const { powerMonitor, screen } = require('electron');

class StatusTracker {
    constructor() {
        this.status = 'active'; // active, inactive, idle
        this.lastActivity = new Date();
        this.idleThreshold = 5 * 60 * 1000; // 5 minutes in milliseconds
        this.inactiveThreshold = 10 * 60 * 1000; // 10 minutes in milliseconds
        this.statusCheckInterval = null;
        this.listeners = [];
        
        this.init();
    }

    init() {
        // Start monitoring
        this.startMonitoring();
        
        // Listen for system events
        this.setupSystemListeners();
    }

    startMonitoring() {
        // Check status every 30 seconds
        this.statusCheckInterval = setInterval(() => {
            this.checkStatus();
        }, 30000);
        
        // Initial status check
        this.checkStatus();
    }

    setupSystemListeners() {
        try {
            // Monitor system suspend/resume
            powerMonitor.on('suspend', () => {
                this.updateStatus('inactive');
                console.log('System suspended - Status: inactive');
            });

            powerMonitor.on('resume', () => {
                this.lastActivity = new Date();
                this.updateStatus('active');
                console.log('System resumed - Status: active');
            });

            // Monitor screen lock/unlock
            powerMonitor.on('lock-screen', () => {
                this.updateStatus('inactive');
                console.log('Screen locked - Status: inactive');
            });

            powerMonitor.on('unlock-screen', () => {
                this.lastActivity = new Date();
                this.updateStatus('active');
                console.log('Screen unlocked - Status: active');
            });

            // Monitor user activity (mouse/keyboard)
            this.setupActivityMonitoring();
            
        } catch (error) {
            console.error('Failed to setup system listeners:', error);
        }
    }

    setupActivityMonitoring() {
        // Note: Idle detection is now handled by IdleDetector class
        // StatusTracker only responds to external status updates
        console.log('📊 StatusTracker: Idle detection delegated to IdleDetector');
    }

    setupFallbackMonitoring() {
        // Fallback monitoring is now handled by IdleDetector
        console.log('📊 StatusTracker: Fallback monitoring delegated to IdleDetector');
    }

    checkStatus() {
        // Status checking is now handled by IdleDetector
        // This method is kept for compatibility but does nothing
        // console.log('📊 StatusTracker: Status check delegated to IdleDetector');
    }

    updateStatus(newStatus) {
        if (this.status !== newStatus) {
            const oldStatus = this.status;
            this.status = newStatus;
            
            console.log(`Status changed: ${oldStatus} -> ${newStatus}`);
            
            // Notify listeners
            this.notifyListeners(newStatus, oldStatus);
        }
    }

    recordActivity() {
        this.lastActivity = new Date();
        if (this.status !== 'active') {
            this.updateStatus('active');
        }
    }

    getStatus() {
        return {
            status: this.status,
            lastActivity: this.lastActivity,
            idleTime: new Date() - this.lastActivity,
            timestamp: new Date()
        };
    }

    getStatusDisplay() {
        const statusMap = {
            'active': { text: 'Active', color: '#4CAF50', icon: '🟢' },
            'idle': { text: 'Idle', color: '#FF9800', icon: '🟡' },
            'inactive': { text: 'Inactive', color: '#F44336', icon: '🔴' }
        };
        
        return statusMap[this.status] || statusMap['active'];
    }

    // Event listener management
    onStatusChange(callback) {
        this.listeners.push(callback);
    }

    removeStatusListener(callback) {
        const index = this.listeners.indexOf(callback);
        if (index > -1) {
            this.listeners.splice(index, 1);
        }
    }

    notifyListeners(newStatus, oldStatus) {
        this.listeners.forEach(callback => {
            try {
                callback(newStatus, oldStatus, this.getStatus());
            } catch (error) {
                console.error('Error in status change listener:', error);
            }
        });
    }

    // Configuration methods
    setIdleThreshold(minutes) {
        this.idleThreshold = minutes * 60 * 1000;
        console.log(`Idle threshold set to ${minutes} minutes`);
    }

    setInactiveThreshold(minutes) {
        this.inactiveThreshold = minutes * 60 * 1000;
        console.log(`Inactive threshold set to ${minutes} minutes`);
    }

    // Cleanup
    destroy() {
        if (this.statusCheckInterval) {
            clearInterval(this.statusCheckInterval);
        }
        
        this.listeners = [];
        console.log('Status tracker destroyed');
    }

    // Manual status override (for testing or special cases)
    setStatus(status) {
        if (['active', 'idle', 'inactive'].includes(status)) {
            this.updateStatus(status);
            this.lastActivity = new Date();
        }
    }

    // Get detailed status information
    getDetailedStatus() {
        const now = new Date();
        const timeSinceLastActivity = now - this.lastActivity;
        
        return {
            status: this.status,
            lastActivity: this.lastActivity,
            timeSinceLastActivity: timeSinceLastActivity,
            idleThreshold: this.idleThreshold,
            inactiveThreshold: this.inactiveThreshold,
            isIdle: timeSinceLastActivity > this.idleThreshold,
            isInactive: timeSinceLastActivity > this.inactiveThreshold,
            timestamp: now,
            display: this.getStatusDisplay()
        };
    }
}

module.exports = StatusTracker;
