<?php

namespace App\Controller\Admin;

use App\Entity\MasterEmployee;
use App\Entity\User;
use App\Repository\AttendanceRepository;
use App\Repository\HolidayRepository;
use App\Repository\MasterEmployeeRepository;
use App\Service\Admin\AttendenceTableService;
use App\Service\TimezoneService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\Session\SessionInterface;
use Symfony\Component\Routing\Annotation\Route;
#[Route('/admin')]
class AttendenceTableController extends AbstractController
{
    public function __construct(
        private readonly AttendenceTableService $attendenceTableService,
        private readonly TimezoneService $timezoneService,
    )
    {
    }

    #[Route('/attendance-report', name: 'admin_attendance_report', methods: ['GET', 'POST'])]
    public function attendenceTable(Request $request,SessionInterface $session): Response
    {
        $user = $this->getUser();
        if (!$user instanceof User) {
            throw $this->createAccessDeniedException('This can only be accessed by an admin.');
        }
        $filters = $session->get('attendance_filters', []);
        if ($request->query->has('year') && $request->query->has('month')) {
            $filters['year'] = $request->query->getInt('year');
            $filters['month'] = $request->query->getInt('month');
            $session->set('attendance_filters', $filters);
        } elseif (!$request->query->get('keep_filters')) {
            $session->remove('attendance_filters');
            $filters = [];
        }
        $table = $this->attendenceTableService->createAttendanceTable($request);

        if ($table->isCallback()) {
            return $table->getResponse();
        }

        return $this->render('admin/Attendance/index.html.twig', [
            'datatable' => $table,
            'filters' => $filters,
        ]);
    }

    /**
     * @throws \DateMalformedStringException
     */
    #[Route('/attendance-table/calendar-data', name: 'admin_attendance-table_calendar_data', methods: ['GET'])]
    public function getCalendarData(
        Request $request,
        AttendanceRepository $attendanceRepo,
        HolidayRepository $holidayRepo,
        MasterEmployeeRepository $employeeRepo
    ): JsonResponse {
        $employeeId = $request->query->getInt('employeeId');
        $year = $request->query->getInt('year', (int) date('Y'));
        $month = $request->query->getInt('month', (int) date('m'));
        /** @var MasterEmployee|null $employee */
        $employee = $employeeRepo->find($employeeId);

        if (!$employee) {
            return new JsonResponse(['error' => 'Employee not found'], 404);
        }

        $startDate = new \DateTimeImmutable("$year-$month-01");
        $endDate = $startDate->modify('last day of this month');

        $attendances = $attendanceRepo->findAttendanceByEmployeeAndDateRange($employee, $startDate, $endDate);
        $presentDates = [];

        foreach ($attendances as $attendance) {
            if ($attendance->getStatus() === 'Office In') {
                $presentDates[$attendance->getTimestamp()->format('Y-m-d')] = true;
            }
        }
        $holidays = $holidayRepo->findByMonthAndYear($year, $month);
        $holidayDates = array_map(fn($h) => $h->getDate()->format('Y-m-d'), $holidays);
        $results = [];
        $current = $startDate;
        $today = new \DateTimeImmutable('today');
        while ($current <= $endDate) {
            if ($current > $today) {
                break;
            }
            $dateStr = $current->format('Y-m-d');
            $dayOfWeek = (int) $current->format('N');
            $dayAttendances = array_filter($attendances, static function ($a) use ($dateStr) {
                return $a->getTimestamp()->format('Y-m-d') === $dateStr;
            });
            $details = [];
            foreach ($dayAttendances as $a) {
                $details[] = [
                    'status' => $a->getStatus(),
                    'time' => $this->timezoneService->formatDateTime($a->getTimestamp(), 'h:i A'),
                ];
            }
            if ($dayOfWeek < 6 && !in_array($dateStr, $holidayDates, true)) {
                $results[] = [
                    'date' => $dateStr,
                    'status' => $presentDates[$dateStr] ?? false ? 'present' : 'absent',
                    'details' => $details,
                ];
            } else {
                $results[] = [
                    'date' => $dateStr,
                    'status' => 'no-record',
                    'details' => $details,
                ];
            }

            $current = $current->modify('+1 day');
        }

        return new JsonResponse($results);
    }
}
