<?php

namespace App\Entity;

use App\Repository\SalaryStructureRepository;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Context\ExecutionContextInterface;

#[ORM\Entity(repositoryClass: SalaryStructureRepository::class)]
#[ORM\Table(name: 'salary_structure')]
class SalaryStructure
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private ?int $id = null;

    #[ORM\ManyToOne(targetEntity: MasterEmployee::class)]
    #[ORM\JoinColumn(nullable: false)]
    private ?MasterEmployee $employee = null;

    #[ORM\Column(type: 'date')]
    private ?\DateTimeInterface $startDate = null;

    #[ORM\Column(type: 'date', nullable: true)]
    private ?\DateTimeInterface $endDate = null;

    #[ORM\Column(type: 'decimal', precision: 12, scale: 2)]
    private ?float $ctc = null;

    #[ORM\Column(type: 'boolean')]
    private bool $deductPF = false;

    #[ORM\Column(type: 'boolean')]
    private bool $deductESIC = false;

    #[ORM\Column(type: 'datetime')]
    private ?\DateTimeInterface $createdAt = null;

    #[ORM\Column(type: 'datetime')]
    private ?\DateTimeInterface $updatedAt = null;

    public function __construct()
    {
        $this->createdAt = new \DateTime();
        $this->updatedAt = new \DateTime();
    }

    public function getId(): ?int { return $this->id; }
    public function getEmployee(): ?MasterEmployee { return $this->employee; }
    public function setEmployee(?MasterEmployee $employee): void { $this->employee = $employee; }

    public function getStartDate(): ?\DateTimeInterface { return $this->startDate; }
    public function setStartDate(?\DateTimeInterface $startDate): void { $this->startDate = $startDate; }
    public function getEndDate(): ?\DateTimeInterface { return $this->endDate; }
    public function setEndDate(?\DateTimeInterface $endDate): void { $this->endDate = $endDate; }

    public function getCtc(): ?float { return $this->ctc; }
    public function setCtc(?float $ctc): void { $this->ctc = $ctc; }

    public function isDeductPF(): bool { return $this->deductPF; }
    public function setDeductPF(bool $deductPF): void { $this->deductPF = $deductPF; }

    public function isDeductESIC(): bool { return $this->deductESIC; }
    public function setDeductESIC(bool $deductESIC): void { $this->deductESIC = $deductESIC; }

    public function getCreatedAt(): ?\DateTimeInterface { return $this->createdAt; }
    public function setCreatedAt(?\DateTimeInterface $createdAt): void { $this->createdAt = $createdAt; }
    public function getUpdatedAt(): ?\DateTimeInterface { return $this->updatedAt; }
    public function setUpdatedAt(?\DateTimeInterface $updatedAt): void { $this->updatedAt = $updatedAt; }
    #[Assert\Callback]
    public function validateDates(ExecutionContextInterface $context): void
    {
        if ($this->endDate !== null && $this->startDate !== null && $this->endDate <= $this->startDate) {
            $context->buildViolation('End date must be after start date.')
                ->atPath('endDate')
                ->addViolation();
        }
    }
}