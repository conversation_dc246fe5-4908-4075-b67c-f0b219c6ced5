<?php

namespace App\MessageHandler;

use App\Entity\LeaveRequest;
use App\Message\EmployeesOnLeaveNotificationMessage;
use App\Repository\UserRepository;
use App\Service\Common\EmailService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\Mailer\Exception\TransportExceptionInterface;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;
use App\Traits\ExceptionLoggerTrait;

#[AsMessageHandler]
class EmployeesOnLeaveNotificationHandler
{
    use ExceptionLoggerTrait;

    public function __construct(
        private readonly UserRepository $userRepository,
        private readonly EntityManagerInterface $entityManager,
        private readonly EmailService $emailService,
        private readonly ParameterBagInterface $parameterBag
    ) {
        $this->initializeLogger($parameterBag, 'employees_on_leave_notifications_handler');
    }

    public function __invoke(EmployeesOnLeaveNotificationMessage $message): void
    {
        $date = $message->getDate();
        $employees = [];
        $leaveDetails = [];

        foreach ($message->getLeaveRequestIds() as $leaveRequestId) {
            $leaveRequest = $this->entityManager->getRepository(LeaveRequest::class)->find($leaveRequestId);
            if (!$leaveRequest) {
                $this->log('Leave request not found', ['leave_request_id' => $leaveRequestId]);
                continue;
            }

            $employee = $leaveRequest->getEmployee();
            if (!$employee) {
                $this->log('Employee not found for leave request', ['leave_request_id' => $leaveRequestId]);
                continue;
            }

            $employees[$employee->getId()] = $employee;
            $leaveDetails[] = [
                'employee_name' => $employee->getName(),
                'leave_type' => $leaveRequest->getLeaveType()->getLeaveType()->getName(),
                'start_date' => $leaveRequest->getStartDate()->format('d-m-Y'),
                'end_date' => $leaveRequest->getEndDate()->format('d-m-Y'),
                'days_requested' => $leaveRequest->getDaysRequested(),
                'start_half_day' => $leaveRequest->getStartHalfDay(),
                'end_half_day' => $leaveRequest->getEndHalfDay(),
            ];
        }

        if (empty($leaveDetails)) {
            $this->log('No valid leave requests found for notification', ['date' => $date->format('Y-m-d')]);
            return;
        }
        $admins = $this->userRepository->findAll();
        if (empty($admins)) {
            $this->log('No admins found to send notification', ['date' => $date->format('Y-m-d')]);
            return;
        }
        $placeholders = [
            '{{date}}' => $date->format('d-m-Y'),
            '{{employee_count}}' => count($employees),
            '{{leave_details}}' => $this->formatLeaveDetails($leaveDetails),
            '{{app_base_url}}' => rtrim($this->parameterBag->get('app_base_url'), '/') . '/admin/leave-requests',
        ];

        $emailContent = $this->emailService->generateEmailContent('employees_on_leave_notification', $placeholders);

        foreach ($admins as $admin) {
            if ($admin && $admin->getEmail()) {
                try {
                    $this->emailService->sendEmail(
                        $admin->getEmail(),
                        sprintf('Employees on Leave for %s', $date->format('d-m-Y')),
                        $emailContent
                    );
                } catch (\Exception $e) {
                    $this->log('Failed to send email', [
                        'recipient' => $admin->getEmail(),
                        'error' => $e->getMessage(),
                    ]);
                } catch (TransportExceptionInterface $e) {
                }
            }
        }
    }

    private function formatLeaveDetails(array $leaveDetails): string
    {
        $output = '';
        foreach ($leaveDetails as $detail) {
            $halfDayInfo = '';
            if ($detail['start_half_day']) {
                $halfDayInfo .= " (Start: {$detail['start_half_day']})";
            }
            if ($detail['end_half_day']) {
                $halfDayInfo .= " (End: {$detail['end_half_day']})";
            }
            $output .= sprintf(
                "- %s: %s, %s to %s (%s days)%s\n",
                $detail['employee_name'],
                $detail['leave_type'],
                $detail['start_date'],
                $detail['end_date'],
                $detail['days_requested'],
                $halfDayInfo
            );
        }
        return $output;
    }
}