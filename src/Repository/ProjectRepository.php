<?php

namespace App\Repository;

use App\Entity\Project;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Project>
 */
class ProjectRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Project::class);
    }

    //    /**
    //     * @return Project[] Returns an array of Project objects
    //     */
    //    public function findByExampleField($value): array
    //    {
    //        return $this->createQueryBuilder('p')
    //            ->andWhere('p.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->orderBy('p.id', 'ASC')
    //            ->setMaxResults(10)
    //            ->getQuery()
    //            ->getResult()
    //        ;
    //    }

    /**
     * Find active projects for an employee (either as project manager or team member)
     */
    public function findActiveProjectsForEmployee($employee): array
    {
        return $this->createQueryBuilder('p')
            ->leftJoin('p.projectTeams', 'pt')
            ->where('p.status = :status')
            ->andWhere('pt.teamMember = :employee OR p.projectManager = :employee')
            ->setParameter('status', 'active')
            ->setParameter('employee', $employee)
            ->orderBy('p.name', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find projects by status
     */
    public function findByStatus(string $status): array
    {
        return $this->createQueryBuilder('p')
            ->where('p.status = :status')
            ->setParameter('status', $status)
            ->orderBy('p.name', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Get project statistics for an employee
     */
    public function getProjectStatsForEmployee($employee): array
    {
        $qb = $this->createQueryBuilder('p')
            ->select('p.status, COUNT(p.id) as count')
            ->leftJoin('p.projectTeams', 'pt')
            ->where('pt.teamMember = :employee OR p.projectManager = :employee')
            ->setParameter('employee', $employee)
            ->groupBy('p.status');

        return $qb->getQuery()->getResult();
    }
}
