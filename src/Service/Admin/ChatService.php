<?php

namespace App\Service\Admin;

use App\Entity\Chat;
use App\Entity\EmployeeNotification;
use App\Service\TimezoneService;
use Doctrine\ORM\EntityManagerInterface;

class ChatService
{
    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly TimezoneService $timezoneService
    ) {}

    public function markEmployeeChatsAsRead(EmployeeNotification $notification): void
    {
        foreach ($notification->getChats() as $chat) {
            if ($chat->isFromEmployee() && $chat->isUnread()) {
                $chat->markAsRead();
            }
        }

        $this->entityManager->flush();
    }

    public function formatChatsForResponse(EmployeeNotification $notification): array
    {
        $chats = [];
        foreach ($notification->getChats() as $chat) {
            $chats[] = [
                'message' => $chat->getMessage(),
                'sentAt' => $chat->getCreatedAt()->format('d-m-Y H:i:s'),
                'isFromEmployee' => $chat->isFromEmployee(),
                'isDisapprovalReason' => $chat->isDisapprovalReason(),
                'readAt' => $chat->getReadAt()?->format('d-m-Y H:i:s'),
                'notificationId' => $notification->getId(),
                'reportDate' => $notification->getEmployeeHours()?->getReportDate()?->format('Y-m-d'),
            ];
        }

        usort($chats, static fn($a, $b) => strcmp($a['sentAt'], $b['sentAt']));

        return $chats;
    }

    public function sendChat(EmployeeNotification $notification, string $message): Chat
    {
        $chat = new Chat(
            message: $message,
            isFromEmployee: false,
            employeeNotification: $notification,
            isDisapprovalReason: false
        );

        $this->entityManager->persist($chat);
        $this->entityManager->flush();

        return $chat;
    }

    public function formatDateTime(\DateTimeInterface $dateTime): string
    {
        return $this->timezoneService->formatDateTime($dateTime);
    }
}