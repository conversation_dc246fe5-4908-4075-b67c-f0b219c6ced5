* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.popup-container {
    background: white;
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    width: 380px;
    max-width: 90vw;
    overflow: hidden;
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.popup-header {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
    padding: 20px;
    text-align: center;
}

.warning-icon {
    font-size: 2.5rem;
    margin-bottom: 10px;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

.popup-header h2 {
    font-size: 1.4rem;
    font-weight: 600;
    margin: 0;
}

.popup-content {
    padding: 25px 20px;
    text-align: center;
}

.idle-message {
    font-size: 1rem;
    color: #333;
    margin-bottom: 25px;
    line-height: 1.5;
}

.idle-message span {
    font-weight: 600;
    color: #ee5a24;
}

.countdown-container {
    margin-bottom: 25px;
}

.countdown-circle {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
    position: relative;
    overflow: hidden;
}

.countdown-circle::before {
    content: '';
    position: absolute;
    top: 3px;
    left: 3px;
    right: 3px;
    bottom: 3px;
    background: white;
    border-radius: 50%;
}

.countdown-number {
    font-size: 1.8rem;
    font-weight: bold;
    color: #667eea;
    position: relative;
    z-index: 1;
    animation: countdownPulse 1s infinite;
}

@keyframes countdownPulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

.countdown-text {
    font-size: 0.9rem;
    color: #666;
    line-height: 1.4;
}

.countdown-text strong {
    color: #ee5a24;
    font-weight: 600;
}

.action-buttons {
    display: flex;
    gap: 12px;
    justify-content: center;
}

.btn {
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    min-width: 120px;
    justify-content: center;
}

.btn-primary {
    background: linear-gradient(135deg, #00b894, #00a085);
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #00a085, #008f75);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 184, 148, 0.3);
}

.btn-secondary {
    background: linear-gradient(135deg, #636e72, #2d3436);
    color: white;
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #2d3436, #1e2124);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(45, 52, 54, 0.3);
}

.btn:active {
    transform: translateY(0);
}

.popup-footer {
    background: #f8f9fa;
    padding: 15px 20px;
    border-top: 1px solid #e9ecef;
}

.footer-text {
    font-size: 0.8rem;
    color: #6c757d;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
}

.footer-text i {
    color: #667eea;
}

/* Responsive adjustments */
@media (max-width: 400px) {
    .popup-container {
        width: 95vw;
        margin: 10px;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .btn {
        width: 100%;
    }
}

/* Animation for urgent countdown */
.countdown-urgent {
    animation: urgentPulse 0.5s infinite alternate;
}

@keyframes urgentPulse {
    from {
        background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    }
    to {
        background: linear-gradient(135deg, #ee5a24, #d63031);
    }
}
