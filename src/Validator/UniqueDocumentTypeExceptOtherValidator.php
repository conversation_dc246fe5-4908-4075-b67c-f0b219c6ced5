<?php
namespace App\Validator;

use App\Entity\EmployeeDocument;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;

class UniqueDocumentTypeExceptOtherValidator extends ConstraintValidator
{
    public function __construct(private readonly EntityManagerInterface $entityManager) {}

    public function validate($object, Constraint $constraint): void
    {
        if (!$object instanceof EmployeeDocument) {
            return;
        }

        $docType = $object->getDocumentType();

        if ($docType === 'OTHER') {
            return;
        }

        $existing = $this->entityManager->getRepository(EmployeeDocument::class)->findOneBy([
            'masterEmployee' => $object->getMasterEmployee(),
            'documentType' => $docType,
        ]);

        if (!$constraint instanceof UniqueDocumentTypeExceptOther) {
            throw new \LogicException('Invalid constraint type');
        }
        if ($existing && $existing->getId() !== $object->getId()) {
            $this->context->buildViolation($constraint->message)
                ->atPath('documentType')
                ->addViolation();
        }
    }
}
