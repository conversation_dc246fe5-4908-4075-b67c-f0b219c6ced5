<?php

namespace App\Controller\Api;

use App\Entity\ApplicationUsage;
use App\Repository\ApplicationUsageRepository;
use App\Repository\MasterEmployeeRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

#[Route('/api/application-usage', name: 'api_application_usage_')]
class ApplicationUsageController extends AbstractController
{
    public function __construct(
        private ApplicationUsageRepository $applicationUsageRepository,
        private MasterEmployeeRepository $employeeRepository,
        private EntityManagerInterface $entityManager
    ) {}

    #[Route('/sync', name: 'sync', methods: ['POST'])]
    public function syncApplicationUsage(Request $request): JsonResponse
    {
        try {
            $data = json_decode($request->getContent(), true);
            
            if (!$data || !isset($data['usage'])) {
                return new JsonResponse([
                    'success' => false,
                    'error' => 'Invalid data format'
                ], 400);
            }

            $syncedCount = 0;
            $errors = [];

            foreach ($data['usage'] as $usageData) {
                try {
                    // Validate required fields
                    if (!isset($usageData['employee_id'], $usageData['usage_date'], $usageData['application_name'])) {
                        $errors[] = 'Missing required fields in usage data';
                        continue;
                    }

                    // Find the employee
                    $employee = $this->employeeRepository->find($usageData['employee_id']);
                    if (!$employee) {
                        $errors[] = "Employee not found: {$usageData['employee_id']}";
                        continue;
                    }

                    // Create new ApplicationUsage entity
                    $applicationUsage = new ApplicationUsage();
                    $applicationUsage->setEmployee($employee);
                    $applicationUsage->setUsageDate(new \DateTime($usageData['usage_date']));
                    $applicationUsage->setApplicationName($usageData['application_name']);
                    
                    if (isset($usageData['application_path'])) {
                        $applicationUsage->setApplicationPath($usageData['application_path']);
                    }
                    
                    if (isset($usageData['window_title'])) {
                        $applicationUsage->setWindowTitle($usageData['window_title']);
                    }
                    
                    if (isset($usageData['start_time'])) {
                        $applicationUsage->setStartTime(new \DateTime($usageData['start_time']));
                    }
                    
                    if (isset($usageData['end_time']) && $usageData['end_time']) {
                        $applicationUsage->setEndTime(new \DateTime($usageData['end_time']));
                    }
                    
                    if (isset($usageData['duration'])) {
                        $applicationUsage->setDuration($usageData['duration']);
                    }
                    
                    if (isset($usageData['category'])) {
                        $applicationUsage->setCategory($usageData['category']);
                    }
                    
                    if (isset($usageData['switch_count'])) {
                        $applicationUsage->setSwitchCount($usageData['switch_count']);
                    }
                    
                    if (isset($usageData['device_id'])) {
                        $applicationUsage->setDeviceId($usageData['device_id']);
                    }
                    
                    if (isset($usageData['metadata'])) {
                        // Handle metadata - convert JSON string to array if needed
                        $metadata = $usageData['metadata'];
                        if (is_string($metadata)) {
                            $metadata = json_decode($metadata, true);
                        }
                        $applicationUsage->setMetadata($metadata);
                    }
                    
                    $applicationUsage->setIsSynced(true);
                    $applicationUsage->setCreatedAt(new \DateTime());
                    $applicationUsage->setUpdatedAt(new \DateTime());

                    // Persist to database
                    $this->entityManager->persist($applicationUsage);
                    $syncedCount++;
                    
                } catch (\Exception $e) {
                    $errors[] = $e->getMessage();
                }
            }

            // Flush all changes to database
            if ($syncedCount > 0) {
                $this->entityManager->flush();
            }

            return new JsonResponse([
                'success' => true,
                'data' => [
                    'synced_count' => $syncedCount,
                    'errors' => $errors
                ]
            ]);
            
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => 'Failed to sync application usage',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    #[Route('/stats', name: 'stats', methods: ['GET'])]
    public function getApplicationStats(Request $request): JsonResponse
    {
        try {
            $employeeId = $request->query->get('employee_id');
            $date = $request->query->get('date', date('Y-m-d'));
            
            if (!$employeeId) {
                return new JsonResponse([
                    'success' => false,
                    'error' => 'Employee ID is required'
                ], 400);
            }

            // Get application usage stats for the day
            $stats = $this->applicationUsageRepository->getApplicationStatsForDate($employeeId, $date);

            return new JsonResponse([
                'success' => true,
                'data' => $stats
            ]);
            
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => 'Failed to fetch application stats',
                'message' => $e->getMessage()
            ], 500);
        }
    }
}
