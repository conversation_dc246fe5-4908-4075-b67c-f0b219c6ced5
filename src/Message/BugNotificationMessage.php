<?php
namespace App\Message;

readonly class BugNotificationMessage
{
    public function __construct(
        private int $bugId,
        private int $employeeId,
        private string $action,
        private array $changes
    ) {}

    public function getBugId(): int
    {
        return $this->bugId;
    }

    public function getEmployeeId(): int
    {
        return $this->employeeId;
    }


    public function getAction(): string
    {
        return $this->action;
    }

    public function getChanges(): ?array
    {
        return $this->changes;
    }
}