<?php


namespace App\Controller\Admin;
use App\Entity\FormSection;
use App\Form\FormSectionType;
use App\Repository\FormTemplateRepository;
use App\Service\Admin\FormSectionService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Csrf\CsrfToken;
use Symfony\Component\Security\Csrf\CsrfTokenManagerInterface;

#[Route('/admin/form-template')]
class FormSectionController extends AbstractController
{
    public function __construct(
        private readonly FormTemplateRepository $formTemplateRepository,
        private readonly EntityManagerInterface $entityManager ,
        private readonly FormSectionService $formSectionService,
    ) {}

    #[Route('/{template_id}/sections', name: 'form_section_index', methods: ['GET', 'POST'])]
    public function index(Request $request,int $template_id): Response
    {
        $template = $this->formTemplateRepository->find($template_id);

        if (!$template) {
            throw new NotFoundHttpException('Template not found');
        }
        $table = $this->formSectionService->createMasterFormSectionTable($request, $template_id);

        if ($table->isCallback()) {
            return $table->getResponse();   
        }
        return $this->render('admin/form_section/index.html.twig', [
            'datatable' => $table,
            'template' => $template,
        ]);
    }

    #[Route('/template/{template_id}/new', name: 'form_section_new', methods: ['GET', 'POST'])]
    public function new(Request $request, int $template_id): Response
    {
        $template = $this->formTemplateRepository->find($template_id);

        if (!$template) {
            throw new NotFoundHttpException('Template not found');
        }

        $section = new FormSection();
        $section->setTemplate($template);
        $lastOrderNumber = $this->entityManager->getRepository(FormSection::class)
            ->findOneBy(['template' => $template], ['orderNumber' => 'DESC'])
            ?->getOrderNumber() ?? 0;

        $section->setOrderNumber($lastOrderNumber + 1);

        $form = $this->createForm(FormSectionType::class, $section);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $this->entityManager->persist($section);
            $this->entityManager->flush();

            return $this->redirectToRoute('form_section_index', ['template_id' => $template->getId()]);
        }

        return $this->render('admin/form_section/new.html.twig', [
            'template' => $template,
            'form' => $form->createView(),
        ]);
    }


    #[Route('/{template_id}/sections/{id}/edit', name: 'form_section_edit', methods: ['GET', 'POST'])]
    public function edit(Request $request, FormSection $section, int $template_id): Response
    {
        $template = $this->formTemplateRepository->find($template_id);

        if (!$template) {
            throw new NotFoundHttpException('Template not found');
        }

        $form = $this->createForm(FormSectionType::class, $section);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $section->setUpdatedAt(new \DateTime());
            $this->entityManager->flush();

            return $this->redirectToRoute('form_section_index', ['template_id' => $section->getTemplate()->getId()]);
        }

        return $this->render('admin/form_section/new.html.twig', [
            'section' => $section,
            'template' => $template,
            'form' => $form->createView(),
        ]);
    }

    #[Route('/{template_id}/sections/{id}/delete', name: 'form_section_delete', methods: ['POST'])]
    public function deleteFormSection(
        Request                $request,
        FormSection            $section,
        EntityManagerInterface $entityManager,
        CsrfTokenManagerInterface $csrfTokenManager,
        int                    $template_id
    ): Response
    {
        $template = $this->formTemplateRepository->find($template_id);

        if (!$template) {
            return $this->json([
                'success' => false,
                'message' => 'Template not found'
            ], Response::HTTP_NOT_FOUND);
        }

        $token = $request->request->get('_token');
        if (!$csrfTokenManager->isTokenValid(new CsrfToken('delete' . $section->getId(), $token))) {
            return $this->json([
                'success' => false,
                'message' => 'Invalid CSRF token.'
            ], Response::HTTP_FORBIDDEN);
        }

        $section->setIsDelete(true);
        $entityManager->persist($section);
        $entityManager->flush();

        return $this->json([
            'success' => true,
            'message' => 'Section has been deleted'
        ], Response::HTTP_OK);
    }

    #[Route('/{id}/toggle-status', name: 'form_section_toggle_status', methods: ['POST'])]
    public function toggleStatus(Request $request, FormSection $section): Response
    {
        if (!$request->isXmlHttpRequest()) {
            return $this->json([
                'success' => false,
                'message' => 'This endpoint only accepts AJAX requests.'
            ], 400);
        }

        if (!$this->isCsrfTokenValid('toggle-status' . $section->getId(), $request->request->get('_token'))) {
            return $this->json([
                'success' => false,
                'message' => 'Invalid CSRF token.'
            ], 400);
        }

        try {
            $section->setStatus(!$section->isStatus());
            $section->setUpdatedAt(new \DateTime());
            $this->entityManager->flush();

            return $this->json([
                'success' => true,
                'message' => 'Section visibility status updated successfully.',
                'isVisible' => $section->isStatus()
            ]);
        } catch (\Exception $e) {
            return $this->json([
                'success' => false,
                'message' => 'Error updating status: ' . $e->getMessage()
            ], 500);
        }
    }

    #[Route('/{id}/reorder', name: 'form_section_reorder', methods: ['GET'])]
    public function reorder(Request $request, FormSection $section): Response
    {
        $direction = $request->query->get('direction');
        $currentOrder = $section->getOrderNumber();
        $template = $section->getTemplate();

        if ($direction === 'up' && $currentOrder > 1) {
            $swapSection = $this->entityManager->getRepository(FormSection::class)
                ->findOneBy(['template' => $template, 'orderNumber' => $currentOrder - 1]);

            if ($swapSection) {
                $swapSection->setOrderNumber($currentOrder);
                $section->setOrderNumber($currentOrder - 1);
            }
        } elseif ($direction === 'down') {
            $swapSection = $this->entityManager->getRepository(FormSection::class)
                ->findOneBy(['template' => $template, 'orderNumber' => $currentOrder + 1]);

            if ($swapSection) {
                $swapSection->setOrderNumber($currentOrder);
                $section->setOrderNumber($currentOrder + 1);
            }
        }
        $this->entityManager->flush();

        return $this->redirectToRoute('form_section_index', ['template_id' => $template->getId()]);
    }

}