<?php

namespace App\Service\Admin;

use App\Traits\ExceptionLoggerTrait;
use App\Repository\SettingRepository;
use Exception;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;

class ManualEntryService
{
    use ExceptionLoggerTrait;
    private array $configCache = [];

    public function __construct(
        private readonly HttpClientInterface $httpClient,
        ParameterBagInterface $parameterBag,
        private readonly SettingRepository $configurationRepository
    ) {
        $this->initializeLogger($parameterBag, 'manual_entry_service');
    }

    private function formatEntries(array $entries): array
    {
        return array_map(static function ($entry) {
            $timezone = new \DateTimeZone('Asia/Kolkata');

            if (isset($entry['startTime'])) {
                $startTime = (new \DateTime())->setTimestamp((int)($entry['startTime'] / 1000))->setTimezone($timezone);
                $entry['startTimeFormatted'] = $startTime->format('d-m-Y H:i:s');
            }
            if (isset($entry['endTime'])) {
                $endTime = (new \DateTime())->setTimestamp((int)($entry['endTime'] / 1000))->setTimezone($timezone);
                $entry['endTimeFormatted'] = $endTime->format('d-m-Y H:i:s');
            }
            return $entry;
        }, $entries);
    }

    /**
     * Get all manual entries from the API
     *
     * @return array
     * @throws Exception
     */
    public function getManualEntries(): array
    {
        try {
            $apiUrl = $this->getConfigurationValue('Base_URL', $_ENV['BASE_URL']) . 'integration/manual_entries';
            $response = $this->httpClient->request('GET', $apiUrl, [
                'headers' => $this->getHeaders(),
            ]);

            if ($response->getStatusCode() !== 200) {
                $this->logger->error('Failed to fetch manual entries from API.', [
                    'status_code' => $response->getStatusCode(),
                    'response_body' => $response->getContent(false),
                ]);
                throw new Exception(sprintf('Failed to fetch manual entries. Status code: %d', $response->getStatusCode()));
            }

            $entries = $response->toArray();
            return $this->formatEntries($entries);
        } catch (Exception $e) {
            $this->logger->error('Error in getManualEntries', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * Update the status of manual entries
     *
     * @param array $entryIds
     * @param string $status
     * @return array
     * @throws Exception
     */
    public function updateEntriesStatus(array $entryIds, string $status): array
    {
        try {
            $apiUrl = $this->getConfigurationValue('Base_URL', $_ENV['BASE_URL']) . 'integration/update_manual_entry_status';
            $response = $this->httpClient->request('POST', $apiUrl, [
                'headers' => $this->getHeaders(),
                'json' => [
                    'entry_ids' => $entryIds,
                    'status' => $status,
                ],
            ]);

            if ($response->getStatusCode() !== 200) {
                $this->logger->error('Failed to update entries status.', [
                    'status_code' => $response->getStatusCode(),
                    'response_body' => $response->getContent(false),
                    'entry_ids' => $entryIds,
                    'status' => $status
                ]);
                throw new \RuntimeException(sprintf('Failed to update entries status. Status code: %d', $response->getStatusCode()));
            }

            return $response->toArray();
        } catch (Exception $e) {
            $this->logger->error('Error in updateEntriesStatus', [
                'error' => $e->getMessage(),
                'entry_ids' => $entryIds,
                'status' => $status,
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * Retrieve configuration values with caching to avoid repetitive repository calls.
     *
     * @param string $fieldName
     * @param string|null $defaultValue
     * @return string|null
     */
    private function getConfigurationValue(string $fieldName, ?string $defaultValue): ?string
    {
        if (!isset($this->configCache[$fieldName])) {
            $configuration = $this->configurationRepository->findOneBy(['fieldName' => $fieldName]);
            $this->configCache[$fieldName] = $configuration ? $configuration->getFieldValue() : $defaultValue;
        }

        return $this->configCache[$fieldName];
    }

    /**
     * Get headers for API requests
     *
     * @return array
     */
    private function getHeaders(): array
    {
        $apiKey = $this->getConfigurationValue('TEAMLOGGER_API_KEY', $_ENV['TEAMLOGGER_API_KEY']);

        return [
            'Authorization' => "Bearer $apiKey",
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
        ];
    }
}