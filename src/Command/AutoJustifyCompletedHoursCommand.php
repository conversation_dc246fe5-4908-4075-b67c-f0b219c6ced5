<?php

namespace App\Command;

use App\Helper\TimeHelper;
use App\Repository\EmployeeNotificationRepository;
use App\Repository\SettingRepository;
use App\Service\Admin\EmployeeHoursService;
use App\Traits\ExceptionLoggerTrait;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use App\Helper\SettingHelper;

class AutoJustifyCompletedHoursCommand extends Command
{
    use ExceptionLoggerTrait;

    protected static $defaultName = 'app:auto-justify-completed-hours';

    public function __construct(
        private readonly EmployeeNotificationRepository $notificationRepository,
        private readonly EmployeeHoursService           $employeeHoursService,
        private readonly SettingHelper                  $settingHelper,
        private readonly EntityManagerInterface         $entityManager,
        private readonly SettingRepository              $configurationRepository,
        ParameterBagInterface                           $parameterBag,
        private readonly TimeHelper                     $timeHelper,
    )
    {
        parent::__construct(self::$defaultName);
        $this->initializeLogger($parameterBag, 'auto_justify_completed_hours');
    }

    protected function configure(): void
    {
        $this
            ->setDescription('Automatically justify notifications for employees who have met the hours threshold.')
            ->setHelp('This command checks pending notifications and marks them as justified if the employee’s hours meet or exceed the threshold, preventing unnecessary resend justification emails.')
            ->addOption(
                'date',
                'd',
                InputOption::VALUE_OPTIONAL,
                'Specify the date to check notifications (default: 7 days ago).'
            );
    }

    /**
     * @param string $fieldName
     * @param $defaultValue
     * @return string|null
     */
    private function getConfigurationValue(string $fieldName, $defaultValue): ?string
    {
        $configuration = $this->configurationRepository->findOneBy(['fieldName' => $fieldName]);
        return $configuration ? $configuration->getFieldValue() : $defaultValue;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        try {
            $inputDate = $input->getOption('date');
            $reminderDays = $this->settingHelper->getSettingConfiguration()['resend'] ?? 7;
            $offsetDays = (int)$reminderDays;
            $referenceDate = $inputDate
                ? new \DateTimeImmutable($inputDate)
                : (new \DateTimeImmutable())->modify("-{$offsetDays} days");

            $output->writeln(sprintf('Checking notifications pending since: %s', $referenceDate->format('Y-m-d')));

            $threshold = $this->getConfigurationValue('THRESHOLD', $_ENV['THRESHOLD']);
            $threshold = is_numeric($threshold) ? (float)$threshold : 0.0;

            $pendingNotifications = $this->notificationRepository->findPendingJustifications($referenceDate);

            if (empty($pendingNotifications)) {
                $output->writeln('No pending notifications found for the given date.');
                return Command::SUCCESS;
            }

            foreach ($pendingNotifications as $notification) {
                $employeeHours = $notification->getEmployeeHours();
                if (!$employeeHours) {
                    $output->writeln(sprintf(
                        'Skipping notification ID %d: No associated employee hours.',
                        $notification->getId()
                    ));
                    continue;
                }

                $totalHours = $employeeHours->getTotalHours();
                $formattedTotalHours = $this->timeHelper->convertDecimalToTimeFormat($totalHours);

                if ($formattedTotalHours >= $threshold) {
                    $notification->setJustification('System Approved');
                    $notification->setIsApproved(true);
                    $notification->setJustifiedAt(new \DateTimeImmutable());
                    $notification->setStatus(true);

                    $this->entityManager->persist($notification);
                    $output->writeln(sprintf(
                        'Notification ID %d for employee ID %s justified: %s hours >= %s threshold.',
                        $notification->getId(),
                        $notification->getEmployee()->getId(),
                        $totalHours,
                        $threshold
                    ));
                }
            }

            $this->entityManager->flush();
            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->log(
                'Error in AutoJustifyCompletedHoursCommand',
                [
                    'error_message' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                    'trace' => $e->getTraceAsString(),
                ]
            );
            $output->writeln('An error occurred: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }
}