<?php

namespace App\Service\Common;

use App\Entity\FormResponse;
use App\Entity\FormSection;
use App\Entity\FormSubmission;
use App\Entity\MasterEmployee;
use App\Entity\User;
use App\Helper\TeamloggerActivePercentage;
use App\Repository\EmployeeHoursRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Security\Core\User\UserInterface;

readonly class FormFillService
{
    public function __construct(
        private EntityManagerInterface     $entityManager,
        private TeamloggerActivePercentage $teamloggerActivePercentage,
    ) {
    }

    /**
     * Processes form sections and extracts field types
     *
     * @param array $sections
     * @return array Field types indexed by field ID
     * @throws \JsonException
     */
    public function processSections(array &$sections): array
    {
        $fieldTypes = [];
        foreach ($sections as $section) {
            $fields = $this->getSortedFields($section);
            $section->sortedFields = $fields;
            foreach ($fields as $field) {
                $options = $field->getFieldOptions();
                if (is_string($options)) {
                    try {
                        $options = json_decode($options, true, 512, JSON_THROW_ON_ERROR);
                    } catch (\JsonException $e) {
                        $options = [];
                    }
                } elseif (!is_array($options)) {
                    $options = [];
                }

                $fieldTypes[$field->getId()] = [
                    'type' => $field->getFieldType(),
                    'options' => $options
                ];
            }
        }

        return $fieldTypes;
    }

    /**
     * Calculate overall rating from submitted form data
     *
     * @param array $sections
     * @param array $fieldTypes
     * @param Request $request
     * @return array Contains totalRating and ratingCount
     */
    public function calculateOverallRating(array $sections, array $fieldTypes, Request $request): array
    {
        $totalRating = 0;
        $ratingCount = 0;

        foreach ($sections as $section) {
            $fields = $this->getSortedFields($section);
            $section->sortedFields = $fields;

            foreach ($fields as $field) {
                $fieldId = $field->getId();

                $fieldName = 'field_' . $fieldId;

                if (isset($fieldTypes[$fieldId]) &&
                    $fieldTypes[$fieldId]['type'] === 'rating') {
                    $value = $request->request->get($fieldName);
                    if (is_numeric($value)) {
                        $totalRating += (float)$value;
                        $ratingCount++;
                    }
                }
            }
        }

        return [
            'totalRating' => $totalRating,
            'ratingCount' => $ratingCount
        ];
    }

    /**
     * Get employee activity data
     *
     * @param EmployeeHoursRepository $employeeHoursRepository
     * @param object $employee
     * @return array Contains activity metrics
     */
    public function getEmployeeActivityData(EmployeeHoursRepository $employeeHoursRepository, object $employee): array
    {
        $employeeHours = $employeeHoursRepository->findByMasterEmployeeAndPreviousMonth($employee);
        $totalActiveMinutes = 0;
        $totalActiveSeconds = 0;
        $daysCount = count($employeeHours);

        foreach ($employeeHours as $hours) {
            $totalActiveMinutes += $hours->getActiveMinutes();
            $totalActiveSeconds += $hours->getActiveSeconds();
        }

        $averageActiveMinutes = $daysCount > 0 ? ($totalActiveMinutes / $daysCount) * 100 : 0;
        $averageActiveSeconds = $daysCount > 0 ? ($totalActiveSeconds / $daysCount) * 100 : 0;

        return [
            'averageActiveMinutes' => $averageActiveMinutes,
            'averageActiveSeconds' => $averageActiveSeconds,
        ];
    }

    /**
     * Create form submission entity
     *
     * @param object $template
     * @param object $employee
     * @param UserInterface $reviewer
     * @param float|string|null $overallRating
     * @param float $averageActiveMinutes
     * @param float $averageActiveSeconds
     * @param bool $isAdmin
     * @return FormSubmission
     */
    public function createSubmission(
        object $template,
        object $employee,
        UserInterface $reviewer,
        float|string|null $overallRating,
        float $averageActiveMinutes,
        float $averageActiveSeconds,
        bool $isAdmin = false
    ): FormSubmission {
        $submission = new FormSubmission();
        $submission->setTemplate($template);
        $submission->setEmployee($employee);

        // Set reviewer based on user type
        if ($isAdmin) {
            if (!$reviewer instanceof User) {
                throw new \LogicException('Admin user must be an instance of User.');
            }
            $submission->setUserReviewer($reviewer);
        } else {
            if (!$reviewer instanceof MasterEmployee) {
                throw new \LogicException('Non-admin user must be an instance of MasterEmployee.');
            }
            $submission->setEmployeeReviewer($reviewer);
        }

        $submission->setStatus(true);
        $submission->setReviewPeriod(date('Y-m'));
        $submission->setSubmissionDate(new \DateTime());
        $submission->setCreatedAt(new \DateTime());
        $submission->setUpdatedAt(new \DateTime());
        $submission->setRating($overallRating);
        $submission->setActiveMinRating($averageActiveMinutes);
        $submission->setActiveSecRating($averageActiveSeconds);

        $this->entityManager->persist($submission);

        return $submission;
    }

    /**
     * Process form responses for each field
     *
     * @param FormSubmission $submission
     * @param array $sections
     * @param array $fieldTypes
     * @param Request $request
     * @param UserInterface $user
     * @param bool $isAdmin
     * @throws \JsonException
     */
    public function processFormResponses(
        FormSubmission $submission,
        array $sections,
        array $fieldTypes,
        Request $request,
        UserInterface $user,
        bool $isAdmin = false
    ): void {
        foreach ($sections as $section) {
            $fields = $this->getSortedFields($section);
            $section->sortedFields = $fields;
            foreach ($fields as $field) {
                $fieldId = $field->getId();
                $fieldName = 'field_' . $fieldId;

                $responseValue = $this->getFieldResponseValue($fieldTypes[$fieldId]['type'], $fieldName, $request);

                if ($responseValue !== null) {
                    $response = new FormResponse();
                    $response->setSubmission($submission);
                    $response->setField($field);
                    $response->setSection($section);
                    $response->setResponseValue($responseValue);

                    // Set modifier based on user type
                    if ($isAdmin) {
                        if (!$user instanceof User) {
                            throw new \LogicException('Admin user must be an instance of User.');
                        }
                        $response->setModifiedByUser($user);
                    } else {
                        if (!$user instanceof MasterEmployee) {
                            throw new \LogicException('Non-admin user must be an instance of MasterEmployee.');
                        }
                        $response->setModifiedBy($user);
                    }

                    $response->setIsDraft(false);
                    $response->setCreatedAt(new \DateTime());
                    $response->setUpdatedAt(new \DateTime());

                    $this->entityManager->persist($response);
                }
            }
        }
    }

    /**
     * Get response value based on field type
     *
     * @param string $fieldType
     * @param string $fieldName
     * @param Request $request
     * @return string|null
     * @throws \JsonException
     */
    private function getFieldResponseValue(string $fieldType, string $fieldName, Request $request): ?string
    {
        switch ($fieldType) {
            case 'text':
            case 'email':
            case 'password':
                return trim($request->request->get($fieldName, ''));

            case 'textarea':
                return $request->request->get($fieldName, '');

            case 'number':
                $value = $request->request->get($fieldName);
                return is_numeric($value) ? $value : null;

            case 'date':
                $dateValue = $request->request->get($fieldName);
                if (!empty($dateValue)) {
                    try {
                        $date = new \DateTime($dateValue);
                        return $date->format('Y-m-d');
                    } catch (\Exception $e) {
                        return null;
                    }
                }
                return null;

            case 'checkbox':
                $checkboxValues = $request->request->all($fieldName) ?: [];
                return json_encode($checkboxValues, JSON_THROW_ON_ERROR);

            case 'select':
                $selectedValues = $request->request->get($fieldName);
                return $selectedValues ?? '';

            case 'rating':
            case 'radio':
                return $request->request->get($fieldName, '');

            default:
                return $request->request->get($fieldName, '');
        }
    }

    /**
     * Get employee activity data for all employees
     *
     * @param EmployeeHoursRepository $employeeHoursRepository
     * @param array $employees
     * @return array
     */
    public function getEmployeesActivityData(EmployeeHoursRepository $employeeHoursRepository, array $employees): array
    {
        return $this->teamloggerActivePercentage->getEmployeeActivityData($employeeHoursRepository, $employees);
    }

    public function getSortedFields(FormSection $section): array
    {
        $fields = $section->getFields()->filter(fn($field) =>$field->isVisible() && !$field->getIsDelete())->toArray();
        usort($fields, fn($a, $b) => $a->getFieldOrder() <=> $b->getFieldOrder());
        return $fields;
    }
}