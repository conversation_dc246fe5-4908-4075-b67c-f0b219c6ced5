class IdleWarningManager {
    constructor() {
        this.countdownSeconds = 60;
        this.timeoutMinutes = 5;
        this.init();
    }

    init() {
        console.log('🚨 Idle Warning popup initialized');
        
        // Setup event listeners
        this.setupEventListeners();
        
        // Listen for IPC messages
        this.setupIpcListeners();
    }

    setupEventListeners() {
        // Resume button
        const resumeBtn = document.getElementById('resumeBtn');
        resumeBtn.addEventListener('click', () => {
            this.handleResume();
        });

        // Continue idle button
        const continueIdleBtn = document.getElementById('continueIdleBtn');
        continueIdleBtn.addEventListener('click', () => {
            this.handleContinueIdle();
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (event) => {
            if (event.key === 'Enter' || event.key === ' ') {
                event.preventDefault();
                this.handleResume();
            } else if (event.key === 'Escape') {
                event.preventDefault();
                this.handleContinueIdle();
            }
        });

        // Mouse movement detection (user activity) - immediate response
        let activityDetected = false;
        const detectActivity = (activityType) => {
            if (!activityDetected) {
                activityDetected = true;
                console.log(`${activityType} detected - user is active`);
                // Use the new activity detection method for immediate response
                window.idleWarningAPI.activityDetected();
                this.handleResume();
            }
        };

        document.addEventListener('mousemove', () => detectActivity('🖱️ Mouse movement'));
        document.addEventListener('keydown', () => detectActivity('⌨️ Keyboard activity'));
        document.addEventListener('click', (event) => {
            if (!event.target.closest('.btn')) {
                detectActivity('🖱️ Click');
            }
        });
        document.addEventListener('scroll', () => detectActivity('📜 Scroll'));
        document.addEventListener('wheel', () => detectActivity('🖱️ Mouse wheel'));
        document.addEventListener('touchstart', () => detectActivity('👆 Touch'));
        document.addEventListener('touchmove', () => detectActivity('👆 Touch move'));
    }

    setupIpcListeners() {
        // Listen for popup initialization
        window.idleWarningAPI.onInitPopup((event, data) => {
            console.log('📨 Received init data:', data);
            this.timeoutMinutes = data.timeoutMinutes;
            this.countdownSeconds = data.countdownSeconds;
            this.updateDisplay();
        });

        // Listen for countdown updates
        window.idleWarningAPI.onUpdateCountdown((event, seconds) => {
            this.countdownSeconds = seconds;
            this.updateCountdown();
        });

        // Listen for "On Break" status
        window.idleWarningAPI.onShowOnBreakStatus(() => {
            this.showOnBreakStatus();
        });
    }

    updateDisplay() {
        // Update idle time display
        const idleTimeElement = document.getElementById('idleTime');
        if (idleTimeElement) {
            idleTimeElement.textContent = `${this.timeoutMinutes} minute${this.timeoutMinutes !== 1 ? 's' : ''}`;
        }

        // Update initial countdown
        this.updateCountdown();
    }

    updateCountdown() {
        const countdownNumber = document.getElementById('countdownNumber');
        const countdownSeconds = document.getElementById('countdownSeconds');
        const countdownCircle = document.querySelector('.countdown-circle');

        if (countdownNumber) {
            countdownNumber.textContent = this.countdownSeconds;
        }

        if (countdownSeconds) {
            countdownSeconds.textContent = this.countdownSeconds;
        }

        // Add urgent styling when countdown is low
        if (countdownCircle) {
            if (this.countdownSeconds <= 10) {
                countdownCircle.classList.add('countdown-urgent');
            } else {
                countdownCircle.classList.remove('countdown-urgent');
            }
        }

        // Change button text when time is running out
        const resumeBtn = document.getElementById('resumeBtn');
        if (resumeBtn) {
            if (this.countdownSeconds <= 10) {
                resumeBtn.innerHTML = '<i class="fas fa-bolt"></i> Quick! I\'m Here';
                resumeBtn.style.animation = 'pulse 0.5s infinite';
            } else {
                resumeBtn.innerHTML = '<i class="fas fa-play"></i> I\'m Still Here';
                resumeBtn.style.animation = '';
            }
        }

        // Auto-focus resume button when countdown is low
        if (this.countdownSeconds <= 5 && resumeBtn) {
            resumeBtn.focus();
        }
    }

    showOnBreakStatus() {
        console.log('❗ Showing "On Break" status - popup stays visible');

        // Update header to show "On Break"
        const header = document.querySelector('.popup-header h2');
        if (header) {
            header.textContent = 'On Break';
        }

        // Update icon to break icon
        const icon = document.querySelector('.warning-icon i');
        if (icon) {
            icon.className = 'fas fa-coffee'; // Coffee break icon
        }

        // Update message
        const message = document.querySelector('.idle-message');
        if (message) {
            message.innerHTML = 'You are currently <strong>on break</strong>.<br>Click below when you\'re ready to resume work.';
        }

        // Hide countdown (no longer needed)
        const countdownContainer = document.querySelector('.countdown-container');
        if (countdownContainer) {
            countdownContainer.style.display = 'none';
        }

        // Update buttons
        const resumeBtn = document.getElementById('resumeBtn');
        const continueIdleBtn = document.getElementById('continueIdleBtn');

        if (resumeBtn) {
            resumeBtn.innerHTML = '<i class="fas fa-play"></i> Resume Work';
            resumeBtn.style.animation = '';
        }

        if (continueIdleBtn) {
            continueIdleBtn.style.display = 'none'; // Hide continue idle button
        }

        // Change popup styling to "on break" theme
        const container = document.querySelector('.popup-container');
        if (container) {
            container.classList.add('on-break-mode');
        }
    }

    handleResume() {
        console.log('✅ User activity detected - resuming session');

        // Immediately notify main process to close window
        window.idleWarningAPI.resumeActivity();

        // Window will be destroyed immediately, no need for animations
    }

    handleContinueIdle() {
        console.log('⏸️ User chose to continue being idle');
        
        // Add visual feedback
        const continueIdleBtn = document.getElementById('continueIdleBtn');
        if (continueIdleBtn) {
            continueIdleBtn.innerHTML = '<i class="fas fa-check"></i> Pausing...';
            continueIdleBtn.disabled = true;
        }

        // Notify main process
        window.idleWarningAPI.continueIdle();
    }

    // Cleanup when popup is closed
    cleanup() {
        window.idleWarningAPI.removeAllListeners('init-popup');
        window.idleWarningAPI.removeAllListeners('update-countdown');
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new IdleWarningManager();
});

// Cleanup on unload
window.addEventListener('beforeunload', () => {
    if (window.idleWarningManager) {
        window.idleWarningManager.cleanup();
    }
});
