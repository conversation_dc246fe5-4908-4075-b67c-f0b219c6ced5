<?php

namespace App\Controller\Admin;

use App\Entity\EmailTemplate;
use App\Form\EmailTemplateType;
use App\Repository\EmailTemplateRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Csrf\CsrfTokenManagerInterface;

#[Route('/admin')]
class EmailTemplateController extends AbstractController
{
    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly EmailTemplateRepository $emailTemplateRepository,
        private readonly CsrfTokenManagerInterface $csrfTokenManager
    ) {}

    #[Route('/email-template/create', name: 'email_template_create', methods: ['GET','POST'])]
    public function createEmailTemplate(Request $request): Response
    {
        $emailTemplate = new EmailTemplate();
        $form = $this->createForm(EmailTemplateType::class, $emailTemplate);

        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $this->entityManager->persist($emailTemplate);
            $this->entityManager->flush();

            $this->addFlash('success', 'Email template created successfully.');
            return $this->redirectToRoute('email_template_list');
        }

        return $this->render('admin/EmailTemplate/email_template_form.html.twig', [
            'form' => $form->createView(),
        ]);
    }

    #[Route('/email-template', name: 'email_template_list', methods: ['GET'])]
    public function listEmailTemplates(): Response
    {
        $emailTemplates = $this->emailTemplateRepository->findAll();

        return $this->render('admin/EmailTemplate/email_template_list.html.twig', [
            'emailTemplates' => $emailTemplates,
        ]);
    }

    #[Route('/email-template/edit/{id}', name: 'email_template_edit', methods: ['GET', 'POST'])]
    public function editEmailTemplate(Request $request, EmailTemplate $emailTemplate): Response
    {
        $form = $this->createForm(EmailTemplateType::class, $emailTemplate);

        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $this->entityManager->flush();

            $this->addFlash('success', 'Email template updated successfully.');
            return $this->redirectToRoute('email_template_list');
        }

        return $this->render('admin/EmailTemplate/email_template_form.html.twig', [
            'form' => $form->createView(),
            'emailTemplate' => $emailTemplate,
        ]);
    }

    #[Route('/email-template/delete/{id}', name: 'email_template_delete', methods: ['POST'])]
    public function deleteEmailTemplate(Request $request, EmailTemplate $emailTemplate): Response
    {
        $token = $request->request->get('_token');
        // Change the token ID to match what's in the template
        if ($this->csrfTokenManager->isTokenValid(new \Symfony\Component\Security\Csrf\CsrfToken('delete' . $emailTemplate->getId(), $token))) {
            $this->entityManager->remove($emailTemplate);
            $this->entityManager->flush();

            $this->addFlash('success', 'Email template deleted successfully.');
        } else {
            $this->addFlash('error', 'Invalid CSRF token.');
        }

        return $this->redirectToRoute('email_template_list');
    }
    #[Route('/check-identifier-unique', name: 'check_identifier_unique', methods: ['POST'])]
    public function checkIdentifierUnique(Request $request,EntityManagerInterface $entityManager): JsonResponse
    {
        $identifier = $request->request->get('identifier');
        $currentId = $request->request->get('currentId');

        $repository = $entityManager->getRepository(EmailTemplate::class);
        $template = $repository->findOneBy(['identifier' => $identifier]);

        $isUnique = $template === null || ($currentId && $template->getId() === $currentId);

        return new JsonResponse(['isUnique' => $isUnique]);
    }
}
