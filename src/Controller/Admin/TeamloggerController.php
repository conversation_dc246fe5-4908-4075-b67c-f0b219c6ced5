<?php

namespace App\Controller\Admin;

use App\Entity\Setting;
use App\Repository\SettingRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\HttpFoundation\JsonResponse;

#[Route('/admin')]
class TeamloggerController extends AbstractController
{
    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly SettingRepository $teamLoggerConfigRepository
    )
    {
    }

    /**
     * @return Response
     */
    #[Route(path: '/setting', name: 'setting', methods: ['GET'])]
    public function index(): Response
    {
        $base = $this->getConfigurationValue('Base_URL', $_ENV['BASE_URL']);
        $api = $this->getConfigurationValue('TEAMLOGGER_API_KEY', $_ENV['TEAMLOGGER_API_KEY']);
        $threshold = $this->getConfigurationValue('THRESHOLD', $_ENV['THRESHOLD']);

        return $this->render('admin/teamlogger_config.html.twig', [
            'Base_URL' => $base,
            'TEAMLOGGER_API_KEY' => $api,
            'THRESHOLD' => $threshold,
        ]);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    #[Route(path: '/setting/update', name: 'setting_update', methods: ['POST'])]
    public function update(Request $request): JsonResponse
    {
        $requestData = json_decode($request->getContent(), true);

        if (!$requestData || !isset($requestData['Base_URL']) || !isset($requestData['TEAMLOGGER_API_KEY']) || !isset($requestData['THRESHOLD'])) {
            return new JsonResponse(['message' => 'Invalid data'], 400);
        }

        $base = $requestData['Base_URL'];
        $api = $requestData['TEAMLOGGER_API_KEY'];
        $threshold = $requestData['THRESHOLD'];

        // Update Base_Url configuration
        $this->updateConfiguration('Base_URL', $base);

        // Update API Key configuration
        $this->updateConfiguration('TEAMLOGGER_API_KEY', $api);

        // Update THRESHOLD configuration
        $this->updateConfiguration('THRESHOLD', $threshold);

        return new JsonResponse(['message' => 'Settings updated successfully']);
    }

    /**
     * @param string $fieldName
     * @param string $defaultValue
     * @return string|null
     */
    private function getConfigurationValue(string $fieldName, string $defaultValue): ?string
    {
        $configuration = $this->teamLoggerConfigRepository->findOneBy(['fieldName' => $fieldName]);
        return $configuration ? $configuration->getFieldValue() : $defaultValue;
    }

    /**
     * @param string $fieldName
     * @param $fieldValue
     * @return void
     */
    private function updateConfiguration(string $fieldName, $fieldValue): void
    {
        $configuration = $this->teamLoggerConfigRepository->findOneBy(['fieldName' => $fieldName]);
        if (!$configuration) {
            $configuration = new Setting();
            $configuration->setFieldName($fieldName);
        }
        $configuration->setFieldValue($fieldValue);
        $configuration->setDateUpdated(new \DateTime());

        $this->entityManager->persist($configuration);
        $this->entityManager->flush();
    }
}
