<?php

namespace App\Helper;

class BootstrapIconHelper
{
    /**
     * Returns an array of all Font Awesome icons
     *
     * @return array
     */
    public static function getIconsList(): array
    {
        return [
            'fa fa-user' => 'User',
            'fa fa-home' => 'Home',
            'fa fa-envelope' => 'Envelope',
            'fa fa-bell' => 'Bell',
            'fa fa-book' => 'Book',
            'fa fa-camera' => 'Camera',
            'fa fa-car' => 'Car',
            'fa fa-check' => 'Check',
            'fa fa-clock' => 'Clock',
            'fa fa-cloud' => 'Cloud',
            'fa fa-code' => 'Code',
            'fa fa-cog' => 'Settings',
            'fa fa-comments' => 'Comments',
            'fa fa-download' => 'Download',
            'fa fa-edit' => 'Edit',
            'fa fa-exclamation' => 'Exclamation',
            'fa fa-eye' => 'Eye',
            'fa fa-file' => 'File',
            'fa fa-folder' => 'Folder',
            'fa fa-globe' => 'Globe',
            'fa fa-heart' => 'Heart',
            'fa fa-info' => 'Info',
            'fa fa-key' => 'Key',
            'fa fa-lock' => 'Lock',
            'fa fa-map' => 'Map',
            'fa fa-music' => 'Music',
            'fa fa-phone' => 'Phone',
            'fa fa-play' => 'Play',
            'fa fa-print' => 'Print',
            'fa fa-search' => 'Search',
            'fa fa-share' => 'Share',
            'fa fa-shopping-cart' => 'Shopping Cart',
            'fa fa-star' => 'Star',
            'fa fa-trash' => 'Trash',
            'fa fa-upload' => 'Upload',
            'fa fa-user-circle' => 'User Circle',
            'fa fa-wrench' => 'Wrench',
        ];
    }
}
