<?php

namespace App\Form;

use App\Entity\SocialMediaPost;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\CallbackTransformer;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\Length;
use Symfony\Component\Validator\Constraints\NotBlank;

class SocialMediaPostType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('title', TextType::class, [
                'label' => 'Post Title',
                'attr' => [
                    'placeholder' => 'Enter post title',
                    'class' => 'form-control',
                    'maxlength' => 255,
                    'data-validation' => 'max:255'
                ],
                'constraints' => [
                    new NotBlank([
                        'message' => 'Post title cannot be blank',
                    ]),
                    new Length([
                        'min' => 2,
                        'max' => 255,
                        'minMessage' => 'Post title must be at least {{ limit }} characters long',
                        'maxMessage' => 'Post title cannot be longer than {{ limit }} characters',
                    ]),
                ],
            ])
            ->add('description', TextareaType::class, [
                'label' => 'Description',
                'required' => false,
                'attr' => [
                    'placeholder' => 'Enter post description (optional)',
                    'class' => 'form-control',
                    'rows' => 1,
                    'maxlength' => 255,
                    'data-validation' => 'max:255'
                ],
                'constraints' => [
                    new Length([
                        'max' => 255,
                        'maxMessage' => 'Description cannot be longer than {{ limit }} characters',
                    ]),
                ],
            ])
            ->add('postDate', TextType::class, [
                'label' => 'Post Date',
                'attr' => [
                    'class' => 'form-control datepicker',
                    'placeholder' => 'Select date',
                    'required' => 'required',
                ],
            ])
            ->add('mediaType', ChoiceType::class, [
                'label' => 'Media Type',
                'choices' => [
                    'Image' => 'image',
                    'Video' => 'video',
                ],
                'attr' => [
                    'class' => 'form-control'
                ],
                'constraints' => [
                    new NotBlank([
                        'message' => 'Media type cannot be blank',
                    ]),
                ],
            ]);

        $builder->get('postDate')
            ->addModelTransformer(new CallbackTransformer(
                function ($dateAsObject) {
                    return $dateAsObject instanceof \DateTimeInterface ? $dateAsObject->format('d-m-Y') : '';
                },
                function ($dateAsString) {
                    return $dateAsString ? \dateTime::createFromFormat('d-m-Y', $dateAsString) : null;
                }
            ));
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => SocialMediaPost::class,
        ]);
    }
}