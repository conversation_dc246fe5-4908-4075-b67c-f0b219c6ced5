{% extends 'Front/dashboard/base.html.twig' %}

{% block title %}
    {{ mode == 'create' ? 'Create New Bug' : 'Edit Bug' }}
{% endblock %}

{% block body %}
    {% set breadcrumbs = [
        { 'label': 'Dashboard', 'url': url('dashboard_employee') },
        {
            'label': isHistory ? 'Task History' : 'My Tasks',
            'url': isHistory ? path('employee_task_history') : path('employee_task')
        },
    ] %}

    {% if taskAssignment is defined and taskAssignment %}
        {% set task = taskAssignment.task %}
        {% set breadcrumbs = breadcrumbs|merge([
            {
                'label': task.title ~ '',
                'url': path('bug_index', {'taskAssignmentId': taskAssignment.id})
            }
        ]) %}
    {% elseif task is defined and task %}
        {% set breadcrumbs = breadcrumbs|merge([
            {
                'label': task.title ~ '',
                'url': path('bug_index', {'task': task.id})
            }
        ]) %}
    {% endif %}

    {% include 'admin/partials/breadcrumbs.html.twig' with {
        title: 'Bugs',
        breadcrumbs: breadcrumbs,
        active: mode == 'create' ? 'Create Bug' : 'Edit Bug'
    } %}
    {% include 'CommonTemplate/Bug/new.html.twig' %}

    <script>
        window.isAdmin = {{ is_granted('ROLE_ADMIN') ? 'true' : 'false' }};
    </script>
    <script src="{{ asset('assets/js/front/employeeBug.js') }}"></script>
{% endblock %}
