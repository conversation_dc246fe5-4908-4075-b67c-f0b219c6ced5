<?php

namespace App\Service\Common;

use App\Entity\EmployeeDocument;
use App\Entity\MasterEmployee;
use App\Helper\ModalboxHelper;
use Doctrine\ORM\QueryBuilder;
use Omines\DataTablesBundle\Adapter\Doctrine\ORMAdapter;
use Omines\DataTablesBundle\Column\DateTimeColumn;
use Omines\DataTablesBundle\Column\TextColumn;
use Omines\DataTablesBundle\DataTable;
use Omines\DataTablesBundle\DataTableFactory;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Security\Csrf\CsrfTokenManagerInterface;
use App\Traits\ExceptionLoggerTrait;

class EmployeeDocumentService
{
    use ExceptionLoggerTrait;

    public function __construct(
        private readonly DataTableFactory $dataTableFactory,
        private readonly RouterInterface $router,
        private readonly ParameterBagInterface $parameterBag,
        private readonly CsrfTokenManagerInterface $csrfTokenManager,
        private readonly ModalboxHelper $modalboxHelper,
        private readonly Security $security
    ) {
        $this->initializeLogger($parameterBag, 'document_service');
    }

    public function createDocumentTable(Request $request, ?MasterEmployee $employee = null): DataTable
    {
        $dataTable = $this->dataTableFactory->create();

        foreach ($this->getColumns() as $key => $column) {
            $dataTable->add($key, $column['className'], array_filter($column, fn($k) => $k !== 'className', ARRAY_FILTER_USE_KEY));
        }

        return $dataTable->createAdapter(ORMAdapter::class, [
            'entity' => EmployeeDocument::class,
            'query' => fn(QueryBuilder $qb) => $this->buildQuery($qb, $employee),
        ])
            ->addOrderBy('uploadedAt', 'desc')
            ->handleRequest($request);
    }
    private function getColumns(): array
    {
        return [
            'documentType' => [
                'label' => 'Document Type',
                'field' => 'd.documentType',
                'className' => TextColumn::class,
            ],
            'notes' => [
                'label' => 'Notes',
                'field' => 'd.notes',
                'className' => TextColumn::class,
                'render' => function ($value) {
                    if (empty($value)) {
                        return '-';
                    }
                    $description = (string) $value;
                    $maxLength = 20;
                    if (strlen($description) > $maxLength) {
                        $truncated = substr($description, 0, $maxLength) . '...';
                        return sprintf('<span title="%s">%s</span>', htmlspecialchars($description), htmlspecialchars($truncated));
                    }
                    return htmlspecialchars($description);
                },
            ],
            'fileName' => [
                'label' => 'File',
                'field' => 'd.filePath',
                'className' => TextColumn::class,
                'render' => fn($value) => sprintf('<a href="%s" target="_blank" style="color:#007bff;">Download</a>', $value),
            ],
            'uploadedAt' => [
                'label' => 'Uploaded Date',
                'field' => 'd.uploadedAt',
                'format' => 'd-m-Y',
                'className' => DateTimeColumn::class,
                'searchable' => false,
            ],
            'uploadedBy' => [
                'label' => 'Uploaded By',
                'className' => TextColumn::class,
                'searchable' => false,
                'orderable' => false,
                'render' => function ($value, $context) {
                    $admin = $context->getUploadedByAdmin();
                    $employee = $context->getUploadedBy();
                    $name = $admin ? $admin->getUsername() : ($employee ? $employee->getName() : 'Unknown');
                    return $name ;
                },
            ],
            'actions' => [
                'label' => 'Actions',
                'field' => 'd.id',
                'className' => TextColumn::class,
                'orderable' => false,
                'render' => fn($value, $context) => $this->renderActionColumn($context),
            ],
        ];
    }

    private function renderActionColumn($context): string
    {
        $entityId = $context->getId();
        $isAdmin = $this->security->isGranted('ROLE_ADMIN');
        $baseRoute = $isAdmin ? 'admin' : 'my';
        $editUrl = $this->router->generate("{$baseRoute}_employee_document_edit", ['id' => $entityId]);
        $deleteUrl = $this->router->generate("{$baseRoute}_employee_document_delete", ['id' => $entityId]);
        $csrfToken = $this->csrfTokenManager->getToken('delete' . $entityId)->getValue();
        $filePath = $context->getFilePath();
        $documentTitle = $context->getDocumentType();

        $modalId = 'deleteModal_' . $entityId;
        $modalBody = 'Are you sure you want to permanently delete this document?<br><br><strong>Warning:</strong> This action cannot be undone.';
        $footerButtons = sprintf(
            '<button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>' .
            '<button type="button" class="btn btn-primary delete-document-btn" data-delete="true" data-url="%s" data-token="%s">Delete</button>',
            $deleteUrl,
            $csrfToken
        );

        $deleteModal = $this->modalboxHelper->renderModal(
            $modalId,
            'Confirm Document Deletion',
            $modalBody,
            $footerButtons
        );

        return sprintf(
            '<div class="d-inline-flex gap-2">
                <a href="#" class="bs-btn btn btn-primary btn-sm" id="pdfViewerModal" style="margin: 2px;" onclick="openPdfModal(\'%s\', \'%s\'); return false;" title="View">
                    <i class="bs-fa fa fa-eye"></i>
                </a>
                <a href="%s" class="bs-btn btn btn-primary btn-sm" style="margin: 2px;"><i class="bs-fa fa fa-edit"></i></a>
                <button type="button" class="bs-btn btn btn-primary btn-sm" style="margin: 2px;" data-toggle="modal" data-target="#%s">
                    <i class="bs-fa fa fa-trash"></i>
                </button>
            </div>%s',
            $filePath,
            $documentTitle,
            $editUrl,
            $modalId,
            $deleteModal
        );
    }

    private function buildQuery(QueryBuilder $qb, ?MasterEmployee $employee = null): void
    {
        $qb->select('d')
            ->from(EmployeeDocument::class, 'd');

        if ($employee) {
            $qb->andWhere('d.masterEmployee = :employee')
                ->setParameter('employee', $employee);
        }
    }

    public function handleFileUpload($file, EmployeeDocument $document): void
    {
        if ($file) {
            $employeeName = $document->getMasterEmployee()->getName();
            $documentType = $document->getDocumentType();

            $safeEmployeeName = preg_replace('/[^a-zA-Z0-9]/', '_', strtolower($employeeName));
            $safeDocumentType = preg_replace('/[^a-zA-Z0-9]/', '_', strtolower($documentType));
            $extension = $file->guessExtension() ?: 'bin';

            $fileName = sprintf('%s_%s.%s', $safeEmployeeName, $safeDocumentType, $extension);

            $uploadDir = $this->parameterBag->get('uploads_directory');
            $filePath = $uploadDir . '/' . $fileName;

            if (file_exists($filePath)) {
                unlink($filePath);
            }

            $file->move($uploadDir, $fileName);

            $document->setFileName($fileName);
            $document->setFilePath('/uploads/documents/' . $fileName);
        }
    }


    public function deleteFile(EmployeeDocument $document): void
    {
        if ($document->getFilePath()) {
            $filePath = $this->parameterBag->get('uploads_directory') . '/' . basename($document->getFilePath());
            if (file_exists($filePath)) {
                unlink($filePath);
            }
        }
    }
}