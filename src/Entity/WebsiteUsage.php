<?php

namespace App\Entity;

use App\Repository\WebsiteUsageRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: WebsiteUsageRepository::class)]
#[ORM\Table(name: 'website_usage')]
#[ORM\Index(columns: ['employee_id', 'visit_date'], name: 'idx_employee_visit_date')]
#[ORM\Index(columns: ['domain'], name: 'idx_domain')]
#[ORM\Index(columns: ['category'], name: 'idx_category')]
class WebsiteUsage
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\ManyToOne(targetEntity: MasterEmployee::class)]
    #[ORM\JoinColumn(nullable: false)]
    private ?MasterEmployee $employee = null;

    #[ORM\Column(type: Types::DATE_MUTABLE)]
    private ?\DateTimeInterface $visitDate = null;

    #[ORM\Column(type: Types::TEXT)]
    private ?string $url = null;

    #[ORM\Column(length: 255)]
    private ?string $domain = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $pageTitle = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $visitTime = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $leaveTime = null;

    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    private ?int $duration = null; // Duration in seconds

    #[ORM\Column(length: 50, nullable: true)]
    private ?string $category = null; // 'work', 'social', 'news', 'entertainment', 'shopping', etc.

    #[ORM\Column(length: 50, nullable: true)]
    private ?string $productivityLevel = null; // 'productive', 'neutral', 'distracting'

    #[ORM\Column(length: 100, nullable: true)]
    private ?string $browserName = null;

    #[ORM\Column(type: Types::INTEGER, options: ['default' => 1])]
    private int $visitCount = 1;

    #[ORM\Column(length: 100, nullable: true)]
    private ?string $deviceId = null;

    #[ORM\Column(type: Types::JSON, nullable: true)]
    private ?array $metadata = null;

    #[ORM\Column(type: Types::BOOLEAN, options: ['default' => false])]
    private bool $isSynced = false;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $createdAt = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $updatedAt = null;

    public function __construct()
    {
        $this->visitDate = new \DateTime();
        $this->visitTime = new \DateTime();
        $this->createdAt = new \DateTime();
        $this->updatedAt = new \DateTime();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getEmployee(): ?MasterEmployee
    {
        return $this->employee;
    }

    public function setEmployee(?MasterEmployee $employee): static
    {
        $this->employee = $employee;
        return $this;
    }

    public function getVisitDate(): ?\DateTimeInterface
    {
        return $this->visitDate;
    }

    public function setVisitDate(\DateTimeInterface $visitDate): static
    {
        $this->visitDate = $visitDate;
        return $this;
    }

    public function getUrl(): ?string
    {
        return $this->url;
    }

    public function setUrl(string $url): static
    {
        $this->url = $url;
        // Auto-extract domain
        $this->domain = parse_url($url, PHP_URL_HOST);
        return $this;
    }

    public function getDomain(): ?string
    {
        return $this->domain;
    }

    public function setDomain(string $domain): static
    {
        $this->domain = $domain;
        return $this;
    }

    public function getPageTitle(): ?string
    {
        return $this->pageTitle;
    }

    public function setPageTitle(?string $pageTitle): static
    {
        $this->pageTitle = $pageTitle;
        return $this;
    }

    public function getVisitTime(): ?\DateTimeInterface
    {
        return $this->visitTime;
    }

    public function setVisitTime(\DateTimeInterface $visitTime): static
    {
        $this->visitTime = $visitTime;
        return $this;
    }

    public function getLeaveTime(): ?\DateTimeInterface
    {
        return $this->leaveTime;
    }

    public function setLeaveTime(?\DateTimeInterface $leaveTime): static
    {
        $this->leaveTime = $leaveTime;
        return $this;
    }

    public function getDuration(): ?int
    {
        return $this->duration;
    }

    public function setDuration(?int $duration): static
    {
        $this->duration = $duration;
        return $this;
    }

    public function getCategory(): ?string
    {
        return $this->category;
    }

    public function setCategory(?string $category): static
    {
        $this->category = $category;
        return $this;
    }

    public function getProductivityLevel(): ?string
    {
        return $this->productivityLevel;
    }

    public function setProductivityLevel(?string $productivityLevel): static
    {
        $this->productivityLevel = $productivityLevel;
        return $this;
    }

    public function getBrowserName(): ?string
    {
        return $this->browserName;
    }

    public function setBrowserName(?string $browserName): static
    {
        $this->browserName = $browserName;
        return $this;
    }

    public function getVisitCount(): int
    {
        return $this->visitCount;
    }

    public function setVisitCount(int $visitCount): static
    {
        $this->visitCount = $visitCount;
        return $this;
    }

    public function incrementVisitCount(): static
    {
        $this->visitCount++;
        return $this;
    }

    public function getDeviceId(): ?string
    {
        return $this->deviceId;
    }

    public function setDeviceId(?string $deviceId): static
    {
        $this->deviceId = $deviceId;
        return $this;
    }

    public function getMetadata(): ?array
    {
        return $this->metadata;
    }

    public function setMetadata(?array $metadata): static
    {
        $this->metadata = $metadata;
        return $this;
    }

    public function isSynced(): bool
    {
        return $this->isSynced;
    }

    public function setIsSynced(bool $isSynced): static
    {
        $this->isSynced = $isSynced;
        return $this;
    }

    public function getCreatedAt(): ?\DateTimeInterface
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTimeInterface $createdAt): static
    {
        $this->createdAt = $createdAt;
        return $this;
    }

    public function getUpdatedAt(): ?\DateTimeInterface
    {
        return $this->updatedAt;
    }

    public function setUpdatedAt(\DateTimeInterface $updatedAt): static
    {
        $this->updatedAt = $updatedAt;
        return $this;
    }

    #[ORM\PreUpdate]
    public function setUpdatedAtValue(): void
    {
        $this->updatedAt = new \DateTime();
    }

    // Helper methods
    public function calculateDuration(): int
    {
        if ($this->visitTime && $this->leaveTime) {
            return $this->leaveTime->getTimestamp() - $this->visitTime->getTimestamp();
        }
        return 0;
    }

    public function isActive(): bool
    {
        return $this->leaveTime === null;
    }

    public function getDurationFormatted(): string
    {
        $duration = $this->duration ?? $this->calculateDuration();
        $hours = floor($duration / 3600);
        $minutes = floor(($duration % 3600) / 60);
        $seconds = $duration % 60;
        
        return sprintf('%02d:%02d:%02d', $hours, $minutes, $seconds);
    }

    public function getProductivityScore(): int
    {
        return match($this->productivityLevel) {
            'productive' => 100,
            'neutral' => 50,
            'distracting' => 0,
            default => 50
        };
    }

    public function getShortUrl(): string
    {
        if (strlen($this->url) > 50) {
            return substr($this->url, 0, 47) . '...';
        }
        return $this->url;
    }
}
