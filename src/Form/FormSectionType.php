<?php

namespace App\Form;

use App\Entity\FormSection;
use App\Helper\BootstrapIconHelper;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\Length;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\PositiveOrZero;

class FormSectionType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {

        $builder
            ->add('name', TextType::class, [
                'label' => 'Section Name',
                'required' => true,
                'constraints' => [
                    new NotBlank([
                        'message' => 'Section name is required.',
                    ]),
                    new Length([
                        'min' => 2,
                        'max' => 255,
                        'minMessage' => 'Section name must be at least {{ limit }} characters long',
                        'maxMessage' => 'Section name cannot be longer than {{ limit }} characters',
                    ]),
                ],
                'attr' => [
                    'placeholder' => 'Enter section name',
                    'class' => 'form-control',
                    'data-validation' => 'required|min:2|max:255',
                    'maxlength' => 255,
                ]
            ])
            ->add('description', TextareaType::class, [
                'label' => 'Description',
                'required' => false,
                'constraints' => [
                    new Length([
                        'max' => 255,
                        'maxMessage' => 'Description cannot be longer than {{ limit }} characters',
                    ]),
                ],
                'attr' => [
                    'placeholder' => 'Enter section description',
                    'class' => 'form-control',
                    'rows' => 1,
                    'data-validation' => 'max:255',
                    'maxlength' => 255,
                ]
            ])
//            ->add('isMandatory', CheckboxType::class, [
//                'label' => 'Required Section',
//                'required' => false,
//                'attr' => [
//                    'class' => 'form-check-input'
//                ]
//            ])
            ->add('icon', ChoiceType::class, [
                'label' => 'Section Icon',
                'required' => false,
                'choices' => array_flip(BootstrapIconHelper::getIconsList()),
                'attr' => [
                    'class' => 'form-control icon-select',
                    'data-live-search' => 'true',
                ],
                'choice_attr' => function ($choice, $key, $value) {
                    return ['data-icon' => $value];
                },
            ])
            ->add('orderNumber', IntegerType::class, [
                'required' => true,
                'constraints' => [
                    new NotBlank([
                        'message' => 'orderNumber is required.',
                    ]),
                    new PositiveOrZero([
                        'message' => 'orderNumber must be zero or positive.',
                    ]),
                ],
                'attr' => [
                    'class' => 'form-control',
                    'data-validation' => 'required|numeric|min:0',
                ],
            ])
            ->add('status', ChoiceType::class, [
                'label' => 'Status',
                'choices' => [
                    'Active' => true,
                    'Inactive' => false,
                ],
            ])
        ;

    }
    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => FormSection::class,
        ]);
    }
}