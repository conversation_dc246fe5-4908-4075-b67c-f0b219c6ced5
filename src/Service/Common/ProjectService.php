<?php

namespace App\Service\Common;

use App\Entity\MasterEmployee;
use App\Entity\Project;
use App\Helper\ModalboxHelper;
use Doctrine\ORM\QueryBuilder;
use Omines\DataTablesBundle\Adapter\Doctrine\ORMAdapter;
use Omines\DataTablesBundle\Column\DateTimeColumn;
use Omines\DataTablesBundle\Column\TextColumn;
use Omines\DataTablesBundle\DataTable;
use Omines\DataTablesBundle\DataTableFactory;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Request;
use App\Traits\ExceptionLoggerTrait;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Security\Csrf\CsrfTokenManagerInterface;

class ProjectService
{
    use ExceptionLoggerTrait;

    public function __construct(
        private readonly DataTableFactory $dataTableFactory,
        private readonly RouterInterface $router,
        ParameterBagInterface $parameterBag,
        private readonly CsrfTokenManagerInterface $csrfTokenManager,
        private readonly ModalboxHelper $modalboxHelper
    ) {
        $this->initializeLogger($parameterBag, 'project_table_service');
    }

    public function createMasterProjectTable(Request $request): DataTable
    {
        $dataTable = $this->dataTableFactory->create();

        foreach ($this->getColumns() as $key => $column) {
            $dataTable->add($key, $column['className'], array_filter($column, fn($k) => $k !== 'className', ARRAY_FILTER_USE_KEY));
        }

        return $dataTable->createAdapter(ORMAdapter::class, [
            'entity' => Project::class,
            'query' => fn(QueryBuilder $qb) => $this->buildQuery($qb),
        ])
            ->addOrderBy('name', 'asc')
            ->handleRequest($request);
    }

    /**
     * @throws \Exception
     */
    public function createEmployeeProjectTable(Request $request, MasterEmployee $employee): DataTable
    {
        try {
            $dataTable = $this->dataTableFactory->create();

            $dataTable
                ->add('name', TextColumn::class, [
                    'label' => 'Project Name',
                    'field' => 'p.name',
                ])
                ->add('projectCode', TextColumn::class, [
                    'label' => 'Project Code',
                    'field' => 'p.projectCode',
                ])
                ->add('projectType', TextColumn::class, [
                    'label' => 'Type',
                    'field' => 'p.projectType',
                ])
                ->add('actions', TextColumn::class, [
                    'label' => 'Actions',
                    'field' => 'p.id',
                    'orderable' => false,
                    'render' => fn($value, $context) => $this->renderAction($context),
                ]);

            return $dataTable->createAdapter(ORMAdapter::class, [
                'entity' => Project::class,
                'query' => function (QueryBuilder $qb) use ($employee) {
                    $qb->select('p')
                        ->from(Project::class, 'p')
                        ->where('p.projectManager = :project_manager')
                        ->andWhere('p.status = :status')
                        ->setParameter('project_manager', $employee)
                        ->setParameter('status', 'active');
                },
            ])
                ->addOrderBy('name', 'asc')
                ->handleRequest($request);
        } catch (\Exception $e) {
            $this->log(
                'Error creating employee project table for employee ID.',
                [
                    'employee' => $employee->getId(),
                    'error_message' => $e->getMessage(),
                ]
            );
            throw $e;
        }
    }

    private function renderAction(Project $context): string
    {
        $entityId = $context->getId();
        $tasksUrl = $this->router->generate('employee_project_task', ['project_id' => $entityId]);
        $manageTeamUrl = $this->router->generate('employee_project_team_manage', ['project_id' => $entityId]);
        return sprintf(
            '
                 <div style="display: flex ; align-items: center; justify-content: center;">
                 <a href="%s" class="bs-btn btn btn-primary btn-sm m-1" title="Manage Tasks"><i class="bs-fa fa fa-tasks"></i></a>
                 <a href="%s" class="bs-btn btn btn-primary btn-sm" title="Manage Team"><i class="bs-fa fa fa-users"></i></a></div>',
            $tasksUrl,
            $manageTeamUrl
        );
    }

    private function getColumns(): array
    {
        return [
            'name' => [
                'label' => 'Project Name',
                'field' => 'p.name',
                'className' => TextColumn::class,
            ],
            'projectCode' => [
                'label' => 'Project Code',
                'field' => 'p.projectCode',
                'className' => TextColumn::class,
            ],
            'projectType' => [
                'label' => 'Type',
                'field' => 'p.projectType',
                'className' => TextColumn::class,
            ],
            'department' => [
                'label' => 'Department',
                'field' => 'd.depName',
                'className' => TextColumn::class,
                'render' => fn($value, $context) => htmlspecialchars($context->getDepartment()?->getdepName() ?? 'N/A'),
            ],
            'projectManager' => [
                'label' => 'Manager',
                'field' => 'pm.name',
                'className' => TextColumn::class,
                'render' => fn($value, $context) => htmlspecialchars($context->getProjectManager()?->getName() ?? 'N/A'),
            ],
            'status' => [
                'label' => 'Status',
                'field' => 'p.status',
                'className' => TextColumn::class,
            ],
            'startDate' => [
                'label' => 'Start Date',
                'field' => 'p.startDate',
                'format' => 'd-m-Y',
                'className' => DateTimeColumn::class,
                'searchable' => false,
            ],
            'actions' => [
                'label' => 'Actions',
                'field' => 'p.id',
                'className' => TextColumn::class,
                'orderable' => false,
                'render' => fn($value, $context) => $this->renderActionColumn($context),
            ],
        ];
    }

    private function renderActionColumn($context): string
    {
        $entityId = $context->getId();
        $editUrl = $this->router->generate('project_edit', ['id' => $entityId]);
        $viewUrl = $this->router->generate('project_view', ['id' => $entityId]);
        $deleteUrl = $this->router->generate('project_delete', ['id' => $entityId]);
        $tasksUrl = $this->router->generate('project_task', ['project_id' => $entityId]);
        $manageTeamUrl = $this->router->generate('admin_project_team_manage', ['project_id' => $entityId]);
        $csrfToken = $this->csrfTokenManager->getToken('delete' . $entityId)->getValue();

        $modalId = 'deleteModal_' . $entityId;
        $modalBody = 'Are you sure you want to delete this project?<br><br><strong>Warning:</strong> This action cannot be undone.';
        $footerButtons = sprintf(
            '<button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>' .
            '<button type="button" class="btn btn-primary delete-project-btn" data-url="%s" data-delete="true" data-token="%s">Delete</button>',
            $deleteUrl,
            $csrfToken
        );

        $deleteModal = $this->modalboxHelper->renderModal(
            $modalId,
            'Confirm Project Deletion',
            $modalBody,
            $footerButtons
        );

        return sprintf(
            '<div class="d-inline-flex gap-2">
                <a href="%s" class="bs-btn btn btn-primary btn-sm"  title="View" style="margin: 2px;"><i class="bs-fa fa fa-eye"></i></a>
                <a href="%s" class="bs-btn btn btn-primary btn-sm"  title="Edit" style="margin: 2px;"><i class="bs-fa fa fa-edit"></i></a>
                <a href="%s" class="bs-btn btn btn-primary btn-sm" style="margin: 2px;" title="View Tasks"><i class="bs-fa fa fa-tasks"></i></a>
                <a href="%s" class="bs-btn btn btn-primary btn-sm" style="margin: 2px;" title="Manage Team"><i class="bs-fa fa fa-users"></i></a>
                <button type="button" class="bs-btn btn btn-primary btn-sm" title="Delete" style="margin: 2px;" data-toggle="modal" data-target="#%s">
                    <i class="bs-fa fa fa-trash"></i>
                </button>
            </div>%s',
            $viewUrl,
            $editUrl,
            $tasksUrl,
            $manageTeamUrl,
            $modalId,
            $deleteModal
        );
    }

    private function buildQuery(QueryBuilder $qb): void
    {
        $qb->select('p')
            ->addSelect('d', 'pm')
            ->from(Project::class, 'p')
            ->leftJoin('p.department', 'd')
            ->leftJoin('p.projectManager', 'pm');
    }
}