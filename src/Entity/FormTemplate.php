<?php

namespace App\Entity;

use App\Repository\FormTemplateRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: FormTemplateRepository::class)]
class FormTemplate
{
    public const INACTIVE = false;
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private ?int $id = null;

    #[ORM\Column(type: 'string', length: 255)]
    private string $name;

    #[ORM\Column(type: 'text', nullable: true)]
    private ?string $description = null;

    #[ORM\Column(type: 'string', length: 100, nullable: true)]
    private ?string $category = null;

    #[ORM\Column(type: 'string', length: 50, nullable: true)]
    private ?string $frequency = null;

    #[ORM\Column(type: 'boolean')]
    private bool $status = true;

    #[ORM\Column(type: 'integer')]
    private int $version = 1;

    #[ORM\ManyToOne(targetEntity: User::class)]
    #[ORM\JoinColumn(nullable: true)]
    private ?User $createdBy = null;

    #[ORM\Column(type: 'datetime')]
    private \DateTimeInterface $createdAt;

    #[ORM\Column(type: 'datetime')]
    private \DateTimeInterface $updatedAt;

    #[ORM\OneToMany(targetEntity: FormSection::class, mappedBy: 'template', orphanRemoval: true)]
    private Collection $sections;

    #[ORM\ManyToMany(targetEntity: MasterEmployee::class)]
    #[ORM\JoinTable(name: "form_template_team_leader")]
    private Collection $teamLeaders;
    #[ORM\Column(type: Types::BOOLEAN, options: ["default" => false])]
    private ?bool $isDelete = self::INACTIVE;

    #[ORM\Column(type: 'boolean')]
    private bool $assignToAll = false;

    public function __construct()
    {
        $this->sections = new ArrayCollection();
        $this->createdAt = new \DateTime();
        $this->updatedAt = new \DateTime();
        $this->teamLeaders = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): void
    {
        $this->id = $id;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): void
    {
        $this->name = $name;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): void
    {
        $this->description = $description;
    }

    public function getCategory(): ?string
    {
        return $this->category;
    }

    public function setCategory(?string $category): void
    {
        $this->category = $category;
    }

    public function getFrequency(): ?string
    {
        return $this->frequency;
    }

    public function setFrequency(?string $frequency): void
    {
        $this->frequency = $frequency;
    }



    public function getVersion(): int
    {
        return $this->version;
    }

    public function setVersion(int $version): void
    {
        $this->version = $version;
    }

    public function getCreatedBy(): User
    {
        return $this->createdBy;
    }

    public function setCreatedBy(User $createdBy): void
    {
        $this->createdBy = $createdBy;
    }

    public function getCreatedAt(): \DateTimeInterface
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTimeInterface $createdAt): void
    {
        $this->createdAt = $createdAt;
    }

    public function getUpdatedAt(): \DateTimeInterface
    {
        return $this->updatedAt;
    }

    public function setUpdatedAt(\DateTimeInterface $updatedAt): void
    {
        $this->updatedAt = $updatedAt;
    }

    public function getSections(): Collection
    {
        return $this->sections;
    }

    public function setSections(Collection $sections): void
    {
        $this->sections = $sections;
    }

    /**
     * @return Collection
     */
    public function getTeamLeaders(): Collection
    {
        return $this->teamLeaders;
    }

    public function addTeamLeader(MasterEmployee $teamLeader): static
    {
        if (!$this->teamLeaders->contains($teamLeader)) {
            $this->teamLeaders[] = $teamLeader;
        }
        return $this;
    }

    public function removeTeamLeader(MasterEmployee $teamLeader): static
    {
        $this->teamLeaders->removeElement($teamLeader);
        return $this;
    }

    public function isStatus(): bool
    {
        return $this->status;
    }

    public function setStatus(bool $status): void
    {
        $this->status = $status;
    }

    public function getIsDelete(): ?bool
    {
        return $this->isDelete;
    }

    public function setIsDelete(?bool $isDelete): void
    {
        $this->isDelete = $isDelete;
    }

    public function isAssignToAll(): bool
    {
        return $this->assignToAll;
    }

    public function setAssignToAll(bool $assignToAll): void
    {
        $this->assignToAll = $assignToAll;
    }
}