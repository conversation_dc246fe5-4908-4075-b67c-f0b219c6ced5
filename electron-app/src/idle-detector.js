const { powerMonitor } = require('electron');
const IdleWarningPopup = require('./idle-warning-popup');

class IdleDetector {
    constructor(statusTracker, timeLogger, store) {
        this.statusTracker = statusTracker;
        this.timeLogger = timeLogger;
        this.store = store;

        // Idle detection state
        this.lastActivityTime = new Date();
        this.currentIdleState = 'active';
        this.isWarningShown = false;
        this.warningTimeout = null;
        this.isIdleTimeoutHandled = false;

        // Configuration (simplified)
        this.config = {
            idleTimeoutEnabled: true,
            idleTimeoutMinutes: 5, // Default 5 minutes
            warningDurationSeconds: 60 // 1 minute warning
        };

        // Detection intervals
        this.idleCheckInterval = null;

        // Idle warning popup
        this.idleWarningPopup = new IdleWarningPopup();
        this.idleWarningPopup.setupIpcHandlers();

        this.init();
    }

    async init() {
        console.log('🔍 Initializing Simplified Idle Detector...');

        // Load configuration
        await this.loadConfiguration();

        // Setup detection system
        this.setupIdleDetection();

        console.log('✅ Simplified Idle Detector initialized');
    }

    async loadConfiguration() {
        try {
            const savedConfig = await this.store.get('idleDetectorConfig');
            if (savedConfig) {
                this.config = { ...this.config, ...savedConfig };
            }
            console.log('⚙️ Idle detector configuration loaded:', this.config);
        } catch (error) {
            console.error('Failed to load idle detector configuration:', error);
        }
    }

    setupIdleDetection() {
        if (!this.config.idleTimeoutEnabled) {
            console.log('⚠️ Idle detection is disabled');
            return;
        }

        // Monitor system-level idle time more frequently for better responsiveness
        this.idleCheckInterval = setInterval(() => {
            this.checkIdleTime();
        }, 1000); // Check every 1 second for better activity detection

        console.log(`🔍 Idle detection started - timeout: ${this.config.idleTimeoutMinutes} minutes`);
    }

    async checkIdleTime() {
        if (!this.config.idleTimeoutEnabled) {
            return;
        }

        try {
            const systemIdleTime = powerMonitor.getSystemIdleTime(); // in seconds
            const idleTimeoutSeconds = this.config.idleTimeoutMinutes * 60;
            const warningTimeoutSeconds = idleTimeoutSeconds - this.config.warningDurationSeconds;

            // If user is active, reset everything
            if (systemIdleTime === 0) {
                this.handleActivity();
                return;
            }

            // Also check if user was recently active (within last 2 seconds)
            // This helps catch activity that might not reset system idle time immediately
            if (systemIdleTime <= 2 && this.isWarningShown) {
                console.log('🟢 Recent activity detected while warning shown - hiding popup');
                this.handleActivity();
                return;
            }

            // Check if we should show warning (at timeout - 1 minute)
            if (systemIdleTime >= warningTimeoutSeconds && !this.isWarningShown) {
                this.showIdleWarning();
            }

            // Check if idle timeout reached (after warning period)
            if (systemIdleTime >= idleTimeoutSeconds && this.isWarningShown && !this.isIdleTimeoutHandled) {
                this.handleIdleTimeout();
            }

        } catch (error) {
            console.error('Idle time check failed:', error);
        }
    }

    handleActivity() {
        const wasIdle = this.currentIdleState !== 'active';

        if (this.isWarningShown) {
            console.log('🟢 Activity detected during warning - hiding popup and continuing session');
            this.hideIdleWarning();
        }

        this.lastActivityTime = new Date();
        this.currentIdleState = 'active';
        this.isIdleTimeoutHandled = false;

        // Update status tracker
        if (this.statusTracker) {
            this.statusTracker.updateStatus('active');
        }

        // If user was idle and now active, start a new session
        if (wasIdle && this.timeLogger) {
            console.log('🔄 User returned from idle - starting new session');
            this.timeLogger.startSessionAfterIdle('returned_from_idle');
        }
    }

    showIdleWarning() {
        if (this.isWarningShown) {
            return;
        }

        console.log(`⚠️ Showing idle warning - ${this.config.idleTimeoutMinutes} minutes idle`);
        this.isWarningShown = true;

        // Show popup with callbacks
        this.idleWarningPopup.show(
            this.config.idleTimeoutMinutes,
            () => this.handleIdleTimeout(), // onTimeout
            () => this.handleIdleResume()   // onResume
        );
    }

    hideIdleWarning() {
        if (!this.isWarningShown) {
            return;
        }

        console.log('✅ Hiding idle warning');
        this.isWarningShown = false;
        this.idleWarningPopup.hide();
    }

    handleIdleTimeout() {
        console.log('🔴 Idle timeout reached - ending current session and starting break period');
        this.isWarningShown = false;
        this.currentIdleState = 'idle';
        this.isIdleTimeoutHandled = true;

        // Update status tracker to show idle
        if (this.statusTracker) {
            this.statusTracker.updateStatus('idle');
        }

        // END the current session when idle timeout is reached
        if (this.timeLogger) {
            const idleStartTime = new Date(Date.now() - (this.config.idleTimeoutMinutes * 60 * 1000));
            this.timeLogger.endSessionDueToIdle(idleStartTime, 'idle_timeout_reached');
            console.log('⏹️ Session ended due to idle timeout - break period started');
        }
    }

    handleIdleResume() {
        console.log('🟢 User resumed activity from idle warning');
        this.isWarningShown = false;
        this.currentIdleState = 'active';
        this.lastActivityTime = new Date();

        // Update status tracker
        if (this.statusTracker) {
            this.statusTracker.updateStatus('active');
        }

        // Session was never stopped, so no need to resume
        if (this.timeLogger) {
            this.timeLogger.logActivity('idle_warning_dismissed', 'User dismissed idle warning and continued working');
        }
    }

    // Manual activity recording (for external triggers)
    recordActivity(activityType = 'manual') {
        console.log(`👆 Activity recorded: ${activityType}`);
        this.handleActivity();
    }

    // Force hide popup and resume (called from popup or external sources)
    forceResumeFromIdle() {
        console.log('🔄 Force resume from idle called');
        if (this.isWarningShown) {
            this.hideIdleWarning();
            this.handleIdleResume();
        }
        this.handleActivity();
    }

    // Public methods
    getCurrentIdleState() {
        return {
            state: this.currentIdleState,
            lastActivityTime: this.lastActivityTime,
            isWarningShown: this.isWarningShown,
            config: this.config
        };
    }

    async updateConfiguration(newConfig) {
        console.log('⚙️ Updating idle detector configuration:', newConfig);

        const oldEnabled = this.config.idleTimeoutEnabled;
        this.config = { ...this.config, ...newConfig };

        await this.store.set('idleDetectorConfig', this.config);

        // Restart detection if enabled state changed
        if (oldEnabled !== this.config.idleTimeoutEnabled) {
            this.destroy();
            this.setupIdleDetection();
        }

        console.log('✅ Idle detector configuration updated');
    }

    getConfiguration() {
        return { ...this.config };
    }

    // Manual activity recording (for external triggers)
    manualActivityTrigger(activityType = 'manual') {
        this.recordActivity(activityType);
    }

    async destroy() {
        console.log('🔄 Destroying idle detector...');

        if (this.idleCheckInterval) {
            clearInterval(this.idleCheckInterval);
            this.idleCheckInterval = null;
        }

        if (this.warningTimeout) {
            clearTimeout(this.warningTimeout);
            this.warningTimeout = null;
        }

        this.hideIdleWarning();
        this.idleWarningPopup.removeIpcHandlers();

        console.log('✅ Idle detector destroyed');
    }
}

module.exports = IdleDetector;
