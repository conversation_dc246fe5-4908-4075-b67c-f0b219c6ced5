{% extends 'admin/dashboard/base.html.twig' %}

{% block title %}Add Extra Pay{% endblock %}

{% block body %}
    {% include 'admin/partials/breadcrumbs.html.twig' with {
        breadcrumbs: [
            { 'label': 'Dashboard', 'url': url('admin_dashboard') },
            { 'label': 'Extra Pay', 'url': url('admin_extrapay') }
        ],
        active: mode == 'edit' ? 'Edit Extra Pay' : 'Add Extra Pay'
    } %}

    <div class="content">
        <div class="animated fadeIn">
            <div class="row">
                <div class="col-lg-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <strong class="card-title">
                                {{ mode == 'edit' ? 'Edit Extra Pay' : 'Add Extra Pay' }}
                            </strong>
                            <div class="d-flex align-items-center">
                                <a href="{{ path('admin_extrapay') }}" class="btn btn-sm btn-outline-secondary mr-2">
                                    <i class="fa fa-list mr-1"></i>Back to List
                                </a>
                            </div>
                        </div>
                        <div class="card-body">
                            {{ form_start(form, { 'attr': { 'class': 'needs-validation', 'novalidate': 'novalidate','data-turbo': 'false' } }) }}
                            <div class="form-section">
                                <h5 class="section-header">
                                    <i class="fa fa-calendar mr-2"></i>Employee Information
                                </h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            {{ form_label(form.employee, 'Employee', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text"><i class="fa fa-user"></i></span>
                                                </div>
                                                {{ form_widget(form.employee, { 'attr': { 'class': 'form-control' } }) }}
                                            </div>
                                            {{ form_errors(form.employee) }}
                                            <small class="form-text text-muted">Select the employee for whom you are adding extra pay</small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            {{ form_label(form.totalPay, 'Total Pay', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text"><i class="fa fa-rupee"></i></span>
                                                </div>
                                                {{ form_widget(form.totalPay, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter total pay' } }) }}
                                            </div>
                                            {{ form_errors(form.totalPay) }}
                                            <small class="form-text text-muted">Total amount paid to the employee</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="form-section">
                                <h5 class="section-header">
                                    <i class="fa fa-calendar mr-2"></i>Extra Pay Details
                                </h5>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            {{ form_label(form.month, 'Month', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text"><i class="fa fa-calendar"></i></span>
                                                </div>
                                                {{ form_widget(form.month, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter month (1-12)' } }) }}
                                            </div>
                                            {{ form_errors(form.month) }}
                                            <small class="form-text text-muted">Enter the month (1-12)</small>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            {{ form_label(form.year, 'Year', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text"><i class="fa fa-calendar"></i></span>
                                                </div>
                                                {{ form_widget(form.year, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter year' } }) }}
                                            </div>
                                            {{ form_errors(form.year) }}
                                            <small class="form-text text-muted">Enter the year (e.g., 2025)</small>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            {{ form_label(form.hours, 'Hours', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text"><i class="fa fa-clock-o"></i></span>
                                                </div>
                                                {{ form_widget(form.hours, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter hours' } }) }}
                                            </div>
                                            {{ form_errors(form.hours) }}
                                            <small class="form-text text-muted">Enter the number of extra hours worked</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            {{ form_label(form.bonus, 'Bonus', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text"><i class="fa fa-gift"></i></span>
                                                </div>
                                                {{ form_widget(form.bonus, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter bonus (if any)' } }) }}
                                            </div>
                                            {{ form_errors(form.bonus) }}
                                            <small class="form-text text-muted">Enter any bonus amount (optional)</small>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            {{ form_label(form.officeExpense, 'Office Expense', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text"><i class="fa fa-briefcase"></i></span>
                                                </div>
                                                {{ form_widget(form.officeExpense, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter office expense (if any)' } }) }}
                                            </div>
                                            {{ form_errors(form.officeExpense) }}
                                            <small class="form-text text-muted">Enter any office expense amount (optional)</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="d-flex justify-content-end mt-4">
                                <a href="{{ path('admin_extrapay') }}" class="btn btn-secondary mr-2">Cancel</a>
                                <button type="submit" class="btn btn-primary pl-3 pr-3">{{ mode == 'edit' ? 'Update Extra Pay' : 'Create Extra Pay' }}</button>
                            </div>
                            {{ form_end(form) }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="clearfix"></div>
    <link rel="stylesheet" href="{{ asset('assets/css/Error/error.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/AdminForm/Form.css') }}">
    <script src="{{ asset('assets/js/datatables.min.js') }}"></script>
    <script src="{{ asset('bundles/datatables/js/datatables.js') }}"></script>
    <script src="{{ asset('assets/js/admin/common.js') }}"></script>
    <script src="{{ asset('assets/js/admin/extrapay.js') }}"></script>
{% endblock %}