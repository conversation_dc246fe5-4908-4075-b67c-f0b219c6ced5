<?php

namespace App\Entity;

use App\Repository\ApplicationUsageRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: ApplicationUsageRepository::class)]
#[ORM\Table(name: 'application_usage')]
#[ORM\Index(columns: ['employee_id', 'usage_date'], name: 'idx_employee_usage_date')]
#[ORM\Index(columns: ['application_name'], name: 'idx_application_name')]
class ApplicationUsage
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\ManyToOne(targetEntity: MasterEmployee::class)]
    #[ORM\JoinColumn(nullable: false)]
    private ?MasterEmployee $employee = null;

    #[ORM\Column(type: Types::DATE_MUTABLE)]
    private ?\DateTimeInterface $usageDate = null;

    #[ORM\Column(length: 255)]
    private ?string $applicationName = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $applicationPath = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $windowTitle = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $startTime = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $endTime = null;

    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    private ?int $duration = null; // Duration in seconds

    #[ORM\Column(length: 50, nullable: true)]
    private ?string $category = null; // 'productive', 'neutral', 'distracting'

    #[ORM\Column(type: Types::INTEGER, options: ['default' => 1])]
    private int $switchCount = 1; // Number of times switched to this app

    #[ORM\Column(length: 100, nullable: true)]
    private ?string $deviceId = null;

    #[ORM\Column(type: Types::JSON, nullable: true)]
    private ?array $metadata = null;

    #[ORM\Column(type: Types::BOOLEAN, options: ['default' => false])]
    private bool $isSynced = false;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $createdAt = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $updatedAt = null;

    public function __construct()
    {
        $this->usageDate = new \DateTime();
        $this->startTime = new \DateTime();
        $this->createdAt = new \DateTime();
        $this->updatedAt = new \DateTime();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getEmployee(): ?MasterEmployee
    {
        return $this->employee;
    }

    public function setEmployee(?MasterEmployee $employee): static
    {
        $this->employee = $employee;
        return $this;
    }

    public function getUsageDate(): ?\DateTimeInterface
    {
        return $this->usageDate;
    }

    public function setUsageDate(\DateTimeInterface $usageDate): static
    {
        $this->usageDate = $usageDate;
        return $this;
    }

    public function getApplicationName(): ?string
    {
        return $this->applicationName;
    }

    public function setApplicationName(string $applicationName): static
    {
        $this->applicationName = $applicationName;
        return $this;
    }

    public function getApplicationPath(): ?string
    {
        return $this->applicationPath;
    }

    public function setApplicationPath(?string $applicationPath): static
    {
        $this->applicationPath = $applicationPath;
        return $this;
    }

    public function getWindowTitle(): ?string
    {
        return $this->windowTitle;
    }

    public function setWindowTitle(?string $windowTitle): static
    {
        $this->windowTitle = $windowTitle;
        return $this;
    }

    public function getStartTime(): ?\DateTimeInterface
    {
        return $this->startTime;
    }

    public function setStartTime(\DateTimeInterface $startTime): static
    {
        $this->startTime = $startTime;
        return $this;
    }

    public function getEndTime(): ?\DateTimeInterface
    {
        return $this->endTime;
    }

    public function setEndTime(?\DateTimeInterface $endTime): static
    {
        $this->endTime = $endTime;
        return $this;
    }

    public function getDuration(): ?int
    {
        return $this->duration;
    }

    public function setDuration(?int $duration): static
    {
        $this->duration = $duration;
        return $this;
    }

    public function getCategory(): ?string
    {
        return $this->category;
    }

    public function setCategory(?string $category): static
    {
        $this->category = $category;
        return $this;
    }

    public function getSwitchCount(): int
    {
        return $this->switchCount;
    }

    public function setSwitchCount(int $switchCount): static
    {
        $this->switchCount = $switchCount;
        return $this;
    }

    public function incrementSwitchCount(): static
    {
        $this->switchCount++;
        return $this;
    }

    public function getDeviceId(): ?string
    {
        return $this->deviceId;
    }

    public function setDeviceId(?string $deviceId): static
    {
        $this->deviceId = $deviceId;
        return $this;
    }

    public function getMetadata(): ?array
    {
        return $this->metadata;
    }

    public function setMetadata(?array $metadata): static
    {
        $this->metadata = $metadata;
        return $this;
    }

    public function isSynced(): bool
    {
        return $this->isSynced;
    }

    public function setIsSynced(bool $isSynced): static
    {
        $this->isSynced = $isSynced;
        return $this;
    }

    public function getCreatedAt(): ?\DateTimeInterface
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTimeInterface $createdAt): static
    {
        $this->createdAt = $createdAt;
        return $this;
    }

    public function getUpdatedAt(): ?\DateTimeInterface
    {
        return $this->updatedAt;
    }

    public function setUpdatedAt(\DateTimeInterface $updatedAt): static
    {
        $this->updatedAt = $updatedAt;
        return $this;
    }

    #[ORM\PreUpdate]
    public function setUpdatedAtValue(): void
    {
        $this->updatedAt = new \DateTime();
    }

    // Helper methods
    public function calculateDuration(): int
    {
        if ($this->startTime && $this->endTime) {
            return $this->endTime->getTimestamp() - $this->startTime->getTimestamp();
        }
        return 0;
    }

    public function isActive(): bool
    {
        return $this->endTime === null;
    }

    public function getDurationFormatted(): string
    {
        $duration = $this->duration ?? $this->calculateDuration();
        $hours = floor($duration / 3600);
        $minutes = floor(($duration % 3600) / 60);
        $seconds = $duration % 60;
        
        return sprintf('%02d:%02d:%02d', $hours, $minutes, $seconds);
    }

    public function getProductivityScore(): int
    {
        return match($this->category) {
            'productive' => 100,
            'neutral' => 50,
            'distracting' => 0,
            default => 50
        };
    }
}
