{% extends 'Front/dashboard/base.html.twig' %}

{% block title %}My Screenshots{% endblock %}

{% block body %}
<div class="content">
    <div class="animated fadeIn">
        <div class="row">
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-header">
                        <strong class="card-title">
                            <i class="fa fa-camera"></i> My Screenshots
                        </strong>
                    </div>
                    <div class="card-body">
                        <!-- Date Selection Form -->
                        <form method="GET" class="mb-4">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="date">Select Date</label>
                                        <input type="date" name="date" id="date" class="form-control" 
                                               value="{{ selectedDate }}" onchange="this.form.submit()">
                                    </div>
                                </div>
                                <div class="col-md-8">
                                    <div class="form-group">
                                        <label>&nbsp;</label>
                                        <div>
                                            {% if totalScreenshots > 0 %}
                                                <a href="{{ path('employee_screenshot_daily_report', {'date': selectedDate}) }}" 
                                                   class="btn btn-success">
                                                    <i class="fa fa-file-pdf-o"></i> Daily Report
                                                </a>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>

                        <!-- Summary Info -->
                        <div class="alert alert-info">
                            <strong>Date:</strong> {{ selectedDate|date('F j, Y') }} | 
                            <strong>Total Screenshots:</strong> {{ totalScreenshots }}
                            {% if totalScreenshots > 0 %}
                                | <strong>Active Hours:</strong> {{ screenshotsByHour|length }}
                            {% endif %}
                        </div>

                        {% if totalScreenshots > 0 %}
                            <!-- Activity Timeline -->
                            <div class="activity-timeline">
                                {% for hour, hourScreenshots in screenshotsByHour %}
                                    <div class="hour-section">
                                        <div class="hour-header">
                                            <h6>
                                                <i class="fa fa-clock-o"></i> 
                                                {{ hour }} - {{ (hour|split(':')|first + 1) ~ ':00' }}
                                                <span class="badge badge-primary">{{ hourScreenshots|length }} screenshots</span>
                                            </h6>
                                        </div>
                                        <div class="hour-screenshots">
                                            <div class="row">
                                                {% for screenshot in hourScreenshots %}
                                                    <div class="col-lg-2 col-md-3 col-sm-4 col-6 mb-3">
                                                        <div class="screenshot-card">
                                                            <div class="screenshot-preview">
                                                                {% if screenshot.fileName %}
                                                                    {% set imagePath = screenshot.filePath %}
                                                                    {% if imagePath starts with '/uploads/' %}
                                                                        <img src="{{ asset(imagePath) }}" 
                                                                             alt="Screenshot" class="img-fluid screenshot-thumb"
                                                                             onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                                                                        <div class="screenshot-placeholder" style="display: none;">
                                                                            <i class="fa fa-camera fa-2x text-primary"></i>
                                                                            <p class="text-muted mt-1">Processing...</p>
                                                                        </div>
                                                                    {% else %}
                                                                        <div class="screenshot-placeholder">
                                                                            <i class="fa fa-camera fa-2x text-primary"></i>
                                                                            <p class="text-muted mt-1">Processing...</p>
                                                                        </div>
                                                                    {% endif %}
                                                                {% else %}
                                                                    <div class="no-image">
                                                                        <i class="fa fa-image fa-2x text-muted"></i>
                                                                        <p class="text-muted">No Image</p>
                                                                    </div>
                                                                {% endif %}
                                                            </div>
                                                            <div class="screenshot-info">
                                                                <div class="screenshot-time">
                                                                    {{ screenshot.capturedAt|date('H:i:s') }}
                                                                </div>
                                                                {% if screenshot.activeApplication and screenshot.activeApplication != 'Unknown Application' %}
                                                                    <div class="screenshot-app">
                                                                        <small>{{ screenshot.activeApplication|slice(0, 15) }}{% if screenshot.activeApplication|length > 15 %}...{% endif %}</small>
                                                                    </div>
                                                                {% endif %}
                                                                <div class="mt-2">
                                                                    <a href="{{ path('employee_screenshot_view', {'id': screenshot.id}) }}" 
                                                                       class="btn btn-sm btn-primary">
                                                                        <i class="fa fa-eye"></i> View
                                                                    </a>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                {% endfor %}
                                            </div>
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>

                            <!-- Quick Stats -->
                            <div class="row mt-4">
                                <div class="col-md-3">
                                    <div class="stat-box">
                                        <div class="stat-number">{{ totalScreenshots }}</div>
                                        <div class="stat-label">Total Screenshots</div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="stat-box">
                                        <div class="stat-number">{{ screenshotsByHour|length }}</div>
                                        <div class="stat-label">Active Hours</div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="stat-box">
                                        <div class="stat-number">{{ (totalScreenshots / screenshotsByHour|length)|round(1) }}</div>
                                        <div class="stat-label">Avg per Hour</div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="stat-box">
                                        <div class="stat-number">
                                            {% set firstScreenshot = screenshots|first %}
                                            {% set lastScreenshot = screenshots|last %}
                                            {% if firstScreenshot and lastScreenshot %}
                                                {{ ((lastScreenshot.capturedAt.timestamp - firstScreenshot.capturedAt.timestamp) / 3600)|round(1) }}h
                                            {% else %}
                                                0h
                                            {% endif %}
                                        </div>
                                        <div class="stat-label">Work Duration</div>
                                    </div>
                                </div>
                            </div>
                        {% else %}
                            <div class="alert alert-warning">
                                <i class="fa fa-exclamation-triangle"></i>
                                No screenshots found for {{ selectedDate|date('F j, Y') }}.
                                {% if selectedDate == date('Y-m-d') %}
                                    Make sure the desktop application is running and taking screenshots.
                                {% else %}
                                    Try selecting a different date.
                                {% endif %}
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.hour-section {
    margin-bottom: 25px;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    overflow: hidden;
}

.hour-header {
    background-color: #f8f9fa;
    padding: 10px 15px;
    border-bottom: 1px solid #dee2e6;
}

.hour-header h6 {
    margin: 0;
    color: #495057;
}

.hour-screenshots {
    padding: 15px;
}

.screenshot-card {
    text-align: center;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 10px;
    transition: transform 0.2s;
}

.screenshot-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.screenshot-preview {
    height: 100px;
    background-color: #f8f9fa;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 8px;
}

.screenshot-thumb {
    max-height: 100%;
    max-width: 100%;
    border-radius: 4px;
}

.screenshot-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
}

.no-image {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
}

.screenshot-time {
    font-size: 0.9em;
    font-weight: bold;
    color: #495057;
}

.screenshot-app {
    font-size: 0.75em;
    color: #6c757d;
    margin-top: 2px;
}

.stat-box {
    text-align: center;
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 15px;
}

.stat-number {
    font-size: 1.8em;
    font-weight: bold;
    color: #007bff;
}

.stat-label {
    color: #6c757d;
    font-size: 0.9em;
}
</style>
{% endblock %}
