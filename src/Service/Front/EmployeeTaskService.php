<?php

namespace App\Service\Front;

use App\Entity\MasterEmployee;
use App\Entity\Task;
use App\Entity\TaskAssignment;
use App\Entity\TaskAssignmentStatusHistory;
use App\Entity\TaskAssignmentTimeLog;
use App\Entity\TaskStatusHistory;
use App\Entity\TaskTimeLog;
use App\Helper\ModalboxHelper;
use Doctrine\ORM\EntityManagerInterface;
use Omines\DataTablesBundle\Adapter\ArrayAdapter;
use Omines\DataTablesBundle\Column\DateTimeColumn;
use Omines\DataTablesBundle\Column\TextColumn;
use Omines\DataTablesBundle\DataTable;
use Omines\DataTablesBundle\DataTableFactory;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Request;
use App\Traits\ExceptionLoggerTrait;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Security\Csrf\CsrfTokenManagerInterface;
use App\Service\TimezoneService;

class EmployeeTaskService
{
    use ExceptionLoggerTrait;

    public function __construct(
        private readonly DataTableFactory $dataTableFactory,
        private readonly RouterInterface $router,
        ParameterBagInterface $parameterBag,
        private readonly CsrfTokenManagerInterface $csrfTokenManager,
        private readonly ModalboxHelper $modalboxHelper,
        private readonly EntityManagerInterface $entityManager,
        private readonly TimezoneService $timezoneService
    ) {
        $this->initializeLogger($parameterBag, 'EmployeeTaskService');
    }

    public function createEmployeeTaskTable(Request $request, MasterEmployee $employee): DataTable
    {
        $dataTable = $this->dataTableFactory->create();

        foreach ($this->getColumns() as $key => $column) {
            $dataTable->add($key, $column['className'], array_filter($column, fn($k) => $k !== 'className', ARRAY_FILTER_USE_KEY));
        }

        $data = $this->getCombinedTaskData($employee, false);

        return $dataTable->createAdapter(ArrayAdapter::class, $data)
            ->addOrderBy('assignedAt', 'DESC')
            ->handleRequest($request);
    }

    public function createEmployeeTaskHistoryTable(Request $request, MasterEmployee $employee): DataTable
    {
        $dataTable = $this->dataTableFactory->create();

        foreach ($this->getColumns() as $key => $column) {
            $dataTable->add($key, $column['className'], array_filter($column, fn($k) => $k !== 'className', ARRAY_FILTER_USE_KEY));
        }

        $data = $this->getCombinedTaskData($employee, true);

        return $dataTable->createAdapter(ArrayAdapter::class, $data)
            ->addOrderBy('assignedAt', 'DESC')
            ->handleRequest($request);
    }

    private function getCombinedTaskData(MasterEmployee $employee, bool $completed): array
    {
        $taskStatus = $completed ? 'COMPLETED' : null;

        $tasks = $this->entityManager->getRepository(Task::class)->findBy(
            ['assignedTo' => $employee] + ($completed ? ['status' => 'COMPLETED'] : [])
        );

        $taskAssignments = $this->entityManager->getRepository(TaskAssignment::class)->findBy(
            ['assignedTo' => $employee] + ($completed ? ['status' => 'COMPLETED'] : [])
        );

        $data = [];

        foreach ($tasks as $task) {
            if (!$completed && $task->getStatus() === 'COMPLETED') {
                continue;
            }

            $data[] = $this->formatTaskData($task);
        }

        foreach ($taskAssignments as $taskAssignment) {
            if (!$completed && $taskAssignment->getStatus() === 'COMPLETED') {
                continue;
            }

            $data[] = $this->formatTaskAssignmentData($taskAssignment);
        }

        usort($data, fn($a, $b) => $this->compareDates($a['assignedAt'], $b['assignedAt']));

        return $data;
    }

    private function formatTaskData(Task $task): array
    {
        return [
            'id' => 'task_' . $task->getId(),
            'type' => 'Task',
            'entity' => $task,
            'name' => $task->getProject()?->getName() ?? '-',
            'title' => $task->getTitle() ?? '-',
            'status' => $task->getStatus(),
            'priority' => $task->getPriority(),
            'assignedBy' => $task->getAssignedByAdmin()?->getUsername() ?? $task->getAssignedBy()?->getName() ?? '-',
            'estimatedHours' => $task->getEstimatedHours(),
            'assignedAt' => $task->getAssignedAt()
                ? $this->timezoneService->formatDateTime($task->getAssignedAt(), 'd-m-Y') : '-',
        ];
    }

    private function formatTaskAssignmentData(TaskAssignment $taskAssignment): array
    {
        return [
            'id' => 'taskAssignment_' . $taskAssignment->getId(),
            'type' => 'Task Assignment',
            'entity' => $taskAssignment,
            'name' => $taskAssignment->getTask()->getProject()?->getName() ?? '-',
            'title' => $taskAssignment->getTask()->getTitle() ?? '-',
            'status' => $taskAssignment->getStatus(),
            'priority' => $taskAssignment->getPriority(),
            'assignedBy' => $taskAssignment->getAssignedBy()?->getName() ?? '-',
            'estimatedHours' => null,
            'assignedAt' => $taskAssignment->getCreatedAt()
                ? $this->timezoneService->formatDateTime($taskAssignment->getCreatedAt(), 'd-m-Y') : '-',
        ];
    }

    private function compareDates(string $dateA, string $dateB): int
    {
        $timeA = $dateA !== '-' ? strtotime($dateA) : 0;
        $timeB = $dateB !== '-' ? strtotime($dateB) : 0;

        return $timeB <=> $timeA;
    }

    private function getColumns(): array
    {
        return [
//            'type' => [
//                'label' => 'Type',
//                'className' => TextColumn::class,
//                'render' => fn($value, $context) => $context['type'],
//            ],
            'name' => [
                'label' => 'Project Name',
                'className' => TextColumn::class,
                'render' => fn($value, $context) => $context['name'],
                'orderable' => true,
            ],
            'title' => [
                'label' => 'Title',
                'className' => TextColumn::class,
                'render' => function ($value, $context) {
                    $title = $context['title'];
                    $maxLength = 50;
                    if (strlen($title) > $maxLength) {
                        $truncated = substr($title, 0, $maxLength) . '...';
                        return sprintf('<span title="%s">%s</span>', htmlspecialchars($title), htmlspecialchars($truncated));
                    }
                    return htmlspecialchars($title);
                },
            ],
            'status' => [
                'label' => 'Status',
                'className' => TextColumn::class,
                'render' => fn($value, $context) => $this->renderStatusBadge($context['status']),
                'orderable' => true,
            ],
            'priority' => [
                'label' => 'Priority',
                'className' => TextColumn::class,
                'render' => fn($value, $context) => $this->renderPriorityBadge($context['priority']),
            ],
            'assignedBy' => [
                'label' => 'Assigned By',
                'className' => TextColumn::class,
                'render' => fn($value, $context) => $context['assignedBy'],
            ],
            'estimatedHours' => [
                'label' => 'Estimation Hours',
                'className' => TextColumn::class,
                'render' => function ($value, $context) {
                    if ($context['type'] === 'Task Assignment') {
                        return '-';
                    }
                    if ($context['estimatedHours'] === null) {
                        return '-';
                    }
                    $formattedValue = number_format($context['estimatedHours'], 2, '.', '');
                    $parts = explode('.', $formattedValue);
                    $hours = (int) $parts[0];
                    $minutes = isset($parts[1]) ? (int) $parts[1] : 0;
                    $formatted = '';
                    if ($hours > 0) {
                        $formatted .= $hours . ' hr';
                    }
                    if ($minutes > 0) {
                        $formatted .= ($hours > 0 ? ' ' : '') . $minutes . ' min';
                    }
                    return $formatted ?: '0 hr';
                },
            ],
            'assignedAt' => [
                'label' => 'Assigned On',
                'className' => DateTimeColumn::class,
                'format' => 'd-m-Y',
                'searchable' => false,
                'render' => fn($value, $context) => $context['assignedAt'],
            ],
            'actions' => [
                'label' => 'Actions',
                'className' => TextColumn::class,
                'orderable' => false,
                'render' => fn($value, $context) => $context['entity'] instanceof Task
                    ? $this->renderActionColumn($context['entity'])
                    : $this->renderTaskAssignmentActionColumn($context['entity']),
            ],
        ];
    }

    private function renderStatusBadge(string $status): string
    {
        return match ($status) {
            'NEW' => '<span class="badge btn-primary">New</span>',
            'IN_PROGRESS' => '<span class="badge btn-primary">In Progress</span>',
            'COMPLETED' => '<span class="badge bg-success">Completed</span>',
            'REASSIGNED' => '<span class="badge bg-danger">Reassigned</span>',
            default => '<span class="badge bg-secondary">Unknown</span>',
        };
    }

    private function renderPriorityBadge(string $priority): string
    {
        $colors = [
            'LOW' => 'badge btn-primary',
            'MEDIUM' => 'badge btn-primary',
            'HIGH' => 'badge bg-danger',
        ];
        return sprintf('<span class="%s">%s</span>', $colors[$priority] ?? 'badge bg-secondary', $priority);
    }
    private function renderActionColumn(Task $task): string
    {
        $entityId = $task->getId();
        $viewModalId = 'viewModal_task_' . $entityId;
        $timeLogsModalId = 'timeLogsModal_task_' . $entityId;
        $updateStatusUrl = $this->router->generate('employee_task_update_status', ['id' => $entityId]);
        $progressUrl = $this->router->generate('employee_task_progress', ['task_id' => $entityId]);
        $taskAssignment = $this->router->generate('employee_task_assignment', ['task_id' => $entityId]);
        $startTimeUrl = $this->router->generate('employee_task_time_tracking_start', ['id' => $entityId]);
        $stopTimeUrl = $this->router->generate('employee_task_time_tracking_stop', ['id' => $entityId]);
        $statusUrl = $this->router->generate('employee_task_time_tracking_status', ['id' => $entityId]);
        $addBugUrl = $this->router->generate('bug_index', ['task' => $entityId]);
        $csrfToken = $this->csrfTokenManager->getToken('time_tracking_' . $entityId)->getValue();
        $statusHistories = $task->getStatusHistories()->toArray();
        $statusHistoryTableRows = $this->generateStatusHistoryTableRows($statusHistories);

        $statusForm = sprintf(
            '<form id="statusForm_%s" action="%s" method="POST" class="mt-3">
            <input type="hidden" name="_csrf_token" value="%s">
            <div class="form-group">
               <label for="status_%s" class="form-label fw-bold"><i class="bs-fa fa fa-tasks mr-1"></i> Update Status</label>
                <select name="status" id="status_%s" class="form-control">
                    <option value="NEW" %s>New</option>
                    <option value="IN_PROGRESS" %s>In Progress</option>
                    <option value="COMPLETED" %s>Completed</option>
                </select>
            </div>
            <button type="submit" class="btn btn-primary mt-2">Update Status</button>
        </form>',
            $entityId,
            $updateStatusUrl,
            $this->csrfTokenManager->getToken('update_status_' . $entityId)->getValue(),
            $entityId,
            $entityId,
            $task->getStatus() === 'NEW' ? 'selected' : '',
            $task->getStatus() === 'IN_PROGRESS' ? 'selected' : '',
            $task->getStatus() === 'COMPLETED' ? 'selected' : ''
        );

        $timeTrackingButtons = '';
        if ($task->getStatus() !== 'COMPLETED') {
            $timeTrackingButtons = sprintf(
                '<button type="button" class="bs-btn btn btn-success btn-sm start-time-btn" style="margin: 2px;" data-task-id="%s" title="Start Tracking" data-url="%s" data-csrf="%s">
                <i class="bs-fa fa fa-play"></i>
            </button>
            <button type="button" class="bs-btn btn btn-danger btn-sm stop-time-btn" style="margin: 2px; display: none;" data-task-id="%s" title="Stop Time Tracking" data-url="%s" data-csrf="%s">
                <i class="bs-fa fa fa-stop"></i>
            </button>',
                $entityId,
                $startTimeUrl,
                $csrfToken,
                $entityId,
                $stopTimeUrl,
                $csrfToken
            );
        }
        $timeLogs = $this->entityManager->getRepository(TaskTimeLog::class)->findBy(
            ['task' => $task],
            ['createdAt' => 'DESC']
        );

        $timeLogsTableRows = $this->generateTimeLogsTableRows($timeLogs);

        $totalHours = 0.0;
        foreach ($timeLogs as $log) {
            if ($log->getDuration()) {
                $totalHours += $log->getDuration();
            }
        }

        $hours = floor($totalHours);
        $minutes = round(($totalHours - $hours) * 60);
        if ($minutes == 60) {
            $hours += 1;
            $minutes = 0;
        }
        $totalDurationFormatted = '';
        if ($hours > 0) {
            $totalDurationFormatted .= $hours . ' hr';
        }
        if ($minutes > 0) {
            $totalDurationFormatted .= ($hours > 0 ? ' ' : '') . $minutes . ' min';
        }
        if ($totalDurationFormatted === '') {
            $totalDurationFormatted = '0 min';
        }

        $timeLogsModal = $this->modalboxHelper->renderModal(
            $timeLogsModalId,
            'Time Logs for Task: ' . htmlspecialchars($task->getTitle() ?? '-'),
            sprintf(
                '<table class="table table-bordered table-striped">
                <thead>
                    <tr>
                       <th><i class="bs-fa fa fa-calendar mr-1"></i> Start Time</th>
                            <th><i class="bs-fa fa fa-calendar-check-o mr-1"></i> End Time</th>
                            <th><i class="bs-fa fa fa-hourglass-half mr-1"></i> Duration (Minutes)</th>
                    </tr>
                </thead>
                <tbody>%s</tbody>
                <tfoot>
                    <tr>
                        <td colspan="2"><strong>Total:</strong></td>
                        <td><strong>%s</strong></td>
                    </tr>
                </tfoot>
            </table>',
                $timeLogsTableRows,
                $totalDurationFormatted
            ),
            '<button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>'
        );

        $timeLogsButton = sprintf(
            '<button type="button" class="bs-btn btn btn-primary btn-sm" style="margin: 2px;" data-toggle="modal" data-target="#%s" title="View Time Logs">
            <i class="bs-fa fa fa-clock-o"></i>
        </button>',
            $timeLogsModalId
        );

        $assignedEmployee = $task->getAssignedTo();
        $isQaDepartment = false;

        if ($assignedEmployee) {
            foreach ($assignedEmployee->getDepartments() as $department) {
                if (strtolower($department->getDepName()) === 'qa') {
                    $isQaDepartment = true;
                    break;
                }
            }
        }
        $addBugButton = '';
        if ($isQaDepartment && !$task->getAssignedByAdmin() ) {
            $addBugButton = sprintf(
                '<a href="%s" class="bs-btn btn btn-primary btn-sm" style="margin: 2px;" title="Add Bug">
                <i class="bs-fa fa fa-bug"></i>
            </a>',
                $addBugUrl
            );
        }
        $ajaxScript = sprintf(
            '<script>
    function initializeTimeTracking(taskId, startUrl, stopUrl, statusUrl, csrfToken) {
        const startBtn = document.querySelector(`.start-time-btn[data-task-id="${taskId}"]`);
        const stopBtn = document.querySelector(`.stop-time-btn[data-task-id="${taskId}"]`);

        fetch("%s", { method: "GET" })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.isTracking) {
                    startBtn.style.display = "none";
                    stopBtn.style.display = "inline-block";
                } else {
                    startBtn.style.display = "inline-block";
                    stopBtn.style.display = "none";
                }
            })
            .catch(error => console.error("Error checking tracking status:", error));

        startBtn.addEventListener("click", () => {
      
            const activeStopButtons = document.querySelectorAll(".stop-time-btn:not([style*=\'display: none\'])");
            if (activeStopButtons.length > 0) {
                const activeTaskId = activeStopButtons[0].dataset.taskId;
                const activeStopUrl = activeStopButtons[0].dataset.url;
                const activeCsrfToken = activeStopButtons[0].dataset.csrf;

                fetch(activeStopUrl, {
                    method: "POST",
                    headers: { "Content-Type": "application/x-www-form-urlencoded" },
                    body: `_csrf_token=${encodeURIComponent(activeCsrfToken)}`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const activeStartBtn = document.querySelector(`.start-time-btn[data-task-id="${activeTaskId}"]`);
                        const activeStopBtn = document.querySelector(`.stop-time-btn[data-task-id="${activeTaskId}"]`);
                        activeStartBtn.style.display = "inline-block";
                        activeStopBtn.style.display = "none";
                        startNewTimer();
                    } else {
                        alert("Error stopping active timer: " + (data.error || "Failed to stop time tracking."));
                    }
                })
                .catch(error => {
                    alert("Error stopping active timer: " + error.message);
                });
            } else {
                startNewTimer();
            }

            function startNewTimer() {
                fetch(startUrl, {
                    method: "POST",
                    headers: { "Content-Type": "application/x-www-form-urlencoded" },
                    body: `_csrf_token=${encodeURIComponent(csrfToken)}`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        startBtn.style.display = "none";
                        stopBtn.style.display = "inline-block";
                        if (recordsTable) {
                            recordsTable.then(dt => dt.ajax.reload(null, false));
                        }
                    } else {
                        alert("Error: " + (data.error || "Failed to start time tracking."));
                    }
                })
                .catch(error => alert("Error: " + error.message));
            }
        });
        stopBtn.addEventListener("click", () => {
            fetch(stopUrl, {
                method: "POST",
                headers: { "Content-Type": "application/x-www-form-urlencoded" },
                body: `_csrf_token=${encodeURIComponent(csrfToken)}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    startBtn.style.display = "inline-block";
                    stopBtn.style.display = "none";
                    if (recordsTable) {
                        recordsTable.then(dt => dt.ajax.reload(null, false));
                    }
                } else {
                    alert("Error: " + (data.error || "Failed to stop time tracking."));
                }
            })
            .catch(error => alert("Error: " + error.message));
        });
    }

    document.getElementById("statusForm_%s").addEventListener("submit", function(e) {
        e.preventDefault();
        const form = this;
        const formData = new FormData(form);

        fetch(form.action, {
            method: "POST",
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                closeModal(document.getElementById("%s"));
                if (recordsTable) {
                    recordsTable.then(dt => dt.ajax.reload(null, false));
                }
            } else {
                alert("Error: " + (data.error || "Failed to update status."));
            }
        })
        .catch(error => {
            alert("Error: " + error.message);
        });
    });

    function closeModal(modal) {
        if (modal) {
            if (typeof $ !== "undefined" && $.fn.modal) {
                $(modal).modal("hide");
            } else {
                modal.classList.remove("show");
                modal.style.display = "none";
            }

            document.body.classList.remove("modal-open");
            const backdrop = document.querySelector(".modal-backdrop");
            if (backdrop) {
                backdrop.remove();
            }
        }
    }
%s
    initializeTimeTracking("%s", "%s", "%s", "%s", "%s");
</script>',
            $statusUrl,
            $entityId,
            $viewModalId,
            $entityId,
            $entityId,
            $startTimeUrl,
            $stopTimeUrl,
            $statusUrl,
            $csrfToken
        );
        $taskDescription = $task->getDescription() ?? 'No description available.';

        $descriptionStyles = '';

        $viewModalBody = sprintf(
            '%s
        <div class="task-description-container">%s</div>
        <div class="status-form-container">%s</div>
        <div class="task-section">
            <h5 class="font-weight-bold m-3">Status Change History</h5>
            <table class="table table-bordered table-striped status-history-table">
                <thead>
                    <tr>
                        <th>Changed By</th>
                        <th>Old Status</th>
                        <th>New Status</th>
                        <th>Changed At</th>
                    </tr>
                </thead>
                <tbody>%s</tbody>
            </table>
        </div>',
            $descriptionStyles,
            $taskDescription,
            $statusForm,
            $statusHistoryTableRows
        );

        $viewFooterButtons = '<button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>';

        $viewModal = $this->modalboxHelper->renderModal(
            $viewModalId,
            'Task Description',
            $viewModalBody,
            $viewFooterButtons
        );

        return sprintf(
            '<div class="d-inline-flex gap-2">
            <button type="button" class="bs-btn btn btn-primary btn-sm" style="margin: 2px;" data-toggle="modal" data-target="#%s" title="Task Description">
                <i class="bs-fa fa fa-eye"></i>
            </button>
            <a href="%s" class="bs-btn btn btn-primary btn-sm" style="margin: 2px;" title="Add Progress">
                <i class="bs-fa fa fa-plus"></i>
            </a>
              <a href="%s" class="bs-btn btn btn-primary btn-sm" style="margin: 2px;" title="Assign Task">
                <i class="bs-fa fa fa-share"></i>
            </a>
            %s
            %s
            %s
        </div>%s%s%s',
            $viewModalId,
            $progressUrl,
            $taskAssignment,
            $timeLogsButton,
            $addBugButton,
            $timeTrackingButtons,
            $viewModal,
            $timeLogsModal,
            $ajaxScript
        );
    }

    private function renderTaskAssignmentActionColumn(TaskAssignment $taskAssignment): string
    {
        $entityId = $taskAssignment->getId();
        $viewModalId = 'viewModal_taskAssignment_' . $entityId;
        $timeLogsModalId = 'timeLogsModal_taskAssignment_' . $entityId;
        $updateStatusUrl = $this->router->generate('employee_task_assignment_update_status', ['id' => $entityId]);
        $startTimeUrl = $this->router->generate('employee_task_assignment_time_tracking_start', ['id' => $entityId]);
        $stopTimeUrl = $this->router->generate('employee_task_assignment_time_tracking_stop', ['id' => $entityId]);
        $statusUrl = $this->router->generate('employee_task_assignment_time_tracking_status', ['id' => $entityId]);
        $addBugUrl = $this->router->generate('bug_index', ['taskAssignmentId' => $entityId]);
        $csrfToken = $this->csrfTokenManager->getToken('time_tracking_assignment_' . $entityId)->getValue();
        $statusHistories = $taskAssignment->getStatusHistories()->toArray();
        $statusHistoryTableRows = $this->generateAssignmentStatusHistoryTableRows($statusHistories);

        $statusForm = sprintf(
            '<form id="statusForm_%s" action="%s" method="POST" class="mt-3">
            <input type="hidden" name="_csrf_token" value="%s">
            <div class="form-group">
               <label for="status_%s" class="form-label fw-bold"><i class="bs-fa fa fa-tasks mr-1"></i> Update Status</label>
                <select name="status" id="status_%s" class="form-control">
                    <option value="NEW" %s>New</option>
                    <option value="IN_PROGRESS" %s>In Progress</option>
                    <option value="COMPLETED" %s>Completed</option>
                </select>
            </div>
            <button type="submit" class="btn btn-primary mt-2">Update Status</button>
        </form>',
            $entityId,
            $updateStatusUrl,
            $this->csrfTokenManager->getToken('update_status_assignment_' . $entityId)->getValue(),
            $entityId,
            $entityId,
            $taskAssignment->getStatus() === 'NEW' ? 'selected' : '',
            $taskAssignment->getStatus() === 'IN_PROGRESS' ? 'selected' : '',
            $taskAssignment->getStatus() === 'COMPLETED' ? 'selected' : ''
        );

        $timeTrackingButtons = '';
        if ($taskAssignment->getStatus() !== 'COMPLETED') {
            $timeTrackingButtons = sprintf(
                '<button type="button" class="bs-btn btn btn-success btn-sm start-time-btn" style="margin: 2px;" data-task-id="%s" title="Start Time Tracking" data-url="%s" data-csrf="%s">
                <i class="bs-fa fa fa-play"></i>
            </button>
            <button type="button" class="bs-btn btn btn-danger btn-sm stop-time-btn" style="margin: 2px; display: none;" data-task-id="%s" title="Stop Time Tracking" data-url="%s" data-csrf="%s">
                <i class="bs-fa fa fa-stop"></i>
            </button>',
                $entityId,
                $startTimeUrl,
                $csrfToken,
                $entityId,
                $stopTimeUrl,
                $csrfToken
            );
        }
        $timeLogs = $this->entityManager->getRepository(TaskAssignmentTimeLog::class)->findBy(
            ['taskAssignment' => $taskAssignment],
            ['createdAt' => 'DESC']
        );

        $timeLogsTableRows = $this->generateAssignmentTimeLogsTableRows($timeLogs);

        $totalHours = 0.0;
        foreach ($timeLogs as $log) {
            if ($log->getDuration()) {
                $totalHours += $log->getDuration();
            }
        }

        $hours = floor($totalHours);
        $minutes = round(($totalHours - $hours) * 60);
        if ($minutes == 60) {
            $hours += 1;
            $minutes = 0;
        }
        $totalDurationFormatted = '';
        if ($hours > 0) {
            $totalDurationFormatted .= $hours . ' hr';
        }
        if ($minutes > 0) {
            $totalDurationFormatted .= ($hours > 0 ? ' ' : '') . $minutes . ' min';
        }
        if ($totalDurationFormatted === '') {
            $totalDurationFormatted = '0 min';
        }
//Task Assignment
        $timeLogsModal = $this->modalboxHelper->renderModal(
            $timeLogsModalId,
            'Time Logs for Task : ' . $taskAssignment->getTask()->getTitle() ?? 'N/A',
            sprintf(
                '<table class="table table-bordered table-striped">
                <thead>
                    <tr>
                       <th><i class="bs-fa fa fa-calendar mr-1"></i> Start Time</th>
                            <th><i class="bs-fa fa fa-calendar-check-o mr-1"></i> End Time</th>
                            <th><i class="bs-fa fa fa-hourglass-half mr-1"></i> Duration (Minutes)</th>
                    </tr>
                </thead>
                <tbody>%s</tbody>
                <tfoot>
                    <tr>
                        <td colspan="2"><strong>Total:</strong></td>
                        <td><strong>%s</strong></td>
                    </tr>
                </tfoot>
            </table>',
                $timeLogsTableRows,
                $totalDurationFormatted
            ),
            '<button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>'
        );

        $timeLogsButton = sprintf(
            '<button type="button" class="bs-btn btn btn-primary btn-sm" style="margin: 2px;" data-toggle="modal" data-target="#%s" title="View Time Logs">
            <i class="bs-fa fa fa-clock-o"></i>
        </button>',
            $timeLogsModalId
        );

        $assignedEmployee = $taskAssignment->getAssignedTo();
        $isQaDepartment = false;

        if ($assignedEmployee) {
            foreach ($assignedEmployee->getDepartments() as $department) {
                if (strtolower($department->getDepName()) === 'qa') {
                    $isQaDepartment = true;
                    break;
                }
            }
        }
        $addBugButton = '';
        if ($isQaDepartment || $taskAssignment->getCategory() === 'TESTING') {
            $addBugButton = sprintf(
                '<a href="%s" class="bs-btn btn btn-primary btn-sm" style="margin: 2px;" title="Add Bug">
                <i class="bs-fa fa fa-bug"></i>
            </a>',
                $addBugUrl
            );
        }
        $ajaxScript = sprintf(
            '<script>
    function initializeTimeTracking(taskId, startUrl, stopUrl, statusUrl, csrfToken) {
        const startBtn = document.querySelector(`.start-time-btn[data-task-id="${taskId}"]`);
        const stopBtn = document.querySelector(`.stop-time-btn[data-task-id="${taskId}"]`);

        fetch("%s", { method: "GET" })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.isTracking) {
                    startBtn.style.display = "none";
                    stopBtn.style.display = "inline-block";
                } else {
                    startBtn.style.display = "inline-block";
                    stopBtn.style.display = "none";
                }
            })
            .catch(error => console.error("Error checking tracking status:", error));

        startBtn.addEventListener("click", () => {
            const activeStopButtons = document.querySelectorAll(".stop-time-btn:not([style*=\'display: none\'])");
            if (activeStopButtons.length > 0) {
                const activeTaskId = activeStopButtons[0].dataset.taskId;
                const activeStopUrl = activeStopButtons[0].dataset.url;
                const activeCsrfToken = activeStopButtons[0].dataset.csrf;

                fetch(activeStopUrl, {
                    method: "POST",
                    headers: { "Content-Type": "application/x-www-form-urlencoded" },
                    body: `_csrf_token=${encodeURIComponent(activeCsrfToken)}`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const activeStartBtn = document.querySelector(`.start-time-btn[data-task-id="${activeTaskId}"]`);
                        const activeStopBtn = document.querySelector(`.stop-time-btn[data-task-id="${activeTaskId}"]`);
                        activeStartBtn.style.display = "inline-block";
                        activeStopBtn.style.display = "none";
                        startNewTimer();
                    } else {
                        alert("Error stopping active timer: " + (data.error || "Failed to stop time tracking."));
                    }
                })
                .catch(error => {
                    alert("Error stopping active timer: " + error.message);
                });
            } else {
                startNewTimer();
            }

            function startNewTimer() {
                fetch(startUrl, {
                    method: "POST",
                    headers: { "Content-Type": "application/x-www-form-urlencoded" },
                    body: `_csrf_token=${encodeURIComponent(csrfToken)}`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        startBtn.style.display = "none";
                        stopBtn.style.display = "inline-block";
                        if (recordsTable) {
                            recordsTable.then(dt => dt.ajax.reload(null, false));
                        }
                    } else {
                        alert("Error: " + (data.error || "Failed to start time tracking."));
                    }
                })
                .catch(error => alert("Error: " + error.message));
            }
        });
        stopBtn.addEventListener("click", () => {
            fetch(stopUrl, {
                method: "POST",
                headers: { "Content-Type": "application/x-www-form-urlencoded" },
                body: `_csrf_token=${encodeURIComponent(csrfToken)}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    startBtn.style.display = "inline-block";
                    stopBtn.style.display = "none";
                    if (recordsTable) {
                        recordsTable.then(dt => dt.ajax.reload(null, false));
                    }
                } else {
                    alert("Error: " + (data.error || "Failed to stop time tracking."));
                }
            })
            .catch(error => alert("Error: " + error.message));
        });
    }

    document.getElementById("statusForm_%s").addEventListener("submit", function(e) {
        e.preventDefault();
        const form = this;
        const formData = new FormData(form);

        fetch(form.action, {
            method: "POST",
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                closeModal(document.getElementById("%s"));
                if (recordsTable) {
                    recordsTable.then(dt => dt.ajax.reload(null, false));
                }
            } else {
                alert("Error: " + (data.error || "Failed to update status."));
            }
        })
        .catch(error => {
            alert("Error: " + error.message);
        });
    });

    function closeModal(modal) {
        if (modal) {
            if (typeof $ !== "undefined" && $.fn.modal) {
                $(modal).modal("hide");
            } else {
                modal.classList.remove("show");
                modal.style.display = "none";
            }

            document.body.classList.remove("modal-open");
            const backdrop = document.querySelector(".modal-backdrop");
            if (backdrop) {
                backdrop.remove();
            }
        }
    }
%s
    initializeTimeTracking("%s", "%s", "%s", "%s", "%s");
</script>',
            $statusUrl,
            $entityId,
            $viewModalId,
            $entityId,
            $entityId,
            $startTimeUrl,
            $stopTimeUrl,
            $statusUrl,
            $csrfToken
        );
        $taskDescription = $taskAssignment->getDescription() ?? 'No description available.';

        $descriptionStyles = '';

        $viewModalBody = sprintf(
            '%s
        <div class="task-description-container">%s</div>
        <div class="status-form-container">%s</div>
        <div class="task-section">
            <h5 class="font-weight-bold m-3">Status Change History</h5>
            <table class="table table-bordered table-striped status-history-table">
                <thead>
                    <tr>
                        <th>Changed By</th>
                        <th>Old Status</th>
                        <th>New Status</th>
                        <th>Changed At</th>
                    </tr>
                </thead>
                <tbody>%s</tbody>
            </table>
        </div>',
            $descriptionStyles,
            $taskDescription,
            $statusForm,
            $statusHistoryTableRows
        );

        $viewFooterButtons = '<button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>';

        $viewModal = $this->modalboxHelper->renderModal(
            $viewModalId,
            'Task Description',
            $viewModalBody,
            $viewFooterButtons
        );

        return sprintf(
            '<div class="d-inline-flex gap-2">
            <button type="button" class="bs-btn btn btn-primary btn-sm" style="margin: 2px;" data-toggle="modal" data-target="#%s" title="Task Assignment Description">
                <i class="bs-fa fa fa-eye"></i>
            </button>
            %s
            %s
            %s
        </div>%s%s%s',
            $viewModalId,
            $timeLogsButton,
            $addBugButton,
            $timeTrackingButtons,
            $viewModal,
            $timeLogsModal,
            $ajaxScript
        );
    }

    private function generateTimeLogsTableRows(array $timeLogs): string
    {
        if (empty($timeLogs)) {
            return '<tr><td colspan="3">No time logs available</td></tr>';
        }

        $rows = array_map(function (TaskTimeLog $log) {
            $startTimeFormatted = $this->timezoneService->formatDateTime($log->getStartTime(), 'd-m-Y H:i:s');
            $startTimestamp = $log->getStartTime()->getTimestamp();

            if ($log->getEndTime()) {
                $endTimeFormatted = $this->timezoneService->formatDateTime($log->getEndTime(), 'd-m-Y H:i:s');
                $durationMinutes = number_format($log->getDuration() * 60, 1) . ' mins';
            } else {
                $endTimeFormatted = '⏳ Running...';
                $durationMinutes = "<span class='live-duration' data-start='$startTimestamp'>Calculating...</span>";
            }
            return "<tr>
                    <td>$startTimeFormatted</td>
                    <td>$endTimeFormatted</td>
                    <td>$durationMinutes</td>
                </tr>";
        }, $timeLogs);

        return implode('', $rows);
    }


    private function generateAssignmentTimeLogsTableRows(array $timeLogs): string
    {
        if (empty($timeLogs)) {
            return '<tr><td colspan="3">No time logs available</td></tr>';
        }

        $rows = array_map(function (TaskAssignmentTimeLog $log) {
            $startTimeFormatted = $this->timezoneService->formatDateTime($log->getStartTime(), 'd-m-Y H:i:s');
            $startTimestamp = $log->getStartTime()->getTimestamp();

            if ($log->getEndTime()) {
                $endTimeFormatted = $this->timezoneService->formatDateTime($log->getEndTime(), 'd-m-Y H:i:s');
                $durationMinutes = number_format($log->getDuration() * 60, 1) . ' mins';
            } else {
                $endTimeFormatted = '⏳ Running...';
                $durationMinutes = "<span class='live-duration' data-start='$startTimestamp'>Calculating...</span>";
            }
            return "<tr>
                    <td>$startTimeFormatted</td>
                    <td>$endTimeFormatted</td>
                    <td>$durationMinutes</td>
                </tr>";
        }, $timeLogs);

        return implode('', $rows);
    }

    private function generateStatusHistoryTableRows(array $statusHistories): string
    {
        if (empty($statusHistories)) {
            return '<tr><td colspan="4">No status history available</td></tr>';
        }

        $rows = array_map(function (TaskStatusHistory $history) {
            $changedBy = $history->getChangedByAdmin()?->getUsername() ?? $history->getChangedBy()?->getName() ?? 'Unknown';
            $changedAt = $this->timezoneService->formatDateTime($history->getChangedAt(), 'd-m-Y H:i:s');
            $oldStatus = htmlspecialchars($history->getOldStatus());
            $newStatus = htmlspecialchars($history->getNewStatus());

            return "<tr><td>$changedBy</td><td>$oldStatus</td><td>$newStatus</td><td>$changedAt</td></tr>";
        }, $statusHistories);

        return implode('', $rows);
    }

    private function generateAssignmentStatusHistoryTableRows(array $statusHistories): string
    {
        if (empty($statusHistories)) {
            return '<tr><td colspan="4">No status history available</td></tr>';
        }

        $rows = array_map(function (TaskAssignmentStatusHistory $history) {
            $changedBy = $history->getChangedBy()?->getName() ?? 'Unknown';
            $changedAt = $this->timezoneService->formatDateTime($history->getChangedAt(), 'd-m-Y H:i:s');
            $oldStatus = htmlspecialchars($history->getOldStatus());
            $newStatus = htmlspecialchars($history->getNewStatus());

            return "<tr><td>$changedBy</td><td>$oldStatus</td><td>$newStatus</td><td>$changedAt</td></tr>";
        }, $statusHistories);

        return implode('', $rows);
    }
}