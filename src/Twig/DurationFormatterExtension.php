<?php

namespace App\Twig;

use Twig\Extension\AbstractExtension;
use Twig\TwigFilter;

class DurationFormatterExtension extends AbstractExtension
{
    public function getFilters(): array
    {
        return [
            new TwigFilter('format_duration', [$this, 'formatDuration']),
        ];
    }

    public function formatDuration(?float $hours): string
    {
        if ($hours === null || $hours <= 0) {
            return 'N/A';
        }

        $hoursPart = floor($hours);
        $minutesPart = round(($hours - $hoursPart) * 60);
        if ($minutesPart == 60) {
            $hoursPart += 1;
            $minutesPart = 0;
        }

        $formatted = '';
        if ($hoursPart > 0) {
            $formatted .= $hoursPart . ' hr';
        }
        if ($minutesPart > 0) {
            $formatted .= ($hoursPart > 0 ? ' ' : '') . $minutesPart . ' min';
        }
        if ($formatted === '') {
            $formatted = '0 min';
        }

        return $formatted;
    }
}