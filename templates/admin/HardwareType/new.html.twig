{% extends 'admin/dashboard/base.html.twig' %}

{% block body %}
    {% include 'admin/partials/breadcrumbs.html.twig' with {
        title: 'Hardware Types',
        breadcrumbs: [
            { 'label': 'Dashboard', 'url': url('admin_dashboard') },
            { 'label': 'Hardware Types', 'url': url('hardware_type') }
        ],
        active: mode == 'edit' ? 'Edit Hardware Type' : 'Create Hardware Type'
    } %}

    <div class="content">
        <div class="animated fadeIn">
            <div class="row">
                <div class="col-lg-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <strong class="card-title">
                                {{mode == 'edit' ? 'Edit Hardware Type' : 'Create Hardware Type'}}
                            </strong>
                        </div>
                        <div class="card-body">
                            <div id="hardware-type-form">
                                {% if form is defined %}
                                    {{ form_start(form, { 'attr': { 'class': 'needs-validation use-loader', 'novalidate': 'novalidate', 'id': 'hardware-type-form-element' } }) }}
                                    <div class="form-section">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.name, 'Hardware Type Name', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-building"></i></span>
                                                        </div>
                                                        {{ form_widget(form.name, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter hardware type name', 'required': 'required' } }) }}
                                                    </div>
                                                    {{ form_errors(form.name) }}
                                                    <small class="form-text text-muted">Enter a hardware type name</small>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.dataStringSmall, 'Short Code', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-code"></i></span>
                                                        </div>
                                                        {{ form_widget(form.dataStringSmall, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter short code (e.g., LAP)' } }) }}
                                                    </div>
                                                    {{ form_errors(form.dataStringSmall) }}
                                                    <small class="form-text text-muted">Enter a short code (max 3 characters)</small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-12">
                                                <div class="mb-3">
                                                    {{ form_label(form.description, 'Description', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-info-circle"></i></span>
                                                        </div>
                                                        {{ form_widget(form.description, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter description' } }) }}
                                                    </div>
                                                    {{ form_errors(form.description) }}
                                                    <small class="form-text text-muted">Provide a brief description of the hardware type</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="d-flex justify-content-end mt-4">
                                        <a href="{{ path('hardware_type') }}" class="btn btn-secondary mr-2">
                                            Cancel
                                        </a>
                                        <button type="submit" class="btn btn-primary pr-3 pl-3">
                                            {{ hardwareType.id is defined ? 'Update Hardware Type' : 'Create Hardware Type' }}
                                        </button>
                                    </div>
                                    {{ form_end(form) }}
                                {% else %}
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="clearfix"></div>
    <link rel="stylesheet" href="{{ asset('assets/css/Error/error.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/AdminForm/Form.css') }}">
    <script src="{{ asset('assets/js/admin/hardwaretype.js') }}"></script>
{% endblock %}