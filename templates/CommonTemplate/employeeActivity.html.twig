<div class="content">
    <div class="animated fadeIn">
        <div class="row">
            <div class="col-md-12">
                <div id="flash-container"></div>
                <div class="card">
                    <div class="card-header d-flex justify-content-between">
                        {% if view %}
                        <strong class="card-title">Other Activity</strong>
                        {% endif %}
                        {% if viewEmployee %}
                            <strong class="card-title">Employee Activity</strong>
                        {% endif %}
                        {% if show %}
                        <a href="{{ path('employee_activity_new') }}" class="btn btn-primary">Add New Activity</a>
                        {% endif %}
                    </div>
                    <div class="card-body">
                        <div class="table-stats order-table ov-h text-center">
                            <div id="taskTable" class="datatable-main-wrapper">Loading...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="clearfix"></div>
<script src="{{ asset('assets/js/datatables.min.js') }}"></script>
<script src="{{ asset('bundles/datatables/js/datatables.js') }}"></script>
<script>
    $(function () {
        recordsTable = $('#taskTable').initDataTables({{ datatable_settings(datatable) }});
    });
</script>
<script src="{{ asset('assets/js/datatableclearsearch.js') }}"></script>
<script src="{{ asset('assets/js/admin/common.js') }}"></script>