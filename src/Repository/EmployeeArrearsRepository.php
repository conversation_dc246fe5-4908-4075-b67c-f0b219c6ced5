<?php

namespace App\Repository;

use App\Entity\EmployeeArrears;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<EmployeeArrears>
 */
class EmployeeArrearsRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, EmployeeArrears::class);
    }

    /**
     * Get the sum of totalArrears for an employee for a given month and year.
     */
    public function getTotalArrearsForEmployeeMonthYear($employee, int $month, int $year): float
    {
        $qb = $this->createQueryBuilder('e')
            ->select('SUM(e.totalArrears) as totalArrearsSum')
            ->where('e.employee = :employee')
            ->andWhere('e.month = :month')
            ->andWhere('e.year = :year')
            ->setParameter('employee', $employee)
            ->setParameter('month', $month)
            ->setParameter('year', $year);

        $result = $qb->getQuery()->getSingleScalarResult();
        return (float) $result;
    }
}
