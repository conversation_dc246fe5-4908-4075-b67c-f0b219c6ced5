<?php

namespace App\Controller\Admin;

use App\Entity\EmployeeDocument;
use App\Entity\MasterEmployee;
use App\Entity\User;
use App\Form\EmployeeDocumentType;
use App\Service\Common\EmployeeDocumentService;
use App\Traits\ExceptionLoggerTrait;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Csrf\CsrfToken;
use Symfony\Component\Security\Csrf\CsrfTokenManagerInterface;

#[Route('/admin')]
class EmployeeDocumentController extends AbstractController
{
    use ExceptionLoggerTrait;

    public function __construct(
        private readonly EntityManagerInterface    $entityManager,
        private readonly EmployeeDocumentService   $documentService,
        private readonly CsrfTokenManagerInterface $csrfTokenManager
    ) {
    }

    #[Route('/employee/{employeeId}/documents', name: 'admin_employee_documents', methods: ['GET', 'POST'])]
    public function index(Request $request, int $employeeId): Response
    {
        $user = $this->getUser();
        if (!$user instanceof User) {
            throw $this->createAccessDeniedException('This can only be accessed by an admin.');
        }
        $employee = $this->entityManager->getRepository(MasterEmployee::class)->find($employeeId);
        if (!$employee) {
            throw $this->createNotFoundException('Employee not found');
        }

        $table = $this->documentService->createDocumentTable($request, $employee);

        if ($table->isCallback()) {
            return $table->getResponse();
        }

        return $this->render('admin/EmployeeDocument/index.html.twig', [
            'datatable' => $table,
            'employee' => $employee,
        ]);
    }

    #[Route('/employee/{employeeId}/documents/new', name: 'admin_employee_document_new', methods: ['GET', 'POST'])]
    public function new(Request $request, int $employeeId): Response
    {
        $user = $this->getUser();
        if (!$user instanceof User) {
            throw $this->createAccessDeniedException();
        }

        $employee = $this->entityManager->getRepository(MasterEmployee::class)->find($employeeId);
        if (!$employee) {
            throw $this->createNotFoundException('Employee not found');
        }

        $document = new EmployeeDocument();
        $document->setMasterEmployee($employee);

        $form = $this->createForm(EmployeeDocumentType::class, $document, ['is_new' => true]);
        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            try {
                $file = $form->get('file')->getData();
                if (!$file) {
                    $this->addFlash('error', 'Please select a file to upload.');
                    return $this->redirectToRoute('admin_employee_document_new', ['employeeId' => $employeeId]);
                }

                if ($file->getSize() > 5 * 1024 * 1024) {
                    $this->addFlash('error', 'File size must be less than 5MB.');
                    return $this->redirectToRoute('admin_employee_document_new', ['employeeId' => $employeeId]);
                }

                $this->documentService->handleFileUpload($file, $document);
                $document->setUploadedByAdmin($user);

                $this->entityManager->persist($document);
                $this->entityManager->flush();

                $this->addFlash('success', 'Document uploaded successfully.');
                return $this->redirectToRoute('admin_employee_documents', ['employeeId' => $employeeId]);

            } catch (\Exception $e) {
                $this->addFlash('error', 'Error uploading document: ' . $e->getMessage());
                return $this->redirectToRoute('admin_employee_document_new', ['employeeId' => $employeeId]);
            }
        }

        return $this->render('admin/EmployeeDocument/new.html.twig', [
            'form' => $form->createView(),
            'employee' => $employee,
            'mode' => 'create',
        ]);
    }

    #[Route('/employee/documents/edit/{id}', name: 'admin_employee_document_edit', methods: ['GET', 'POST'])]
    public function edit(Request $request, EmployeeDocument $document): Response
    {
        $form = $this->createForm(EmployeeDocumentType::class, $document, ['is_new' => false]);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $file = $form->get('file')->getData();
            if ($file) {
                $this->documentService->deleteFile($document);
                $this->documentService->handleFileUpload($file, $document);
            }
            $this->entityManager->flush();

            $this->addFlash('success', 'Document updated successfully.');
            return $this->redirectToRoute('admin_employee_documents', ['employeeId' => $document->getMasterEmployee()->getId()]);
        }

        return $this->render('admin/EmployeeDocument/new.html.twig', [
            'form' => $form->createView(),
            'document' => $document,
            'employee' => $document->getMasterEmployee(),
            'mode' => 'edit',
        ]);
    }

    #[Route('/employee/documents/delete/{id}', name: 'admin_employee_document_delete', methods: ['POST'])]
    public function delete(Request $request, EmployeeDocument $document): Response
    {
        $token = $request->request->get('_token');
        if (!$this->csrfTokenManager->isTokenValid(new CsrfToken('delete' . $document->getId(), $token))) {
            return $this->json([
                'success' => false,
                'message' => 'Invalid CSRF token.'
            ], Response::HTTP_FORBIDDEN);
        }

        $this->documentService->deleteFile($document);
        $this->entityManager->remove($document);
        $this->entityManager->flush();

        return $this->json([
            'success' => true,
            'message' => 'Document has been deleted.'
        ], Response::HTTP_OK);
    }
}