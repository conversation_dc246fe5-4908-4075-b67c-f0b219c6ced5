{% extends 'Front/dashboard/base.html.twig' %}

{% block body %}
    {% include 'Front/partials/breadcrumbs.html.twig' with {
        breadcrumbs: [
            { 'label': 'Dashboard', 'url': url('dashboard_employee') },
            { 'label': 'Employee Activities', 'url': path('employee_activity_index') },
        ],
        active: mode == 'edit' ? 'Edit Activity' : 'Add Activity'
    } %}

    <div class="content">
        <div class="animated fadeIn">
            <div class="row">
                <div class="col-lg-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <strong class="card-title">{{ mode == 'edit' ? 'Edit  Activity' : 'Add  Activity' }}</strong>
                            <div class="d-flex align-items-center">
                                <a href="{{ path('employee_activity_index') }}" class="btn btn-sm btn-outline-secondary mr-2">
                                    <i class="fa fa-list mr-1"></i>Back to List
                                </a>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="activity-form">
                                {{ form_start(form, {
                                    'attr': {
                                        'class': 'needs-validation use-loader',
                                        'novalidate': 'novalidate',
                                        'data-turbo': 'false'
                                    }
                                }) }}
                                <div class="form-section">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                {{ form_label(form.activityType, 'Activity Type', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text"><i class="fa fa-tasks"></i></span>
                                                    </div>
                                                    {{ form_widget(form.activityType, { 'attr': { 'class': 'form-control', 'required': 'required' } }) }}
                                                </div>
                                                {{ form_errors(form.activityType) }}
                                                <small class="form-text text-muted">Select the type of activity</small>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                {{ form_label(form.hours, 'Hours Worked', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text"><i class="fa fa-clock-o"></i></span>
                                                    </div>
                                                    {{ form_widget(form.hours, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter hours worked', 'required': 'required', 'step': '0.01' } }) }}
                                                </div>
                                                {{ form_errors(form.hours) }}
                                                <small class="form-text text-muted">Enter hours worked on this activity</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                {{ form_label(form.date, 'Activity Date', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text"><i class="fa fa-calendar-check-o"></i></span>
                                                    </div>
                                                    {{ form_widget(form.date, {
                                                        'attr': {
                                                            'class': 'form-control datepicker',
                                                            'data-initial-date': employee_activity.date is defined and employee_activity.date ? employee_activity.date|date('d-m-Y') : ''
                                                        }
                                                    }) }}
                                                </div>
                                                {{ form_errors(form.date) }}
                                                <small class="form-text text-muted">Select the date and time of the activity</small>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                {{ form_label(form.relatedTask, 'Related Task', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text"><i class="fa fa-tasks"></i></span>
                                                    </div>
                                                    {{ form_widget(form.relatedTask, { 'attr': { 'class': 'form-control' } }) }}
                                                </div>
                                                {{ form_errors(form.relatedTask) }}
                                                <small class="form-text text-muted">Select a related task (optional)</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="mb-3">
                                                {{ form_label(form.description, 'Description', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text"><i class="fa fa-comment"></i></span>
                                                    </div>
                                                    {{ form_widget(form.description, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter description' } }) }}
                                                </div>
                                                {{ form_errors(form.description) }}
                                                <small class="form-text text-muted">Provide details about the activity</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="action-footer mt-4">
                                    <a href="{{ path('employee_activity_index') }}" class="btn btn-secondary mr-2">
                                        Cancel
                                    </a>
                                    <button type="submit" class="btn btn-primary pr-3 pl-3" id="submitButton" data-update="{{ mode == 'edit' ? 'true' : 'false' }}">
                                        {{ mode == 'edit' ? 'Update Activity' : 'Save Activity' }}
                                    </button>
                                </div>
                                {{ form_end(form) }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="clearfix"></div>
    <link rel="stylesheet" href="{{ asset('assets/css/Error/error.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/AdminForm/Form.css') }}">
    <script src="{{ asset('assets/js/datatables.min.js') }}"></script>
    <script src="{{ asset('bundles/datatables/js/datatables.js') }}"></script>
    <script src="{{ asset('assets/js/moment.min.js') }}"></script>
    <script src="{{ asset('assets/js/daterangepicker.min.js') }}"></script>
    <script src="{{ asset('assets/js/datepickerUtility.js') }}"></script>
    <script src="{{ asset('assets/js/front/employee_activity.js') }}"></script>
{% endblock %}