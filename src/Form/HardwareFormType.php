<?php

namespace App\Form;

use App\Entity\Hardware;
use App\Entity\HardwareType;
use App\Entity\MasterEmployee;
use Doctrine\ORM\EntityRepository;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\CallbackTransformer;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\Length;
use Symfony\Component\Validator\Constraints\NotBlank;

class HardwareFormType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('hardwareName', TextType::class, [
                'label' => 'Hardware Name',
                'required' => true,
                'constraints' => [
                    new NotBlank([
                        'message' => 'Hardware name is required.',
                    ]),
                    new Length([
                        'min' => 2,
                        'max' => 255,
                        'minMessage' => 'Hardware name must be at least {{ limit }} characters long',
                        'maxMessage' => 'Hardware name cannot be longer than {{ limit }} characters',
                    ]),
                ],
                'attr' => [
                    'maxlength' => 255,
                    'class' => 'form-control',
                    'data-validation' => 'required|min:2|max:255',
                    'placeholder' => 'Enter hardware name',
                ],
            ])
            ->add('hardwareType', EntityType::class, [
                'label' => 'Hardware Type',
                'autocomplete'=>true,
                'class' => HardwareType::class,
                'choice_label' => 'name',
                'placeholder' => '--Select--',
                'required' => true,
                'constraints' => [
                    new NotBlank([
                        'message' => 'Hardware type is required.',
                    ]),
                ],
                'attr' => [
                    'class' => 'form-control',
                    'data-validation' => 'required',
                ],
            ])
            ->add('employee', EntityType::class, [
                'label' => 'Assigned Employee',
                'autocomplete'=>true,
                'class' => MasterEmployee::class,
                'choice_label' => 'name',
                'placeholder' => '--Select Employee--',
                'required' => false,
                'query_builder' => function (EntityRepository $er) {
                    return $er->createQueryBuilder('e')
                        ->where('e.isDelete = :isDelete')
                        ->setParameter('isDelete', false)
                        ->orderBy('e.name', 'ASC');
                },
                'attr' => [
                    'class' => 'form-control',
                ],
            ])
            ->add('description', TextType::class, [
                'label' => 'Description',
                'required' => false,
                'constraints' => [
                    new Length([
                        'min' => 2,
                        'max' => 255,
                        'minMessage' => 'Description must be at least {{ limit }} characters long',
                        'maxMessage' => 'Description cannot be longer than {{ limit }} characters',
                    ]),
                ],
                'attr' => [
                    'maxlength' => 255,
                    'class' => 'form-control',
                    'data-validation' => 'min:2|max:255',
                    'placeholder' => 'Enter description',
                ],
            ])
            ->add('purchaseDate', TextType::class, [
                'label' => 'Purchase Date',
                'required' => true,
                'attr' => [
                    'class' => 'form-control datepicker',
                    'placeholder' => 'Select purchase date',
                    'required' => 'required',
                ],
                'constraints' => [new NotBlank()],
            ])
            ->add('warrantyEndDate', TextType::class, [
                'label' => 'Warranty End Date',
                'required' => true,
                'attr' => [
                    'class' => 'form-control datepicker',
                    'placeholder' => 'Select warranty end date',
                    'required' => 'required',
                ],
                'constraints' => [new NotBlank()],
            ])
            ->add('vendorName', TextType::class, [
                'label' => 'Vendor Name',
                'required' => true,
                'constraints' => [
                    new NotBlank([
                        'message' => 'Vendor name is required.',
                    ]),
                    new Length([
                        'min' => 2,
                        'max' => 255,
                        'minMessage' => 'Vendor name must be at least {{ limit }} characters long',
                        'maxMessage' => 'Vendor name cannot be longer than {{ limit }} characters',
                    ]),
                ],
                'attr' => [
                    'maxlength' => 255,
                    'class' => 'form-control',
                    'data-validation' => 'required|min:2|max:255',
                    'placeholder' => 'Enter vendor name',
                ],
            ])
            ->add('manufacturerName', TextType::class, [
                'label' => 'Manufacturer Name',
                'required' => false,
                'constraints' => [
                    new Length([
                        'min' => 2,
                        'max' => 255,
                        'minMessage' => 'Manufacturer name must be at least {{ limit }} characters long',
                        'maxMessage' => 'Manufacturer name cannot be longer than {{ limit }} characters',
                    ]),
                ],
                'attr' => [
                    'maxlength' => 255,
                    'class' => 'form-control',
                    'data-validation' => 'min:2|max:255',
                    'placeholder' => 'Enter manufacturer name',
                ],
            ])
            ->add('manufacturerModel', TextType::class, [
                'label' => 'Manufacturer Model',
                'required' => false,
                'constraints' => [
                    new Length([
                        'min' => 2,
                        'max' => 255,
                        'minMessage' => 'Manufacturer model must be at least {{ limit }} characters long',
                        'maxMessage' => 'Manufacturer model cannot be longer than {{ limit }} characters',
                    ]),
                ],
                'attr' => [
                    'maxlength' => 255,
                    'class' => 'form-control',
                    'data-validation' => 'min:2|max:255',
                    'placeholder' => 'Enter manufacturer model',
                ],
            ])
            ->add('manufacturerSerial', TextType::class, [
                'label' => 'Manufacturer Serial',
                'required' => false,
                'constraints' => [
                    new Length([
                        'min' => 2,
                        'max' => 255,
                        'minMessage' => 'Manufacturer serial must be at least {{ limit }} characters long',
                        'maxMessage' => 'Manufacturer serial cannot be longer than {{ limit }} characters',
                    ]),
                ],
                'attr' => [
                    'maxlength' => 255,
                    'class' => 'form-control',
                    'data-validation' => 'min:2|max:255',
                    'placeholder' => 'Enter manufacturer serial',
                ],
            ])
            ->add('condition', ChoiceType::class, [
                'label' => 'Condition',
                'autocomplete'=>true,
                'choices' => [
                    'New' => 'New',
                    'Used' => 'Used',
                    'Refurbished' => 'Refurbished',
                    'Damaged' => 'Damaged',
                ],
                'required' => true,
                'attr' => [
                    'class' => 'form-control',
                ],
            ])
            ->add('ipAddress', TextType::class, [
                'label' => 'IP Address',
                'required' => false,
                'constraints' => [
                    new Length([
                        'min' => 7,
                        'max' => 15,
                        'minMessage' => 'IP address must be at least {{ limit }} characters long',
                        'maxMessage' => 'IP address cannot be longer than {{ limit }} characters',
                    ]),
                ],
                'attr' => [
                    'maxlength' => 15,
                    'class' => 'form-control',
                    'data-validation' => 'min:7|max:15',
                    'placeholder' => 'Enter IP address',
                ],
            ])
            ->add('macAddress', TextType::class, [
                'label' => 'MAC Address',
                'required' => false,
                'constraints' => [
                    new Length([
                        'min' => 12,
                        'max' => 17,
                        'minMessage' => 'MAC address must be at least {{ limit }} characters long',
                        'maxMessage' => 'MAC address cannot be longer than {{ limit }} characters',
                    ]),
                ],
                'attr' => [
                    'maxlength' => 17,
                    'class' => 'form-control',
                    'data-validation' => 'min:12|max:17',
                    'placeholder' => 'Enter MAC address',
                ],
            ])
            ->add('location', TextType::class, [
                'label' => 'Location',
                'required' => false,
                'constraints' => [
                    new Length([
                        'min' => 2,
                        'max' => 255,
                        'minMessage' => 'Location must be at least {{ limit }} characters long',
                        'maxMessage' => 'Location cannot be longer than {{ limit }} characters',
                    ]),
                ],
                'attr' => [
                    'maxlength' => 255,
                    'class' => 'form-control',
                    'data-validation' => 'min:2|max:255',
                    'placeholder' => 'Enter location',
                ],
            ])
            ->add('pcName', TextType::class, [
                'label' => 'PC Name',
                'required' => false,
                'constraints' => [
                    new Length([
                        'min' => 2,
                        'max' => 255,
                        'minMessage' => 'PC name must be at least {{ limit }} characters long',
                        'maxMessage' => 'PC name cannot be longer than {{ limit }} characters',
                    ]),
                ],
                'attr' => [
                    'maxlength' => 255,
                    'class' => 'form-control',
                    'data-validation' => 'min:2|max:255',
                    'placeholder' => 'Enter PC name',
                ],
            ])
            ->add('ram', TextType::class, [
                'label' => 'RAM',
                'required' => false,
                'constraints' => [
                    new Length([
                        'min' => 2,
                        'max' => 50,
                        'minMessage' => 'RAM must be at least {{ limit }} characters long',
                        'maxMessage' => 'RAM cannot be longer than {{ limit }} characters',
                    ]),
                ],
                'attr' => [
                    'maxlength' => 50,
                    'class' => 'form-control',
                    'data-validation' => 'min:2|max:50',
                    'placeholder' => 'Enter RAM (e.g., 8GB)',
                ],
            ])
            ->add('showOnDashboard', ChoiceType::class, [
                'label' => 'Show On Dashboard',
                'choices' => [
                    'Yes' => true,
                    'No' => false,
                ],
                'required' => true,
            ])
            ->add('rom', TextType::class, [
                'label' => 'ROM',
                'required' => false,
                'constraints' => [
                    new Length([
                        'min' => 2,
                        'max' => 50,
                        'minMessage' => 'ROM must be at least {{ limit }} characters long',
                        'maxMessage' => 'ROM cannot be longer than {{ limit }} characters',
                    ]),
                ],
                'attr' => [
                    'maxlength' => 50,
                    'class' => 'form-control',
                    'data-validation' => 'min:2|max:50',
                    'placeholder' => 'Enter ROM (e.g., 256GB SSD)',
                ],
            ]);
        foreach (['purchaseDate', 'warrantyEndDate'] as $field) {
            $builder->get($field)
                ->addModelTransformer(new CallbackTransformer(
                    function ($dateAsObject) {
                        return $dateAsObject instanceof \DateTimeInterface ? $dateAsObject->format('d-m-Y') : '';
                    },
                    function ($dateAsString) {
                        return $dateAsString ? \DateTime::createFromFormat('d-m-Y', $dateAsString) : null;
                    }
                ));
        }
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => Hardware::class,
        ]);
    }
}