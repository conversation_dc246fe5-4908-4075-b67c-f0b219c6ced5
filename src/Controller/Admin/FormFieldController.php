<?php

namespace App\Controller\Admin;

use App\Entity\FormField;
use App\Form\FormFieldType;
use App\Repository\FormSectionRepository;
use App\Repository\FormTemplateRepository;
use App\Service\Admin\FormFieldService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Csrf\CsrfToken;
use Symfony\Component\Security\Csrf\CsrfTokenManagerInterface;

#[Route('/admin/form-template')]
class FormFieldController extends AbstractController
{
    public function __construct(
        private readonly FormSectionRepository $formSectionRepository,
        private readonly FormFieldService $fieldService,
        private readonly FormTemplateRepository $formTemplateRepository,
        private readonly EntityManagerInterface $entityManager
    ) {}

    #[Route('/{template_id}/sections/{sections_id}/field', name: 'form_field_index', methods: ['GET', 'POST'])]
    public function index(Request $request, int $sections_id, int $template_id): Response
    {
        $section = $this->formSectionRepository->find($sections_id);
        $template = $this->formTemplateRepository->find($template_id);

        if (!$section || !$template) {
            throw new NotFoundHttpException('Template or Section not found.');
        }

        $table = $this->fieldService->createFormFieldTable($request, $sections_id);

        if ($table->isCallback()) {
            return $table->getResponse();
        }

        return $this->render('admin/form_field/index.html.twig', [
            'datatable' => $table,
            'section' => $section,
            'template' => $template,
        ]);
    }

    #[Route('/{template_id}/sections/{sections_id}/new', name: 'form_field_new', methods: ['GET', 'POST'])]
    public function new(Request $request, int $template_id, int $sections_id , FormFieldService $fieldService): Response
    {
        $template = $this->formTemplateRepository->find($template_id);
        $section = $this->formSectionRepository->find($sections_id);

        if (!$template || !$section) {
            throw new NotFoundHttpException('Template or Section not found');
        }

        $field = new FormField();
        $field->setSection($section);

        $lastOrderNumber = $this->entityManager->getRepository(FormField::class)
            ->findOneBy(['section' => $section], ['fieldOrder' => 'DESC']);
        $field->setFieldOrder($lastOrderNumber ? $lastOrderNumber->getFieldOrder() + 1 : 1);

        $form = $this->createForm(FormFieldType::class, $field);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            try {
                $fieldService->setDefaultRatingOption($field);
                $field->setCreatedAt(new \DateTime());
                $field->setUpdatedAt(new \DateTime());
                $this->entityManager->persist($field);
                $section->setUpdatedAt(new \DateTime());
                $this->entityManager->flush();

                $this->addFlash('success', 'Form field created successfully.');

                return $this->redirectToRoute('form_field_index', [
                    'template_id' => $template->getId(),
                    'sections_id' => $section->getId(),
                ]);
            } catch (\Exception $e) {
                $this->addFlash('error', 'Error creating form field: ' . $e->getMessage());
            }
        } elseif ($form->isSubmitted()) {
            $this->addFlash('error', 'Please correct the errors below.');
        }

        return $this->render('admin/form_field/new.html.twig', [
            'template' => $template,
            'form' => $form,
            'section' => $section,
        ]);
    }

    #[Route('/{template_id}/sections/{sections_id}/fields/{id}/edit', name: 'form_field_edit', methods: ['GET', 'POST'])]
    public function edit(Request $request, FormField $field, int $template_id, int $sections_id, FormFieldService $fieldService): Response
    {
        $template = $this->formTemplateRepository->find($template_id);
        $section = $this->formSectionRepository->find($sections_id);

        if (!$template || !$section) {
            throw new NotFoundHttpException('Template or Section not found.');
        }

        $form = $this->createForm(FormFieldType::class, $field);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            try {
                $fieldService->setDefaultRatingOption($field);
                $field->setUpdatedAt(new \DateTime());
                $section->setUpdatedAt(new \DateTime());

                $this->entityManager->flush();

                $this->addFlash('success', 'Form field updated successfully.');

                return $this->redirectToRoute('form_field_index', [
                    'template_id' => $template->getId(),
                    'sections_id' => $section->getId(),
                ]);
            } catch (\Exception $e) {
                $this->addFlash('error', 'Error updating form field: ' . $e->getMessage());
            }
        } elseif ($form->isSubmitted()) {
            $this->addFlash('error', 'Please correct the errors below.');
        }

        return $this->render('admin/form_field/new.html.twig', [
            'form' => $form,
            'section' => $section,
            'template' => $template,
            'field' => $field,
        ]);
    }

    #[Route('/{template_id}/sections/{sections_id}/fields/{id}/delete', name: 'form_field_delete', methods: ['POST'])]
    public function deleteFormField(
        Request                $request,
        FormField              $field,
        int                    $template_id,
        int                    $sections_id,
        EntityManagerInterface $entityManager,
        CsrfTokenManagerInterface $csrfTokenManager
    ): Response
    {
        $template = $this->formTemplateRepository->find($template_id);
        $section = $this->formSectionRepository->find($sections_id);

        if (!$template || !$section) {
            return $this->json([
                'success' => false,
                'message' => 'Template or Section not found.'
            ], Response::HTTP_NOT_FOUND);
        }

        $token = $request->request->get('_token');
        if (!$csrfTokenManager->isTokenValid(new CsrfToken('delete' . $field->getId(), $token))) {
            return $this->json([
                'success' => false,
                'message' => 'Invalid CSRF token.'
            ], Response::HTTP_FORBIDDEN);
        }

        $field->setIsDelete(true);
        $entityManager->persist($field);
        $entityManager->flush();

        return $this->json([
            'success' => true,
            'message' => 'Field has been deleted.'
        ], Response::HTTP_OK);
    }

    #[Route('/{template_id}/sections/{sections_id}/fields/{id}/reorder', name: 'form_field_reorder', methods: ['GET'])]
    public function reorder(Request $request, FormField $field): Response
    {
        $direction = $request->query->get('direction');
        $currentOrder = $field->getFieldOrder();
        $section = $field->getSection();

        if ($direction === 'up' && $currentOrder > 1) {
            $swapField = $this->entityManager->getRepository(FormField::class)
                ->findOneBy(['section' => $section, 'fieldOrder' => $currentOrder - 1]);

            if ($swapField) {
                $swapField->setFieldOrder($currentOrder);
                $field->setFieldOrder($currentOrder - 1);
            }
        } elseif ($direction === 'down') {
            $swapField = $this->entityManager->getRepository(FormField::class)
                ->findOneBy(['section' => $section, 'fieldOrder' => $currentOrder + 1]);

            if ($swapField) {
                $swapField->setFieldOrder($currentOrder);
                $field->setFieldOrder($currentOrder + 1);
            }
        }

        $section->setUpdatedAt(new \DateTime());
        $this->entityManager->flush();

        return $this->redirectToRoute('form_field_index', [
            'template_id' => $section->getTemplate()->getId(),
            'sections_id' => $section->getId(),
        ]);
    }
    #[Route('/{template_id}/sections/{sections_id}/fields/{id}/toggle-status', name: 'form_field_toggle_status', methods: ['POST'])]
    public function toggleStatus(Request $request, int $template_id, int $sections_id, FormField $field): Response
    {
        $template = $this->formTemplateRepository->find($template_id);
        $section = $this->formSectionRepository->find($sections_id);

        if (!$template || !$section) {
            throw new NotFoundHttpException('Template or Section not found.');
        }

        if (!$request->isXmlHttpRequest()) {
            return $this->json([
                'success' => false,
                'message' => 'This endpoint only accepts AJAX requests.'
            ], 400);
        }

        if (!$this->isCsrfTokenValid('toggle-status' . $field->getId(), $request->request->get('_token'))) {
            return $this->json([
                'success' => false,
                'message' => 'Invalid CSRF token.'
            ], 400);
        }

        try {
            $field->setIsVisible(!$field->isVisible());
            $field->setUpdatedAt(new \DateTime());
            $this->entityManager->flush();

            return $this->json([
                'success' => true,
                'message' => 'Field visibility status updated successfully.',
                'isVisible' => $field->isVisible()
            ]);
        } catch (\Exception $e) {
            return $this->json([
                'success' => false,
                'message' => 'Error updating status: ' . $e->getMessage()
            ], 500);
        }
    }
}