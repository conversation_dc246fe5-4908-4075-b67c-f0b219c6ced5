<?php

namespace App\Service\Admin;

use App\Entity\Employee;
use Doctrine\ORM\QueryBuilder;
use Omines\DataTablesBundle\Adapter\Doctrine\ORMAdapter;
use Omines\DataTablesBundle\Column\TextColumn;
use Omines\DataTablesBundle\DataTable;
use Omines\DataTablesBundle\DataTableFactory;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Request;
use App\Traits\ExceptionLoggerTrait;

class EmployeeTableService
{
    use ExceptionLoggerTrait;

    public function __construct(
        private readonly DataTableFactory $dataTableFactory,
        ParameterBagInterface $parameterBag
    )
    {
        $this->initializeLogger($parameterBag, 'employee_Table_service');
    }

    public function createEmployeeTable(Request $request): DataTable
    {
        $dataTable = $this->dataTableFactory->create();

        foreach ($this->getColumns() as $key => $column) {
            $dataTable->add($key, $column['className'], array_filter($column, fn($k) => $k !== 'className'));
        }

        return $dataTable->createAdapter(ORMAdapter::class, [
            'entity' => Employee::class,
            'query' => fn(QueryBuilder $qb) => $this->buildQuery($qb),
        ])
            ->addOrderBy('name', 'asc')
            ->handleRequest($request);
    }

    private function getColumns(): array
    {
        return [
            'name' => [
                'label' => 'Name',
                'field' => 'me.name',
                'className' => TextColumn::class,
                'orderField' =>'me.name',
            ],
            'email' => [
                'label' => 'Email',
                'field' => 'me.email',
                'className' => TextColumn::class,
            ],
            'employeeCode' => [
                'label' => 'Code',
                'field' => 'me.employeeCode',
                'className' => TextColumn::class,
            ],
        ];
    }

    private function buildQuery(QueryBuilder $qb): void
    {
        $qb->select('e, me')
            ->from(Employee::class, 'e')
            ->join('e.masterEmployee', 'me');
    }
}