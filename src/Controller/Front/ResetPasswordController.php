<?php

namespace App\Controller\Front;

use App\Entity\MasterEmployee;
use App\Form\ChangePasswordFormType;
use App\Form\ResetPasswordRequestFormType;
use App\Repository\ResetPasswordRequestEmployeeRepository;
use App\Repository\SettingRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bridge\Twig\Mime\BodyRenderer;
use Symfony\Bridge\Twig\Mime\TemplatedEmail;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Mailer\Mailer;
use Symfony\Component\Mailer\MailerInterface;
use Symfony\Component\Mailer\Transport;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Contracts\Translation\TranslatorInterface;
use SymfonyCasts\Bundle\ResetPassword\Controller\ResetPasswordControllerTrait;
use SymfonyCasts\Bundle\ResetPassword\Exception\ResetPasswordExceptionInterface;
use SymfonyCasts\Bundle\ResetPassword\ResetPasswordHelperInterface;
use Twig\Environment;

#[Route('/reset-password')]
class ResetPasswordController extends AbstractController
{
    use ResetPasswordControllerTrait;


    public function __construct(
        private readonly ResetPasswordHelperInterface $resetPasswordHelper,
        private readonly EntityManagerInterface       $entityManager,
        private readonly SettingRepository            $settingRepository,
        private readonly Environment                  $twig,
        private readonly ResetPasswordRequestEmployeeRepository    $resetPasswordRequestEmployeeRepository,
    )
    {

    }

    private function getEmailConfiguration(): array
    {
        $dsnSetting = $this->settingRepository->findOneBy(['fieldName' => 'MAILER_DSN']);
        $senderSetting = $this->settingRepository->findOneBy(['fieldName' => 'MAILER_FROM_ADDRESS']);
        if (!$dsnSetting || !$senderSetting) {
            throw new \RuntimeException('Email configuration is incomplete.');
        }

        return [
            'dsn' => $dsnSetting->getFieldValue(),
            'sender' => $senderSetting->getFieldValue(),
        ];
    }

    /**
     * API endpoint to check if a non-expired reset token exists for an email.
     * @throws \JsonException
     */
    #[Route('/api/check-reset-token', name: 'api_check_reset_token', methods: ['POST'])]
    public function checkResetToken(Request $request): JsonResponse
    {
        $data = json_decode($request->getContent(), true, 512, JSON_THROW_ON_ERROR);
        $email = $data['email'] ?? null;

        if (!$email) {
            return new JsonResponse(['message' => 'Email is required.'], Response::HTTP_BAD_REQUEST);
        }

        $user = $this->entityManager->getRepository(MasterEmployee::class)->findOneBy(['email' => $email]);
        if (!$user) {
            return new JsonResponse(['hasNonExpiredToken' => false], Response::HTTP_OK);
        }

        $lastRequestDate = $this->resetPasswordRequestEmployeeRepository->getMostRecentNonExpiredRequestDate($user);
        if ($lastRequestDate && (new \DateTime())->diff($lastRequestDate)->h < 1) {
            return new JsonResponse(['hasNonExpiredToken' => true], Response::HTTP_OK);
        }

        return new JsonResponse(['hasNonExpiredToken' => false], Response::HTTP_OK);
    }

    /**
     * Display & process form to request a password reset.
     */
    #[Route('', name: 'app_forgot_password_request_employee')]
    public function request(Request $request, MailerInterface $mailer, TranslatorInterface $translator): Response
    {
        return $this->render('Front/reset_password/request.html.twig');
    }

    /**
     * Validates and process the reset URL that the user clicked in their email.
     */
    #[Route('/reset/{token}', name: 'app_reset_password_employee')]
    public function reset(Request $request, UserPasswordHasherInterface $passwordHasher, TranslatorInterface $translator, ?string $token = null): Response
    {
        if ($token) {
            // We store the token in session and remove it from the URL, to avoid the URL being
            // loaded in a browser and potentially leaking the token to 3rd party JavaScript.
            $this->storeTokenInSession($token);

            return $this->redirectToRoute('app_reset_password_employee');
        }

        $token = $this->getTokenFromSession();

        if (null === $token) {
            throw $this->createNotFoundException('No reset password token found in the URL or in the session.');
        }

        try {
            /** @var MasterEmployee $user */
            $user = $this->resetPasswordHelper->validateTokenAndFetchUser($token);
        } catch (ResetPasswordExceptionInterface $e) {
            $this->addFlash('reset_password_error', sprintf(
                '%s - %s',
                $translator->trans(ResetPasswordExceptionInterface::MESSAGE_PROBLEM_VALIDATE, [], 'ResetPasswordBundle'),
                $translator->trans($e->getReason(), [], 'ResetPasswordBundle')
            ));

            return $this->redirectToRoute('app_forgot_password_request_employee');
        }

        // The token is valid; allow the user to change their password.
        $form = $this->createForm(ChangePasswordFormType::class);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            // A password reset token should be used only once, remove it.
            $this->resetPasswordHelper->removeResetRequest($token);

            /** @var string $plainPassword */
            $plainPassword = $form->get('plainPassword')->getData();

            // Encode(hash) the plain password, and set it.
            $user->setPassword($passwordHasher->hashPassword($user, $plainPassword));

            $this->entityManager->flush();

            // The session is cleaned up after the password has been changed.
            $this->cleanSessionAfterReset();
            $this->addFlash('success', 'Password changed successfully.');
            return $this->redirectToRoute('app_login_employee');
        }

        return $this->render('Front/reset_password/reset.html.twig', [
            'resetForm' => $form->createView(),
        ]);
    }

    /**
     * API endpoint to request a password reset.
     */
    #[Route('/api/user/forgot-password', name: 'api_user_forgot_password', methods: ['POST'])]
    public function apiForgotPassword(Request $request, MailerInterface $mailer, TranslatorInterface $translator): JsonResponse
    {
        $data = json_decode($request->getContent(), true, 512, JSON_THROW_ON_ERROR);
        $email = $data['email'] ?? null;

        if (!$email) {
            return new JsonResponse(['message' => 'Email is required.'], Response::HTTP_BAD_REQUEST);
        }

        $user = $this->entityManager->getRepository(MasterEmployee::class)->findOneBy(['email' => $email]);
        if (!$user) {
            // Don’t reveal if the user exists, just return success to prevent enumeration
            return new JsonResponse(['message' => 'If the email exists, a reset link has been sent.'], Response::HTTP_OK);
        }

        $lastRequestDate = $this->resetPasswordRequestEmployeeRepository->getMostRecentNonExpiredRequestDate($user);
        if ($lastRequestDate && (new \DateTime())->diff($lastRequestDate)->h < 1) {
            return new JsonResponse(
                ['message' => 'A reset email has already been sent. Please wait 1 hour or check your inbox.'],
                Response::HTTP_TOO_MANY_REQUESTS
            );
        }

        try {
            $resetToken = $this->resetPasswordHelper->generateResetToken($user);

            $emailConfig = $this->getEmailConfiguration();
            $transport = Transport::fromDsn($emailConfig['dsn']);
            $sender = $emailConfig['sender'];
            $customMailer = new Mailer($transport);
            $email = (new TemplatedEmail())
                ->from($sender)
                ->to((string)$user->getEmail())
                ->subject('Your password reset request')
                ->htmlTemplate('Front/reset_password/email.html.twig')
                ->context(['resetToken' => $resetToken]);
            $renderer = new BodyRenderer($this->twig);
            $renderer->render($email);
            $customMailer->send($email);

            $this->setTokenObjectInSession($resetToken);
            return new JsonResponse(['message' => 'Reset email sent successfully.'], Response::HTTP_OK);
        } catch (ResetPasswordExceptionInterface $e) {
            return new JsonResponse(
                ['message' => $translator->trans($e->getReason(), [], 'ResetPasswordBundle')],
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        } catch (\Symfony\Component\Mailer\Exception\TransportExceptionInterface $e) {
            return new JsonResponse(
                ['message' => 'Failed to send email: ' . $e->getMessage()],
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }
}
