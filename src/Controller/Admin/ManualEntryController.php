<?php
namespace App\Controller\Admin;

use App\Service\Admin\ManualEntryService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\HttpFoundation\Response;

#[Route('/admin/api/teamlogger', name: 'api_teamlogger_')]
class ManualEntryController extends AbstractController
{
    public function __construct(
        private readonly ManualEntryService $teamLoggerService
    ) {}

    /**
     * @throws \Exception
     */
    #[Route('/manual/entries', name: 'manual_entries', methods: ['GET'])]
    public function manualEntries(): Response
    {
        $entries = $this->teamLoggerService->getManualEntries();

        return $this->render('manual_entries/index.html.twig', [
            'entries' => $entries
        ]);
    }


    #[Route('/approve', name: 'approve_entries', methods: ['POST'])]
    public function approveEntries(Request $request): JsonResponse
    {
        try {
            $data = json_decode($request->getContent(), true, 512, JSON_THROW_ON_ERROR);
            $entryIds = $data['entry_ids'] ?? [];

            if (empty($entryIds)) {
                return $this->json(['error' => 'No entry IDs provided'], 400);
            }

            $result = $this->teamLoggerService->updateEntriesStatus($entryIds, 'approved');
            return $this->json($result);
        } catch (\Exception $e) {
            return $this->json(['error' => $e->getMessage()], 500);
        }
    }

    #[Route('/reject', name: 'reject_entries', methods: ['POST'])]
    public function rejectEntries(Request $request): JsonResponse
    {
        try {
            $data = json_decode($request->getContent(), true, 512, JSON_THROW_ON_ERROR);
            $entryIds = $data['entry_ids'] ?? [];

            if (empty($entryIds)) {
                return $this->json(['error' => 'No entry IDs provided'], 400);
            }

            $result = $this->teamLoggerService->updateEntriesStatus($entryIds, 'rejected');
            return $this->json($result);
        } catch (\Exception $e) {
            return $this->json(['error' => $e->getMessage()], 500);
        }
    }
}