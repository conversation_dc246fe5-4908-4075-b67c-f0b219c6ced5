<?php

namespace App\Service\Admin;

use App\Entity\LeaveRequest;
use App\Entity\MasterEmployee;
use App\Helper\ModalboxHelper;
use App\Repository\AttendanceRepository;
use App\Repository\HolidayRepository;
use App\Repository\LeaveRequestRepository;
use App\Repository\MasterEmployeeRepository;
use App\Traits\ExceptionLoggerTrait;
use Omines\DataTablesBundle\Adapter\ArrayAdapter;
use Omines\DataTablesBundle\Column\TextColumn;
use Omines\DataTablesBundle\DataTable;
use Omines\DataTablesBundle\DataTableFactory;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Request;

class AttendenceTableService
{
    use ExceptionLoggerTrait;

    public function __construct(
        private readonly AttendanceRepository     $attendanceRepository,
        private readonly MasterEmployeeRepository $employeeRepository,
        private readonly HolidayRepository        $holidayRepository,
        private readonly DataTableFactory         $dataTableFactory,
        private readonly ParameterBagInterface    $parameterBag,
        private readonly LeaveRequestRepository   $leaveRequestRepository,
        private readonly ModalboxHelper $modalboxHelper,
    ) {
        $this->initializeLogger($parameterBag, 'Attendance_Table_Service');
    }

    public function createAttendanceTable(Request $request): DataTable
    {
        $year = $request->query->getInt('year', (int) date('Y'));
        $month = $request->query->getInt('month', (int) date('m'));
        $employees = $this->employeeRepository->findBy(['isDelete' => false]);
        $data = array_map(static function (MasterEmployee $employee) use ($year, $month) {
            return [
                'id' => $employee->getId(),
                'name' => $employee->getName(),
                'year' => $year,
                'month' => $month,
                'employeeId' => $employee->getId(),
            ];
        }, $employees);
        return $this->dataTableFactory->create()
            ->add('name', TextColumn::class, [
                'label' => 'Name',
                'orderable' => true,
            ])
            ->add('totalDays', TextColumn::class, [
                'label' => 'Total Working Days',
                'orderable' => false,
                'searchable' => false,
                'render' => fn($value, $context) => (string) $this->getTotalWorkingDays($context['year'], $context['month']),
            ])
            ->add('presentDays', TextColumn::class, [
                'label' => 'Present Days',
                'orderable' => false,
                'searchable' => false,
                'render' => function ($value, $context) {
                    $employee = $this->employeeRepository->find($context['employeeId']);
                    return (string) $this->getPresentDays($employee, $context['year'], $context['month']);
                },
            ])
            ->add('absentDays', TextColumn::class, [
                'label' => 'Absent Days',
                'orderable' => false,
                'searchable' => false,
                'render' => function ($value, $context) {
                    $employee = $this->employeeRepository->find($context['employeeId']);
                    $total = $this->getTotalWorkingDays($context['year'], $context['month']);
                    $present = $this->getPresentDays($employee, $context['year'], $context['month']);
                    $totalAbsent = $total - $present;
                    return (string) $totalAbsent;
                },
            ])
            ->add('leaveDays', TextColumn::class, [
                'label' => 'Leave Days',
                'orderable' => false,
                'searchable' => false,
                'render' => function ($value, $context) {
                    $employee = $this->employeeRepository->find($context['employeeId']);
                    return (string) $this->getLeaveDays($employee, $context['year'], $context['month']);
                },
            ])

            ->add('actions', TextColumn::class, [
                'label' => 'Actions',
                'orderable' => false,
                'searchable' => false,
                'render' => fn($value, $context) => $this->renderActionColumn($context),
            ])
            ->createAdapter(ArrayAdapter::class, $data)
            ->handleRequest($request);
    }

    private function renderActionColumn(array $context): string
    {
        $employeeId = $context['id'];
        $employeeName = htmlspecialchars($context['name'] ?? 'N/A');
        $year = $context['year'];
        $month = $context['month'];

        $modalId = 'calendarModal_' . $employeeId;
        $modalBody = sprintf('
        <div class="calendar-container" data-employee-id="%d" data-year="%d" data-month="%d">
            <div class="text-center">
                <i class="fa fa-spinner fa-spin"></i> Loading calendar...
            </div>
        </div>',
            $employeeId,
            $year,
            $month
        );

        $footerButtons = '<button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>';

        $viewModal = $this->modalboxHelper->renderModal(
            $modalId,
            sprintf('Attendance Calendar - %s (%s %d)', $employeeName, date('F', mktime(0, 0, 0, $month, 1)), $year),
            $modalBody,
            $footerButtons,
        );

        $viewButton = sprintf(
            '<button type="button" class="btn btn-primary btn-sm bs-btn-main" data-toggle="modal" data-target="#%s" title="View Calendar" onclick="loadCalendarData(%d, %d, %d, \'%s\')">
            <i class="fa fa-calendar"></i>
        </button>',
            $modalId,
            $employeeId,
            $year,
            $month,
            $modalId
        );

        return $viewButton . $viewModal;
    }
    /**
     * Calculates total working days in a month (Mon–Fri, excluding public holidays)
     * @throws \DateMalformedStringException
     */
    public function getTotalWorkingDays(int $year, int $month): int
    {
        $startDate = new \DateTimeImmutable(sprintf('%d-%02d-01', $year, $month));
        $endDate = $startDate->modify('last day of this month');

        $holidays = $this->holidayRepository->findByMonthAndYear($year, $month);
        $holidayDates = array_map(fn($holiday) => $holiday->getDate()->format('Y-m-d'), $holidays);

        $workingDays = 0;
        $currentDate = $startDate;

        while ($currentDate <= $endDate) {
            $dayOfWeek = (int) $currentDate->format('N');
            $dateString = $currentDate->format('Y-m-d');

            if ($dayOfWeek < 6 && !in_array($dateString, $holidayDates, true)) {
                $workingDays++;
            }

            $currentDate = $currentDate->modify('+1 day');
        }

        return $workingDays;
    }

    /**
     * @throws \DateMalformedStringException
     */
    private function getPresentDays(MasterEmployee $employee, int $year, int $month): int
    {
        $startDate = new \DateTimeImmutable(sprintf('%d-%02d-01 00:00:00', $year, $month));
        $endDate = $startDate->modify('last day of this month')->setTime(23, 59, 59);
        $holidays = $this->holidayRepository->findByMonthAndYear($year, $month);
        $holidayDates = array_map(fn($holiday) => $holiday->getDate()->format('Y-m-d'), $holidays);
        $attendances = $this->attendanceRepository->createQueryBuilder('a')
            ->select('a.timestamp')
            ->where('a.employee = :employee')
            ->andWhere('a.status = :status')
            ->andWhere('a.timestamp BETWEEN :start AND :end')
            ->setParameter('employee', $employee)
            ->setParameter('status', 'Office In')
            ->setParameter('start', $startDate)
            ->setParameter('end', $endDate)
            ->getQuery()
            ->getResult();

        $presentDates = [];

        foreach ($attendances as $attendance) {
            $date = $attendance['timestamp']->format('Y-m-d');
            $dayOfWeek = (int) $attendance['timestamp']->format('N');

            if ($dayOfWeek < 6 && !in_array($date, $holidayDates, true)) {
                $presentDates[$date] = true;
            }
        }
        return count($presentDates);
    }

    /**
     * @throws \DateMalformedStringException
     */
    public function getLeaveDays(MasterEmployee $employee, int $year, int $month): float
    {
        $startDate = new \DateTimeImmutable(sprintf('%d-%02d-01', $year, $month));
        $endDate = $startDate->modify('last day of this month');

        $holidays = $this->holidayRepository->findByMonthAndYear($year, $month);
        $holidayDates = array_map(fn($h) => $h->getDate()->format('Y-m-d'), $holidays);

        $leaves = $this->leaveRequestRepository->createQueryBuilder('lr')
            ->where('lr.employee = :employee')
            ->andWhere('lr.status = :status')
            ->andWhere('lr.startDate <= :end')
            ->andWhere('lr.endDate >= :start')
            ->setParameter('employee', $employee)
            ->setParameter('status', LeaveRequest::STATUS_APPROVED)
            ->setParameter('start', $startDate)
            ->setParameter('end', $endDate)
            ->getQuery()
            ->getResult();

        $leaveDays = 0.0;
        foreach ($leaves as $leave) {
            $leaveStart = $leave->getStartDate();
            $leaveEnd = $leave->getEndDate();

            $current = clone max($leaveStart, $startDate);
            $end = min($leaveEnd, $endDate);

            while ($current <= $end) {
                $day = $current->format('Y-m-d');
                $dayOfWeek = (int) $current->format('N');
                if ($dayOfWeek < 6 && !in_array($day, $holidayDates, true)) {
                    $isStart = $day === $leaveStart->format('Y-m-d');
                    $isEnd = $day === $leaveEnd->format('Y-m-d');

                    if ($leaveStart == $leaveEnd) {
                        if ($leave->getStartHalfDay() || $leave->getEndHalfDay()) {
                            $leaveDays += 0.5;
                        } else {
                            $leaveDays += 1.0;
                        }
                    } else {
                        if ($isStart && $leave->getStartHalfDay()) {
                            $leaveDays += 0.5;
                        } elseif ($isEnd && $leave->getEndHalfDay()) {
                            $leaveDays += 0.5;
                        } elseif (!$isStart && !$isEnd) {
                            $leaveDays += 1.0;
                        } elseif ($isStart && !$leave->getStartHalfDay()) {
                            $leaveDays += 1.0;
                        } elseif ($isEnd && !$leave->getEndHalfDay()) {
                            $leaveDays += 1.0;
                        }
                    }
                }

                $current = $current->modify('+1 day');
            }
        }
        return $leaveDays;
    }

    /**
     * Get the number of working days after a given date in a month for an employee (excluding weekends, holidays, and approved leaves).
     * @throws \DateMalformedStringException
     */
    public function getWorkingDaysAfterDate(int $employeeId, int $year, int $month, ?\DateTimeInterface $changeDate): int
    {
        if (!$changeDate) {
            return 0;
        }
        $firstDayOfMonth = new \DateTimeImmutable(sprintf('%d-%02d-01', $year, $month));
        $startDate = $changeDate > $firstDayOfMonth ? $changeDate : $firstDayOfMonth;
        $endDate = $firstDayOfMonth->modify('last day of this month');
        $holidays = $this->holidayRepository->findByMonthAndYear($year, $month);
        $holidayDates = array_flip(array_map(fn($h) => $h->getDate()->format('Y-m-d'), $holidays));
        $employee = $this->employeeRepository->find($employeeId);
        $leaves = $this->leaveRequestRepository->createQueryBuilder('lr')
            ->where('lr.employee = :employee')
            ->andWhere('lr.status = :status')
            ->andWhere('lr.startDate <= :end')
            ->andWhere('lr.endDate >= :start')
            ->setParameter('employee', $employee)
            ->setParameter('status', 'Approved')
            ->setParameter('start', $startDate)
            ->setParameter('end', $endDate)
            ->getQuery()
            ->getResult();
        $leaveDates = [];
        foreach ($leaves as $leave) {
            $current = clone $leave->getStartDate();
            $end = $leave->getEndDate();
            while ($current <= $end) {
                $day = $current->format('Y-m-d');
                $dayOfWeek = (int)$current->format('N');
                if ($dayOfWeek < 6 && !isset($holidayDates[$day])) {
                    $leaveDates[$day] = true;
                }
                $current = $current->modify('+1 day');
            }
        }
        $workingDays = 0;
        $currentDate = clone $startDate;
        while ($currentDate <= $endDate) {
            $dayOfWeek = (int)$currentDate->format('N');
            $dateString = $currentDate->format('Y-m-d');
            if ($dayOfWeek < 6 && !isset($holidayDates[$dateString]) && !isset($leaveDates[$dateString])) {
                $workingDays++;
            }
            $currentDate = $currentDate->modify('+1 day');
        }

        return $workingDays;
    }
}