<?php

namespace App\Repository;

use App\Entity\TrackingConfiguration;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<TrackingConfiguration>
 *
 * @method TrackingConfiguration|null find($id, $lockMode = null, $lockVersion = null)
 * @method TrackingConfiguration|null findOneBy(array $criteria, array $orderBy = null)
 * @method TrackingConfiguration[]    findAll()
 * @method TrackingConfiguration[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class TrackingConfigurationRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, TrackingConfiguration::class);
    }

    /**
     * Get configuration value by key
     */
    public function getConfigValue(string $key, string $type = 'global', ?int $employeeId = null): ?string
    {
        $qb = $this->createQueryBuilder('tc')
            ->select('tc.configValue')
            ->where('tc.configKey = :key')
            ->andWhere('tc.configType = :type')
            ->andWhere('tc.isActive = true')
            ->setParameter('key', $key)
            ->setParameter('type', $type);

        if ($employeeId && $type === 'employee') {
            $qb->andWhere('tc.employee = :employeeId')
               ->setParameter('employeeId', $employeeId);
        }

        $result = $qb->getQuery()->getOneOrNullResult();
        
        return $result ? $result['configValue'] : null;
    }

    /**
     * Get all configurations for a specific type
     */
    public function getConfigurationsByType(string $type = 'global', ?int $employeeId = null): array
    {
        $qb = $this->createQueryBuilder('tc')
            ->where('tc.configType = :type')
            ->andWhere('tc.isActive = true')
            ->setParameter('type', $type)
            ->orderBy('tc.configKey', 'ASC');

        if ($type === 'global') {
            // Global configs have employee = null
            $qb->andWhere('tc.employee IS NULL');
        } elseif ($employeeId && $type === 'employee') {
            // Employee-specific configs
            $qb->andWhere('tc.employee = :employeeId')
               ->setParameter('employeeId', $employeeId);
        }

        return $qb->getQuery()->getResult();
    }

    /**
     * Get configurations organized by category
     */
    public function getConfigurationsByCategory(): array
    {
        // Get global configs where employee is null
        $configs = $this->findBy([
            'configType' => 'global',
            'employee' => null,  // Global configs have null employee
            'isActive' => true
        ]);

        $categorized = [
            'idle_detection' => [],
            'time_tracking' => [],
            'screenshots' => [],
            'webcam' => [],
            'application_tracking' => [],
            'website_tracking' => [],
            'productivity' => []
        ];

        foreach ($configs as $config) {
            $category = $this->getCategoryForKey($config->getConfigKey());
            if (isset($categorized[$category])) {
                $categorized[$category][] = $config;
            }
        }

        return $categorized;
    }

    /**
     * Set or update a configuration value
     */
    public function setConfigValue(string $key, string $value, string $type = 'global', ?int $employeeId = null, ?string $description = null): TrackingConfiguration
    {
        $config = $this->findOneBy([
            'configKey' => $key,
            'configType' => $type,
            'employee' => $employeeId
        ]);

        if (!$config) {
            $config = new TrackingConfiguration();
            $config->setConfigKey($key);
            $config->setConfigType($type);
            
            if ($employeeId && $type === 'employee') {
                // You'll need to fetch the employee entity here
                // $employee = $this->entityManager->getRepository(MasterEmployee::class)->find($employeeId);
                // $config->setEmployee($employee);
            }
            
            if ($description) {
                $config->setDescription($description);
            }
        }

        $config->setConfigValue($value);
        
        $this->getEntityManager()->persist($config);
        $this->getEntityManager()->flush();

        return $config;
    }

    /**
     * Get effective configuration for an employee (employee-specific overrides global)
     */
    public function getEffectiveConfiguration(?int $employeeId = null): array
    {
        // Get global configurations
        $globalConfigs = $this->getConfigurationsByType('global');
        $config = [];
        
        foreach ($globalConfigs as $globalConfig) {
            $config[$globalConfig->getConfigKey()] = $globalConfig->getConfigValue();
        }

        // Override with employee-specific configurations if employee ID provided
        if ($employeeId) {
            $employeeConfigs = $this->getConfigurationsByType('employee', $employeeId);
            foreach ($employeeConfigs as $employeeConfig) {
                $config[$employeeConfig->getConfigKey()] = $employeeConfig->getConfigValue();
            }
        }

        return $config;
    }

    /**
     * Get configurations that have been updated since a specific date
     */
    public function getUpdatedSince(\DateTime $since, string $type = 'global', ?int $employeeId = null): array
    {
        $qb = $this->createQueryBuilder('tc')
            ->where('tc.configType = :type')
            ->andWhere('tc.isActive = true')
            ->andWhere('tc.updatedAt > :since')
            ->setParameter('type', $type)
            ->setParameter('since', $since)
            ->orderBy('tc.updatedAt', 'DESC');

        if ($employeeId && $type === 'employee') {
            $qb->andWhere('tc.employee = :employeeId')
               ->setParameter('employeeId', $employeeId);
        }

        return $qb->getQuery()->getResult();
    }

    /**
     * Bulk update configurations
     */
    public function bulkUpdateConfigurations(array $configurations, string $type = 'global', ?int $employeeId = null): void
    {
        $em = $this->getEntityManager();
        
        foreach ($configurations as $key => $value) {
            $config = $this->findOneBy([
                'configKey' => $key,
                'configType' => $type,
                'employee' => $employeeId
            ]);

            if (!$config) {
                $config = new TrackingConfiguration();
                $config->setConfigKey($key);
                $config->setConfigType($type);
                
                if ($employeeId && $type === 'employee') {
                    // You'll need to fetch the employee entity here
                    // $employee = $em->getRepository(MasterEmployee::class)->find($employeeId);
                    // $config->setEmployee($employee);
                }
            }

            $config->setConfigValue((string) $value);
            $em->persist($config);
        }

        $em->flush();
    }

    /**
     * Reset configurations to default values
     */
    public function resetToDefaults(): void
    {
        $defaults = $this->getDefaultConfigurations();
        $this->bulkUpdateConfigurations($defaults, 'global');
    }

    /**
     * Get default configuration values
     */
    private function getDefaultConfigurations(): array
    {
        return [
            // Idle Detection (Simplified)
            'idle_timeout_enabled' => '1',
            'idle_timeout_minutes' => '5', // 5 minutes default
            
            // Time Tracking
            'silent_mode_enabled' => '1',
            'auto_start_tracking' => '1',
            'max_daily_hours' => '8',
            'min_session_duration' => '60', // 1 minute
            'max_session_duration' => '14400', // 4 hours
            'working_hours_start' => '9',
            'working_hours_end' => '18',
            
            // Screenshots
            'screenshot_enabled' => '1',
            'screenshot_interval_seconds' => '300', // 5 minutes
            'screenshot_quality' => '80',
            'screenshot_burst_mode' => '0',
            'screenshot_random_interval' => '1',
            

            
            // Application Tracking
            'app_tracking_enabled' => '1',
            'track_window_titles' => '1',
            'productivity_categorization' => '1',
            
            // Website Tracking
            'website_tracking_enabled' => '1',
            'track_browser_tabs' => '1',
            'website_categorization' => '1',
            
            // Productivity
            'productivity_analysis_enabled' => '1',
            'break_reminder_interval' => '60', // 1 hour
            'focus_time_threshold' => '25' // 25 minutes
        ];
    }

    /**
     * Get category for a configuration key
     */
    private function getCategoryForKey(string $key): string
    {
        $categoryMap = [
            // Idle Detection
            'thinking_pause_threshold' => 'idle_detection',
            'short_idle_threshold' => 'idle_detection',
            'long_idle_threshold' => 'idle_detection',
            'inactive_threshold' => 'idle_detection',
            'enable_smart_detection' => 'idle_detection',
            'enable_working_hours_adjustment' => 'idle_detection',
            'weekend_idle_multiplier' => 'idle_detection',
            
            // Time Tracking
            'silent_mode_enabled' => 'time_tracking',
            'auto_start_tracking' => 'time_tracking',
            'max_daily_hours' => 'time_tracking',
            'min_session_duration' => 'time_tracking',
            'max_session_duration' => 'time_tracking',
            'working_hours_start' => 'time_tracking',
            'working_hours_end' => 'time_tracking',
            
            // Screenshots
            'screenshot_enabled' => 'screenshots',
            'screenshot_interval_seconds' => 'screenshots',
            'screenshot_quality' => 'screenshots',
            'screenshot_burst_mode' => 'screenshots',
            'screenshot_random_interval' => 'screenshots',
            

            
            // Application Tracking
            'app_tracking_enabled' => 'application_tracking',
            'track_window_titles' => 'application_tracking',
            'productivity_categorization' => 'application_tracking',
            
            // Website Tracking
            'website_tracking_enabled' => 'website_tracking',
            'track_browser_tabs' => 'website_tracking',
            'website_categorization' => 'website_tracking',
            
            // Productivity
            'productivity_analysis_enabled' => 'productivity',
            'break_reminder_interval' => 'productivity',
            'focus_time_threshold' => 'productivity'
        ];
        
        return $categoryMap[$key] ?? 'general';
    }
}
