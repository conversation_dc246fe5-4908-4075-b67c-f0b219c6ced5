<?php

namespace App\Service\Admin;

use App\Entity\FormTemplate;
use App\Helper\ModalboxHelper;
use Doctrine\ORM\QueryBuilder;
use Omines\DataTablesBundle\Adapter\Doctrine\ORMAdapter;
use Omines\DataTablesBundle\Column\TextColumn;
use Omines\DataTablesBundle\DataTable;
use Omines\DataTablesBundle\DataTableFactory;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Request;
use App\Traits\ExceptionLoggerTrait;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Security\Csrf\CsrfTokenManagerInterface;

class FormTemplateService
{
    use ExceptionLoggerTrait;

    public function __construct(
        private readonly DataTableFactory          $dataTableFactory,
        private readonly RouterInterface           $router,
        ParameterBagInterface                      $parameterBag,
        private readonly CsrfTokenManagerInterface $csrfTokenManager,
        private readonly ModalboxHelper            $modalboxHelper
    )
    {
        $this->initializeLogger($parameterBag, 'FormTemplate_Table_service');
    }

    public function createMasterFormTemplateTable(Request $request): DataTable
    {
        $dataTable = $this->dataTableFactory->create();

        foreach ($this->getColumns() as $key => $column) {
            $dataTable->add($key, $column['className'], array_filter($column, fn($k) => $k !== 'className', ARRAY_FILTER_USE_KEY));
        }

        return $dataTable->createAdapter(ORMAdapter::class, [
            'entity' => FormTemplate::class,
            'query' => fn(QueryBuilder $qb) => $this->buildQuery($qb),
        ])->handleRequest($request);
    }

    private function getColumns(): array
    {
        return [
            'name' => [
                'label' => 'Template Name',
                'field' => 'f.name',
                'className' => TextColumn::class,
            ],
            'frequency' => [
                'label' => 'Frequency',
                'field' => 'f.frequency',
                'className' => TextColumn::class,
            ],
            'status' => [
                'label' => 'Status',
                'field' => 'f.status',
                'className' => TextColumn::class,
                'render' => fn($value) => $this->renderStatusBadge($value),
            ],
            'actions' => [
                'label' => 'Actions',
                'field' => 'f.id',
                'className' => TextColumn::class,
                'orderable' => false,
                'render' => fn($value, $context) => $this->renderActionColumn($context),
            ],
        ];
    }

    private function renderStatusBadge(bool $status): string
    {
        return $status
            ? '<span class="badge bg-success">Active</span>'
            : '<span class="badge bg-danger">Inactive</span>';
    }

    private function renderActionColumn(FormTemplate $formTemplate): string
    {
        $entityId = $formTemplate->getId();
        $editUrl = $this->router->generate('form_template_edit', ['id' => $entityId]);
        $viewUrl = $this->router->generate('form_template_view', ['id' => $entityId]);
        $toggleStatusUrl = $this->router->generate('form_template_toggle_status', ['id' => $entityId]);
        $csrfToken = $this->csrfTokenManager->getToken('toggle-status' . $entityId)->getValue();
        $sectionsUrl = $this->router->generate('form_section_index', ['template_id' => $entityId]);
        $previewUrl = $this->router->generate('fill_preview', ['templateId' => $entityId]);
        $deleteUrl = $this->router->generate('form_template_delete', ['id' => $entityId]);
        $deleteToken = $this->csrfTokenManager->getToken('delete' . $entityId)->getValue();

        $isActive = $formTemplate->isStatus();

        $hasSectionsWithFields = $formTemplate->getSections()
                ->filter(fn($section) =>
                    $section->isStatus() &&
                    !$section->getIsDelete() &&
                    $section->getFields()
                        ->filter(fn($field) => $field->isVisible() && !$field->getIsDelete())
                        ->count() > 0
                )
                ->count() > 0;

        $modalId = 'deleteModal_' . $entityId;
        $modalBody = sprintf(
            'Are you sure you want to delete the template "%s"?<br><br>' .
            '<strong>Warning:</strong> This action will also delete all connected sections and fields associated with this template. This action cannot be undone.',
            htmlspecialchars($formTemplate->getName())
        );

        $footerButtons = sprintf(
            '<button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>' .
            '<button type="button" class="btn btn-primary delete-form-template-btn"  data-delete="true" data-url="%s" data-token="%s">Delete</button>',
            $deleteUrl,
            $deleteToken
        );

        $deleteModal = $this->modalboxHelper->renderModal(
            $modalId,
            'Confirm Template Deletion',
            $modalBody,
            $footerButtons
        );

        return sprintf(
            '<div class="d-inline-flex gap-2">
            <a href="%s" class="bs-btn btn btn-primary btn-sm bs-m" title="View"><i class="bs-fa fa fa-eye"></i></a>
            <a href="%s" class="bs-btn btn btn-primary btn-sm bs-m" title="Edit"><i class="bs-fa fa fa-edit"></i></a>
            <button class="bs-btn btn btn-primary btn-sm bs-m toggle-status-btn" 
                    data-id="%s" 
                    data-token="%s" 
                    data-url="%s" 
                    title="Toggle Status">
                <i class="bs-fa fa fa-toggle-on"></i>
            </button>
            <a href="%s" class="bs-btn btn btn-primary btn-sm bs-m" title="Manage Sections"><i class="bs-fa fa fa-list"></i></a>
            %s
            <button type="button" class="bs-btn btn btn-primary btn-sm bs-m" data-toggle="modal" data-target="#%s" title="Deactivate">
                <i class="bs-fa fa fa-trash"></i>
            </button>
        </div>%s',
            $viewUrl,
            $editUrl,
            $entityId,
            $csrfToken,
            $toggleStatusUrl,
            $sectionsUrl,
            $isActive && $hasSectionsWithFields ? sprintf(
                '<a href="%s" class="bs-btn btn btn-primary btn-sm bs-m" title="Preview"><i class="bs-fa fa fa-street-view"></i></a>',
                $previewUrl
            ) : '',
            $modalId,
            $deleteModal
        );
    }

    private function buildQuery(QueryBuilder $qb): QueryBuilder
    {
        return $qb->select('f')
            ->from(FormTemplate::class, 'f')
            ->where('f.isDelete = :isDeleted')
            ->setParameter('isDeleted', false);
    }
}