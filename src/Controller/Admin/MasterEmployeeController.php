<?php

namespace App\Controller\Admin;

use App\Entity\EmployeeReportsTo;
use App\Entity\MasterEmployee;
use App\Form\MasterEmployeeType;
use App\Service\Admin\MasterEmployeeService;
use App\Traits\ExceptionLoggerTrait;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Csrf\CsrfToken;
use Symfony\Component\Security\Csrf\CsrfTokenManagerInterface;
use App\Repository\PayrollRepository;

#[Route('/admin')]
class MasterEmployeeController extends AbstractController
{
    use ExceptionLoggerTrait;

    public function __construct(
        ParameterBagInterface                   $parameterBag,
        private readonly EntityManagerInterface $entityManager,
        private readonly MasterEmployeeService $masterEmployeeService,
    )
    {
        $this->initializeLogger($parameterBag, 'employee_controller');
    }

    #[Route(path: '/master-employee', name: 'master_employee', methods: ['GET', 'POST'])]
    public function index(Request $request): Response
    {
        $table = $this->masterEmployeeService->createMasterEmployeeTable($request);

        if ($table->isCallback()) {
            return $table->getResponse();
        }

        return $this->render('admin/MasterEmployee/index.html.twig', [
            'datatable' => $table,
        ]);
    }

    #[Route('/master-employee/create', name: 'master-employee_create', methods: ['GET','POST'])]
    public function createMasterEmployee(Request $request, UserPasswordHasherInterface $passwordHasher): Response
    {
        $employee = new MasterEmployee();
        $form = $this->createForm(MasterEmployeeType::class, $employee);

        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $hashedPassword = $passwordHasher->hashPassword($employee, 'Brainee@123');
            $employee->setPassword($hashedPassword);
            $employee->setRoles(['ROLE_EMPLOYEE', 'ROLE_USER']);
            $employee->setTeamLeader($form->get('teamLeader')->getData() ?? false);
            $selectedTeamLeaders = $form->get('reportsTo')->getData();
            if ($selectedTeamLeaders) {
                foreach ($selectedTeamLeaders as $teamLeader) {
                    $employeeReportsTo = new EmployeeReportsTo();
                    $employeeReportsTo->setEmployee($employee);
                    $employeeReportsTo->setTeamLeader($teamLeader);

                    $employee->addReportsTo($employeeReportsTo);
                    $this->entityManager->persist($employeeReportsTo);
                }
            }

            $this->entityManager->persist($employee);
            $this->entityManager->flush();

            $this->addFlash('success', 'Employee created successfully.');
            return $this->redirectToRoute('master_employee');
        }

        return $this->render('admin/MasterEmployee/new.html.twig', [
            'form' => $form->createView(),
            'mode' => 'create',
        ]);
    }

    #[Route('/master-employee/edit/{id}', name: 'master_employee_edit', methods: ['GET', 'POST'])]
    public function editMasterEmployee(
        Request $request,
        MasterEmployee $employee,
    ): Response {
        $originalTeamLeaderStatus = $employee->isTeamLeader();
        $existingTeamLeaders = $employee->getReportsTo()
            ->map(function($reportsTo) {
                $teamLeader = $reportsTo->getTeamLeader();
                return ($teamLeader->getIsDelete() || !$teamLeader->isTeamLeader()) ? null : $teamLeader;
            })
            ->filter(function($teamLeader) {
                return $teamLeader !== null;
            })
            ->toArray();
        $form = $this->createForm(MasterEmployeeType::class, $employee);
        $form->get('reportsTo')->setData($existingTeamLeaders);

        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $newTeamLeaderStatus = $form->get('teamLeader')->getData() ?? false;
            if ($originalTeamLeaderStatus && !$newTeamLeaderStatus) {
                $reportsToReferences = $this->entityManager->getRepository(EmployeeReportsTo::class)
                    ->findBy(['teamLeader' => $employee]);
                foreach ($reportsToReferences as $reference) {
                    $relatedEmployee = $reference->getEmployee();
                    if ($relatedEmployee) {
                        $relatedEmployee->removeReportsTo($reference);
                    }
                    $this->entityManager->remove($reference);
                }
            }
            $existingReportsTo = $employee->getReportsTo();
            foreach ($existingReportsTo as $reportsTo) {
                $employee->removeReportsTo($reportsTo);
                $this->entityManager->remove($reportsTo);
            }
            $selectedTeamLeaders = $form->get('reportsTo')->getData();
            if ($selectedTeamLeaders) {
                foreach ($selectedTeamLeaders as $teamLeader) {
                    $employeeReportsTo = new EmployeeReportsTo();
                    $employeeReportsTo->setEmployee($employee);
                    $employeeReportsTo->setTeamLeader($teamLeader);

                    $employee->addReportsTo($employeeReportsTo);
                    $this->entityManager->persist($employeeReportsTo);
                }
            }

            $this->entityManager->flush();
            $this->addFlash('success', 'Employee updated successfully.');
            return $this->redirectToRoute('master_employee');
        }

        return $this->render('admin/MasterEmployee/new.html.twig', [
            'form' => $form->createView(),
            'employee' => $employee,
            'mode' => 'edit',
        ]);
    }
    #[Route('/master-employee/delete/{id}', name: 'master_employee_delete', methods: ['POST'])]
    public function deleteMasterEmployee(
        Request $request,
        MasterEmployee $employee,
        CsrfTokenManagerInterface $csrfTokenManager,
        EntityManagerInterface $entityManager
    ): Response {
        $token = $request->request->get('_token');

        if (!$csrfTokenManager->isTokenValid(new CsrfToken('delete' . $employee->getId(), $token))) {
            return $this->json([
                'success' => false,
                'message' => 'Invalid CSRF token.'
            ], Response::HTTP_FORBIDDEN);
        }

        if ($employee->isTeamLeader()) {
            $reportsToReferences = $entityManager->getRepository(EmployeeReportsTo::class)
                ->findBy(['teamLeader' => $employee]);
            foreach ($reportsToReferences as $reference) {
                $relatedEmployee = $reference->getEmployee();
                if ($relatedEmployee) {
                    $relatedEmployee->removeReportsTo($reference);
                }
                $entityManager->remove($reference);
            }
        }

        $employee->setIsDelete(true);
        $entityManager->persist($employee);
        $entityManager->flush();

        return $this->json([
            'success' => true,
            'message' => 'Employee marked as deleted.'
        ], Response::HTTP_OK);
    }

    #[Route('/master-employee/view/{id}', name: 'master_employee_view', methods: ['GET'])]
    public function show(MasterEmployee $employee, PayrollRepository $payrollRepository): Response
    {
        $payrolls = $payrollRepository->findByEmployeeOrderedByYearMonth($employee->getId());
        return $this->render('admin/MasterEmployee/new.html.twig', [
            'employee' => $employee,
            'mode' => 'view',
            'payrolls' => $payrolls,
        ]);
    }
    #[Route('/master-employee/check-field', name: 'master_employee_check_field', methods: ['POST'])]
    public function checkField(Request $request): JsonResponse
    {
        $field = $request->request->get('field');
        $value = $request->request->get('value');
        $currentId = $request->request->get('currentId');
        $criteria = [$field => $value];
        $employee = $this->entityManager->getRepository(MasterEmployee::class)->findOneBy($criteria);
        $isUnique = !$employee || ($currentId && $employee->getId() == $currentId);

        return new JsonResponse(['isUnique' => $isUnique]);
    }
}