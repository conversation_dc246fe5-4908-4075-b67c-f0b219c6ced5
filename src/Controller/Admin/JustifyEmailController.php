<?php

namespace App\Controller\Admin;

use App\Entity\EmployeeNotification;
use App\Entity\EmployeeRemainingHours;
use App\Helper\TimeHelper;
use App\Message\EmployeeNotificationMessage;
use App\Service\Admin\JustifyService;
use App\Traits\ExceptionLoggerTrait;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\Session\SessionInterface;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Messenger\MessageBusInterface;
use App\Helper\SettingHelper;

#[Route('/admin')]
class JustifyEmailController extends AbstractController
{
    use ExceptionLoggerTrait;

    public function __construct(
        private readonly JustifyService  $justifyService,
        ParameterBagInterface            $parameterBag,
        private readonly EntityManagerInterface $entityManager,
        private readonly MessageBusInterface $messageBus,
        private readonly SettingHelper $settingHelper,
    )
    {
        $this->initializeLogger($parameterBag, 'employee_controller');
    }

    /**
     * @param Request $request
     * @return Response
     */
    #[Route(path: '/report', name: 'report', methods: ['GET', 'POST'])]
    public function index(Request $request , SessionInterface $session): Response
    {
        $table = $this->justifyService->createEmployeeNotificationTable($request);

        if ($table->isCallback()) {
            return $table->getResponse();
        }

        if (!$request->query->get('keep_filters')) {
            $session->remove('report_filters');
        }
        return $this->render('admin/justifyemail.html.twig', [
            'datatable' => $table,
            'filters' => [],
        ]);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    #[Route('/report/summary', name: 'report_summary', methods: ['POST'])]
    public function fetchjustifyReport(Request $request): JsonResponse
    {
        try {
            $data = json_decode($request->getContent(), true);
            if (!is_array($data) || !isset($data['startDate'], $data['endDate'])) {
                throw new \InvalidArgumentException('Invalid input data');
            }

            $startDate = new \DateTime($data['startDate']);
            $endDate = new \DateTime($data['endDate']);
            $request->getSession()->set('report_filters', [
                'startDate' => $startDate->format('Y-m-d'),
                'endDate' => $endDate->format('Y-m-d'),
            ]);

            return new JsonResponse([
                'success' => true,
                'message' => 'Filters updated successfully.',
            ]);
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'message' => 'Error: ' . $e->getMessage(),
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws \DateMalformedStringException
     */
    #[Route('/report/filters', name: 'report_filters', methods: ['GET'])]
    public function getFilters(Request $request): JsonResponse
    {
        $filters = $request->getSession()->get('report_filters', []);

        $startDate = new \DateTime($filters['startDate'] ?? 'now');
        $endDate = new \DateTime($filters['endDate'] ?? 'now');
        $dateRange = $startDate->format('d-m-Y') . ' - ' . $endDate->format('d-m-Y');
        return new JsonResponse([
            'success' => true,
            'filters' => [
                'dateRange' => $dateRange,
                'employeeId' => $filters['employeeId'] ?? null,
            ],
        ]);
    }

    #[Route('/report/employee-notification/approve/{id}', name: 'employee_notification_approve', methods: ['POST'])]
    public function approve(Request $request, EmployeeNotification $notification): JsonResponse
    {
        try {
            $notification->setIsApproved(true);
            $notification->setDisapprovalReason(null);
            $this->entityManager->flush();

            return new JsonResponse([
                'success' => true,
                'message' => 'Justification approved successfully.'
            ]);
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'message' => 'Error approving justification: ' . $e->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    #[Route('/report/employee-notification/disapprove/{id}', name: 'employee_notification_disapprove', methods: ['POST'])]
    public function disapprove(Request $request, EmployeeNotification $notification): JsonResponse
    {
        try {
            $data = json_decode($request->getContent(), true, 512, JSON_THROW_ON_ERROR);
            $reason = $data['disapprovalReason'] ?? '';

            if (empty($reason)) {
                return new JsonResponse([
                    'success' => false,
                    'message' => 'Disapproval reason is required.'
                ], Response::HTTP_BAD_REQUEST);
            }

            $notification->setIsApproved(false);
            $notification->setDisapprovalReason($reason);
            $notification->setJustificationCount($notification->getJustificationCount() + 1);
            $justificationLimit = (int)$this->settingHelper->getSettingConfiguration()['remain'];
            $threshold = $this->settingHelper->getSettingConfiguration()['hours'];
            if ($notification->getJustificationCount() === $justificationLimit) {
                $employeeHours = $notification->getEmployeeHours();
                $remainingHours = max($threshold - $employeeHours->getTotalHours(), 0);
                $remainingHoursEntity = new EmployeeRemainingHours($notification, $remainingHours);
                $this->entityManager->persist($remainingHoursEntity);
            }
            $this->entityManager->flush();
            $employeeHours = $notification->getEmployeeHours();
            try {
                $message = new EmployeeNotificationMessage(
                    $employeeHours->getEmployee()->getId(),
                    $employeeHours->getTotalHours(),
                    $employeeHours->getId(),
                    $employeeHours->getReportDate()->format('Y-m-d'),
                    true,
                    $reason,
                    false,
                    null,
                );
                $this->messageBus->dispatch($message);
            }catch (\Exception $e) {
                $this->log('Notification dispatch failed: ' . $e->getMessage());
            }
            return new JsonResponse([
                'success' => true,
                'message' => 'Justification disapproved successfully.'
            ]);
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'message' => 'Error disapproving justification: ' . $e->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

}
