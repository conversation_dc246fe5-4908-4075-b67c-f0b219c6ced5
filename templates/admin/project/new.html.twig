{% extends 'admin/dashboard/base.html.twig' %}

{% block body %}
    {% include 'admin/partials/breadcrumbs.html.twig' with {
        title: 'Projects',
        breadcrumbs: [
            { 'label': 'Dashboard', 'url': url('admin_dashboard') },
            { 'label': 'Projects', 'url': url('project') }
        ],
        active: mode == 'view' ? 'View Project' : (mode == 'edit' ? 'Edit Project' : 'Create Project')
    } %}

    <div class="content">
        <div class="animated fadeIn">
            <div class="row">
                <div class="col-lg-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <strong class="card-title">
                                {{ mode == 'view' ? 'View Project' : (mode == 'edit' ? 'Edit Project' : 'Create Project') }}
                            </strong>
                            <div class="d-flex align-items-center">
                                <a href="{{ path('project') }}" class="btn btn-sm btn-outline-secondary mr-2">
                                    <i class="fa fa-list mr-1"></i>Back to List
                                </a>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="project-form">
                                {% if form is defined %}
                                    {{ form_start(form, { 'attr': { 'class': 'needs-validation use-loader', 'novalidate': 'novalidate', 'id': 'project-form-element' } }) }}

                                    <!-- General Information Section -->
                                    <div class="form-section">
                                        <h5 class="section-header">
                                            <i class="fa fa-info mr-2"></i>General Information
                                        </h5>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.name, 'Project Name', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-product-hunt"></i></span>
                                                        </div>
                                                        {{ form_widget(form.name, { 'attr': { 'class': 'form-control', 'required': 'required', 'id': 'project_name' } }) }}
                                                    </div>
                                                    {{ form_errors(form.name) }}
                                                    <small class="form-text text-muted">Name of the project</small>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.projectCode, 'Project Code', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-barcode"></i></span>
                                                        </div>
                                                        {{ form_widget(form.projectCode, { 'attr': { 'class': 'form-control', 'required': 'required', 'id': 'project_projectCode' } }) }}
                                                    </div>
                                                    {{ form_errors(form.projectCode) }}
                                                    <small class="form-text text-muted">Unique project code (e.g., PROJ-2025-001)</small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-12">
                                                <div class="mb-3">
                                                    {{ form_label(form.description, 'Description', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-info-circle"></i></span>
                                                        </div>
                                                        {{ form_widget(form.description, { 'attr': { 'class': 'form-control', 'id': 'project_description' } }) }}
                                                    </div>
                                                    {{ form_errors(form.description) }}
                                                    <small class="form-text text-muted">Brief description of the project</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Assignment Section -->
                                    <div class="form-section">
                                        <h5 class="section-header">
                                            <i class="fa fa-users mr-2"></i>Assignment
                                        </h5>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.department, 'Department', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-building"></i></span>
                                                        </div>
                                                        {{ form_widget(form.department, { 'attr': { 'class': 'form-control', 'required': 'required', 'id': 'project_department' } }) }}
                                                    </div>
                                                    {{ form_errors(form.department) }}
                                                    <small class="form-text text-muted">Department responsible for the project</small>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.projectManager, 'Project Manager', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-user-plus"></i></span>
                                                        </div>
                                                        {{ form_widget(form.projectManager, { 'attr': { 'class': 'form-control', 'required': 'required', 'id': 'project_projectManager' } }) }}
                                                    </div>
                                                    {{ form_errors(form.projectManager) }}
                                                    <small class="form-text text-muted">Employee managing the project</small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.bde, 'Business Development Executive', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-user"></i></span>
                                                        </div>
                                                        {{ form_widget(form.bde, { 'attr': { 'class': 'form-control', 'required': 'required', 'id': 'project_bde' } }) }}
                                                    </div>
                                                    {{ form_errors(form.bde) }}
                                                    <small class="form-text text-muted">Employee who brought the project</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Client Information Section -->
                                    <div class="form-section">
                                        <h5 class="section-header">
                                            <i class="fa fa-user-circle mr-2"></i>Client Information
                                        </h5>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.clientName, 'Client Name', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-user"></i></span>
                                                        </div>
                                                        {{ form_widget(form.clientName, { 'attr': { 'class': 'form-control', 'id': 'project_clientName' } }) }}
                                                    </div>
                                                    {{ form_errors(form.clientName) }}
                                                    <small class="form-text text-muted">Name of the client</small>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.clientContact, 'Client Contact', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-envelope"></i></span>
                                                        </div>
                                                        {{ form_widget(form.clientContact, { 'attr': { 'class': 'form-control', 'id': 'project_clientContact' } }) }}
                                                    </div>
                                                    {{ form_errors(form.clientContact) }}
                                                    <small class="form-text text-muted">Client's email or phone</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Project Details Section -->
                                    <div class="form-section">
                                        <h5 class="section-header">
                                            <i class="fa fa-cogs mr-2"></i>Project Details
                                        </h5>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.projectType, 'Project Type', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-tags"></i></span>
                                                        </div>
                                                        {{ form_widget(form.projectType, { 'attr': { 'class': 'form-control', 'required': 'required', 'id': 'project_projectType' } }) }}
                                                    </div>
                                                    {{ form_errors(form.projectType) }}
                                                    <small class="form-text text-muted">Type of project (e.g., Internal, External)</small>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.status, 'Status', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-check-circle"></i></span>
                                                        </div>
                                                        {{ form_widget(form.status, { 'attr': { 'class': 'form-control', 'required': 'required', 'id': 'project_status' } }) }}
                                                    </div>
                                                    {{ form_errors(form.status) }}
                                                    <small class="form-text text-muted">Current status of the project</small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.budgetCurrency, 'Budget Currency', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-money"></i></span>
                                                        </div>
                                                        {{ form_widget(form.budgetCurrency, { 'attr': { 'class': 'form-control', 'id': 'project_budgetCurrency' } }) }}
                                                    </div>
                                                    {{ form_errors(form.budgetCurrency) }}
                                                    <small class="form-text text-muted">Currency of the budget</small>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.budget, 'Budget', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-dollar"></i></span>
                                                        </div>
                                                        {{ form_widget(form.budget, { 'attr': { 'class': 'form-control', 'id': 'project_budget' } }) }}
                                                    </div>
                                                    {{ form_errors(form.budget) }}
                                                    <small class="form-text text-muted">Budget amount for the project</small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                        <div class="col-md-6">
                                        <div class="mb-3">
                                            {{ form_label(form.priority, 'Priority', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text"><i class="fa fa-exclamation"></i></span>
                                                </div>
                                                {{ form_widget(form.priority, { 'attr': { 'class': 'form-control', 'required': 'required', 'id': 'project_priority' } }) }}
                                            </div>
                                            {{ form_errors(form.priority) }}
                                            <small class="form-text text-muted">Priority level of the project</small>
                                        </div>
                                    </div>
                                        </div>
                                    </div>

                                    <!-- Timeline Section -->
                                    <div class="form-section">
                                        <h5 class="section-header">
                                            <i class="fa fa-calendar mr-2"></i>Timeline
                                        </h5>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.startDate, 'Start Date', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-calendar"></i></span>
                                                        </div>
                                                        {{ form_widget(form.startDate, {
                                                            'attr': {
                                                                'class': 'form-control datepicker',
                                                                'required': 'required',
                                                                'data-initial-date': project_form.startDate is defined and project_form.startDate ? project_form.startDate|date('d-m-Y') : ''
                                                            }
                                                        }) }}
                                                    </div>
                                                    {{ form_errors(form.startDate) }}
                                                    <small class="form-text text-muted">Project start date</small>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.endDate, 'End Date', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-calendar"></i></span>
                                                        </div>
                                                        {{ form_widget(form.endDate, {
                                                            'attr': {
                                                                'class': 'form-control datepicker',
                                                                'required': 'required',
                                                                'data-initial-date': project_form.endDate is defined and project_form.endDate ? project_form.endDate|date('d-m-Y') : ''
                                                            }
                                                        }) }}
                                                    </div>
                                                    {{ form_errors(form.endDate) }}
                                                    <small class="form-text text-muted">Planned end date</small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.deadline, 'Deadline', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-calendar"></i></span>
                                                        </div>
                                                        {{ form_widget(form.deadline, {
                                                            'attr': {
                                                                'class': 'form-control datepicker',
                                                                'required': 'required',
                                                                'data-initial-date': project_form.deadline is defined and project_form.deadline ? project_form.deadline|date('d-m-Y') : ''
                                                            }
                                                        }) }}
                                                    </div>
                                                    {{ form_errors(form.deadline) }}
                                                    <small class="form-text text-muted">Final project deadline</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="d-flex justify-content-end mt-4">
                                        <a href="{{ path('project') }}" class="btn btn-secondary mr-2">Cancel</a>
                                        <button type="submit" class="btn btn-primary pl-3 pr-3">
                                            {{ project.id is defined ? 'Update Project' : 'Create Project' }}
                                        </button>
                                    </div>

                                    {{ form_end(form) }}
                                {% else %}
                                    <!-- View Mode with Tabs -->
                                    <div class="profile-container">
                                        <div class="tab-content active" id="project-details">
                                            <div class="form-section project-details-section">
                                                <!-- General Information Section -->
                                                <div class="details-section">
                                                    <h5 class="section-header">
                                                        <i class="fa fa-info mr-2"></i>General Information
                                                    </h5>
                                                    <div class="details-container">
                                                        <div class="detail-row">
                                                            <div class="detail-item col-md-4"><strong class="detail-label">Project Name:</strong></div>
                                                            <div class="detail-item col-md-8">{{ project.name ?: 'N/A' }}</div>
                                                        </div>
                                                        <div class="detail-row">
                                                            <div class="detail-item col-md-4"><strong class="detail-label">Project Code:</strong></div>
                                                            <div class="detail-item col-md-8">{{ project.projectCode ?: 'N/A' }}</div>
                                                        </div>
                                                        <div class="detail-row">
                                                            <div class="detail-item col-md-4"><strong class="detail-label">Description:</strong></div>
                                                            <div class="detail-item col-md-8">{{ project.description ?: 'N/A' }}</div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Assignment Section -->
                                                <div class="details-section">
                                                    <h5 class="section-header">
                                                        <i class="fa fa-users mr-2"></i>Assignment
                                                    </h5>
                                                    <div class="details-container">
                                                        <div class="detail-row">
                                                            <div class="detail-item col-md-4"><strong class="detail-label">Department:</strong></div>
                                                            <div class="detail-item col-md-8">{{ project.department ? project.department.depName : 'N/A' }}</div>
                                                        </div>
                                                        <div class="detail-row">
                                                            <div class="detail-item col-md-4"><strong class="detail-label">Project Manager:</strong></div>
                                                            <div class="detail-item col-md-8">{{ project.projectManager ? project.projectManager.name : 'N/A' }}</div>
                                                        </div>
                                                        <div class="detail-row">
                                                            <div class="detail-item col-md-4"><strong class="detail-label">Business Development Executive:</strong></div>
                                                            <div class="detail-item col-md-8">{{ project.bde ? project.bde.name : 'N/A' }}</div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Client Information Section -->
                                                <div class="details-section">
                                                    <h5 class="section-header">
                                                        <i class="fa fa-user-circle mr-2"></i>Client Information
                                                    </h5>
                                                    <div class="details-container">
                                                        <div class="detail-row">
                                                            <div class="detail-item col-md-4"><strong class="detail-label">Client Name:</strong></div>
                                                            <div class="detail-item col-md-8">{{ project.clientName ?: 'N/A' }}</div>
                                                        </div>
                                                        <div class="detail-row">
                                                            <div class="detail-item col-md-4"><strong class="detail-label">Client Contact:</strong></div>
                                                            <div class="detail-item col-md-8">{{ project.clientContact ?: 'N/A' }}</div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Project Details Section -->
                                                <div class="details-section">
                                                    <h5 class="section-header">
                                                        <i class="fa fa-cogs mr-2"></i>Project Details
                                                    </h5>
                                                    <div class="details-container">
                                                        <div class="detail-row">
                                                            <div class="detail-item col-md-4"><strong class="detail-label">Project Type:</strong></div>
                                                            <div class="detail-item col-md-8">{{ project.projectType ?: 'N/A' }}</div>
                                                        </div>
                                                        <div class="detail-row">
                                                            <div class="detail-item col-md-4"><strong class="detail-label">Status:</strong></div>
                                                            <div class="detail-item col-md-8">{{ project.status ?: 'N/A' }}</div>
                                                        </div>
                                                        <div class="detail-row">
                                                            <div class="detail-item col-md-4"><strong class="detail-label">Priority:</strong></div>
                                                            <div class="detail-item col-md-8">{{ project.priority ?: 'N/A' }}</div>
                                                        </div>
                                                        <div class="detail-row">
                                                            <div class="detail-item col-md-4"><strong class="detail-label">Budget:</strong></div>
                                                            <div class="detail-item col-md-8">{{ project.budget ?: 'N/A' }}</div>
                                                        </div>
                                                        <div class="detail-row">
                                                            <div class="detail-item col-md-4"><strong class="detail-label">Budget Currency :</strong></div>
                                                            <div class="detail-item col-md-8">{{ project.budgetCurrency ?: 'N/A' }}</div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Timeline Section -->
                                                <div class="details-section">
                                                    <h5 class="section-header">
                                                        <i class="fa fa-calendar mr-2"></i>Timeline
                                                    </h5>
                                                    <div class="details-container">
                                                        <div class="detail-row">
                                                            <div class="detail-item col-md-4"><strong class="detail-label">Start Date:</strong></div>
                                                            <div class="detail-item col-md-8">{{ project.startDate ? project.startDate| custom_date : 'N/A' }}</div>
                                                        </div>
                                                        <div class="detail-row">
                                                            <div class="detail-item col-md-4"><strong class="detail-label">End Date:</strong></div>
                                                            <div class="detail-item col-md-8">{{ project.endDate ? project.endDate|custom_date : 'N/A' }}</div>
                                                        </div>
                                                        <div class="detail-row">
                                                            <div class="detail-item col-md-4"><strong class="detail-label">Deadline:</strong></div>
                                                            <div class="detail-item col-md-8">{{ project.deadline ? project.deadline|custom_date : 'N/A' }}</div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Metadata Section -->
                                                <div class="details-section">
                                                    <h5 class="section-header">
                                                        <i class="fa fa-info mr-2"></i>Metadata
                                                    </h5>
                                                    <div class="details-container">
                                                        <div class="detail-row">
                                                            <div class="detail-item col-md-4"><strong class="detail-label">Created By:</strong></div>
                                                            <div class="detail-item col-md-8">{{ project.createdBy ? project.createdBy.username : 'N/A' }}</div>
                                                        </div>
                                                        <div class="detail-row">
                                                            <div class="detail-item col-md-4"><strong class="detail-label">Created At:</strong></div>
                                                            <div class="detail-item col-md-8">{{ project.createdAt ? project.createdAt|custom_date : 'N/A' }}</div>
                                                        </div>
                                                        <div class="detail-row">
                                                            <div class="detail-item col-md-4"><strong class="detail-label">Updated At:</strong></div>
                                                            <div class="detail-item col-md-8">{{ project.updatedAt ? project.updatedAt|custom_date  : 'N/A' }}</div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="action-footer mt-4">
                                            <a href="{{ path('project') }}" class="btn btn-secondary mr-2">
                                                Back to List
                                            </a>
                                            <a href="{{ path('project_edit', {'id': project.id}) }}" class="btn btn-primary pr-3 pl-3" style="padding-top: 10px;">
                                                Edit Project
                                            </a>
                                        </div>
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="clearfix"></div>
    <link rel="stylesheet" href="{{ asset('assets/css/Error/error.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/AdminForm/Form.css') }}">
    <script src="{{ asset('assets/js/datatables.min.js') }}"></script>
    <script src="{{ asset('bundles/datatables/js/datatables.js') }}"></script>
    <script src="{{ asset('assets/js/moment.min.js') }}"></script>
    <script src="{{ asset('assets/js/daterangepicker.min.js') }}"></script>
    <script src="{{ asset('assets/js/commonDatepicker.js') }}"></script>
    <script>
        const currentProjectId = "{{ project.id is defined ? project.id : '' }}";
    </script>
    <script src="{{ asset('assets/js/admin/project.js') }}"></script>
{% endblock %}