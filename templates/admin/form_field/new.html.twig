{% extends 'admin/dashboard/base.html.twig' %}

{% block title %}Form Fields - {{ template.name }}{% endblock %}
{% block body %}
    {% include 'admin/partials/breadcrumbs.html.twig' with {
        title: 'Forms',
        breadcrumbs: [
            { 'label': 'Dashboard', 'url': url('admin_dashboard') },
            { 'label': 'Performance Feedback', 'url': path('form_template') },
            { 'label': template.name ~ ' Sections', 'url': path('form_section_index', { 'template_id': template.id }) },
            { 'label': section.name ~ ' Section', 'url': path('form_field_index', { 'template_id': template.id, 'sections_id': section.id }) }
        ],
        active: field.id is defined ? 'Edit Form Field' : 'Create Form Field'
    } %}

    <div class="content">
        <div class="animated fadeIn">
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <strong class="card-title">
                                {{ field.id is defined ? 'Edit Form Field' : 'Create Form Field' }}
                            </strong>
                            <div class="d-flex align-items-center">
                                <a href="{{ path('form_field_index', { 'template_id': template.id, 'sections_id': section.id }) }}" class="btn btn-sm btn-outline-secondary mr-2">
                                    <i class="fa fa-list mr-1"></i>Back to List
                                </a>
                                {% if field.id is defined and form is not defined %}
                                    <a href="{{ path('form_field_edit', { 'id': field.id, 'template_id': template.id, 'sections_id': section.id }) }}" class="btn btn-sm btn-primary">
                                        <i class="fa fa-edit mr-1"></i>Edit Form Field
                                    </a>
                                {% endif %}
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="field-form">
                                {{ form_start(form, { 'attr': { 'class': 'needs-validation use-loader', 'novalidate': 'novalidate' } }) }}
                                <div class="form-section">
{#                                    <h5 class="section-header">#}
{#                                        <i class="fa fa-question-circle mr-2"></i>Form Field Information#}
{#                                    </h5>#}
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                {{ form_label(form.fieldLabel, 'Field Label', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text"><i class="fa fa-tag"></i></span>
                                                    </div>
                                                    {{ form_widget(form.fieldLabel, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter field label', 'required': 'required' } }) }}
                                                </div>
                                                {{ form_errors(form.fieldLabel) }}
                                                <small class="form-text text-muted">Enter a label for the field</small>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                {{ form_label(form.fieldOrder, 'Field Order', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text"><i class="fa fa-sort-numeric-asc"></i></span>
                                                    </div>
                                                    {{ form_widget(form.fieldOrder, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter order number' } }) }}
                                                </div>
                                                {{ form_errors(form.fieldOrder) }}
                                                <small class="form-text text-muted">Set the display order of the field</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                {{ form_label(form.fieldType, 'Field Type', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text"><i class="fa fa-cog"></i></span>
                                                    </div>
                                                    {{ form_widget(form.fieldType, { 'attr': { 'class': 'form-control', 'aria-label': 'Select Field Type' } }) }}
                                                </div>
                                                {{ form_errors(form.fieldType) }}
                                                <small class="form-text text-muted">Select the type of field (e.g., Text, Select)</small>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                {{ form_label(form.placeholder, 'Placeholder', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text"><i class="fa fa-info"></i></span>
                                                    </div>
                                                    {{ form_widget(form.placeholder, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter placeholder' } }) }}
                                                </div>
                                                {{ form_errors(form.placeholder) }}
                                                <small class="form-text text-muted">Enter a placeholder text for the field</small>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">Field Options</label>
                                                <div id="options-container">
                                                    {% if form.fieldOptions.vars.data is defined and form.fieldOptions.vars.data is not empty %}
                                                        {% if form.fieldOptions.vars.data.options is defined and form.fieldOptions.vars.data.options|length > 0 %}
                                                            {% for option in form.fieldOptions.vars.data.options %}
                                                                <div class="d-flex mb-2 option-row">
                                                                    <input type="text" class="form-control me-2" placeholder="Option" value="{{ option.label }}" oninput="updateJson()">
                                                                    <button type="button" class="btn btn-danger" onclick="removeOption(this)">Remove</button>
                                                                </div>
                                                            {% endfor %}
                                                        {% else %}
                                                            <div class="d-flex mb-2 option-row">
                                                                <input type="text" class="form-control me-2" placeholder="Option" oninput="updateJson()">
                                                                <button type="button" class="btn btn-danger" onclick="removeOption(this)">Remove</button>
                                                            </div>
                                                        {% endif %}
                                                    {% else %}
                                                        <div class="d-flex mb-2 option-row">
                                                            <input type="text" class="form-control me-2" placeholder="Option" oninput="updateJson()">
                                                            <button type="button" class="btn btn-danger" onclick="removeOption(this)">Remove</button>
                                                        </div>
                                                    {% endif %}
                                                </div>
                                                <button type="button" class="btn btn-sm btn-secondary" onclick="addOption()"><i class="fa fa-plus"></i> Add</button>
                                                <div class="hidden-field-container">
                                                    {{ form_widget(form.fieldOptions, { 'attr': { 'id': 'fieldOptionsJson' } }) }}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                {{ form_label(form.isRequired, 'Required', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                            <span class="input-group-text">
                                                                {{ form_widget(form.isRequired, { 'attr': { 'class': 'custom-checkbox', 'aria-label': 'Checkbox for Is Required' } }) }}
                                                            </span>
                                                    </div>
                                                    <div class="form-control checkbox-label">
                                                        {{ form_label(form.isRequired, null, { 'label_attr': { 'class': 'ml-2' } }) }}
                                                    </div>
                                                </div>
                                                {{ form_errors(form.isRequired) }}
                                                <small class="form-text text-muted">Check if this field is mandatory</small>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                {{ form_label(form.isVisible, 'Active', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                            <span class="input-group-text">
                                                                {{ form_widget(form.isVisible, { 'attr': { 'class': 'custom-checkbox', 'aria-label': 'Checkbox for Is Visible' } }) }}
                                                            </span>
                                                    </div>
                                                    <div class="form-control checkbox-label">
                                                        {{ form_label(form.isVisible, null, { 'label_attr': { 'class': 'ml-2' } }) }}
                                                    </div>
                                                </div>
                                                {{ form_errors(form.isVisible) }}
                                                <small class="form-text text-muted">Check if this field is active to users</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                {{ form_label(form.defaultValue, 'Default Value', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text"><i class="fa fa-check-square-o"></i></span>
                                                    </div>
                                                    {{ form_widget(form.defaultValue, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter default value' } }) }}
                                                </div>
                                                {{ form_errors(form.defaultValue) }}
                                                <small class="form-text text-muted">Enter a default value for the field</small>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                {{ form_label(form.helpText, 'Help Text', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text"><i class="fa fa-question-circle"></i></span>
                                                    </div>
                                                    {{ form_widget(form.helpText, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter help text' } }) }}
                                                </div>
                                                {{ form_errors(form.helpText) }}
                                                <small class="form-text text-muted">Provide help text for users</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="action-footer mt-4">
                                    <a href="{{ path('form_field_index', { 'template_id': template.id, 'sections_id': section.id }) }}" class="btn btn-secondary mr-2">
                                        Cancel
                                    </a>
                                    <button type="submit" class="btn btn-primary pl-3 pr-3">
                                        {{ field.id is defined ? 'Update Form Field' : 'Create Form Field' }}
                                    </button>
                                </div>
                                {{ form_end(form) }}
                            </div>
                        </div>
                    </div> <!-- .card -->
                </div><!--/.col-->
            </div>
        </div>
    </div>
    <div class="clearfix"></div>
    <link rel="stylesheet" href="{{ asset('assets/css/Error/error.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/AdminForm/Form.css') }}">
    <script src="{{ asset('assets/js/admin/formField.js') }}"></script>
{% endblock %}