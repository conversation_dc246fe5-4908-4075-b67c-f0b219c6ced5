<?php

namespace App\Service\Admin;

use App\Entity\EmployeeRemainingHours;
use App\Helper\ModalboxHelper;
use Doctrine\ORM\QueryBuilder;
use Omines\DataTablesBundle\Adapter\Doctrine\ORMAdapter;
use Omines\DataTablesBundle\Column\BoolColumn;
use Omines\DataTablesBundle\Column\DateTimeColumn;
use Omines\DataTablesBundle\Column\TextColumn;
use Omines\DataTablesBundle\DataTable;
use Omines\DataTablesBundle\DataTableFactory;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Request;
use App\Traits\ExceptionLoggerTrait;
use App\Helper\TimeHelper;

class RemainingHoursService
{
    use ExceptionLoggerTrait;

    public function __construct(
        private readonly DataTableFactory $dataTableFactory,
        ParameterBagInterface             $parameterBag,
        private readonly TimeHelper       $timeHelper
    )
    {
        $this->initializeLogger($parameterBag, 'employee_notification_table_service');
    }

    public function createRemainingHoursTable(Request $request): DataTable
    {
        $dataTable = $this->dataTableFactory->create();

        foreach ($this->getColumns() as $key => $column) {
            $dataTable->add($key, $column['className'], array_filter($column, fn($k) => $k !== 'className'));
        }

        return $dataTable->createAdapter(ORMAdapter::class, [
            'entity' => EmployeeRemainingHours::class,
            'query' => fn(QueryBuilder $qb) => $this->buildQuery($qb),
        ])->handleRequest($request);
    }

    private function getColumns(): array
    {
        return [
            'name' => [
                'label' => 'Employee',
                'field' => 'me.name',
                'orderable' => true,
                'className' => TextColumn::class,
            ],
            'totalRemainingHours' => [
                'label' => 'Pending Hours',
                'field' => 'e.totalRemainingHours',
                'render' => fn($value) => $this->timeHelper->convertDecimalToTime($value),
                'className' => TextColumn::class,
            ],
            'completedAt' => [
                'label' => 'Completion Date',
                'field' => 'e.completedAt',
                'className' => DateTimeColumn::class,
                'format' => 'd-m-Y',
                'nullValue' => '-',
                'searchable' => false,
            ],
            'isApproved' => [
                'label' => 'Status',
                'field' => 'e.isApproved',
                'className' => BoolColumn::class,
                'render' => fn($isCompleted, $context) => $this->renderApprovedStatus($context),
            ],
            'approvedAt' => [
                'label' => 'Approval Date',
                'field' => 'e.approvedAt',
                'className' => DateTimeColumn::class,
                'format' => 'd-m-Y',
                'nullValue' => '-',
                'searchable' => false,
            ],
            'createdAt' => [
                'label' => 'Created At',
                'field' => 'e.createdAt',
                'className' => DateTimeColumn::class,
                'format' => 'd-m-Y',
                'searchable' => false,
            ],
            'isCompleted' => [
                'label' => 'Action',
                'field' => 'e.isCompleted',
                'className' => BoolColumn::class,
                'render' => fn($isCompleted, $context) => $this->renderCompletionStatus($context),
            ],
        ];
    }

    private function renderApprovedStatus($context): string
    {
        $isApproved = $context->isApproved();
        return $this->generateStatusBadge($isApproved);
    }

    private function renderCompletionStatus($context): string
    {
        $dailyHoursCompletion = $this->parseDailyHoursCompletion($context->getDailyHoursCompletion());
        $dailyHistoryCompletion = $this->parseDailyHistoryCompletion($context->getEmailContent());
        if (empty($dailyHoursCompletion)) {
            return '';
        }
        $entityId = $context->getEmployeeNotification()->getId();

        $tableRowsHtml = $this->generateDailyHoursTableRows($dailyHoursCompletion);
        $viewButton = $this->generateViewButton($entityId);

        $historyRowsHtml = $this->generateDailyHistoryTableRows($dailyHistoryCompletion);
        $historyButton = !empty($historyRowsHtml) ? $this->generateEmailHistoryButton($entityId) : '';

        $escapedEntityId = htmlspecialchars($entityId, ENT_QUOTES, 'UTF-8');

        $emailButton = '';
        if (!$context->isApproved()) {
            $emailButton = sprintf(
                '<button id="EmailContentButton%d" type="button" class="bs-btn-main btn btn-primary btn-sm" title="Email" data-toggle="modal" data-target="#EmailContentModal%s">
                <i class="bs-fa-main fa fa-envelope"></i>
            </button>',
                $entityId,
                $escapedEntityId
            );
        }

        $modalHtml = $this->generateModalHtml($entityId, $tableRowsHtml);
        $historyHtml = $this->generateHistoryModalHtml($entityId, $historyRowsHtml);
        $modalBody = $this->generateEmailBody($entityId);
        $footerButtons = sprintf(
            '<button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
             <button type="button" class="btn btn-primary" onclick="submitEmail(%d)">Send</button>',
            $entityId
        );
        $emailHtml = ModalBoxHelper::renderModal(
            "EmailContentModal{$escapedEntityId}",
            'Message',
            $modalBody,
            $footerButtons
        );
        return  $viewButton . $modalHtml . ' ' . $emailButton . $emailHtml . ' ' . $historyButton . $historyHtml;
    }

    private function parseDailyHoursCompletion($dailyHoursCompletion): array
    {
        if (is_string($dailyHoursCompletion)) {
            try {
                return json_decode($dailyHoursCompletion, true, 512, JSON_THROW_ON_ERROR);
            } catch (\JsonException $e) {
                return [];
            }
        }
        return is_array($dailyHoursCompletion) ? $dailyHoursCompletion : [];
    }

    private function parseDailyHistoryCompletion($dailyHistoryCompletion): array
    {
        if (is_string($dailyHistoryCompletion)) {
            try {
                return json_decode($dailyHistoryCompletion, true, 512, JSON_THROW_ON_ERROR);
            } catch (\JsonException $e) {
                return [];
            }
        }
        return is_array($dailyHistoryCompletion) ? $dailyHistoryCompletion : [];
    }

    private function generateDailyHoursTableRows(array $dailyHoursCompletion): string
    {
        $totalMinutes = 0;

        $rows = array_map(function ($entry) use (&$totalMinutes) {
            $date = !empty($entry['date']) ? date('d-m-Y', strtotime($entry['date'])) : 'N/A';
            $date = htmlspecialchars($date, ENT_QUOTES, 'UTF-8');
            $decimalHours =($entry['time'] ?? 0);
            $minutes = round($decimalHours * 60);
            $totalMinutes += $minutes;
            $timestamp = $this->timeHelper->convertDecimalToTime($decimalHours);
            return "<tr><td>$date</td><td>$timestamp</td></tr>";
        }, $dailyHoursCompletion);
        $totalHours =($totalMinutes / 60);
        $remainingMinutes = $totalMinutes % 60;
        $totalTimeFormatted = sprintf('%02d:%02d', $totalHours, $remainingMinutes);
        $rows[] = "<tr><td><strong>Total</strong></td><td><strong>$totalTimeFormatted</strong></td></tr>";

        return implode('', $rows);
    }
    private function generateDailyHistoryTableRows(array $dailyHistoryCompletion): string
    {
        $rows = array_map(function ($entry) {
            $content = htmlspecialchars($entry['content'] ?? '', ENT_QUOTES, 'UTF-8');
            $sentAt = !empty($entry['sent_at']) ? date('d-m-Y', strtotime($entry['sent_at'])) : 'N/A';
            $sentAt = htmlspecialchars($sentAt, ENT_QUOTES, 'UTF-8');
            return "<tr><td>$content</td><td>$sentAt</td></tr>";
        }, array_reverse($dailyHistoryCompletion));

        return implode('', $rows);
    }
    private function generateViewButton(string $entityId): string
    {
        return sprintf(
            '<button type="button" class="bs-btn-main btn btn-primary btn-sm" title="View" data-toggle="modal" data-target="#dailyHoursModal%s">
                <i class="bs-fa-main fa fa-eye"></i>
            </button>',
            $entityId
        );
    }
    private function generateEmailHistoryButton(string $entityId): string
    {
        return sprintf(
            '<button type="button" class="bs-btn-main btn btn-primary btn-sm" title="Email History" data-toggle="modal" data-target="#emailHistoryModal%s">
                <i class="bs-fa-main fa fa-file-text"></i>
            </button>',
            $entityId
        );
    }
    private function generateEmailBody(int $entityId): string
    {
        return sprintf(
            '
        <div class="form-group mt-3" id="customReasonDiv%s">
            <textarea class="form-control" id="emailContentBody%s" rows="3" placeholder="Enter message"></textarea>
        </div>',
            $entityId,
            $entityId
        );
    }

    private function generateModalHtml(string $entityId, string $tableRowsHtml): string
    {
        return ModalboxHelper::renderModal(
            "dailyHoursModal{$entityId}",
            'Daily Hours Completion',
            sprintf(
                '<table class="table table-bordered table-striped">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Time (hrs)</th>
                        </tr>
                    </thead>
                    <tbody>%s</tbody>
                </table>',
                $tableRowsHtml
            ),
            '<button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>'
        );
    }


    private function generateHistoryModalHtml(string $entityId, string $tableRowsHtml): string
    {
        if (empty($tableRowsHtml)) {
            return '';
        }
        return ModalboxHelper::renderModal(
            "emailHistoryModal{$entityId}",
            'Email History',
            sprintf(
                '<table class="table table-bordered table-striped">
                    <thead>
                        <tr>
                            <th>Content</th>
                            <th>Sent At</th>
                        </tr>
                    </thead>
                    <tbody>%s</tbody>
                </table>',
                $tableRowsHtml
            ),
            '<button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>'
        );
    }



    private function generateStatusBadge(bool $isCompleted): string
    {
        $status = $isCompleted
            ? '<span class="text-success fa-lg"><i class="fa fa-check"></i></span>'
            : '<span class="text-danger fa-lg"><i class="fa fa-times"></i></span>';
        return $status;
    }

    private function buildQuery(QueryBuilder $qb): void
    {
        $qb->select('e', 'n', 'emp','me')
            ->from(EmployeeRemainingHours::class, 'e')
            ->join('e.employeeNotification', 'n')
            ->join('n.employee', 'emp')
            ->join('emp.masterEmployee', 'me');
    }
}