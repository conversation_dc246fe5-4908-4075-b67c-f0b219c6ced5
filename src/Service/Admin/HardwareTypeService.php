<?php

namespace App\Service\Admin;

use App\Entity\HardwareType;
use App\Helper\ModalboxHelper;
use Doctrine\ORM\QueryBuilder;
use Omines\DataTablesBundle\Adapter\Doctrine\ORMAdapter;
use Omines\DataTablesBundle\Column\TextColumn;
use Omines\DataTablesBundle\DataTable;
use Omines\DataTablesBundle\DataTableFactory;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Request;
use App\Traits\ExceptionLoggerTrait;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Security\Csrf\CsrfTokenManagerInterface;

class HardwareTypeService
{
    use ExceptionLoggerTrait;

    public function __construct(
        private readonly DataTableFactory $dataTableFactory,
        private readonly RouterInterface $router,
        ParameterBagInterface $parameterBag,
        private readonly CsrfTokenManagerInterface $csrfTokenManager,
        private readonly ModalboxHelper $modalboxHelper
    ) {
        $this->initializeLogger($parameterBag, 'hardware_type_table_service');
    }

    public function createMasterHardwareTypeTable(Request $request): DataTable
    {
        $dataTable = $this->dataTableFactory->create();

        foreach ($this->getColumns() as $key => $column) {
            $dataTable->add($key, $column['className'], array_filter($column, fn($k) => $k !== 'className', ARRAY_FILTER_USE_KEY));
        }

        return $dataTable->createAdapter(ORMAdapter::class, [
            'entity' => HardwareType::class,
            'query' => fn(QueryBuilder $qb) => $this->buildQuery($qb),
        ])
            ->addOrderBy('name', 'asc')
            ->handleRequest($request);
    }

    private function getColumns(): array
    {
        return [
            'name' => [
                'label' => 'Hardware Type Name',
                'field' => 'h.name',
                'className' => TextColumn::class,
            ],
            'dataStringSmall' => [
                'label' => 'Short Code',
                'field' => 'h.dataStringSmall',
                'className' => TextColumn::class,
            ],
            'description' => [
                'label' => 'Description',
                'field' => 'h.description',
                'className' => TextColumn::class,
                'render' => function ($value, $context) {
                    $description = $context->getDescription() ?? '-';
                    $maxLength = 25;
                    if (strlen($description) > $maxLength) {
                        $truncated = substr($description, 0, $maxLength) . '...';
                        return sprintf('<span title="%s">%s</span>', htmlspecialchars($description), htmlspecialchars($truncated));
                    }
                    return htmlspecialchars($description);
                },
            ],
            'actions' => [
                'label' => 'Actions',
                'field' => 'h.id',
                'className' => TextColumn::class,
                'orderable' => false,
                'render' => fn($value, $context) => $this->renderActionColumn($context),
            ],
        ];
    }

    private function renderActionColumn($context): string
    {
        $entityId = $context->getId();
        $editUrl = $this->router->generate('hardware_type_edit', ['id' => $entityId]);
        $deleteUrl = $this->router->generate('hardware_type_delete', ['id' => $entityId]);
        $csrfToken = $this->csrfTokenManager->getToken('delete' . $entityId)->getValue();

        $modalId = 'deleteModal_' . $entityId;
        $modalBody = 'Are you sure you want to delete this hardware type?<br><br><strong>Warning:</strong> This action cannot be undone.';
        $footerButtons = sprintf(
            '<button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>' .
            '<button type="button" class="btn btn-primary delete-hardware-type-btn" data-delete="true" data-url="%s" data-token="%s">Delete</button>',
            $deleteUrl,
            $csrfToken
        );

        $deleteModal = $this->modalboxHelper->renderModal(
            $modalId,
            'Confirm Hardware Type Deletion',
            $modalBody,
            $footerButtons
        );

        return sprintf(
            '<div class="d-inline-flex gap-2">
                <a href="%s" class="bs-btn btn btn-primary btn-sm" style="margin: 2px;"><i class="bs-fa fa fa-edit"></i></a>
                <button type="button" class="bs-btn btn btn-danger btn-sm" style="margin: 2px;" data-toggle="modal" data-target="#%s">
                    <i class="bs-fa fa fa-trash"></i>
                </button>
            </div>%s',
            $editUrl,
            $modalId,
            $deleteModal
        );
    }

    private function buildQuery(QueryBuilder $qb): void
    {
        $qb->select('h')
            ->from(HardwareType::class, 'h');
    }
}