 <div class="content">
        <div class="animated fadeIn">
            <div class="row">
                <div class="col-lg-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <strong class="card-title">
                                {{ mode == 'edit' ? 'Edit Document' : 'Upload Document' }}
                            </strong>
                            <div class="d-flex align-items-center">
                                {% if is_granted('ROLE_ADMIN') %}
                                <a href="{{ path('admin_employee_documents', {'employeeId': employee.id}) }}" class="btn btn-sm btn-outline-secondary mr-2">
                                    <i class="fa fa-list mr-1"></i>Back to List
                                </a>
                                {% elseif is_granted('ROLE_EMPLOYEE') %}
                                <a href="{{ path('my_employee_documents') }}" class="btn btn-sm btn-outline-secondary mr-2">
                                    <i class="fa fa-list mr-1"></i>Back to List
                                </a>
                                {% endif %}
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="document-form">
                                {% if form is defined %}
                                    {{ form_start(form, { 'attr': { 'class': 'needs-validation use-loader','data-turbo': 'false','data-employee-id': employee.id, 'novalidate': true, 'id': 'document-form-element','enctype': 'multipart/form-data', } }) }}
                                    <div class="form-section">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.documentType, 'Document Type', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-file"></i></span>
                                                        </div>
                                                        {{ form_widget(form.documentType, { 'attr': { 'class': 'form-control', 'required': true } }) }}
                                                    </div>
                                                    {{ form_errors(form.documentType) }}
                                                    <small class="form-text text-muted">Select the type of document</small>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.file, 'File', { 'label_attr': { 'class': 'form-label control-label mb-1 ' ~ (mode == 'create' ? 'required-field' : '') } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-upload"></i></span>
                                                        </div>
                                                        {{ form_widget(form.file, { 'attr': { 'class': 'form-control', 'required': mode == 'create','style': 'line-height:1.2 !important'} }) }}
                                                    </div>
                                                    {% if mode == 'edit' and document.filePath %}
                                                        <small class="form-text text-muted">
                                                            Current file: <a href="{{ document.filePath }}" target="_blank" class="text-primary">Download</a>
                                                            <br>
                                                        </small>
                                                    {% endif %}
                                                    {{ form_errors(form.file) }}
                                                    {% if mode == 'create' %}
                                                        <small class="form-text text-muted">Select a file to upload (PDF, JPG, PNG, GIF - Max 5MB)</small>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-12">
                                                <div class="mb-3">
                                                    {{ form_label(form.notes, 'Notes', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-comment"></i></span>
                                                        </div>
                                                        {{ form_widget(form.notes, { 'attr': { 'class': 'form-control', 'rows': 4 } }) }}
                                                    </div>
                                                    {{ form_errors(form.notes) }}
                                                    <small class="form-text text-muted">Optional notes about the document</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                {% if is_granted('ROLE_ADMIN') %}
                                    <div class="d-flex justify-content-end mt-4">
                                        <a href="{{ path('admin_employee_documents', { 'employeeId': employee.id }) }}" class="btn btn-secondary mr-2">Cancel</a>
                                        <button type="submit" class="btn btn-primary pr-3 pl-3">
                                            {{ mode == 'create' ? 'Upload Document' : 'Update Document' }}
                                        </button>
                                    </div>
                                {% elseif is_granted('ROLE_EMPLOYEE') %}
                                    <div class="d-flex justify-content-end mt-4">
                                        <a href="{{ path('my_employee_documents') }}" class="btn btn-secondary mr-2">Cancel</a>
                                        <button type="submit" class="btn btn-primary pr-3 pl-3">
                                            {{ mode == 'create' ? 'Upload Document' : 'Update Document' }}
                                        </button>
                                    </div>
                                {% endif %}
                                    {{ form_end(form) }}
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="clearfix"></div>
    <link rel="stylesheet" href="{{ asset('assets/css/Error/error.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/AdminForm/Form.css') }}">
    <script src="{{ asset('assets/js/datatables.min.js') }}"></script>
    <script src="{{ asset('bundles/datatables/js/datatables.js') }}"></script>
    <script src="{{ asset('assets/js/admin/common.js') }}"></script>
    <script src="{{ asset('assets/js/EmployeeDocument.js') }}"></script>
