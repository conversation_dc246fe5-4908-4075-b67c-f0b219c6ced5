* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: #f5f7fa;
    height: 100vh;
    overflow: hidden;
}

.app-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
}

/* Header Styles */
.app-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 100;
}

.header-left .logo {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.2rem;
    font-weight: 600;
}

.header-left .logo i {
    font-size: 1.5rem;
}

.header-center h1 {
    font-size: 1.3rem;
    font-weight: 500;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.user-avatar {
    width: 35px;
    height: 35px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.window-controls {
    display: flex;
    gap: 8px;
}

.control-btn {
    width: 28px;
    height: 28px;
    border: none;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.control-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

/* Main Content */
.main-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto auto 1fr;
    gap: 20px;
}

/* Status Cards */
.status-cards {
    grid-column: 1 / -1;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.status-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    border: 1px solid #e1e5e9;
    display: flex;
    align-items: center;
    gap: 15px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.status-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.status-card.online .card-icon {
    background: linear-gradient(135deg, #4CAF50, #45a049);
}

.status-card.status-active {
    border-left: 4px solid #4CAF50;
}

.status-card.status-idle {
    border-left: 4px solid #FF9800;
}

.status-card.status-inactive {
    border-left: 4px solid #F44336;
}

.status-card.tracking-active {
    border-left: 4px solid #4CAF50;
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.05), rgba(255, 255, 255, 1));
}

.status-card.tracking-inactive {
    border-left: 4px solid #F44336;
    background: linear-gradient(135deg, rgba(244, 67, 54, 0.05), rgba(255, 255, 255, 1));
}

/* Time Tracking Section */
.time-tracking-section {
    grid-column: 1 / -1;
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    border: 1px solid #e1e5e9;
    margin-bottom: 20px;
}

.time-tracking-section h2 {
    font-size: 1.2rem;
    margin-bottom: 20px;
    color: #333;
}

.time-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.time-stat-card {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    display: flex;
    align-items: center;
    gap: 12px;
    border-left: 4px solid #ddd;
    transition: all 0.3s ease;
}

.time-stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.time-stat-card.work {
    border-left-color: #4CAF50;
}

.time-stat-card.break {
    border-left-color: #FF9800;
}

.time-stat-card.idle {
    border-left-color: #F44336;
}

.time-stat-card.sessions {
    border-left-color: #2196F3;
}

.stat-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: white;
}

.time-stat-card.work .stat-icon {
    background: linear-gradient(135deg, #4CAF50, #45a049);
}

.time-stat-card.break .stat-icon {
    background: linear-gradient(135deg, #FF9800, #f57c00);
}

.time-stat-card.idle .stat-icon {
    background: linear-gradient(135deg, #F44336, #d32f2f);
}

.time-stat-card.sessions .stat-icon {
    background: linear-gradient(135deg, #2196F3, #1976d2);
}

.stat-content h3 {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 5px;
    font-weight: 500;
}

.stat-value {
    font-size: 1.3rem;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.current-session-info {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
}

.current-session-info h3 {
    margin-bottom: 10px;
    font-size: 1rem;
}

.session-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.session-type {
    font-size: 1.1rem;
    font-weight: 500;
    text-transform: capitalize;
}

.session-duration {
    font-size: 1.2rem;
    font-weight: 600;
    font-family: 'Courier New', monospace;
}

.card-icon {
    width: 50px;
    height: 50px;
    border-radius: 10px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
}

.card-content h3 {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 5px;
    font-weight: 500;
}

.card-content p {
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
}

/* Quick Actions */
.quick-actions {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    border: 1px solid #e1e5e9;
}

.quick-actions h2 {
    font-size: 1.2rem;
    margin-bottom: 15px;
    color: #333;
}

.action-buttons {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 15px;
    background: #f8f9fa;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    color: #333;
}

.action-btn:hover {
    background: #667eea;
    color: white;
    transform: translateX(5px);
}

.action-btn i {
    font-size: 1rem;
    width: 16px;
}

/* Activity Section */
.activity-section {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    border: 1px solid #e1e5e9;
}

.activity-section h2 {
    font-size: 1.2rem;
    margin-bottom: 15px;
    color: #333;
}

.activity-log {
    max-height: 200px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 10px 0;
    border-bottom: 1px solid #f0f0f0;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.8rem;
}

.activity-content p {
    font-size: 0.9rem;
    color: #333;
    margin-bottom: 2px;
}

.activity-time {
    font-size: 0.8rem;
    color: #666;
}

.activity-details {
    font-size: 0.8rem;
    color: #666;
}

.activity-icon.work {
    background: linear-gradient(135deg, #4CAF50, #45a049);
}

.activity-icon.break {
    background: linear-gradient(135deg, #FF9800, #f57c00);
}

.activity-icon.idle {
    background: linear-gradient(135deg, #F44336, #d32f2f);
}

.activity-icon.offline {
    background: linear-gradient(135deg, #9E9E9E, #757575);
}

.current-session-info.work {
    background: linear-gradient(135deg, #4CAF50, #45a049);
}

.current-session-info.break {
    background: linear-gradient(135deg, #FF9800, #f57c00);
}

.current-session-info.idle {
    background: linear-gradient(135deg, #F44336, #d32f2f);
}

.current-session-info.inactive {
    background: linear-gradient(135deg, #9E9E9E, #757575);
}

/* Idle Detection Section */
.idle-detection-info {
    background: linear-gradient(135deg, #6c5ce7, #a29bfe);
    color: white;
    border-radius: 8px;
    padding: 15px;
    margin-top: 15px;
}

.idle-detection-info h3 {
    margin-bottom: 15px;
    font-size: 1rem;
    text-align: center;
}

.idle-details {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.idle-state,
.idle-activity,
.idle-patterns {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 5px 0;
}

.idle-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

.idle-value {
    font-weight: 600;
    font-size: 0.95rem;
}

.idle-detection-info.thinking {
    background: linear-gradient(135deg, #fdcb6e, #e17055);
}

.idle-detection-info.short_idle {
    background: linear-gradient(135deg, #fd79a8, #e84393);
}

.idle-detection-info.long_idle {
    background: linear-gradient(135deg, #fd79a8, #e84393);
}

.idle-detection-info.inactive {
    background: linear-gradient(135deg, #636e72, #2d3436);
}

.idle-detection-info.away {
    background: linear-gradient(135deg, #636e72, #2d3436);
}

/* Screenshot Section */
.screenshot-info {
    background: linear-gradient(135deg, #00b894, #00cec9);
    color: white;
    border-radius: 8px;
    padding: 15px;
    margin-top: 15px;
}

.screenshot-info h3 {
    margin-bottom: 15px;
    font-size: 1rem;
    text-align: center;
}

.screenshot-details {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 15px;
}

.screenshot-status,
.screenshot-count,
.screenshot-last {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 5px 0;
}

.screenshot-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

.screenshot-value {
    font-weight: 600;
    font-size: 0.95rem;
}

.manual-screenshot-btn,
.test-screenshot-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 10px 15px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    flex: 1;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.manual-screenshot-btn:hover,
.test-screenshot-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

.manual-screenshot-btn:disabled,
.test-screenshot-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.test-screenshot-btn {
    background: rgba(255, 193, 7, 0.3);
    border-color: rgba(255, 193, 7, 0.5);
}

.debug-screenshot-btn,
.dev-tools-btn {
    background: rgba(108, 117, 125, 0.3);
    border: 1px solid rgba(108, 117, 125, 0.5);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.8rem;
    flex: 1;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
}

.debug-screenshot-btn:hover,
.dev-tools-btn:hover {
    background: rgba(108, 117, 125, 0.5);
    transform: translateY(-1px);
}

.dev-tools-btn {
    background: rgba(40, 167, 69, 0.3);
    border-color: rgba(40, 167, 69, 0.5);
}

.dev-tools-btn:hover {
    background: rgba(40, 167, 69, 0.5);
}

.screenshot-info.capturing {
    background: linear-gradient(135deg, #fd79a8, #fdcb6e);
}

.screenshot-info.disabled {
    background: linear-gradient(135deg, #636e72, #2d3436);
}

.idle-settings-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.85rem;
    margin-top: 10px;
    transition: all 0.3s ease;
}

.idle-settings-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

/* Modal Styles */
.modal {
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    border-radius: 12px;
    width: 90%;
    max-width: 500px;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid #e1e5e9;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: #333;
}

.close {
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    color: #999;
    transition: color 0.3s ease;
}

.close:hover {
    color: #333;
}

.modal-body {
    padding: 20px;
}

.setting-group {
    margin-bottom: 20px;
}

.setting-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #333;
}

.setting-group input[type="number"] {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
}

.setting-group input[type="checkbox"] {
    margin-right: 8px;
}

.setting-group small {
    display: block;
    margin-top: 5px;
    color: #666;
    font-size: 0.85rem;
}

.modal-footer {
    padding: 20px;
    border-top: 1px solid #e1e5e9;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.btn-primary,
.btn-secondary {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: #f8f9fa;
    color: #333;
    border: 1px solid #ddd;
}

.btn-secondary:hover {
    background: #e9ecef;
}

/* System Information */
.system-info {
    grid-column: 1 / -1;
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    border: 1px solid #e1e5e9;
}

.system-info h2 {
    font-size: 1.2rem;
    margin-bottom: 15px;
    color: #333;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #f0f0f0;
}

.info-item:last-child {
    border-bottom: none;
}

.info-item label {
    font-weight: 500;
    color: #666;
    font-size: 0.9rem;
}

.info-item span {
    color: #333;
    font-size: 0.9rem;
}

/* Footer */
.app-footer {
    background: white;
    border-top: 1px solid #e1e5e9;
    padding: 10px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.8rem;
    color: #666;
}

.footer-status {
    display: flex;
    align-items: center;
    gap: 8px;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #4CAF50;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Toast Notification */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    border: 1px solid #e1e5e9;
    padding: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
    z-index: 1000;
    animation: slideInRight 0.3s ease;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.toast-content {
    display: flex;
    align-items: center;
    gap: 10px;
}

.toast-icon {
    font-size: 1.2rem;
}

.toast.success .toast-icon {
    color: #4CAF50;
}

.toast.error .toast-icon {
    color: #f44336;
}

.toast.info .toast-icon {
    color: #2196F3;
}

.toast-message {
    font-size: 0.9rem;
    color: #333;
}

.toast-close {
    background: none;
    border: none;
    color: #999;
    cursor: pointer;
    padding: 2px;
}

.toast-close:hover {
    color: #333;
}

/* Enhanced Status Section */
.enhanced-status-section {
    margin: 20px 0;
}

.enhanced-status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 15px;
}

.status-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border: 1px solid #e1e8ed;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.status-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.card-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e1e8ed;
}

.card-header i {
    font-size: 1.2rem;
    color: #667eea;
}

.card-header h3 {
    font-size: 1rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
}

.card-content {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.status-value {
    font-size: 1.1rem;
    font-weight: 600;
    padding: 5px 10px;
    border-radius: 6px;
    text-align: center;
}

.status-active {
    background: #d4edda;
    color: #155724;
}

.status-inactive {
    background: #f8d7da;
    color: #721c24;
}

.last-capture {
    font-size: 0.9rem;
    color: #6c757d;
    text-align: center;
}

.upload-stats {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
}

.upload-stats .stat {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

.stat-label {
    font-size: 0.8rem;
    color: #6c757d;
    font-weight: 500;
}

.queue-count, .uploaded-count {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
}

.upload-status {
    font-size: 0.9rem;
    font-weight: 600;
    padding: 5px 10px;
    border-radius: 6px;
    text-align: center;
}

.upload-status.uploading {
    background: #fff3cd;
    color: #856404;
}

.upload-status.idle {
    background: #d1ecf1;
    color: #0c5460;
}

.reports-summary .stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.productivity-score {
    font-size: 1.1rem;
    font-weight: 600;
    color: #667eea;
}

.btn-sm {
    padding: 8px 16px;
    font-size: 0.9rem;
    border: none;
    border-radius: 6px;
    background: #667eea;
    color: white;
    cursor: pointer;
    transition: background 0.2s ease;
}

.btn-sm:hover {
    background: #5a6fd8;
}

/* Reports Section */
.reports-section {
    margin: 20px 0;
}

.reports-container {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border: 1px solid #e1e8ed;
    margin-top: 15px;
}

.report-item {
    border-bottom: 1px solid #e1e8ed;
    padding: 15px 0;
}

.report-item:last-child {
    border-bottom: none;
}

.report-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.report-date {
    font-weight: 600;
    color: #2c3e50;
}

.report-type {
    background: #e9ecef;
    color: #495057;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    text-transform: capitalize;
}

.report-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 10px;
}

.report-metrics .metric {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.metric-label {
    font-size: 0.9rem;
    color: #6c757d;
}

.metric-value {
    font-weight: 600;
    color: #2c3e50;
}

.no-data {
    text-align: center;
    color: #6c757d;
    font-style: italic;
    padding: 20px;
}

/* Notifications */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 8px;
    color: white;
    font-weight: 500;
    z-index: 1000;
    animation: slideIn 0.3s ease;
}

.notification-success {
    background: #28a745;
}

.notification-error {
    background: #dc3545;
}

.notification-info {
    background: #17a2b8;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .main-content {
        grid-template-columns: 1fr;
        padding: 15px;
    }

    .status-cards {
        grid-template-columns: 1fr;
    }

    .info-grid {
        grid-template-columns: 1fr;
    }

    .header-center h1 {
        display: none;
    }

    .enhanced-status-grid {
        grid-template-columns: 1fr;
    }

    .report-metrics {
        grid-template-columns: 1fr;
    }
}
