<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Idle Warning</title>
    <link rel="stylesheet" href="css/idle-warning.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="popup-container">
        <div class="popup-header">
            <div class="warning-icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <h2>Idle Time Detected</h2>
        </div>
        
        <div class="popup-content">
            <p class="idle-message">
                You have been idle for <span id="idleTime">5 minutes</span>.
            </p>
            
            <div class="countdown-container">
                <div class="countdown-circle">
                    <div class="countdown-number" id="countdownNumber">60</div>
                </div>
                <p class="countdown-text">
                    Your session will be paused in <strong id="countdownSeconds">60</strong> seconds
                </p>
            </div>
            
            <div class="action-buttons">
                <button class="btn btn-primary" id="resumeBtn">
                    <i class="fas fa-play"></i>
                    I'm Still Here
                </button>
                <button class="btn btn-secondary" id="continueIdleBtn">
                    <i class="fas fa-pause"></i>
                    Continue Idle
                </button>
            </div>
        </div>
        
        <div class="popup-footer">
            <p class="footer-text">
                <i class="fas fa-info-circle"></i>
                This helps track your actual working time accurately
            </p>
        </div>
    </div>

    <script src="js/idle-warning.js"></script>
</body>
</html>
