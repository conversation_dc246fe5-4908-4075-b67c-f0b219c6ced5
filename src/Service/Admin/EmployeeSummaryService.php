<?php

namespace App\Service\Admin;

use App\Entity\EmployeeHours;
use Doctrine\ORM\QueryBuilder;
use Omines\DataTablesBundle\Adapter\Doctrine\ORMAdapter;
use Omines\DataTablesBundle\Column\DateTimeColumn;
use Omines\DataTablesBundle\Column\TextColumn;
use Omines\DataTablesBundle\DataTable;
use Omines\DataTablesBundle\DataTableFactory;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use App\Traits\ExceptionLoggerTrait;
use App\Helper\TimeHelper;

class EmployeeSummaryService
{
    use ExceptionLoggerTrait;

    public function __construct(
        private readonly DataTableFactory $dataTableFactory,
        private readonly RequestStack $requestStack,
        ParameterBagInterface $parameterBag,
        private readonly TimeHelper $timeHelper,
    )
    {
        $this->initializeLogger($parameterBag, 'employee_summary_service');
    }

    public function createEmployeeSummary(Request $request): DataTable
    {
        $dataTable = $this->dataTableFactory->create();

        foreach ($this->getColumns() as $key => $column) {
            $dataTable->add($key, $column['className'], array_filter($column, fn($k) => $k !== 'className'));
        }

        return $dataTable->createAdapter(ORMAdapter::class, [
            'entity' => EmployeeHours::class,
            'query' => fn(QueryBuilder $qb) => $this->buildQuery($qb),
        ])->handleRequest($request);
    }

    private function getColumns(): array
    {
        return [
            'employee' => [
                'label' => 'Employee',
                'field' => 'em.name',
                'orderable' => true,
                'className' => TextColumn::class,
            ],
            'onComputerHours' => [
                'label' => 'Working Hours',
                'field' => 'eh.onComputerHours',
                'render' => fn($value) => $this->timeHelper->convertDecimalToTime($value),
                'className' => TextColumn::class,
            ],
            'meetingHours' => [
                'label' => 'Meeting Hours',
                'field' => 'eh.meetingHours',
                'render' => fn($value) => $this->timeHelper->convertDecimalToTime($value),
                'className' => TextColumn::class,
            ],
            'offComputerHours' => [
                'label' => 'Manual Hours',
                'field' => 'eh.offComputerHours',
                'render' => fn($value) => $this->timeHelper->convertDecimalToTime($value),
                'className' => TextColumn::class,
            ],
            'breakHours' => [
                'label' => 'Break Hours',
                'field' => 'eh.breakHours',
                'render' => fn($value) => $this->timeHelper->convertDecimalToTime($value),
                'className' => TextColumn::class,
            ],
            'spanHours' => [
                'label' => 'Span Hours',
                'field' => 'eh.spanHours',
                'render' => fn($value) => $this->timeHelper->convertDecimalToTime($value),
                'className' => TextColumn::class,
            ],
            'totalHours' => [
                'label' => 'Log Hours',
                'field' => 'eh.totalHours',
                'render' => fn($value) => $this->timeHelper->convertDecimalToTime($value),
                'className' => TextColumn::class,
            ],
            'reportDate' => [
                'label' => 'Log Date',
                'field' => 'eh.reportDate',
                'format' => 'd-m-Y',
                'searchable' => false,
                'className' => DateTimeColumn::class,
            ],
        ];
    }

    private function buildQuery(QueryBuilder $qb): void
    {
        $session = $this->requestStack->getSession();
        $filters = $session->get('employee_summary_filters', []);
        $currentDate = new \DateTime();
        $currentDate->setTime(0, 0, 0);

        $qb->select('eh, e, em')
            ->from(EmployeeHours::class, 'eh')
            ->join('eh.employee', 'e')
            ->join('e.masterEmployee', 'em');

        if (empty($filters)) {
            $qb->andWhere('eh.reportDate = :currentDate')
                ->setParameter('currentDate', $currentDate->format('Y-m-d'));
        } else {
            if (isset($filters['startDate'], $filters['endDate'])) {
                $qb->andWhere('eh.reportDate BETWEEN :startDate AND :endDate')
                    ->setParameter('startDate', $filters['startDate'])
                    ->setParameter('endDate', $filters['endDate']);
            }

            if (!empty($filters['employeeId'])) {
                $qb->andWhere('e.id = :employeeId')
                    ->setParameter('employeeId', $filters['employeeId']);
            }
        }
    }
}