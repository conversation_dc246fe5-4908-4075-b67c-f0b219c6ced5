<div class="content">
    <div class="animated fadeIn">
        <div class="row">
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <strong class="card-title">
                            {{ mode == 'create' ? 'Create New Bug' : 'Edit Bug' }}
                        </strong>
                        <div class="d-flex align-items-center">
                            {% if is_granted('ROLE_ADMIN') %}
                            <a href="{{ path('admin_bug_index') }}" class="btn btn-sm btn-outline-secondary mr-2">
                                <i class="fa fa-list mr-1"></i>Back to List
                            </a>
                            {% elseif is_granted('ROLE_EMPLOYEE') %}
                                <a href="{% if taskAssignment is defined and taskAssignment %}
                                         {{ path('bug_index', { 'taskAssignmentId': taskAssignment.id }) }}
                                         {% elseif task is defined and task %}
                                         {{ path('bug_index', { 'task': task.id }) }}
                                         {% endif %}"
                                     class="btn btn-sm btn-outline-secondary mr-2">
                                    <i class="fa fa-list mr-1"></i>Back to List
                                </a>
                            {% endif %}
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="bug-form">
                            {{ form_start(form, { 'attr': { 'class': 'needs-validation use-loader', 'novalidate': 'novalidate' ,'data-mode': mode|default('create')} }) }}
                            {% if is_granted('ROLE_ADMIN') %}
                            <!-- Bug Information Section -->
                            <div class="form-section">
                                <h5 class="section-header">
                                    <i class="fa fa-bug mr-2"></i>Bug Information
                                </h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            {{ form_label(form.project, 'Project', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text"><i class="fa fa-product-hunt"></i></span>
                                                </div>
                                                {{ form_widget(form.project, { 'attr': { 'class': 'form-control', 'placeholder': 'Select a Project' } }) }}
                                            </div>
                                            {{ form_errors(form.project) }}
                                            <small class="form-text text-muted">Select the project this bug belongs to</small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            {{ form_label(form.task, 'Task', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text"><i class="fa fa-tasks"></i></span>
                                                </div>
                                                {{ form_widget(form.task, { 'attr': { 'class': 'form-control', 'placeholder': 'Select a Task' } }) }}
                                            </div>
                                            {{ form_errors(form.task) }}
                                            <small class="form-text text-muted">Select the task related to this bug</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            {{ form_label(form.assignedTo, 'Assigned To', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text"><i class="fa fa-user"></i></span>
                                                </div>
                                                {{ form_widget(form.assignedTo, { 'attr': { 'class': 'form-control', 'placeholder': 'Select an Employee' } }) }}
                                            </div>
                                            {{ form_errors(form.assignedTo) }}
                                            <small class="form-text text-muted">Employee responsible for fixing the bug</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endif %}

                            <!-- Bug Details Section -->
                            <div class="form-section">
                                <h5 class="section-header">
                                    <i class="fa fa-info-circle mr-2"></i>Bug Details
                                </h5>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            {{ form_label(form.priority, 'Priority', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text"><i class="fa fa-exclamation-circle"></i></span>
                                                </div>
                                                {{ form_widget(form.priority, { 'attr': { 'class': 'form-control', 'placeholder': 'Select Priority' } }) }}
                                            </div>
                                            {{ form_errors(form.priority) }}
                                            <small class="form-text text-muted">Priority level of the bug</small>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            {{ form_label(form.severity, 'Severity', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text"><i class="fa fa-exclamation-triangle"></i></span>
                                                </div>
                                                {{ form_widget(form.severity, { 'attr': { 'class': 'form-control', 'placeholder': 'Select Severity' } }) }}
                                            </div>
                                            {{ form_errors(form.severity) }}
                                            <small class="form-text text-muted">Severity level of the bug</small>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            {{ form_label(form.category, 'Category', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text"><i class="fa fa-tags"></i></span>
                                                </div>
                                                {{ form_widget(form.category, { 'attr': { 'class': 'form-control', 'placeholder': 'Select Category' } }) }}
                                            </div>
                                            {{ form_errors(form.category) }}
                                            <small class="form-text text-muted">Category of the bug</small>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            {{ form_label(form.status, 'Status', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text"><i class="fa fa-toggle-on"></i></span>
                                                </div>
                                                {{ form_widget(form.status, { 'attr': { 'class': 'form-control' } }) }}
                                            </div>
                                            {{ form_errors(form.status) }}
                                            <small class="form-text text-muted">Select the bug status</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="mb-3">
                                            {{ form_label(form.comments, 'Comments', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                            {{ form_widget(form.comments, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter comments or testing instructions', 'rows': 5 } }) }}
                                            {{ form_errors(form.comments) }}
                                            <small class="form-text text-muted">Additional comments or testing instructions</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="action-footer mt-4">
                                {% if is_granted('ROLE_ADMIN') %}
                                <a href="{{ path('admin_bug_index') }}" class="btn btn-secondary mr-2">
                                    Cancel
                                </a>
                                    <button type="submit" class="btn btn-primary pr-3 pl-3" id="submitButton" data-update="{{ mode == 'edit' ? 'true' : 'false' }}">
                                        {{ mode == 'create' ? 'Create Bug' : 'Update Bug' }}
                                    </button>
                                {% elseif is_granted('ROLE_EMPLOYEE') %}
                                    <a href="{% if taskAssignment is defined and taskAssignment %}
                                         {{ path('bug_index', { 'taskAssignmentId': taskAssignment.id }) }}
                                         {% elseif task is defined and task %}
                                         {{ path('bug_index', { 'task': task.id }) }}
                                         {% endif %}"
                                       class="btn btn-secondary mr-2">Cancel
                                    </a>
                                    <button type="submit" class="btn btn-primary pr-3 pl-3" id="submitButton" data-update="{{ mode == 'edit' ? 'true' : 'false' }}">
                                        {{ mode == 'create' ? 'Create Bug' : 'Update Bug' }}
                                    </button>
                                {% endif %}
                            </div>
                            {{ form_end(form) }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="clearfix"></div>
<link rel="stylesheet" href="{{ asset('assets/css/AdminForm/Form.css') }}">
<link rel="stylesheet" href="{{ asset('assets/css/Error/error.css') }}">
<script src="{{ asset('assets/js/datatables.min.js') }}"></script>
<script src="{{ asset('bundles/datatables/js/datatables.js') }}"></script>