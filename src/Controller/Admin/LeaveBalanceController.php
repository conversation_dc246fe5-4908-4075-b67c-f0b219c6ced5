<?php

namespace App\Controller\Admin;

use App\Entity\LeaveBalance;
use App\Entity\MasterEmployee;
use App\Form\LeaveBalanceFormType;
use App\Service\Admin\LeaveBalanceService;
use App\Traits\ExceptionLoggerTrait;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

#[Route('/admin')]
class LeaveBalanceController extends AbstractController
{
    use ExceptionLoggerTrait;

    public function __construct(
        ParameterBagInterface                   $parameterBag,
        private readonly EntityManagerInterface $entityManager,
        private readonly LeaveBalanceService $leaveBalanceService,
    )
    {
        $this->initializeLogger($parameterBag, 'LeaveBalance_controller');
    }

    #[Route(path: '/leave/list', name: 'leave_list', methods: ['GET', 'POST'])]
    public function index(Request $request): Response
    {
        $table = $this->leaveBalanceService->createLeaveBalanceTable($request);

        if ($table->isCallback()) {
            return $table->getResponse();
        }

        return $this->render('admin/LeaveBalance/index.html.twig', [
            'datatable' => $table,
        ]);
    }
    #[Route('/leave/edit/{employeeId}', name: 'leave_edit', methods: ['GET', 'POST'])]
    public function edit(Request $request, int $employeeId): Response
    {
        $employee = $this->entityManager->getRepository(MasterEmployee::class)->find($employeeId);
        if (!$employee) {
            $this->addFlash('error', 'Employee not found.');
            return $this->redirectToRoute('leave_list');
        }

        $leaveBalanceRepo = $this->entityManager->getRepository(LeaveBalance::class);
        /** @phpstan-ignore-next-line */
        $maxYear = $leaveBalanceRepo->getMaxYearForEmployee($employee);
        /** @phpstan-ignore-next-line */
        $leaveBalances = $leaveBalanceRepo->findByEmployeeAndYearActive($employee, $maxYear);
        $form = $this->createForm(LeaveBalanceFormType::class, ['leaveBalances' => $leaveBalances], [
            'employee' => $employee,
            'leaveBalances' => $leaveBalances,
            'is_edit' => true
        ]);

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            foreach ($leaveBalances as $balance) {
                $this->entityManager->persist($balance);
            }
            $this->entityManager->flush();

            $this->addFlash('success', 'Leave balances updated successfully.');
            return $this->redirectToRoute('leave_list');
        }
        return $this->render('admin/LeaveBalance/new.html.twig', [
            'form' => $form->createView(),
            'employee' => $employee,
            'mode' => 'edit',
        ]);
    }
}
