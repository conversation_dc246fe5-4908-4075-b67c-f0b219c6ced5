<?php

namespace App\Controller\Admin;

use App\Entity\EmployeeArrears;
use App\Entity\User;
use App\Form\EmployeeArrearsType;
use App\Service\Admin\EmployeeArrearsService;
use App\Traits\ExceptionLoggerTrait;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Csrf\CsrfToken;
use Symfony\Component\Security\Csrf\CsrfTokenManagerInterface;

#[Route('/admin')]
class EmployeeArrearsController extends AbstractController
{
    use ExceptionLoggerTrait;

    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly CsrfTokenManagerInterface $csrfTokenManager,
        private readonly EmployeeArrearsService $employeeArrearsService,
    ) {
    }

    #[Route('/employee-arrears', name: 'admin_employee_arrears', methods: ['GET', 'POST'])]
    public function index(Request $request): Response
    {
        $user = $this->getUser();
        if (!$user instanceof User) {
            throw $this->createAccessDeniedException();
        }
        $table = $this->employeeArrearsService->createEmployeeArrearsTable($request);

        if ($table->isCallback()) {
            return $table->getResponse();
        }

        return $this->render('admin/EmployeeArrears/index.html.twig', [
            'datatable' => $table,
        ]);
    }

    #[Route('/employee-arrears/new', name: 'admin_employee_arrears_new', methods: ['GET', 'POST'])]
    public function new(Request $request): Response
    {
        $user = $this->getUser();
        if (!$user instanceof User) {
            throw $this->createAccessDeniedException();
        }
        $arrears = new EmployeeArrears();
        $form = $this->createForm(EmployeeArrearsType::class, $arrears);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            try {
                $this->entityManager->persist($arrears);
                $this->entityManager->flush();
                $this->addFlash('success', 'Employee arrears record created successfully.');
                return $this->redirectToRoute('admin_employee_arrears');
            } catch (\Exception $e) {
                $this->addFlash('error', 'Error creating arrears record: ' . $e->getMessage());
                return $this->redirectToRoute('admin_employee_arrears_new');
            }
        }

        return $this->render('admin/EmployeeArrears/new.html.twig', [
            'form' => $form->createView(),
            'mode' => 'create',
        ]);
    }

    #[Route('/employee-arrears/{id}/edit', name: 'admin_employee_arrears_edit', methods: ['GET', 'POST'])]
    public function edit(Request $request, int $id): Response
    {
        $arrears = $this->entityManager->getRepository(EmployeeArrears::class)->find($id);
        if (!$arrears) {
            throw $this->createNotFoundException('Employee arrears record not found');
        }
        $form = $this->createForm(EmployeeArrearsType::class, $arrears);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            try {
                $this->entityManager->flush();
                $this->addFlash('success', 'Employee arrears record updated successfully.');
                return $this->redirectToRoute('admin_employee_arrears');
            } catch (\Exception $e) {
                $this->addFlash('error', 'Error updating arrears record: ' . $e->getMessage());
                return $this->redirectToRoute('admin_employee_arrears_edit', ['id' => $id]);
            }
        }

        return $this->render('admin/EmployeeArrears/new.html.twig', [
            'form' => $form->createView(),
            'mode' => 'edit',
        ]);
    }

    #[Route('/employee-arrears/{id}/delete', name: 'admin_employee_arrears_delete', methods: ['POST'])]
    public function delete(Request $request, int $id): Response
    {
        $arrears = $this->entityManager->getRepository(EmployeeArrears::class)->find($id);
        if (!$arrears) {
            return $this->json([
                'success' => false,
                'message' => 'Employee arrears record not found.'
            ], Response::HTTP_NOT_FOUND);
        }

        $token = $request->request->get('_token');
        if (!$this->csrfTokenManager->isTokenValid(new CsrfToken('delete_employee_arrears_' . $id, $token))) {
            return $this->json([
                'success' => false,
                'message' => 'Invalid CSRF token.'
            ], Response::HTTP_FORBIDDEN);
        }

        $this->entityManager->remove($arrears);
        $this->entityManager->flush();
        return $this->json([
            'success' => true,
            'message' => 'Employee arrears record has been deleted.'
        ], Response::HTTP_OK);
    }

    /**
     * @throws \DateMalformedStringException
     */
    #[Route('/employee-arrears/salary-info', name: 'admin_employee_arrears_salary_info', methods: ['GET'])]
    public function salaryInfo(Request $request): Response
    {
        $employeeId = $request->query->get('employee');
        $month = $request->query->get('month');
        $year = $request->query->get('year');
        if (!$employeeId || !$month || !$year) {
            return $this->json([
                'oldSalary' => 0,
                'newSalary' => 0,
                'totalWorkingDays' => 0,
                'arrearsDays' => 0,
            ]);
        }
        $calc = $this->employeeArrearsService->fetchSalaryInfo(
            (int)$employeeId,
            (int)$year,
            (int)$month
        );
        return $this->json($calc);
    }
} 