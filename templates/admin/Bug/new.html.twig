{% extends 'admin/dashboard/base.html.twig' %}

{% block title %}
    {{ mode == 'create' ? 'Create New Bug' : 'Edit Bug' }}
{% endblock %}

{% block body %}
    {% include 'admin/partials/breadcrumbs.html.twig' with {
        title: 'Bugs',
        breadcrumbs: [
            { 'label': 'Dashboard', 'url': url('admin_dashboard') },
            { 'label': 'Bugs', 'url': url('admin_bug_index') }
        ],
        active: mode == 'create' ? 'Create Bug' : 'Edit Bug'
    } %}
    {% include 'CommonTemplate/Bug/new.html.twig' %}
    <script>
        window.isAdmin = {{ is_granted('ROLE_ADMIN') ? 'true' : 'false' }};
    </script>
    <script src="{{ asset('assets/js/bug.js') }}"></script>
{% endblock %}