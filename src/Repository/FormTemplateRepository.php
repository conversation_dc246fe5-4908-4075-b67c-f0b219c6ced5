<?php

namespace App\Repository;

use App\Entity\FormTemplate;
use App\Entity\MasterEmployee;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<FormTemplate>
 */
class FormTemplateRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, FormTemplate::class);
    }

    //    /**
    //     * @return FormTemplate[] Returns an array of FormTemplate objects
    //     */
    //    public function findByExampleField($value): array
    //    {
    //        return $this->createQueryBuilder('f')
    //            ->andWhere('f.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->orderBy('f.id', 'ASC')
    //            ->setMaxResults(10)
    //            ->getQuery()
    //            ->getResult()
    //        ;
    //    }

    //    public function findOneBySomeField($value): ?FormTemplate
    //    {
    //        return $this->createQueryBuilder('f')
    //            ->andWhere('f.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->getQuery()
    //            ->getOneOrNullResult()
    //        ;
    //    }

    public function findTemplatesByTeamLeader(MasterEmployee $teamLeader): array
    {
        return $this->createQueryBuilder('t')
            ->innerJoin('t.teamLeaders', 'tl')
            ->innerJoin('t.sections', 's')
            ->innerJoin('s.fields', 'f')
            ->where('tl.id = :teamLeaderId')
            ->andWhere('t.status = :activeStatus')
            ->andWhere('t.assignToAll = :assignToAll')
            ->andWhere('t.isDelete = :templateNotDeleted')
            ->andWhere('s.status = :sectionStatus')
            ->andWhere('s.isDelete = :sectionNotDeleted')
            ->andWhere('f.isVisible = :fieldStatus')
            ->andWhere('f.isDelete = :fieldNotDeleted')
            ->setParameter('teamLeaderId', $teamLeader->getId())
            ->setParameter('activeStatus', true)
            ->setParameter('assignToAll', false)
            ->setParameter('templateNotDeleted', false)
            ->setParameter('sectionStatus', true)
            ->setParameter('sectionNotDeleted', false)
            ->setParameter('fieldStatus', true)
            ->setParameter('fieldNotDeleted', false)
            ->getQuery()
            ->getResult();
    }

    public function findTemplatesByEmployee(MasterEmployee $employee): array
    {
        return $this->createQueryBuilder('t')
            ->innerJoin('t.sections', 's')
            ->innerJoin('s.fields', 'f')
            ->where('t.assignToAll = :assignToAll')
            ->andWhere('t.isDelete = :templateNotDeleted')
            ->andWhere('t.status = :activeStatus')
            ->andWhere('s.status = :sectionStatus')
            ->andWhere('s.isDelete = :sectionNotDeleted')
            ->andWhere('f.isVisible = :fieldStatus')
            ->andWhere('f.isDelete = :fieldNotDeleted')
            ->setParameter('assignToAll', true)
            ->setParameter('templateNotDeleted', false)
            ->setParameter('activeStatus', true)
            ->setParameter('sectionStatus', true)
            ->setParameter('sectionNotDeleted', false)
            ->setParameter('fieldStatus', true)
            ->setParameter('fieldNotDeleted', false)
            ->getQuery()
            ->getResult();
    }

}
