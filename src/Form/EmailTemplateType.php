<?php

namespace App\Form;

use App\Entity\EmailTemplate;
use FOS\CKEditorBundle\Form\Type\CKEditorType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class EmailTemplateType extends AbstractType
{
    /**
     * @param FormBuilderInterface $builder
     * @param array                $options
     */
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder
            ->add('identifier', TextType::class, [
                'label'     => 'Identifier',
                'attr'      => ['autofocus' => true],
                'row_attr'  => [
                    'class' => 'col-md-12'
                ],
                'help'      => 'Unique identifier for the email template.',
            ])
            ->add('name', TextType::class, [
                'label'     => 'Name',
                'row_attr'  => [
                    'class' => 'col-md-12'
                ],
                'help'      => 'Name of the email template.',
            ])
            ->add('content', CKEditorType::class, [
                'label'     => 'Content',
                'required'  => true,
                'row_attr'  => [
                    'class' => 'col-md-12'
                ],
                'attr' => ['rows' => '10'],
                'help'      => 'The body of the email template, written in HTML.',
            ]);
    }

    /**
     * @param OptionsResolver $resolver
     */
    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults([
            'data_class'            => EmailTemplate::class,
            'allow_extra_fields'    => true,
        ]);
    }
}
