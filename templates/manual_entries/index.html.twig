{% extends 'admin/dashboard/base.html.twig' %}

{% block title %}Manual Entries{% endblock %}
{% macro render_entries_table(entries, status) %}
    <div class="table-responsive">
        <table class="table">
            <thead>
            <tr class="manual-entries-column-width">
                {% if status != 'REJECTED' %}
                    <th>
                        <label>
                            <input type="checkbox" class="form-check-input select-all-checkbox"
                                   data-status="{{ status }}"
                                   onchange="handleSelectAll(this, '{{ status }}')">
                        </label>
                    </th>
                {% endif %}
                <th><b>Employee</b></th>
                <th><b>Start Time</b></th>
                <th><b>End Time</b></th>
                <th><b>Duration</b></th>
                <th><b>Summary</b></th>
                {% if status != 'REJECTED' %}
                    <th><b>Actions</b></th>
                {% endif %}
            </tr>
            </thead>
            <tbody>
            {% for entry in entries|filter(e => e.status == status) %}
                <tr>
                    {% if status != 'REJECTED' %}
                        <td>
                            <label>
                                <input type="checkbox" class="form-check-input entry-checkbox"
                                       data-entry-id="{{ entry.id }}"
                                       data-status="{{ status }}">
                            </label>
                        </td>
                    {% endif %}
                    <td>
                        <div title="{{ entry.employeeEmail }}">{{ entry.employeeName }}</div>
                    </td>
                    <td>{{ entry.startTimeFormatted|default('Invalid date') }}</td>
                    <td>{{ entry.endTimeFormatted|default('Invalid date') }}</td>
                    <td>{{ (entry.durationMinutes // 60) }}h {{ (entry.durationMinutes % 60) }}m</td>
                    <td><b>Task:</b> {{ entry.task|default('Default Task') }} <br><b>Project:</b> {{ entry.project|default('Default Project') }} <br>
                        <b>Notes:</b> {{ entry.notes|default('') }}</td>
                    {% if status != 'REJECTED' %}
                        <td>
                            <div class="btn-group">
                                {% if status != 'APPROVED' %}
                                <button type="button" class="bs-btn btn btn-sm btn-success m-2"
                                        title="Approve" onclick="handleEntry('approve', '{{ entry.id }}')">
                                    <i class="bs-fa fa fa-check"></i>
                                </button>
                                {% endif %}
                                <button type="button" class="bs-btn btn btn-sm btn-danger m-2"
                                        title="Reject" onclick="handleEntry('reject', '{{ entry.id }}')">
                                    <i class="bs-fa fa fa-times"></i>
                                </button>
                            </div>
                        </td>
                    {% endif %}
                </tr>
            {% else %}
                <tr>
                    <td colspan="8" class="text-center">No pending entries found</td>
                </tr>
            {% endfor %}
            </tbody>
        </table>
    </div>
{% endmacro %}

{% block body %}
    {% import _self as tables %}

    {% include 'admin/partials/breadcrumbs.html.twig' with {
        title: 'Manual Request Report',
        breadcrumbs: [
            { 'label': 'Dashboard', 'url': url('admin_dashboard') },
        ],
        active: 'Manual Request'
    } %}

    {% set statusCounts = {
        'UNAPPROVED': entries|filter(e => e.status == 'UNAPPROVED')|length,
        'APPROVED': entries|filter(e => e.status == 'APPROVED')|length,
        'REJECTED': entries|filter(e => e.status == 'REJECTED')|length
    } %}

    <div class="content">
        <div class="col-lg-15">
            <div class="card">
                <div id="order-loader">
                    <img src="{{ asset('images/loader.svg') }}" alt="loader">
                </div>
                <div class="card-header d-flex justify-content-between">
                    <h4>Manual Entries</h4>
                    <div class="col text-end d-flex justify-content-end">
                        <button type="button" class="btn btn-primary pr-3 pl-3" onclick="refreshEntries()">
                            REFRESH
                        </button>
                        <button type="button" class="btn btn-success pr-3 pl-3" onclick="handleBulkAction('approve')">
                            APPROVE
                        </button>
                        <button type="button" class="btn btn-danger pr-3 pl-3" onclick="handleBulkAction('reject')">
                             REJECT
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <div class="search-wrapper">
                                <div class="input-group">
                <span class="input-group-text">
                    <i class="fa fa-search"></i>
                </span>
                                    <input type="text"
                                           class="form-control"
                                           id="searchEntries"
                                           placeholder="Search Employee"
                                           aria-label="Search entries">
                                    <button class="btn btn-clear" type="button" onclick="clearSearch()">
                                        <i class="fa fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="custom-tab">
                        <nav>
                            <div class="nav nav-tabs" id="nav-tab" role="tablist">
                                {% set tabs = [
                                    { id: 'pending', status: 'UNAPPROVED', label: 'PENDING APPROVAL' },
                                    { id: 'approve', status: 'APPROVED', label: 'RECENT APPROVED' },
                                    { id: 'disapprove', status: 'REJECTED', label: 'RECENT REJECTED' }
                                ] %}

                                {% for tab in tabs %}
                                    <a class="nav-item nav-link {{ loop.first ? 'active' }}"
                                       id="custom-nav-{{ tab.id }}-tab"
                                       data-toggle="tab"
                                       href="#custom-nav-{{ tab.id }}"
                                       role="tab"
                                       aria-selected="{{ loop.first ? 'true' : 'false' }}">
                                        {{ tab.label }} ({{ statusCounts[tab.status] }})
                                    </a>
                                {% endfor %}
                            </div>
                        </nav>

                        <div class="tab-content pl-3 pt-2" id="nav-tabContent">
                            {% for tab in tabs %}
                                <div class="tab-pane fade {{ loop.first ? 'show active' }}"
                                     id="custom-nav-{{ tab.id }}"
                                     role="tabpanel">
                                    {{ tables.render_entries_table(entries, tab.status) }}
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="clearfix"></div>

    <script>
        const API_ENDPOINTS = {
            approve: '{{ path('api_teamlogger_approve_entries') }}',
            reject: '{{ path('api_teamlogger_reject_entries') }}'
        };

        const STATUS_MAPPING = {
            'pending': 'UNAPPROVED',
            'approve': 'APPROVED',
            'disapprove': 'REJECTED'
        };
    </script>
    <script src="{{ asset('assets/js/admin/manualentry.js') }}"></script>
    <link rel="stylesheet" href="{{asset('assets/css/manual-entry/manualentry.css') }}">
{% endblock %}