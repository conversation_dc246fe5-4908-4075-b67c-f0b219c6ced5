 <div class="content">
        <div class="animated fadeIn">
            <div class="row">
                <div class="col-md-12">
                    <div id="flash-container"></div>
                    <div class="card">
                        <div class="card-header d-flex justify-content-between">
                            <strong class="card-title">Progress</strong>
                            {% if show %}
                            <a href="{{ path('employee_task_progress_create', {'task_id': task.id}) }}" class="btn btn-primary">Add New Progress</a>
                            {% endif %}
                        </div>
                        <div class="card-body">
                            <div class="table-stats order-table ov-h text-center">
                                <div id="taskTable" class="datatable-main-wrapper">Loading...</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="clearfix"></div>
 <link rel="stylesheet" href="{{ asset('assets/css/task-description.css') }}">
 <script src="{{ asset('assets/js/datatables.min.js') }}"></script>
    <script src="{{ asset('bundles/datatables/js/datatables.js') }}"></script>
    <script>
        $(function () {
            recordsTable = $('#taskTable').initDataTables({{ datatable_settings(datatable) }});
        });
    </script>
    <script src="{{ asset('assets/js/datatableclearsearch.js') }}"></script>
    <script src="{{ asset('assets/js/admin/common.js') }}"></script>