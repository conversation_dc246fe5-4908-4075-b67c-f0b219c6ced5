<?php

namespace App\Form;

use App\Entity\LeaveBalance;
use App\Entity\MasterEmployee;
use App\Repository\LeaveTypeRepository;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\CollectionType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormEvent;
use Symfony\Component\Form\FormEvents;
use Symfony\Component\OptionsResolver\OptionsResolver;

class LeaveBalanceFormType extends AbstractType
{
    private LeaveTypeRepository $leaveTypeRepository;

    public function __construct(LeaveTypeRepository $leaveTypeRepository)
    {
        $this->leaveTypeRepository = $leaveTypeRepository;
    }

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('leaveBalances', CollectionType::class, [
                'entry_type' => LeaveBalanceType::class,
                'label' => false,
                'allow_add' => false,
                'allow_delete' => false,
                'by_reference' => false,
                'entry_options' => [
                    'label' => false,
                ],
                'data' => $options['leaveBalances'],
            ]);

        $builder->addEventListener(FormEvents::PRE_SET_DATA, function (FormEvent $event) use ($options) {
            $form = $event->getForm();
            $data = $event->getData();
            $employee = $options['employee'];
            $existingBalances = $options['leaveBalances'];

            if ($employee && $data && isset($data['leaveBalances'])) {
                $leaveTypes = $this->leaveTypeRepository->findAll();
                $balances = $data['leaveBalances'];

                $existingMap = [];
                foreach ($existingBalances as $balance) {
                    $existingMap[$balance->getLeaveType()->getId()] = $balance;
                }
                foreach ($leaveTypes as $leaveType) {
                    if (!isset($existingMap[$leaveType->getId()])) {
                        $balance = new LeaveBalance();
                        $balance->setEmployee($employee);
                        $balance->setLeaveType($leaveType);
                        $balance->setTotalDays($leaveType->getMaxDays() ?? 0);
                        $balance->setUsedDays(0.0);
                        $balance->setRemainingDays($leaveType->getMaxDays() ?? 0);
                        $balances[] = $balance;
                    }
                }

                $form->get('leaveBalances')->setData($balances);
            } elseif ($employee) {
                $leaveTypes = $this->leaveTypeRepository->findAll();
                $balances = [];
                foreach ($leaveTypes as $leaveType) {
                    $balance = new LeaveBalance();
                    $balance->setEmployee($employee);
                    $balance->setLeaveType($leaveType);
                    $balance->setTotalDays($leaveType->getMaxDays() ?? 0);
                    $balance->setUsedDays(0.0);
                    $balance->setRemainingDays($leaveType->getMaxDays() ?? 0);
                    $balances[] = $balance;
                }
                $form->get('leaveBalances')->setData($balances);
            }
        });
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => null,
            'is_edit' => false,
            'employee' => null,
            'leaveBalances' => [],
        ]);

        $resolver->setAllowedTypes('employee', ['null', MasterEmployee::class]);
        $resolver->setAllowedTypes('leaveBalances', 'array');
    }
}