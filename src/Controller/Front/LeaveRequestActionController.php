<?php
namespace App\Controller\Front;

use App\Entity\LeaveRequest;
use App\Entity\MasterEmployee;
use App\Entity\LeaveBalance;
use App\Message\LeaveRequestStatusNotificationMessage;
use App\Service\Common\LeaveRequestService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Messenger\Exception\ExceptionInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Routing\Annotation\Route;

#[Route('/employee')]
class LeaveRequestActionController extends AbstractController
{
    public function __construct(
        private readonly LeaveRequestService $leaveRequestService,
        private readonly EntityManagerInterface $entityManager,
        private readonly MessageBusInterface $messageBus
    ) {}

    #[Route('/leave', name: 'employee_leave', methods: ['GET', 'POST'])]
    public function listLeaveRequests(Request $request): Response
    {
        $currentUser = $this->getUser();

        if (!$currentUser instanceof MasterEmployee) {
            throw new \LogicException('User must be an instance of MasterEmployee');
        }

        if ($currentUser->isHrAccount()) {
            $table = $this->leaveRequestService->createHrLeaveRequestTable($request,$currentUser);
        } elseif ($currentUser->isTeamLeader()) {
            $table = $this->leaveRequestService->createTeamleaderLeaveRequestTable($request, $currentUser);
        } else {
            throw new \LogicException('User must be either a team leader or HR');
        }

        if ($table->isCallback()) {
            return $table->getResponse();
        }

        return $this->render('Front/LeaveRequest/leaveRequest.html.twig', [
            'datatable' => $table,
            'isHr' => $currentUser->isHrAccount(),
        ]);
    }

    /**
     * @throws ExceptionInterface
     */
    #[Route('/leave/{id}/approve', name: 'employee_leave_request_approve', methods: ['POST'])]
    public function approveLeaveRequest(int $id, Request $request): JsonResponse
    {
        if (!$this->isCsrfTokenValid('approve'.$id, $request->request->get('_token'))) {
            return new JsonResponse(['success' => false, 'message' => 'Invalid CSRF token'], 403);
        }

        $leaveRequest = $this->entityManager->getRepository(LeaveRequest::class)->find($id);
        if (!$leaveRequest) {
            return new JsonResponse(['success' => false, 'message' => 'Leave request not found'], 404);
        }

        if ($leaveRequest->getStatus() !== LeaveRequest::STATUS_PENDING) {
            return new JsonResponse(['success' => false, 'message' => 'This leave request has already been processed'], 400);
        }
        $penaltyDays = (float) $request->request->get('penaltyDays', $leaveRequest->getPenaltyDays());
        $applyPenalty = (bool) $request->request->get('applyPenalty', false);

        $currentUser = $this->getUser();
        if (!$currentUser instanceof MasterEmployee) {
            return new JsonResponse(['success' => false, 'message' => 'User not authenticated'], 401);
        }

        if ($currentUser->isHrAccount()) {
            $leaveRequest->setHrApprovedBy($currentUser);
            $leaveRequest->setHrApprovedOn(new \DateTime());
        } elseif ($currentUser->isTeamLeader()) {
            $leaveRequest->setTeamLeadApprovedBy($currentUser);
            $leaveRequest->setTeamLeadApprovedOn(new \DateTime());
        } else {
            return new JsonResponse(['success' => false, 'message' => 'User lacks required permissions'], 401);
        }

        $leaveRequest->setStatus(LeaveRequest::STATUS_APPROVED);
        $leaveRequest->setPenaltyDays($penaltyDays);
        $leaveBalance = $leaveRequest->getLeaveType();
        if ($leaveBalance instanceof LeaveBalance && $applyPenalty && $penaltyDays > 0) {
            $leaveRequest->setPenaltyApplied(true);
            $remainingDays = $leaveBalance->getRemainingDays();
            if ($remainingDays >= $penaltyDays) {
                $leaveBalance->setUsedDays($leaveBalance->getUsedDays() + $penaltyDays);
            } else {
                $excessPenalty = $penaltyDays - $remainingDays;
                $leaveBalance->setUsedDays($leaveBalance->getUsedDays() + $remainingDays);
                $leaveBalance->setPenaltyDays($leaveBalance->getPenaltyDays() + $excessPenalty);
            }

            $leaveBalance->updateRemainingDays();
            $this->entityManager->persist($leaveBalance);
        } else {
            $leaveRequest->setPenaltyApplied(false);
        }
        $this->entityManager->persist($leaveRequest);
        $this->entityManager->flush();
        $this->messageBus->dispatch(new LeaveRequestStatusNotificationMessage(
            $leaveRequest->getId(),
            LeaveRequest::STATUS_APPROVED
        ));

        return new JsonResponse([
            'success' => true,
            'message' => 'Leave request approved successfully',
            'penaltyApplied' => $applyPenalty,
            'penaltyDays' => $penaltyDays
        ]);
    }

    /**
     * @throws ExceptionInterface
     */
    #[Route('/leave/{id}/reject', name: 'employee_leave_request_reject', methods: ['POST'])]
    public function rejectLeaveRequest(int $id, Request $request): JsonResponse
    {
        if (!$this->isCsrfTokenValid('reject'.$id, $request->request->get('_token'))) {
            return new JsonResponse(['success' => false, 'message' => 'Invalid CSRF token'], 403);
        }

        $leaveRequest = $this->entityManager->getRepository(LeaveRequest::class)->find($id);
        if (!$leaveRequest) {
            return new JsonResponse(['success' => false, 'message' => 'Leave request not found'], 404);
        }

        $currentDate = new \DateTime();
        if ($leaveRequest->getStatus() === LeaveRequest::STATUS_APPROVED && $leaveRequest->getStartDate() < $currentDate) {
            return new JsonResponse(['success' => false, 'message' => 'Cannot reject an approved leave request after its start date'], 400);
        }

        if ($leaveRequest->getStatus() !== LeaveRequest::STATUS_PENDING && $leaveRequest->getStatus() !== LeaveRequest::STATUS_APPROVED) {
            return new JsonResponse(['success' => false, 'message' => 'This leave request has already been processed'], 400);
        }

        $currentUser = $this->getUser();
        if (!$currentUser instanceof MasterEmployee) {
            return new JsonResponse(['success' => false, 'message' => 'User not authenticated'], 401);
        }

        if ($currentUser->isHrAccount()) {
            $leaveRequest->setHrApprovedBy($currentUser);
            $leaveRequest->setHrApprovedOn(new \DateTime());
        } elseif ($currentUser->isTeamLeader()) {
            $leaveRequest->setTeamLeadApprovedBy($currentUser);
            $leaveRequest->setTeamLeadApprovedOn(new \DateTime());
        } else {
            return new JsonResponse(['success' => false, 'message' => 'User lacks required permissions'], 401);
        }

        $leaveBalance = $leaveRequest->getLeaveType();
        if ($leaveBalance instanceof LeaveBalance) {
            if ($leaveRequest->getStatus() === LeaveRequest::STATUS_PENDING) {
                $daysToRefund = $leaveRequest->getDaysRequested();
                $leaveBalance->setUsedDays(max(0, $leaveBalance->getUsedDays() - $daysToRefund));
            } elseif ($leaveRequest->getStatus() === LeaveRequest::STATUS_APPROVED) {
                $daysRequested = $leaveRequest->getDaysRequested();
                if ($leaveRequest->isPenaltyApplied()) {
                    $totalPenaltyDays = $leaveRequest->getPenaltyDays();
                    $totalDays = $leaveBalance->getTotalDays();
                    $availableDaysAtApproval = $totalDays - $daysRequested;
                    if ($totalPenaltyDays > $availableDaysAtApproval) {
                        $excessPenalty = $totalPenaltyDays - $availableDaysAtApproval;
                        $leaveBalance->setPenaltyDays(max(0, $leaveBalance->getPenaltyDays() - $excessPenalty));
                    }
                    $penaltyAddedToUsed = min($totalPenaltyDays, $availableDaysAtApproval);
                    $totalToRefundFromUsed = $daysRequested + $penaltyAddedToUsed;
                    $leaveBalance->setUsedDays(max(0, $leaveBalance->getUsedDays() - $totalToRefundFromUsed));
                } else {
                    $leaveBalance->setUsedDays(max(0, $leaveBalance->getUsedDays() - $daysRequested));
                }
            }

            $leaveBalance->updateRemainingDays();
            $this->entityManager->persist($leaveBalance);
        }
        $leaveRequest->setStatus(LeaveRequest::STATUS_REJECTED);
        $this->entityManager->persist($leaveRequest);
        $this->entityManager->flush();
        $this->messageBus->dispatch(new LeaveRequestStatusNotificationMessage(
            $leaveRequest->getId(),
            LeaveRequest::STATUS_REJECTED
        ));

        return new JsonResponse(['success' => true, 'message' => 'Leave request rejected successfully']);
    }
}