<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;
use Doctrine\DBAL\Types\Types;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;

#[ORM\Entity]
#[ORM\Table(name: 'bug')]
class Bug
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: Types::INTEGER)]
    /** @phpstan-ignore-next-line */
    private ?int $id = null;

    #[ORM\ManyToOne(targetEntity: Project::class)]
    #[ORM\JoinColumn(nullable: false)]
    private ?Project $project = null;

    #[ORM\ManyToOne(targetEntity: MasterEmployee::class)]
    #[ORM\JoinColumn(nullable: false)]
    private ?MasterEmployee $assignedTo = null;

    #[ORM\Column(type: Types::STRING, enumType: Priority::class)]
    private Priority $priority;

    #[ORM\Column(type: Types::STRING, enumType: Severity::class)]
    private Severity $severity;

    #[ORM\Column(type: Types::STRING, length: 50, enumType: Category::class)]
    private Category $category;

    #[ORM\ManyToOne(targetEntity: Task::class)]
    #[ORM\JoinColumn(nullable: true, onDelete: 'CASCADE')]
    private ?Task $task = null;

    #[ORM\ManyToOne(targetEntity: TaskAssignment::class)]
    #[ORM\JoinColumn(nullable: true, onDelete: 'CASCADE')]
    private ?TaskAssignment $taskAssignment = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $comments = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $testerComments = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $createdAt = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $updatedAt = null;

    #[ORM\ManyToOne(targetEntity: MasterEmployee::class)]
    #[ORM\JoinColumn(nullable: true)]
    private ?MasterEmployee $assignedBy = null;

    #[ORM\ManyToOne(targetEntity: User::class)]
    #[ORM\JoinColumn(nullable: true)]
    private ?User $assignedByAdmin = null;

    #[ORM\Column(type: Types::STRING, enumType: Status::class)]
    private Status $status;

    #[ORM\OneToMany(targetEntity: BugStatusHistory::class, mappedBy: 'bug', cascade: ['persist', 'remove'])]
    private Collection $statusHistory;

    public function __construct()
    {
        $this->createdAt = new \DateTime();
        $this->status = Status::OPEN;
        $this->statusHistory = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getProject(): ?Project
    {
        return $this->project;
    }

    public function setProject(?Project $project): void
    {
        $this->project = $project;
    }

    public function getAssignedTo(): ?MasterEmployee
    {
        return $this->assignedTo;
    }

    public function setAssignedTo(?MasterEmployee $assignedTo): void
    {
        $this->assignedTo = $assignedTo;
    }

    public function getPriority(): Priority
    {
        return $this->priority;
    }

    public function setPriority(Priority $priority): void
    {
        $this->priority = $priority;
    }

    public function getSeverity(): Severity
    {
        return $this->severity;
    }

    public function setSeverity(Severity $severity): void
    {
        $this->severity = $severity;
    }

    public function getCategory(): Category
    {
        return $this->category;
    }

    public function setCategory(Category $category): void
    {
        $this->category = $category;
    }



    public function getComments(): ?string
    {
        return $this->comments;
    }

    public function setComments(?string $comments): void
    {
        $this->comments = $comments;
    }

    public function getTesterComments(): ?string
    {
        return $this->testerComments;
    }

    public function setTesterComments(?string $testerComments): void
    {
        $this->testerComments = $testerComments;
    }

    public function getCreatedAt(): ?\DateTimeInterface
    {
        return $this->createdAt;
    }

    public function setCreatedAt(?\DateTimeInterface $createdAt): void
    {
        $this->createdAt = $createdAt;
    }

    public function getUpdatedAt(): ?\DateTimeInterface
    {
        return $this->updatedAt;
    }

    public function setUpdatedAt(?\DateTimeInterface $updatedAt): void
    {
        $this->updatedAt = $updatedAt;
    }

    public function getTaskDescription(): ?string
    {
        return $this->task?->getDescription();
    }

    public function getAssignedByAdmin(): ?User
    {
        return $this->assignedByAdmin;
    }

    public function setAssignedByAdmin(?User $assignedByAdmin): void
    {
        $this->assignedByAdmin = $assignedByAdmin;
    }

    public function getAssignedBy(): ?MasterEmployee
    {
        return $this->assignedBy;
    }

    public function setAssignedBy(?MasterEmployee $assignedBy): void
    {
        $this->assignedBy = $assignedBy;
    }


    public function getStatus(): Status
    {
        return $this->status;
    }

    public function setStatus(Status $status): void
    {
        $this->status = $status;
    }

    public function getStatusHistory(): Collection
    {
        return $this->statusHistory;
    }

    public function getTask(): ?Task
    {
        return $this->task;
    }

    public function setTask(?Task $task): void
    {
        $this->task = $task;
    }

    public function getTaskAssignment(): ?TaskAssignment
    {
        return $this->taskAssignment;
    }

    public function setTaskAssignment(?TaskAssignment $taskAssignment): void
    {
        $this->taskAssignment = $taskAssignment;
    }
}

enum Priority: string
{
    case LOW = 'Low';
    case MEDIUM = 'Medium';
    case HIGH = 'High';
    case URGENT = 'Urgent';
}

enum Severity: string
{
    case MINOR = 'Minor';
    case MODERATE = 'Moderate';
    case MAJOR = 'Major';
    case CRITICAL = 'Critical';
}

enum Category: string
{
    case UI = 'UI';
    case FUNCTIONAL = 'Functional';
    case PERFORMANCE = 'Performance';
    case SECURITY = 'Security';
    case OTHER = 'Other';
}

enum Status: string
{
    case OPEN = 'Open';
    case IN_PROGRESS = 'In Progress';
    case RESOLVED = 'Resolved';
    case CLOSED = 'Closed';
    case REOPENED = 'Reopened';
}
?>