<?php

namespace App\Validator;

use App\Entity\ProjectTeam;
use App\Repository\ProjectTeamRepository;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;

class UniqueProjectTeamMemberValidator extends ConstraintValidator
{
    public function __construct(
        private readonly ProjectTeamRepository $projectTeamRepository
    ) {
    }

    public function validate($object, Constraint $constraint): void
    {
        if (!$object instanceof ProjectTeam) {
            return;
        }

        if (!$constraint instanceof UniqueProjectTeamMember) {
            return;
        }

        $project = $object->getProject();
        $teamMember = $object->getTeamMember();

        if (!$project || !$teamMember) {
            return;
        }

        // Skip validation for existing records (updates)
        if ($object->getId()) {
            return;
        }

        if ($this->projectTeamRepository->isTeamMemberAlreadyAssigned($project->getId(), $teamMember->getId())) {
            $this->context->buildViolation($constraint->message)
                ->atPath('teamMember')
                ->addViolation();
        }
    }
}
