<?php

namespace App\Repository;

use App\Entity\Announcement;
use App\Entity\MasterEmployee;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Announcement>
 */
class AnnouncementRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Announcement::class);
    }

    /**
     * Find important and active announcements, prioritized by isImportant and priority.
     *
     * @param int $limit
     * @return Announcement[]
     */
    public function findImportantAnnouncements(int $limit = 10): array
    {
        return $this->createQueryBuilder('a')
            ->where('a.status = :status')
            ->andWhere('a.startDate <= :now')
            ->andWhere('a.endDate >= :now')
            ->setParameter('status', 'published')
            ->setParameter('now', new \DateTime())
            ->orderBy('a.isImportant', 'DESC')
            ->addOrderBy('
                CASE a.priority
                    WHEN \'high\' THEN 1
                    WHEN \'pinned\' THEN 2
                    WHEN \'normal\' THEN 3
                    ELSE 4
                END', 'ASC')
            ->addOrderBy('a.startDate', 'DESC')
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();
    }

    /**
     * Find announcements relevant to a specific employee.
     *
     * @param MasterEmployee $employee
     * @param int $limit
     * @return Announcement[]
     */
    public function findAnnouncementsForEmployee(MasterEmployee $employee, int $limit = 10): array
    {
        /** @phpstan-ignore-next-line */
        $departmentIds = $employee->getDepartments()
            ? $employee->getDepartments()->map(fn($department) => $department->getId())->toArray()
            : [];

        $qb = $this->createQueryBuilder('a')
            ->leftJoin('a.departments', 'd')
            ->where('a.status = :status')
            ->andWhere('a.startDate <= :now')
            ->andWhere('a.endDate >= :now')
            ->andWhere('
                a.targetAllEmployees = :true OR
                (
                    d.id IN (:departmentIds) AND
                    (
                        a.targetTeamLeadersOnly = :false OR
                        (a.targetTeamLeadersOnly = :true AND :isTeamLeader = :true)
                    )
                )
            ')
            ->setParameter('status', 'published')
            ->setParameter('now', new \DateTime())
            ->setParameter('true', true)
            ->setParameter('false', false)
            ->setParameter('departmentIds', $departmentIds ?: [0])
            /** @phpstan-ignore-next-line */
            ->setParameter('isTeamLeader', $employee->isTeamLeader() ?? false)
            ->orderBy('a.isImportant', 'DESC')
            ->addOrderBy('
                CASE a.priority
                    WHEN \'2\' THEN 1
                    WHEN \'1\' THEN 2
                    WHEN \'0\' THEN 3
                    ELSE 4
                END', 'ASC')
            ->addOrderBy('a.startDate', 'DESC')
            ->setMaxResults($limit)
            ->getQuery();

        return $qb->getResult();
    }
}
