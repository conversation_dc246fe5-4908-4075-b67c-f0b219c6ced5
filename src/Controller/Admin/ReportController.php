<?php
namespace App\Controller\Admin;

use App\Service\Admin\ReportConfigurationService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Csrf\CsrfTokenManagerInterface;
use App\Traits\ExceptionLoggerTrait;

#[Route('/admin')]
class ReportController extends AbstractController
{
    use ExceptionLoggerTrait;
    private const FIELD_REMAINING_JUSTIFICATION = 'REMAINING_JUSTIFICATION';
    private const FIELD_DISAPPROVAL_REASON = 'DisApproval_Reason';
    private const FIELD_RESEND_REASON = 'Justification_Reminder_days';
    private const FIELD_TOTAL_WORKING_HOURS = 'Total_Working_Hours';
    private const FIELD_Min_HOURS_Notification = 'Minimum_Hours_Notification';

    public function __construct(
        private readonly ReportConfigurationService $reportConfigService,
        private readonly CsrfTokenManagerInterface $csrfTokenManager,
        ParameterBagInterface                   $parameterBag,
    ) {
        $this->initializeLogger($parameterBag, 'report_controller');
    }

    #[Route('/report-config-page', name: 'report_config_page', methods: ['GET'])]
    public function reportConfigPage(): Response
    {
        $fields = [
            'Remaining' => self::FIELD_REMAINING_JUSTIFICATION,
            'Disapprove' => self::FIELD_DISAPPROVAL_REASON,
            'ReminderDays' => self::FIELD_RESEND_REASON,
            'WorkingHours' => self::FIELD_TOTAL_WORKING_HOURS,
            'MinimumHours' => self::FIELD_Min_HOURS_Notification,
        ];

        $settings = $this->reportConfigService->getReportSettings($fields);

        return $this->render('admin/report_config.html.twig', [
            'settings' => $settings,
        ]);
    }

    #[Route('/report-config/update', name: 'report_config_update', methods: ['POST'])]
    public function update(Request $request): JsonResponse
    {
        $token = $request->headers->get('X-CSRF-Token');
        if (!$this->csrfTokenManager->isTokenValid(new \Symfony\Component\Security\Csrf\CsrfToken('setting_update', $token))) {
            return new JsonResponse(['message' => 'Invalid CSRF token'], Response::HTTP_FORBIDDEN);
        }

        try {
            $data = json_decode($request->getContent(), true, 512, JSON_THROW_ON_ERROR);

            $fields = [
                'Remaining' => self::FIELD_REMAINING_JUSTIFICATION,
                'Disapprove' => self::FIELD_DISAPPROVAL_REASON,
                'ReminderDays' => self::FIELD_RESEND_REASON,
                'WorkingHours' => self::FIELD_TOTAL_WORKING_HOURS,
                'MinimumHours' => self::FIELD_Min_HOURS_Notification,
            ];

            foreach ($fields as $key => $fieldName) {
                if (isset($data[$key])) {
                    $this->reportConfigService->updateConfiguration($fieldName, $data[$key]);
                }
            }

            return new JsonResponse(['message' => 'Report settings updated successfully']);
        } catch (\JsonException $e) {
            return new JsonResponse(['message' => 'Invalid data'], Response::HTTP_BAD_REQUEST);
        } catch (\Exception $e) {
            $this->reportConfigService->log('Error updating settings', ['exception' => $e]);
            return new JsonResponse(['message' => 'Error updating settings: ' . $e->getMessage()], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}