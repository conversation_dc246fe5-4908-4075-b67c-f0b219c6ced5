<?php

namespace App\Entity;

use App\Repository\ExtraPayRepository;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity(repositoryClass: ExtraPayRepository::class)]
#[ORM\Table(name: 'extra_pay', uniqueConstraints: [
    new ORM\UniqueConstraint(name: 'employee_month_year_unique', columns: ['employee_id', 'month', 'year'])
])]
#[ORM\HasLifecycleCallbacks]
class ExtraPay
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private ?int $id = null;

    #[ORM\ManyToOne(targetEntity: MasterEmployee::class)]
    #[ORM\JoinColumn(nullable: false)]
    private ?MasterEmployee $employee = null;

    #[ORM\Column(type: 'integer')]
    #[Assert\Range(min: 1, max: 12)]
    private int $month;

    #[ORM\Column(type: 'integer')]
    #[Assert\Range(min: 2000, max: 2100)]
    private int $year;

    #[ORM\Column(type: 'float', nullable: true)]
    #[Assert\GreaterThanOrEqual(0)]
    private ?float $hours = null;

    #[ORM\Column(type: 'decimal', precision: 12, scale: 2, nullable: true)]
    #[Assert\GreaterThanOrEqual(0)]
    private ?string $totalPay = null;

    #[ORM\Column(type: 'decimal', precision: 12, scale: 2, nullable: true)]
    #[Assert\GreaterThanOrEqual(0)]
    private ?string $bonus = null;

    #[ORM\Column(type: 'decimal', precision: 12, scale: 2, nullable: true)]
    #[Assert\GreaterThanOrEqual(0)]
    private ?string $officeExpense = null;

    #[ORM\Column(type: 'datetime')]
    private ?\DateTimeInterface $createdAt = null;

    #[ORM\Column(type: 'datetime')]
    private ?\DateTimeInterface $updatedAt = null;

    public function __construct()
    {
        $this->createdAt = new \DateTime();
        $this->updatedAt = new \DateTime();
    }

    #[ORM\PreUpdate]
    public function onPreUpdate(): void
    {
        $this->updatedAt = new \DateTime();
    }

    public function getId(): ?int { return $this->id; }

    public function getEmployee(): ?MasterEmployee { return $this->employee; }
    public function setEmployee(?MasterEmployee $employee): void { $this->employee = $employee; }

    public function getMonth(): int { return $this->month; }
    public function setMonth(int $month): void { $this->month = $month; }

    public function getYear(): int { return $this->year; }
    public function setYear(int $year): void { $this->year = $year; }

    public function getHours(): ?float { return $this->hours; }
    public function setHours(?float $hours): void { $this->hours = $hours; }

    public function getTotalPay(): ?string { return $this->totalPay; }
    public function setTotalPay(?string $totalPay): void { $this->totalPay = $totalPay; }

    public function getBonus(): ?string { return $this->bonus; }
    public function setBonus(?string $bonus): void { $this->bonus = $bonus; }

    public function getOfficeExpense(): ?string { return $this->officeExpense; }
    public function setOfficeExpense(?string $officeExpense): void { $this->officeExpense = $officeExpense; }

    public function getCreatedAt(): ?\DateTimeInterface { return $this->createdAt; }
    public function setCreatedAt(?\DateTimeInterface $createdAt): void { $this->createdAt = $createdAt; }

    public function getUpdatedAt(): ?\DateTimeInterface { return $this->updatedAt; }
    public function setUpdatedAt(?\DateTimeInterface $updatedAt): void { $this->updatedAt = $updatedAt; }
}