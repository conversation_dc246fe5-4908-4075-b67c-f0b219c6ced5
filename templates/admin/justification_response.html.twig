{# templates/admin/justification_response.html.twig #}
<!DOCTYPE html>
<html>
<head>
    <title>Hours Justification</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    {#    <link href="{{ asset('assets/css/justification/justificationresponse.css') }}"> #}
    <style>
        body {
            background-color: #f8f9fa;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .form-container {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 2rem;
            width: 100%;
            max-width: 500px;
        }

        .form-container h1 {
            margin-bottom: 1.5rem;
            color: #212529;
        }

        .alert {
            margin-bottom: 1rem;
            padding: 1rem;
            border: none;
            border-radius: 6px;
        }

        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border-left: 4px solid #28a745;
        }

        .alert-danger {
            background-color: #f8d7da;
            color: #721c24;
            border-left: 4px solid #dc3545;
        }

        .btn-return {
            background-color: #ff7043;
            border: none;
            padding: 12px;
            font-weight: 500;
            width: 100%;
            margin-top: 1rem;
            color: white;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            border-radius: 4px;
            transition: background-color 0.2s;
        }

        .btn-return:hover {
            background-color: #f4511e;
            color: white;
        }
    </style>
</head>
<body>
<div class="form-container">
    {% if status == 'success' %}
        <h1 class="fw-bold">Justification Submitted</h1>
        <div class="alert alert-success">
            <h4 class="alert-heading mb-2">Thank you!</h4>
            <p class="mb-0">{{ message }}</p>
        </div>
    {% elseif status == 'expired' %}
        <h1 class="fw-bold">Link Expired</h1>
        <div class="alert alert-danger">
            <h4 class="alert-heading mb-2">Oops!</h4>
            <p class="mb-0">{{ message }}</p>
        </div>
    {% else %}
        <h1 class="fw-bold">Error</h1>
        <div class="alert alert-danger">
            <h4 class="alert-heading mb-2">Something went wrong</h4>
            <p class="mb-0">{{ message }}</p>
        </div>
    {% endif %}
</div>

<script src="{{ asset('assets/js/bootstrap.bundle.min.js') }}"></script>
</body>
</html>