 <div class="content">
     <div class="card">
         {% include 'admin/partials/filters.html.twig'%}
     </div>
        <div class="animated fadeIn">
            <div class="row">
                <div id="order-loader">
                    <img src="{{ asset('images/loader.svg') }}" alt="loader">
                </div>
                <div class="col-md-12">
                    <div id="flash-container"></div>
                    <div class="card">
                        <div class="card-header d-flex justify-content-between">
                            <strong class="card-title">{{ project.name }} Tasks</strong>
                            {% if is_granted('ROLE_ADMIN') %}
                                <a href="{{ path('task_create', {'project_id': project.id}) }}" class="btn btn-primary">Add New Task</a>
                            {% elseif is_granted('ROLE_EMPLOYEE') %}
                                <a href="{{ path('employee_task_create', {'project_id': project.id}) }}" class="btn btn-primary">Add New Task</a>
                            {% endif %}
                        </div>
                        <div class="card-body">
                            <div class="table-stats order-table ov-h text-center">
                                <div id="taskTable" class="datatable-main-wrapper">Loading...</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="clearfix"></div>
    <script src="{{ asset('assets/js/datatables.min.js') }}"></script>
    <script src="{{ asset('bundles/datatables/js/datatables.js') }}"></script>
 <script>
     $(function () {
         recordsTable = $('#taskTable').initDataTables({{ datatable_settings(datatable) }});
     });
 </script>
    <script src="{{ asset('assets/js/toggle.js') }}"></script>
 <script src="{{ asset('assets/js/admin/employee_task_filter.js') }}"></script>
 <script src="{{ asset('assets/js/moment.min.js') }}"></script>
 <script src="{{ asset('assets/js/daterangepicker.min.js') }}"></script>
    <script src="{{ asset('assets/js/datatableclearsearch.js') }}"></script>
    <script src="{{ asset('assets/js/admin/common.js') }}"></script>
    <script>
     document.addEventListener('DOMContentLoaded', function () {
         document.body.addEventListener('click', function (e) {
             if (e.target.classList.contains('delete-task-btn')) {
                 const button = e.target;
                 button.disabled = true;
                 const url = button.getAttribute('data-url');
                 const token = button.getAttribute('data-token');

                 fetch(url, {
                     method: 'POST',
                     headers: {
                         'Content-Type': 'application/x-www-form-urlencoded',
                         'X-Requested-With': 'XMLHttpRequest',
                     },
                     body: `_token=${encodeURIComponent(token)}`
                 })
                     .then(response => response.json())
                     .then(data => {
                         if (data.success) {
                             recordsTable.then(function(dt) {
                                 dt.ajax.reload(null, false);
                             });
                         }
                     })
             }
         });
     });
    </script>