<div class="feedback-history-container">
    <div class="feedback-history-wrapper">
        <div class="period-selector-card">
            <div class="period-selector-header">
                <div class="selector-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                        <line x1="16" y1="2" x2="16" y2="6"></line>
                        <line x1="8" y1="2" x2="8" y2="6"></line>
                        <line x1="3" y1="10" x2="21" y2="10"></line>
                    </svg>
                </div>
                <span>Select Review Period</span>
            </div>
            <div class="period-selector-content">
                <form method="post" action="
                    {% if app.request.attributes.get('_route') == 'admin_employee_history' %}
                    {{ path('admin_employee_history') }}
                    {% elseif app.request.attributes.get('_route') == 'employee_employee_history' %}
                    {{ path('employee_employee_history') }}
                    {% elseif app.request.attributes.get('_route') == 'employee_history' %}
                    {{ path('employee_history') }}
                     {% elseif app.request.attributes.get('_route') == 'employee_history_self' %}
                    {{ path('employee_history_self') }}
                    {% else %}
                    {{ path('admin_employee_history_self_evaluation') }}
                 {% endif %}
                ">
                    <div class="selection-fields">
                        {% if show %}
                            <div class="select-wrapper">
                                <select name="employee" class="form-select" onchange="this.form.submit()">
                                    <option value="" disabled {% if not selectedEmployee %}selected{% endif %}>Select an Employee</option>
                                    {% for emp in employees %}
                                        <option value="{{ emp.id }}" {% if selectedEmployee and selectedEmployee.id == emp.id %}selected{% endif %}>
                                            {{ emp.name }}
                                        </option>
                                    {% endfor %}
                                </select>
                                <div class="select-arrow">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <polyline points="6 9 12 15 18 9"></polyline>
                                    </svg>
                                </div>
                            </div>
                        {% endif %}
                        <div class="select-wrapper">
                            <select name="date" class="form-select" onchange="this.form.submit()">
                                {% for date in availableDates %}
                                    <option value="{{ date }}" {% if date == selectedDate %}selected{% endif %}>
                                        {{ date|date('F Y') }}
                                    </option>
                                {% endfor %}
                            </select>
                            <div class="select-arrow">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <polyline points="6 9 12 15 18 9"></polyline>
                                </svg>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        {% if groupedSubmissions is empty %}
            <div class="empty-state-card">
                <div class="empty-state-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="#fd7e14" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="12" cy="12" r="10"></circle>
                        <line x1="12" y1="8" x2="12" y2="12"></line>
                        <line x1="12" y1="16" x2="12.01" y2="16"></line>
                    </svg>
                </div>
                <h3>No Feedback History Available</h3>
                <p>There are no feedback submissions for the selected period.</p>
            </div>
        {% else %}
            <div class="timeline">
                {% for period, submissions in groupedSubmissions %}
                    <div class="timeline-period">
                        <div class="timeline-period-header">
                            <div class="timeline-period-marker"></div>
                            <h3>{{ period }}</h3>
                            <div class="period-stats">
                                <span>{{ submissions|length }} evaluation{{ submissions|length > 1 ? 's' : '' }}</span>
                            </div>
                        </div>

                        <div class="timeline-events">
                            {% for submission in submissions %}
                                <div class="feedback-card">
                                    <div class="feedback-card-header">
                                        <div class="feedback-icon">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#fd7e14" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                                                <polyline points="14 2 14 8 20 8"></polyline>
                                                <line x1="16" y1="13" x2="8" y2="13"></line>
                                                <line x1="16" y1="17" x2="8" y2="17"></line>
                                                <polyline points="10 9 9 9 8 9"></polyline>
                                            </svg>
                                        </div>
                                        <div class="feedback-title">
                                            <h4>{{ submission.template.name }}</h4>
                                            <span class="reviewer">
                                                {% if submission.employeeReviewer is not null %}
                                                    Reviewed by: {{ submission.employeeReviewer.name }}
                                                {% else %}
                                                    Reviewed by: {{ submission.userReviewer.username }}
                                                {% endif %}
                                            </span>
                                        </div>
                                        <div class="feedback-meta">
                                            <span class="submission-date">{{ submission.submissionDate|custom_date }}</span>
                                            {% if submission.rating %}
                                                <span class="rating-badge">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="#fd7e14" stroke="#fd7e14" stroke-width="1" stroke-linecap="round" stroke-linejoin="round">
                                                        <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
                                                    </svg>
                                                    {{ submission.rating }}
                                                </span>
                                            {% endif %}
                                        </div>
                                    </div>

                                    <div class="feedback-stats">
                                        {% if submission.ActiveMinRating %}
                                            <div class="stat-item">
                                                <span class="stat-label">Active Minutes</span>
                                                <div class="stat-bar">
                                                    <div class="stat-progress" style="width: {{ submission.ActiveMinRating }}%"></div>
                                                </div>
                                                <span class="stat-value">{{ submission.ActiveMinRating|number_format(2, '.', '') }}%</span>
                                            </div>
                                        {% endif %}

                                        {% if submission.ActiveSecRating %}
                                            <div class="stat-item">
                                                <span class="stat-label">Active Seconds</span>
                                                <div class="stat-bar">
                                                    <div class="stat-progress" style="width: {{ submission.ActiveSecRating }}%"></div>
                                                </div>
                                                <span class="stat-value">{{ submission.ActiveSecRating|number_format(2, '.', '') }}%</span>
                                            </div>
                                        {% endif %}
                                    </div>

                                    {% set responses = submission.responses %}
                                    {% if responses %}
                                        {% set groupedResponses = {} %}
                                        {% for response in responses %}
                                            {% set sectionName = response.section.name %}
                                            {% if groupedResponses[sectionName] is not defined %}
                                                {% set groupedResponses = groupedResponses|merge({
                                                    (sectionName): []
                                                }) %}
                                            {% endif %}
                                            {% set groupedResponses = groupedResponses|merge({
                                                (sectionName): groupedResponses[sectionName]|merge([response])
                                            }) %}
                                        {% endfor %}

                                        <div class="feedback-sections">
                                            {% for sectionName, sectionResponses in groupedResponses %}
                                                <div class="feedback-section">
                                                    <div class="section-header">
                                                        <div class="section-icon">
                                                            {% set icon = sectionResponses[0].section.icon|default('file-text') %}
                                                            {% if icon == 'file-text' %}
                                                                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="#fd7e14" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                                                                    <polyline points="14 2 14 8 20 8"></polyline>
                                                                    <line x1="16" y1="13" x2="8" y2="13"></line>
                                                                    <line x1="16" y1="17" x2="8" y2="17"></line>
                                                                    <polyline points="10 9 9 9 8 9"></polyline>
                                                                </svg>
                                                            {% elseif icon == 'check-circle' %}
                                                                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="#fd7e14" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                                                                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                                                                </svg>
                                                            {% else %}
                                                                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="#fd7e14" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                                    <circle cx="12" cy="12" r="10"></circle>
                                                                </svg>
                                                            {% endif %}
                                                        </div>
                                                        <h5>{{ sectionName }}</h5>
                                                    </div>

                                                    <div class="section-responses">
                                                        {% for response in sectionResponses %}
                                                            <div class="response-item">
                                                                <div class="response-question">{{ response.field.fieldLabel }}</div>
                                                                <div class="response-answer">
                                                                    {% if response.field.fieldType == 'rating' %}
                                                                        <div class="rating-display">
                                                                            {% for i in 1..5 %}
                                                                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="{% if i <= response.responseValue %}#fd7e14{% else %}#e5e7eb{% endif %}" stroke="#fd7e14" stroke-width="1" stroke-linecap="round" stroke-linejoin="round">
                                                                                    <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
                                                                                </svg>
                                                                            {% endfor %}
                                                                        </div>

                                                                    {%  elseif response.field.fieldType == 'date' %}
                                                                        {% set values = response.responseValue|date('d-m-Y') %}
                                                                        <div class="text-response">{{ values }}</div>
                                                                    {%  elseif response.field.fieldType == 'checkbox' %}
                                                                        {% set values = response.responseValue|json_decode %}
                                                                        <div class="checkbox-values">
                                                                            {% for value in values %}
                                                                                <span class="checkbox-tag">{{ value }}</span>
                                                                            {% endfor %}
                                                                        </div>
                                                                    {% else %}
                                                                            <div class="text-response">{{ response.responseValue }}</div>
                                                                    {% endif %}
                                                                </div>
                                                            </div>
                                                        {% endfor %}
                                                    </div>
                                                </div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    </div>
</div>
<div class="clearfix"></div>