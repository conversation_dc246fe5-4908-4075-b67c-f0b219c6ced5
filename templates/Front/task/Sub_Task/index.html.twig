{% extends 'Front/dashboard/base.html.twig' %}

{% block body %}
    {% include 'Front/partials/breadcrumbs.html.twig' with {
        breadcrumbs: [
            { 'label': 'Dashboard', 'url': url('dashboard_employee') },
            {
                'label': isHistory ? 'Task History' : 'My Tasks',
                'url': isHistory ? path('employee_task_history') : path('employee_task')
            },
        ],
        active: task.title
    } %}
    <div class="content">
        <div class="animated fadeIn">
            <div class="row">
                <div class="col-md-12">
                    <div id="flash-container"></div>
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <strong class="card-title">{{ task.title }}</strong>
                            <a href="{{ path('employee_task_assignment_create', { 'task_id': task.id }) }}" class="btn btn-primary">
                               Add Assignment
                            </a>
                        </div>
                        <div class="card-body">
                            <div class="table-stats order-table ov-h text-center">
                                <div id="taskAssignmentTable" class="datatable-main-wrapper">Loading...</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="clearfix"></div>
    <link rel="stylesheet" href="{{ asset('assets/css/task-description.css') }}">
    <script src="{{ asset('assets/js/datatables.min.js') }}"></script>
    <script src="{{ asset('bundles/datatables/js/datatables.js') }}"></script>
    <script src="{{ asset('assets/js/toggle.js') }}"></script>
    <script>
        $(function () {
            recordsTable = $('#taskAssignmentTable').initDataTables({{ datatable_settings(datatable) }});
        });
    </script>
    <script src="{{ asset('assets/js/datatableclearsearch.js') }}"></script>
    <script src="{{ asset('assets/js/admin/common.js') }}"></script>
{% endblock %}