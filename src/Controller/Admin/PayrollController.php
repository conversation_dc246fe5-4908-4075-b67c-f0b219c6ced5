<?php

namespace App\Controller\Admin;

use App\Entity\Payroll;
use App\Form\PayrollFormType;
use App\Repository\EmployeeArrearsRepository;
use App\Repository\ExtraPayRepository;
use App\Repository\HolidayRepository;
use App\Repository\MasterEmployeeRepository;
use App\Repository\SalaryStructureRepository;
use App\Repository\LeaveRequestRepository;
use App\Service\Admin\AttendenceTableService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Form\Extension\Core\Type\CollectionType;

class PayrollController extends AbstractController
{
    /**
     * @throws \DateMalformedStringException
     */
    #[Route('/admin/payroll', name: 'admin_payroll_index', methods: ['GET', 'POST'])]
    public function index(
        Request $request,
        HolidayRepository $holidayRepo,
        MasterEmployeeRepository $employeeRepo,
        EntityManagerInterface $em,
        SalaryStructureRepository $salaryStructureRepo,
        LeaveRequestRepository $leaveRequestRepo,
        AttendenceTableService $attendenceTableService,
        ExtraPayRepository $extraPayRepo,
        EmployeeArrearsRepository $arrearsRepository
    ): Response {
        $year = $request->query->getInt('year', (int) date('Y'));
        $month = $request->query->getInt('month', (int) date('m'));

        $workingDays = $this->calculateWorkingDays($year, $month, $holidayRepo);
        $employees = $employeeRepo->findBy(['isDelete' => false]);
        $payrolls = $em->getRepository(Payroll::class)->findBy(['month' => $month, 'year' => $year]);
        $leaveDaysMap = [];
        $payrollMap = [];
        $ctcMap = [];
        $lwpMap = [];
        $deductionFlags = [];
        // Prepare payroll data and maps
        $selectedDate = new \DateTimeImmutable(sprintf('%04d-%02d-01', $year, $month));
        foreach ($employees as $employee) {
            $salaryStructure = $salaryStructureRepo->findValidCtcForEmployee($employee->getId(), $selectedDate);
            $ctc = $salaryStructure ? $salaryStructure->getCtc() : null;
            $ctcMap[$employee->getId()] = $ctc;
            $lwp = $leaveRequestRepo->getLwpDaysForEmployee($employee, $year, $month);
            $lwpMap[$employee->getId()] = $lwp;
            $leaveDays = $attendenceTableService->getLeaveDays($employee, $year, $month);
            $leaveDaysMap[$employee->getId()] = $leaveDays;
            // Get deduction flags from SalaryStructure
            $deductPF = $salaryStructure ? $salaryStructure->isDeductPF() : true;
            $deductESIC = $salaryStructure ? $salaryStructure->isDeductESIC() : true;
            $deductionFlags[$employee->getId()] = [
                'pf' => $deductPF,
                'esic' => $deductESIC,
            ];

            $extraPaySum = $extraPayRepo->getTotalPayForEmployeeMonthYear($employee, $month, $year) ?? 0.0;
            $arrearsSum = $arrearsRepository->getTotalArrearsForEmployeeMonthYear($employee, $month, $year) ?? 0.0;
            $totalExtraPay = $extraPaySum + $arrearsSum;

            $payroll = $em->getRepository(Payroll::class)->findOneBy([
                'employee' => $employee,
                'month' => $month,
                'year' => $year,
            ]);

            if (!$payroll) {
                $payroll = new Payroll();
                $payroll->setEmployee($employee);
                $payroll->setMonth($month);
                $payroll->setYear($year);
                $payroll->setCtc($ctc);
                $payroll->setInputCtcForCalculation($ctc);
                $payroll->setDeduction($lwp);
                $payroll->setPresentDays($workingDays - $leaveDays);
                $payroll->setWorkingDays($workingDays);
            }
            $payroll->setExtraPay($totalExtraPay);

            /** @var float|null $inputCtc */
            $inputCtc = $payroll->getInputCtcForCalculation();
            /** @var float|null $pfEmployer */
            $pfEmployer = $payroll->getPfEmployer();
            $salary = $inputCtc - $pfEmployer;
            $payroll->setSalary($salary);
            $salary = $payroll->getSalary() ?? 0.0;
            $perDayBasic = $workingDays > 0 ? round($salary / $workingDays, 0) : 0;
            $payroll->setPerDayBasedOnBasic($perDayBasic);
            $workingDays = $payroll->getWorkingDays() ?? 0;
            $deduction = $payroll->getDeduction() ?? 0.0;
            $totalLeaveDeduction = ($workingDays > 0) ? round(($salary / $workingDays) * $deduction, 0) : 0;
            $payroll->setTotalLeaveDeduction($totalLeaveDeduction);
            $totalLeaveDeduction = $payroll->getTotalLeaveDeduction() ?? 0.0;
            $extraPay = $payroll->getExtraPay() ?? 0.0;
            $salaryAfterLeave = round($salary - $totalLeaveDeduction + $extraPay, 0);
            $payroll->setSalaryAfterLeave($salaryAfterLeave);
            $salaryAfterLeave = $payroll->getSalaryAfterLeave() ?? 0.0;
            if ($inputCtc <= 12000) {
                $basicDaSalary = $salaryAfterLeave;
            } else {
                if ($deduction > 0) {
                    $basicDaSalary = round($salaryAfterLeave * 0.6, 0);
                } else {
                    $basicDaSalary = round($inputCtc * 0.6, 0);
                }
            }
            $payroll->setBasicDaSalary($basicDaSalary);
            $basicDaSalary = $payroll->getBasicDaSalary() ?? 0.0;
            if ($inputCtc <= 12000) {
                $hra = 0;
            } else {
                $hra = round($basicDaSalary * 0.4, 0);
            }
            $payroll->setHra($hra);
            if ($inputCtc <= 12000) {
                $conveyanceAllowance = 0;
            } else {
                $conveyanceAllowance = round($basicDaSalary * 0.05, 0);
            }
            $payroll->setConveyanceAllowance($conveyanceAllowance);
            if ($salary > 21000) {
                $medicalAllowance = 1250;
            } else {
                $medicalAllowance = 0;
            }
            $payroll->setMedicalAllowance($medicalAllowance);
            if ($basicDaSalary > 20000) {
                $telephoneAllowance = $basicDaSalary * 0.05;
            } else {
                $telephoneAllowance = 0;
            }
            $payroll->setTelephoneAllowance($telephoneAllowance);
            $hra = (float) ($payroll->getHra() ?? 0);
            $conveyanceAllowance = (float) ($payroll->getConveyanceAllowance() ?? 0);
            $medicalAllowance = (float) ($payroll->getMedicalAllowance() ?? 0);
            $telephoneAllowance = (float) ($payroll->getTelephoneAllowance() ?? 0);
            $sumAllowances = $basicDaSalary + $hra + $conveyanceAllowance + $medicalAllowance + $telephoneAllowance;
            $specialAllowance = $salaryAfterLeave - $sumAllowances;
            $specialAllowance = max(0, $specialAllowance);
            $payroll->setSpecialAllowance($specialAllowance);
            $netSalary = $basicDaSalary + $hra + $conveyanceAllowance + $medicalAllowance + $telephoneAllowance + $specialAllowance;
            $payroll->setNetSalary($netSalary);
            $netSalary =  (float) ($payroll->getNetSalary() ?? 0);
            $diff = $salaryAfterLeave - $netSalary;
            $payroll->setDiff($diff);
            if ($netSalary >= 12000) {
                $pt = 200;
            } else {
                $pt = 0;
            }
            $payroll->setPt($pt);
            // ESIC deduction logic
            if (!$deductESIC) {
                $esic = 0;
            } else if ($netSalary > 21000) {
                $esic = 0;
            } else {
                $esic = round($netSalary * 0.0075, 0);
            }
            $payroll->setEsic($esic);
            $salaryForPFCalculations = $netSalary - $hra;
            $payroll->setSalaryForPFCalculations($salaryForPFCalculations);
            $salaryForPFCalculations = (float) ($payroll->getSalaryForPFCalculations() ?? 0);
            // PF deduction logic
            if (!$deductPF) {
                $pfEmployee = 0;
            } else if ($salaryForPFCalculations >= 15000) {
                $pfEmployee = 1800;
            } else {
                $pfEmployee = round($salaryForPFCalculations * 0.12, 0);
            }
            $payroll->setPfEmployee($pfEmployee);
            $tdsDeduction = $payroll->getTdsDeduction() ?? 0.0;
            $loanOtherDeductions = $payroll->getLoanOtherDeductions() ?? 0.0;
            $totalDeductions = $pt + $esic + $pfEmployee + $tdsDeduction + $loanOtherDeductions;
            $payroll->setTotalDeductions($totalDeductions);
            $salaryPayable = $netSalary - $totalDeductions;
            $payroll->setSalaryPayable($salaryPayable);
//            $pfEmployee = $payroll->getPfEmployee() ?? 0.0;
//            $payroll->setPfEmployer($pfEmployee);
            $payrollMap[$employee->getId()] = $payroll;
        }

        // Create form with a collection of payroll forms
        $form = $this->createFormBuilder(['payrolls' => $payrollMap])
            ->add('payrolls', CollectionType::class, [
                'entry_type' => PayrollFormType::class,
                'entry_options' => ['label' => false],
                'allow_add' => false,
                'allow_delete' => false,
                'by_reference' => false,
            ])
            ->getForm();

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            try {
                foreach ($form->get('payrolls')->getData() as $payroll) {
                    $em->persist($payroll);
                }
                $em->flush();
                $this->addFlash('success', 'Payroll data saved successfully.');
                return $this->redirectToRoute('admin_payroll_index', [
                    'year' => $year,
                    'month' => $month,
                ]);
            } catch (\Exception $e) {
                $this->addFlash('error', 'An error occurred while saving payroll data: ' . $e->getMessage());
            }
        } elseif ($form->isSubmitted() && !$form->isValid()) {
            $this->addFlash('error', 'Please correct the errors in the form.');
        }

        return $this->render('admin/Payroll/index.html.twig', [
            'form' => $form->createView(),
            'year' => $year,
            'month' => $month,
            'selectedMonth' => $month,
            'workingDays' => $workingDays,
            'employees' => $employees,
            'payrollMap' => $payrollMap,
            'ctcMap' => $ctcMap,
            'lwpMap' => $lwpMap,
            'leaveDaysMap' => $leaveDaysMap,
            'deductionFlags' => $deductionFlags,
        ]);
    }

    private function calculateWorkingDays(int $year, int $month, HolidayRepository $holidayRepo): int
    {
        $start = new \DateTime(sprintf('%04d-%02d-01', $year, $month));
        $end = (clone $start)->modify('last day of this month');
        $holidays = $holidayRepo->findByMonthAndYear($year, $month);
        $holidayDates = array_map(fn($h) => $h->getDate()->format('Y-m-d'), $holidays);
        $workingDays = 0;
        for ($date = clone $start; $date <= $end; $date->modify('+1 day')) {
            $isWeekend = in_array($date->format('N'), ['6', '7']);
            $isHoliday = in_array($date->format('Y-m-d'), $holidayDates);
            if (!$isWeekend && !$isHoliday) {
                $workingDays++;
            }
        }
        return $workingDays;
    }
}