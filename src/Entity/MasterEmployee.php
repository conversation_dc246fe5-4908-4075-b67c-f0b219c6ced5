<?php

namespace App\Entity;

use App\Repository\MasterEmployeeRepository;
use Doctrine\ORM\Mapping as ORM;
use Doctrine\Common\Collections\Collection;
use Doctrine\Common\Collections\ArrayCollection;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;
use Symfony\Component\Validator\Context\ExecutionContextInterface;
use Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface;
use Symfony\Component\Security\Core\User\UserInterface;

#[ORM\Entity(repositoryClass: MasterEmployeeRepository::class)]
#[UniqueEntity(fields: ["email"], message: "This email is already in use")]
#[UniqueEntity(fields: ["username"], message: "This username is already taken")]
#[UniqueEntity(fields: ["employeeCode"], message: "This employee code is already in use")]
#[UniqueEntity(fields: ["panNumber"], message: "This PAN number is already registered")]
#[UniqueEntity(fields: ["aadhaarNumber"], message: "This Aadhaar number is already registered")]
class MasterEmployee implements UserInterface, PasswordAuthenticatedUserInterface
{
    public const ACTIVE = true;
    public const INACTIVE = false;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 255, unique: true)]
    #[Assert\NotBlank(message: "Email cannot be blank")]
    #[Assert\Email(message: "Please enter a valid email address")]
    private ?string $email = null;

    #[ORM\Column(length: 50, unique: true)]
    #[Assert\NotBlank(message: "Username cannot be blank")]
    #[Assert\Length(min: 3, max: 50, minMessage: "Username must be at least 3 characters", maxMessage: "Username cannot exceed 50 characters")]
    #[Assert\Regex(pattern: "/^[a-zA-Z0-9_]+$/", message: "Username can only contain letters, numbers, and underscores")]
    private ?string $username = null;

    #[ORM\Column(type: "json")]
    private array $roles = [];

    #[ORM\Column(length: 255)]
    #[Assert\NotBlank(message: "Name cannot be blank")]
    #[Assert\Length(max: 100, maxMessage: "Name cannot exceed 100 characters")]
    #[Assert\Regex(pattern: "/^[a-zA-Z\s]+$/", message: "Name can only contain letters and spaces")]
    private ?string $name = null;

    #[ORM\Column(type: "boolean", options: ["default" => false])]
    private bool $teamLeader = false;

    #[ORM\Column(length: 20, unique: true, nullable: true)]
    #[Assert\Length(max: 20, maxMessage: "Employee code cannot exceed 20 characters")]
    private ?string $employeeCode = null;

    #[ORM\OneToMany(targetEntity: Employee::class, mappedBy: "masterEmployee", cascade: ["persist", "remove"])]
    private Collection $employees;
    #[ORM\Column(type: "date")]
    #[Assert\NotBlank(message: "Joining date cannot be blank")]
//    #[Assert\LessThanOrEqual("today", message: "Joining date cannot be in the future")]
    private ?\DateTimeInterface $joiningDate = null;

    #[ORM\Column(length: 50, nullable: true)]
    #[Assert\Length(max: 50, maxMessage: "Total experience cannot exceed 50 characters")]
    #[Assert\Regex(pattern: "/^\d+\s(years|months)(\s\d+\s(years|months))?$/", message: "Please enter experience in format 'X years Y months'")]
    private ?string $totalExperience = null;

    #[ORM\Column(type: "date", nullable: true)]
    #[Assert\LessThanOrEqual("today", message: "Confirmation date cannot be in the future")]
    private ?\DateTimeInterface $confirmationDate = null;

    #[ORM\Column(length: 100, nullable: true)]
    #[Assert\Length(max: 100, maxMessage: "Previous company name cannot exceed 100 characters")]
    private ?string $previousCompanyName = null;

    #[ORM\Column(length: 15)]
    #[Assert\NotBlank(message: "Personal phone number cannot be blank")]
    #[Assert\Regex(pattern: "/^[0-9]{10,15}$/", message: "Please enter a valid phone number (10-15 digits)")]
    private ?string $personalPhoneNumber = null;

    #[ORM\Column(length: 15, nullable: true)]
    #[Assert\Regex(pattern: "/^[0-9]{10,15}$/", message: "Please enter a valid phone number (10-15 digits)")]
    private ?string $alternativePhoneNumber = null;

    #[ORM\Column(length: 500)]
    #[Assert\NotBlank(message: "Current address cannot be blank")]
    #[Assert\Length(max: 500, maxMessage: "Current address cannot exceed 500 characters")]
    private ?string $currentAddress = null;

    #[ORM\Column(length: 500, nullable: true)]
    #[Assert\Length(max: 500, maxMessage: "Permanent address cannot exceed 500 characters")]
    private ?string $permanentAddress = null;

    #[ORM\Column(type: "date")]
    #[Assert\NotBlank(message: "Birth date cannot be blank")]
    #[Assert\LessThanOrEqual("today", message: "Birth date cannot be in the future")]
    #[Assert\GreaterThanOrEqual("-100 years", message: "Birth date seems invalid")]
    private ?\DateTimeInterface $birthDate = null;

    #[ORM\Column(length: 10)]
    #[Assert\NotBlank(message: "Gender cannot be blank")]
    #[Assert\Choice(choices: ["male", "female", "other"], message: "Please select a valid gender")]
    private ?string $gender = null;

    #[ORM\Column(length: 20, nullable: true)]
    #[Assert\Choice(choices: ["single", "married", "divorced", "widowed"], message: "Please select a valid marital status")]
    private ?string $maritalStatus = null;

    #[ORM\Column(length: 100, nullable: true)]
    #[Assert\Length(max: 100, maxMessage: "Bank name cannot exceed 100 characters")]
    private ?string $bankName = null;

    #[ORM\Column(length: 20, nullable: true)]
    #[Assert\Regex(pattern: "/^\d{9,18}$/", message: "Bank account number must be 9-18 digits")]
    private ?string $bankAccountNumber = null;

    #[ORM\Column(length: 10, unique: true, nullable: true)]
    #[Assert\Length(exactly: 10, exactMessage: "PAN number must be exactly 10 characters")]
    #[Assert\Regex(pattern: "/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/", message: "Please enter a valid PAN number")]
    private ?string $panNumber = null;

    #[ORM\Column(length: 12, unique: true, nullable: true)]
    #[Assert\Length(exactly: 12, exactMessage: "Aadhaar number must be exactly 12 digits")]
    private ?string $aadhaarNumber = null;

    #[ORM\Column(length: 20, nullable: true)]
    #[Assert\Length(max: 20, maxMessage: "ESI number cannot exceed 20 characters")]
    private ?string $esiNumber = null;

    #[ORM\Column(length: 20, nullable: true)]
    #[Assert\Length(max: 20, maxMessage: "PF number cannot exceed 20 characters")]
    private ?string $pfNumber = null;

    #[ORM\Column(length: 12, nullable: true)]
    #[Assert\Length(min: 12, max: 12, minMessage: "UAN must be exactly 12 digits", maxMessage: "UAN must be exactly 12 digits")]
    private ?string $uan = null;

    #[ORM\Column(length: 11, nullable: true)]
    #[Assert\Length(min: 11, max: 11, minMessage: "IFSC code must be exactly 11 characters",maxMessage: "IFSC code must be exactly 11 characters")]
    #[Assert\Regex(pattern: "/^[A-Z]{4}0[A-Z0-9]{6}$/", message: "Please enter a valid IFSC code")]
    private ?string $ifscCode = null;

    #[ORM\Column(type: "boolean", options: ["default" => false])]
    private bool $isDelete = self::INACTIVE;

    #[ORM\Column(length: 255)]
    private ?string $password = null;

    /**
     * @var Collection<int, Department>
     */
    #[ORM\ManyToMany(targetEntity: Department::class, inversedBy: 'employees')]
    #[ORM\JoinTable(
        name: 'employee_departments',
        joinColumns: [new ORM\JoinColumn(name: 'master_employee_id', referencedColumnName: 'id')],
        inverseJoinColumns: [new ORM\JoinColumn(name: 'department_id', referencedColumnName: 'id')]
    )]
    private Collection $departments;

    /**
     * @var Collection<int, EmployeeReportsTo>
     */
    #[ORM\OneToMany(targetEntity: EmployeeReportsTo::class, mappedBy: 'employee', cascade: ['persist', 'remove'])]
    private Collection $reportsTo;

    #[ORM\Column(type: "boolean", options: ["default" => false])]
    private bool $isHrAccount = false;

    /**
     * @var Collection<int, LeaveRequest>
     */
    #[ORM\OneToMany(targetEntity: LeaveRequest::class, mappedBy: "employee", cascade: ["persist", "remove"])]
    private Collection $leaveRequests;

    #[ORM\OneToMany(targetEntity: EmployeeDocument::class, mappedBy: "masterEmployee", cascade: ["persist", "remove"])]
    private Collection $documents;

    #[ORM\OneToMany(targetEntity: Attendance::class, mappedBy: 'employee')]
    private Collection $attendances;

    #[ORM\Column(type: 'boolean')]
    private bool $allowSelfTask = false;

    public function __construct()
    {
        $this->employees = new ArrayCollection();
        $this->documents = new ArrayCollection();
        $this->departments = new ArrayCollection();
        $this->reportsTo = new ArrayCollection();
        $this->leaveRequests = new ArrayCollection();
        $this->attendances = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): void
    {
        $this->id = $id;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function setEmail(?string $email): void
    {
        $this->email = $email;
    }

    public function getEmployees(): Collection
    {
        return $this->employees;
    }

    public function setEmployees(Collection $employees): void
    {
        $this->employees = $employees;
    }

    public function getJoiningDate(): ?\DateTimeInterface
    {
        return $this->joiningDate;
    }

    public function setJoiningDate(?\DateTimeInterface $joiningDate): void
    {
        $this->joiningDate = $joiningDate;
    }

    public function getTotalExperience(): ?string
    {
        return $this->totalExperience;
    }

    public function setTotalExperience(?string $totalExperience): void
    {
        $this->totalExperience = $totalExperience;
    }

    public function getConfirmationDate(): ?\DateTimeInterface
    {
        return $this->confirmationDate;
    }

    public function setConfirmationDate(?\DateTimeInterface $confirmationDate): void
    {
        $this->confirmationDate = $confirmationDate;
    }

    public function getPreviousCompanyName(): ?string
    {
        return $this->previousCompanyName;
    }

    public function setPreviousCompanyName(?string $previousCompanyName): void
    {
        $this->previousCompanyName = $previousCompanyName;
    }

    public function getPersonalPhoneNumber(): ?string
    {
        return $this->personalPhoneNumber;
    }

    public function setPersonalPhoneNumber(?string $personalPhoneNumber): void
    {
        $this->personalPhoneNumber = $personalPhoneNumber;
    }

    public function getAlternativePhoneNumber(): ?string
    {
        return $this->alternativePhoneNumber;
    }

    public function setAlternativePhoneNumber(?string $alternativePhoneNumber): void
    {
        $this->alternativePhoneNumber = $alternativePhoneNumber;
    }

    public function getCurrentAddress(): ?string
    {
        return $this->currentAddress;
    }

    public function setCurrentAddress(?string $currentAddress): void
    {
        $this->currentAddress = $currentAddress;
    }

    public function getPermanentAddress(): ?string
    {
        return $this->permanentAddress;
    }

    public function setPermanentAddress(?string $permanentAddress): void
    {
        $this->permanentAddress = $permanentAddress;
    }

    public function getBirthDate(): ?\DateTimeInterface
    {
        return $this->birthDate;
    }

    public function setBirthDate(?\DateTimeInterface $birthDate): void
    {
        $this->birthDate = $birthDate;
    }

    public function getGender(): ?string
    {
        return $this->gender;
    }

    public function setGender(?string $gender): void
    {
        $this->gender = $gender;
    }

    public function getMaritalStatus(): ?string
    {
        return $this->maritalStatus;
    }

    public function setMaritalStatus(?string $maritalStatus): void
    {
        $this->maritalStatus = $maritalStatus;
    }

    public function getBankName(): ?string
    {
        return $this->bankName;
    }

    public function setBankName(?string $bankName): void
    {
        $this->bankName = $bankName;
    }

    public function getBankAccountNumber(): ?string
    {
        return $this->bankAccountNumber;
    }

    public function setBankAccountNumber(?string $bankAccountNumber): void
    {
        $this->bankAccountNumber = $bankAccountNumber;
    }

    public function getPanNumber(): ?string
    {
        return $this->panNumber;
    }

    public function setPanNumber(?string $panNumber): void
    {
        $this->panNumber = $panNumber;
    }

    public function getAadhaarNumber(): ?string
    {
        return $this->aadhaarNumber;
    }

    public function setAadhaarNumber(?string $aadhaarNumber): void
    {
        $this->aadhaarNumber = $aadhaarNumber;
    }

    public function getEsiNumber(): ?string
    {
        return $this->esiNumber;
    }

    public function setEsiNumber(?string $esiNumber): void
    {
        $this->esiNumber = $esiNumber;
    }

    public function getPfNumber(): ?string
    {
        return $this->pfNumber;
    }

    public function setPfNumber(?string $pfNumber): void
    {
        $this->pfNumber = $pfNumber;
    }

    public function getUan(): ?string
    {
        return $this->uan;
    }

    public function setUan(?string $uan): void
    {
        $this->uan = $uan;
    }

    public function getIfscCode(): ?string
    {
        return $this->ifscCode;
    }

    public function setIfscCode(?string $ifscCode): void
    {
        $this->ifscCode = $ifscCode;
    }

    public function getUsername(): ?string
    {
        return $this->username;
    }

    public function setUsername(?string $username): void
    {
        $this->username = $username;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(?string $name): void
    {
        $this->name = $name;
    }

    public function getEmployeeCode(): ?string
    {
        return $this->employeeCode;
    }

    public function setEmployeeCode(?string $employeeCode): void
    {
        $this->employeeCode = $employeeCode;
    }

    public function getIsDelete(): ?bool
    {
        return $this->isDelete;
    }

    public function setIsDelete(?bool $isDelete): void
    {
        $this->isDelete = $isDelete;
    }

    /**
     * @return Collection
     */
    public function getDepartments(): Collection
    {
        return $this->departments;
    }

    public function addDepartment(Department $department): static
    {
        if (!$this->departments->contains($department)) {
            $this->departments->add($department);
            $department->addEmployee($this);
        }
        return $this;
    }
    public function isTeamLeader(): bool
    {
        return $this->teamLeader;
    }

    public function setTeamLeader(bool $teamLeader): void
    {
        $this->teamLeader = $teamLeader;
    }

    /**
     * @return Collection<int, EmployeeReportsTo>
     */
    public function getReportsTo(): Collection
    {
        return $this->reportsTo;
    }

    public function addReportsTo(EmployeeReportsTo $reportsTo): static
    {
        if (!$this->reportsTo->contains($reportsTo)) {
            $this->reportsTo->add($reportsTo);
            $reportsTo->setEmployee($this);
        }

        return $this;
    }

    public function removeReportsTo(EmployeeReportsTo $reportsTo): static
    {
        if ($this->reportsTo->removeElement($reportsTo)) {
            if ($reportsTo->getEmployee() === $this) {
                $reportsTo->setEmployee(null);
            }
        }

        return $this;
    }
    public function getPassword(): ?string
    {
        return $this->password;
    }

    public function setPassword(?string $password): void
    {
        $this->password = $password;
    }

    public function getRoles(): array
    {
        $roles = $this->roles;
        if (!in_array('ROLE_EMPLOYEE', $roles, true)) {
            $roles[] = 'ROLE_EMPLOYEE';
        }

        return array_unique($roles);
    }

    public function setRoles(array $roles): self
    {
        $this->roles = $roles;

        return $this;
    }

    public function getUserIdentifier(): string
    {
        return (string) $this->email;
    }

    public function eraseCredentials(): void
    {
        // TODO: Implement eraseCredentials() method.
    }

    /**
     * @throws \DateMalformedStringException
     */
    #[Assert\Callback]
    public function validateDates(ExecutionContextInterface $context): void
    {
        if ($this->confirmationDate && $this->joiningDate && $this->confirmationDate < $this->joiningDate) {
            $context->buildViolation('Confirmation date must be after joining date')
                ->atPath('confirmationDate')
                ->addViolation();
        }

        $minAgeDate = (new \DateTime())->modify('-18 years');
        if ($this->birthDate && $this->birthDate > $minAgeDate) {
            $context->buildViolation('Employee must be at least 18 years old')
                ->atPath('birthDate')
                ->addViolation();
        }
    }

    public function isHrAccount(): bool
    {
        return $this->isHrAccount;
    }

    public function setIsHrAccount(bool $isHrAccount): void
    {
        $this->isHrAccount = $isHrAccount;
    }

    public function getLeaveRequests(): Collection
    {
        return $this->leaveRequests;
    }

    public function addLeaveRequest(LeaveRequest $leaveRequest): self
    {
        if (!$this->leaveRequests->contains($leaveRequest)) {
            $this->leaveRequests->add($leaveRequest);
            $leaveRequest->setEmployee($this);
        }
        return $this;
    }

    public function removeLeaveRequest(LeaveRequest $leaveRequest): self
    {
        if ($this->leaveRequests->removeElement($leaveRequest)) {
            if ($leaveRequest->getEmployee() === $this) {
                $leaveRequest->setEmployee(null);
            }
        }
        return $this;
    }

    public function getDocuments(): Collection
    {
        return $this->documents;
    }

    public function addDocument(EmployeeDocument $document): self
    {
        if (!$this->documents->contains($document)) {
            $this->documents->add($document);
            $document->setMasterEmployee($this);
        }
        return $this;
    }

    public function removeDocument(EmployeeDocument $document): self
    {
        if ($this->documents->removeElement($document)) {
            if ($document->getMasterEmployee() === $this) {
                $document->setMasterEmployee(null);
            }
        }
        return $this;
    }

    /**
     * @return Collection
     */
    public function getAttendances(): Collection
    {
        return $this->attendances;
    }

    public function addAttendance(Attendance $attendance): self
    {
        if (!$this->attendances->contains($attendance)) {
            $this->attendances[] = $attendance;
            $attendance->setEmployee($this);
        }
        return $this;
    }

    public function removeAttendance(Attendance $attendance): self
    {
        if ($this->attendances->removeElement($attendance)) {
            if ($attendance->getEmployee() === $this) {
                $attendance->setEmployee(null);
            }
        }
        return $this;
    }
    public function isAllowSelfTask(): bool
    {
        return $this->allowSelfTask;
    }

    public function setAllowSelfTask(bool $allowSelfTask): void
    {
        $this->allowSelfTask = $allowSelfTask;
    }

}
