{% extends 'admin/dashboard/base.html.twig' %}

{% block body %}
    {% include 'admin/partials/breadcrumbs.html.twig' with {
        breadcrumbs: [
            { 'label': 'Dashboard', 'url': url('admin_dashboard') },
        ],
        active: 'Leave Balance'
    } %}
    <div class="content">
        <div class="animated fadeIn">
            <div class="row">
                <div id="order-loader">
                    <img src="{{ asset('images/loader.svg') }}" alt="loader">
                </div>
                <div class="col-md-12">
                    <div id="flash-container"></div>
                    <div class="card">
                        <div class="card-header d-flex justify-content-between ">
                            <strong class="card-title">Leave Balance</strong>
                        </div>
                        <div class="card-body">
                            <div class="table-stats order-table ov-h text-center">
                                <div id="formTemplate" class="datatable-main-wrapper">Loading...</div>
                            </div>
                        </div>
                    </div>
                </div>


            </div>
        </div>
    </div>
    <div class="clearfix"></div>
    <script src="{{ asset('assets/js/datatables.min.js') }}"></script>
    <script src="{{ asset('bundles/datatables/js/datatables.js') }}"></script>
    <script src="{{ asset('assets/js/toggle.js') }}"></script>
    <script>
        $(document).ready(function() {
            let recordsTable = $('#formTemplate').initDataTables({{ datatable_settings(datatable) | raw }})
                .then(function(dt) {
                    initializeToggleButtons();
                    dt.on('draw', function() {
                        initializeToggleButtons();
                    });
                    initializeSearch(dt);

                    return dt;
                })
                .catch(function(error) {
                    console.error('Error initializing DataTable:', error);
                });
        });
    </script>
{% endblock %}