# see https://symfony.com/doc/current/reference/configuration/framework.html
framework:
    secret: '%env(APP_SECRET)%'
    router:
        default_uri: '%env(APP_BASE_URL)%'

    error_controller: 'App\Controller\ExceptionController::show'

    # Note that the session will be started ONLY if you read or write from it.
    session: true

    #esi: true
    #fragments: true

when@test:
    framework:
        test: true
        session:
            storage_factory_id: session.storage.factory.mock_file
