{% extends 'admin/dashboard/base.html.twig' %}


{% block title %}Email Templates{% endblock %}

{% block body %}
    {% include 'admin/partials/breadcrumbs.html.twig' with {
        breadcrumbs: [
            { 'label': 'Dashboard', 'url': url('admin_dashboard') },
        ],
        active: 'Email Template'
    } %}

    <div class="content">
        <div class="animated fadeIn">
            <div class="row">
                <div class="col-md-12">
                    <div id="flash-container"></div>
                    <div class="card">
                        <div class="card-header d-flex justify-content-between">
                            <strong class="card-title">Email Template</strong>
                            <a href="{{ path('email_template_create') }}" class="btn btn-primary">Create New
                                Template</a>
                        </div>
                        <div class="card-body">
                            {% if emailTemplates is not empty %}
                                <table class="table">
                                    <thead>
                                    <tr class="text-center">
                                        <th class="serial">#</th>
                                        <th>Name</th>
                                        <th>Actions</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {% for template in emailTemplates %}
                                        <tr class="text-center">
                                            <th class="serial">{{ loop.index }}</th>
                                            <td class="align-middle">{{ template.name }}</td>
                                            <td class="d-flex align-items-center justify-content-center gap">
                                                <a href="{{ path('email_template_edit', {id: template.id}) }}"
                                                   class="bs-btn-mail btn btn-primary" title="Edit">
                                                    <i class="bs-fa fa fa-edit"></i>
                                                </a>

                                                <button type="button" class="bs-btn-mail btn btn-primary"
                                                        data-toggle="modal"
                                                        data-target="#deleteModal{{ template.id }}"
                                                        title="Delete">
                                                    <i class="bs-fa fa fa-trash"></i>
                                                </button>

                                                {{ render_modal(
                                                    'deleteModal' ~ template.id,
                                                    'Delete Confirmation',
                                                    'Are you sure you want to delete the template "' ~ template.name ~ '"?',
                                                    '<form method="post" action="' ~ path('email_template_delete', {id: template.id}) ~ '" style="display: inline-block;">
                                                    <input type="hidden" name="_token" value="' ~ csrf_token('delete' ~ template.id) ~ '">
                                                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                                                    <button type="submit" class="btn btn-primary">Delete</button>
                                                    </form>'
                                                ) }}
                                            </td>
                                        </tr>
                                    {% endfor %}
                                    </tbody>
                                </table>
                            {% else %}
                                <p>No email templates found.</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="clearfix"></div>


{% endblock %}
