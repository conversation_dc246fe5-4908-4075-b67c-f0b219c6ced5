<?php

namespace App\Form;

use App\Entity\Bug;
use App\Entity\Category;
use App\Entity\Priority;
use App\Entity\Severity;
use App\Entity\Status;
use FOS\CKEditorBundle\Form\Type\CKEditorType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\EnumType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\NotBlank;

class EmployeeBugType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('status', EnumType::class, [
                'class' => Status::class,
                'required' => true,
                'attr' => ['class' => 'form-control'],
            ])
            ->add('priority', EnumType::class, [
                'class' => Priority::class,
                'placeholder' => 'Select Priority',
                'required' => true,
                'attr' => ['class' => 'form-control'],
            ])
            ->add('severity', EnumType::class, [
                'class' => Severity::class,
                'placeholder' => 'Select Severity',
                'required' => true,
                'attr' => ['class' => 'form-control'],
            ])
            ->add('category', EnumType::class, [
                'class' => Category::class,
                'placeholder' => 'Select Category',
                'required' => true,
                'attr' => ['class' => 'form-control'],
            ])
            ->add('comments', CKEditorType::class, [
                'required' => true,
                'constraints' => [new NotBlank()],
                'label' => 'Comments (What to test)',
                'row_attr' => ['class' => 'col-md-12'],
                'attr' => ['rows' => '10'],
            ]);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => Bug::class,
        ]);
    }
}
?>