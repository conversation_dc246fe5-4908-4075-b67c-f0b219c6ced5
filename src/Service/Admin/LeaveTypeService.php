<?php

namespace App\Service\Admin;

use App\Entity\LeaveType;
use App\Helper\ModalboxHelper;
use Doctrine\ORM\QueryBuilder;
use Omines\DataTablesBundle\Adapter\Doctrine\ORMAdapter;
use Omines\DataTablesBundle\Column\TextColumn;
use Omines\DataTablesBundle\DataTable;
use Omines\DataTablesBundle\DataTableFactory;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Request;
use App\Traits\ExceptionLoggerTrait;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Security\Csrf\CsrfTokenManagerInterface;

class LeaveTypeService
{
    use ExceptionLoggerTrait;
    private const PROTECTED_LEAVE_NAMES = [
        'Addon Leave',
        'Sick Leave',
        'Casual Leave'
    ];
    public function __construct(
        private readonly DataTableFactory          $dataTableFactory,
        private readonly RouterInterface           $router,
        ParameterBagInterface                      $parameterBag,
        private readonly CsrfTokenManagerInterface $csrfTokenManager,
        private readonly ModalboxHelper            $modalboxHelper
    )
    {
        $this->initializeLogger($parameterBag, 'LeaveType_Table_service');
    }

    public function createLeaveTypeTable(Request $request): DataTable
    {
        $dataTable = $this->dataTableFactory->create();

        foreach ($this->getColumns() as $key => $column) {
            $dataTable->add($key, $column['className'], array_filter($column, fn($k) => $k !== 'className', ARRAY_FILTER_USE_KEY));
        }

        return $dataTable->createAdapter(ORMAdapter::class, [
            'entity' => LeaveType::class,
            'query' => fn(QueryBuilder $qb) => $this->buildQuery($qb),
        ])
            ->addOrderBy('name', 'asc')
            ->handleRequest($request);
    }

    private function getColumns(): array
    {
        return [
            'name' => [
                'label' => 'Leave Type Name',
                'field' => 'lt.name',
                'className' => TextColumn::class,
                'orderable' => true,
            ],
            'maxDays' => [
                'label' => 'Max Days',
                'field' => 'lt.maxDays',
                'className' => TextColumn::class,
                'searchable' => true,
                'render' => fn($value, $context) => number_format($context->getMaxDays(), 1),
            ],
            'isPaid' => [
                'label' => 'Paid Leave',
                'field' => 'lt.isPaid',
                'className' => TextColumn::class,
                'render' => fn($value) => $this->renderStatusBadge($value),
            ],
            'actions' => [
                'label' => 'Actions',
                'field' => 'lt.id',
                'className' => TextColumn::class,
                'orderable' => false,
                'render' => fn($value, $context) => $this->renderActionColumn($context),
            ],
        ];
    }

    private function renderStatusBadge(bool $isPaid): string
    {
        return $isPaid
            ? '<span class="text-success fa-lg" title="Yes"><i class="fa fa-check"></span>'
            : '<span class="text-danger fa-lg" title="No"><i class="fa fa-times"></span>';
    }


    private function renderActionColumn(LeaveType $leaveType): string
    {
        $entityId = $leaveType->getId();
        $editUrl = $this->router->generate('leave_type_edit');
        $editToken = $this->csrfTokenManager->getToken('edit' . $entityId)->getValue();
        $actions = sprintf(
            '<div class="d-inline-flex gap-2">
            <form action="%s" method="POST" class="d-inline">
                <input type="hidden" name="id" value="%s">
                <input type="hidden" name="_token" value="%s">
                <button type="submit" class="bs-btn btn btn-primary btn-sm bs-m" data-turbo="false" title="Edit">
                    <i class="bs-fa fa fa-edit"></i>
                </button>
            </form>',
            $editUrl,
            $entityId,
            $editToken
        );
        if (!in_array($leaveType->getName(), self::PROTECTED_LEAVE_NAMES)) {
            $deleteUrl = $this->router->generate('leave_type_delete', ['id' => $entityId]);
            $deleteToken = $this->csrfTokenManager->getToken('delete' . $entityId)->getValue();

            $modalId = 'deleteModal_' . $entityId;
            $modalBody = sprintf(
                'Are you sure you want to delete the leave type "%s" with %d max days?<br><br>' .
                '<strong>Warning:</strong> This action cannot be undone.',
                htmlspecialchars($leaveType->getName()),
                $leaveType->getMaxDays()
            );

            $footerButtons = sprintf(
                '<button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>' .
                '<button type="button" class="btn btn-primary delete-leave-type-btn" data-delete="true" data-url="%s" data-token="%s">Delete</button>',
                $deleteUrl,
                $deleteToken
            );

            $deleteModal = $this->modalboxHelper->renderModal(
                $modalId,
                'Confirm Leave Type Deletion',
                $modalBody,
                $footerButtons
            );

            $actions .= sprintf(
                '<button type="button" class="bs-btn btn btn-primary btn-sm bs-m open-delete-leave-type-modal" data-toggle="modal" data-target="#%s" title="Delete">
                    <i class="bs-fa fa fa-trash"></i>
                </button>
                </div>%s',
                $modalId,
                $deleteModal
            );
        } else {
            $actions .= '</div>';
        }

        return $actions;
    }


    private function buildQuery(QueryBuilder $qb): QueryBuilder
    {
        return $qb->select('lt')
            ->from(LeaveType::class, 'lt')
            ->where('lt.isDelete = :isDelete')
            ->setParameter('isDelete', false);
    }
}