{% block title %}Chat - {{ employee.masterEmployee.name }}{% endblock %}

{% block stylesheets %}
    <link rel="stylesheet" href="{{ asset('assets/css/Chat/chat.css') }}">
{% endblock %}

{% block body %}
    <div class="chat-container">
        <h2 class="mb-4">Chat with {{  user[0].username }}</h2>

        <div class="messages-container" id="messages">
            {% for chat in chats %}
                <div class="message {% if chat.fromAdmin %}admin-message{% else %}user-message{% endif %} {% if chat.isDisapprovalReason %}disapproval-reason{% endif %}">
                    <div class="message-content">{{ chat.message }}</div>
                    <div class="timestamp">{{ chat.createdAt|date('d-m-Y H:i:s') }}</div>
                </div>
            {% endfor %}
        </div>

        <div class="message-input">
            <div class="input-group">
                <input type="text" id="messageInput" class="form-control" placeholder="Type your message...">
                <button class="btn btn-primary" id="sendButton">Send</button>
            </div>
        </div>
    </div>
{% endblock %}

{% block javascripts %}
    <script>
        window.notificationUuid = '{{ notification.uuid }}';
    </script>
    <script src="{{ asset('assets/js/chat/employeechat.js') }}"></script>
{% endblock %}