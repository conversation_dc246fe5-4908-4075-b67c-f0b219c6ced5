<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity]
#[ORM\Table(name: "employee_reports_to")]
class EmployeeReportsTo
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    /** @phpstan-ignore-next-line */
    private ?int $id = null;

    #[ORM\ManyToOne(targetEntity: MasterEmployee::class, inversedBy: 'reportsTo')]
    #[ORM\JoinColumn(name: "employee_id", nullable: false)]
    private ?MasterEmployee $employee = null;

    #[ORM\ManyToOne(targetEntity: MasterEmployee::class)]
    #[ORM\JoinColumn(name: "team_leader_id", nullable: false)]
    private ?MasterEmployee $teamLeader = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getEmployee(): ?MasterEmployee
    {
        return $this->employee;
    }

    public function setEmployee(?MasterEmployee $employee): static
    {
        $this->employee = $employee;
        return $this;
    }

    public function getTeamLeader(): ?MasterEmployee
    {
        return $this->teamLeader;
    }

    public function setTeamLeader(?MasterEmployee $teamLeader): static
    {
        $this->teamLeader = $teamLeader;
        return $this;
    }
}