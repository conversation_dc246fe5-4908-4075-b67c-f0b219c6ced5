<?php

namespace App\Entity;

use App\Repository\TrackingConfigurationRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: TrackingConfigurationRepository::class)]
#[ORM\Table(name: 'tracking_configurations')]
#[ORM\Index(columns: ['config_key'], name: 'idx_config_key')]
#[ORM\Index(columns: ['employee_id'], name: 'idx_employee_config')]
class TrackingConfiguration
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 100)]
    private ?string $configKey = null;

    #[ORM\Column(type: Types::TEXT)]
    private ?string $configValue = null;

    #[ORM\Column(length: 50)]
    private ?string $configType = null; // 'global', 'employee', 'department'

    #[ORM\ManyToOne(targetEntity: MasterEmployee::class)]
    #[ORM\JoinColumn(nullable: true)]
    private ?MasterEmployee $employee = null; // null for global settings

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $description = null;

    #[ORM\Column(type: Types::BOOLEAN, options: ['default' => true])]
    private bool $isActive = true;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $createdAt = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $updatedAt = null;

    #[ORM\ManyToOne(targetEntity: MasterEmployee::class)]
    #[ORM\JoinColumn(nullable: true)]
    private ?MasterEmployee $updatedBy = null;

    public function __construct()
    {
        $this->createdAt = new \DateTime();
        $this->updatedAt = new \DateTime();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getConfigKey(): ?string
    {
        return $this->configKey;
    }

    public function setConfigKey(string $configKey): static
    {
        $this->configKey = $configKey;
        return $this;
    }

    public function getConfigValue(): ?string
    {
        return $this->configValue;
    }

    public function setConfigValue(string $configValue): static
    {
        $this->configValue = $configValue;
        return $this;
    }

    public function getConfigType(): ?string
    {
        return $this->configType;
    }

    public function setConfigType(string $configType): static
    {
        $this->configType = $configType;
        return $this;
    }

    public function getEmployee(): ?MasterEmployee
    {
        return $this->employee;
    }

    public function setEmployee(?MasterEmployee $employee): static
    {
        $this->employee = $employee;
        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): static
    {
        $this->description = $description;
        return $this;
    }

    public function isActive(): bool
    {
        return $this->isActive;
    }

    public function setIsActive(bool $isActive): static
    {
        $this->isActive = $isActive;
        return $this;
    }

    public function getCreatedAt(): ?\DateTimeInterface
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTimeInterface $createdAt): static
    {
        $this->createdAt = $createdAt;
        return $this;
    }

    public function getUpdatedAt(): ?\DateTimeInterface
    {
        return $this->updatedAt;
    }

    public function setUpdatedAt(\DateTimeInterface $updatedAt): static
    {
        $this->updatedAt = $updatedAt;
        return $this;
    }

    public function getUpdatedBy(): ?MasterEmployee
    {
        return $this->updatedBy;
    }

    public function setUpdatedBy(?MasterEmployee $updatedBy): static
    {
        $this->updatedBy = $updatedBy;
        return $this;
    }

    #[ORM\PreUpdate]
    public function setUpdatedAtValue(): void
    {
        $this->updatedAt = new \DateTime();
    }

    // Helper methods for common configuration types
    public function getValueAsInt(): int
    {
        return (int) $this->configValue;
    }

    public function getValueAsFloat(): float
    {
        return (float) $this->configValue;
    }

    public function getValueAsBool(): bool
    {
        return filter_var($this->configValue, FILTER_VALIDATE_BOOLEAN);
    }

    public function getValueAsArray(): array
    {
        return json_decode($this->configValue, true) ?? [];
    }

    public function setValueFromArray(array $value): static
    {
        $this->configValue = json_encode($value);
        return $this;
    }

    // Common configuration keys as constants
    public const IDLE_TIMEOUT = 'idle_timeout_minutes';
    public const SCREENSHOT_INTERVAL = 'screenshot_interval_seconds';
    public const SCREENSHOT_ENABLED = 'screenshot_enabled';
    public const WEBCAM_ENABLED = 'webcam_enabled';
    public const WEBCAM_INTERVAL = 'webcam_interval_minutes';
    public const APP_TRACKING_ENABLED = 'app_tracking_enabled';
    public const WEBSITE_TRACKING_ENABLED = 'website_tracking_enabled';
    public const SILENT_MODE = 'silent_mode_enabled';
    public const AUTO_START_TRACKING = 'auto_start_tracking';
    public const BREAK_REMINDER_INTERVAL = 'break_reminder_interval_minutes';
    public const MAX_DAILY_HOURS = 'max_daily_hours';
    public const PRODUCTIVITY_CATEGORIES = 'productivity_categories';
    public const BLOCKED_WEBSITES = 'blocked_websites';
    public const BLOCKED_APPLICATIONS = 'blocked_applications';
    public const SCREENSHOT_QUALITY = 'screenshot_quality';
    public const SCREENSHOT_BURST_MODE = 'screenshot_burst_mode';
    public const OFFLINE_SYNC_INTERVAL = 'offline_sync_interval_minutes';
}
