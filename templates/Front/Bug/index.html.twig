{% extends 'Front/dashboard/base.html.twig' %}

{% block title %}Bug List{% endblock %}

{% block body %}
    {% include 'admin/partials/breadcrumbs.html.twig' with {
        breadcrumbs: [
            { 'label': 'Dashboard', 'url': path('dashboard_employee') },
            {
                'label': isHistory ? 'Task History' : 'My Tasks',
                'url': isHistory ? path('employee_task_history') : path('employee_task')
            },
        ],
        active: task.title ?? taskAssignment.task.title

    } %}
    {% include 'CommonTemplate/Bug/index.htnl.twig' %}

{% endblock %}