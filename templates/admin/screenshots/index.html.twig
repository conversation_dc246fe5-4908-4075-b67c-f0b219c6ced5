{% extends 'admin/dashboard/base.html.twig' %}

{% block title %}Screenshots Report{% endblock %}

{% block body %}
<div class="content">
    <div class="animated fadeIn">
        <div class="row">
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-header">
                        <strong class="card-title">
                            <i class="fa fa-camera"></i> Screenshots Report
                        </strong>
                    </div>
                    <div class="card-body">
                        <!-- Filter Form -->
                        <form method="GET" class="mb-4">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="employee_id">Select Employee</label>
                                        <select name="employee_id" id="employee_id" class="form-control" required>
                                            <option value="">-- Select Employee --</option>
                                            {% for employee in employees %}
                                                <option value="{{ employee.id }}" 
                                                    {% if selectedEmployeeId == employee.id %}selected{% endif %}>
                                                    {{ employee.name }} ({{ employee.employeeCode }})
                                                </option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="date">Select Date</label>
                                        <input type="date" name="date" id="date" class="form-control" 
                                               value="{{ selectedDate }}" required>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label>&nbsp;</label>
                                        <div>
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fa fa-search"></i> View Screenshots
                                            </button>
                                            {% if selectedEmployee and screenshots|length > 0 %}
                                                <a href="{{ path('admin_screenshot_full_report', {
                                                    'employeeId': selectedEmployee.id, 
                                                    'date': selectedDate
                                                }) }}" class="btn btn-success">
                                                    <i class="fa fa-file-pdf-o"></i> Full Report
                                                </a>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>

                        {% if selectedEmployee %}
                            <div class="alert alert-info">
                                <strong>Employee:</strong> {{ selectedEmployee.name }} ({{ selectedEmployee.employeeCode }}) | 
                                <strong>Date:</strong> {{ selectedDate|date('F j, Y') }} | 
                                <strong>Total Screenshots:</strong> {{ screenshots|length }}
                            </div>

                            {% if screenshots|length > 0 %}
                                <!-- Screenshots Grid -->
                                <div class="row">
                                    {% for screenshot in screenshots %}
                                        <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                                            <div class="card screenshot-card">
                                                <div class="card-body text-center">
                                                    <div class="screenshot-preview">
                                                        {% if screenshot.fileName %}
                                                            {% set imagePath = screenshot.filePath %}
                                                            {% if imagePath starts with '/uploads/' %}
                                                                <img src="{{ asset(imagePath) }}"
                                                                     alt="Screenshot" class="img-fluid screenshot-thumb"
                                                                     onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                                                                <div class="screenshot-placeholder" style="display: none;">
                                                                    <i class="fa fa-camera fa-3x text-primary"></i>
                                                                    <p class="text-muted mt-2">Screenshot Captured</p>
                                                                    <small class="text-info">{{ screenshot.fileName }}</small>
                                                                    <div class="mt-2">
                                                                        <span class="badge badge-warning">File Upload Pending</span>
                                                                    </div>
                                                                </div>
                                                            {% else %}
                                                                <div class="screenshot-placeholder">
                                                                    <i class="fa fa-camera fa-3x text-primary"></i>
                                                                    <p class="text-muted mt-2">Screenshot Captured</p>
                                                                    <small class="text-info">{{ screenshot.fileName }}</small>
                                                                    <div class="mt-2">
                                                                        <span class="badge badge-warning">File Upload Pending</span>
                                                                    </div>
                                                                </div>
                                                            {% endif %}
                                                        {% else %}
                                                            <div class="no-image">
                                                                <i class="fa fa-image fa-3x text-muted"></i>
                                                                <p class="text-muted">No Image</p>
                                                            </div>
                                                        {% endif %}
                                                    </div>
                                                    <div class="screenshot-info mt-2">
                                                        <small class="text-muted">
                                                            <i class="fa fa-clock-o"></i> 
                                                            {{ screenshot.capturedAt|date('H:i:s') }}
                                                        </small><br>
                                                        <small class="text-muted">
                                                            <i class="fa fa-desktop"></i> 
                                                            {{ screenshot.resolution ?: 'Unknown' }}
                                                        </small><br>
                                                        <small class="text-muted">
                                                            <i class="fa fa-hdd-o"></i> 
                                                            {{ (screenshot.fileSize / 1024)|round(1) }} KB
                                                        </small>
                                                    </div>
                                                    <div class="mt-2">
                                                        <a href="{{ path('admin_screenshot_view', {'id': screenshot.id}) }}" 
                                                           class="btn btn-sm btn-primary">
                                                            <i class="fa fa-eye"></i> View
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    {% endfor %}
                                </div>
                            {% else %}
                                <div class="alert alert-warning">
                                    <i class="fa fa-exclamation-triangle"></i>
                                    No screenshots found for {{ selectedEmployee.name }} on {{ selectedDate|date('F j, Y') }}.
                                </div>
                            {% endif %}
                        {% else %}
                            <div class="alert alert-info">
                                <i class="fa fa-info-circle"></i>
                                Please select an employee and date to view screenshots.
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.screenshot-card {
    transition: transform 0.2s;
}

.screenshot-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.screenshot-thumb {
    max-height: 150px;
    border-radius: 4px;
    cursor: pointer;
}

.screenshot-preview {
    height: 150px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
    border-radius: 4px;
}

.no-image {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
}

.screenshot-info {
    font-size: 0.85em;
}

.screenshot-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: 10px;
}

.screenshot-placeholder .badge {
    font-size: 0.7em;
}
</style>
{% endblock %}
