const { BrowserWindow, ipcMain } = require('electron');
const path = require('path');

class IdleWarningPopup {
    constructor() {
        this.popup = null;
        this.countdownTimer = null;
        this.countdownSeconds = 60; // 1 minute countdown
        this.isShowing = false;
        this.intentionalClose = false; // Flag to allow intentional closing
        this.onTimeoutCallback = null;
        this.onResumeCallback = null;
    }

    show(timeoutMinutes = 5, onTimeout = null, onResume = null) {
        if (this.isShowing) {
            console.log('⚠️ Idle warning popup already showing');
            return;
        }

        this.onTimeoutCallback = onTimeout;
        this.onResumeCallback = onResume;
        this.countdownSeconds = 60; // Reset to 1 minute
        this.isShowing = true;

        console.log(`⚠️ Showing idle warning popup - ${timeoutMinutes} minutes idle detected`);

        // Create popup window
        this.popup = new BrowserWindow({
            width: 450,
            height: 500,
            frame: false,
            alwaysOnTop: true,
            center: true,
            resizable: false,
            minimizable: false,
            maximizable: false,
            closable: true, // ALLOW CLOSING
            skipTaskbar: true,
            webPreferences: {
                nodeIntegration: false,
                contextIsolation: true,
                preload: path.join(__dirname, '../preload/idle-warning-preload.js')
            }
        });

        // Load the popup HTML
        this.popup.loadFile(path.join(__dirname, '../renderer/idle-warning.html'));

        // Handle popup ready
        this.popup.webContents.once('dom-ready', () => {
            // Send initial data to popup
            this.popup.webContents.send('init-popup', {
                timeoutMinutes: timeoutMinutes,
                countdownSeconds: this.countdownSeconds
            });

            // Start countdown
            this.startCountdown();
        });

        // Handle popup closed
        this.popup.on('closed', () => {
            console.log('🗑️ Popup window closed event fired');
            this.cleanup();
        });

        // DON'T prevent closing - let it close when needed
        this.popup.on('close', (event) => {
            console.log('🚪 Popup window close event fired');
            // Always allow closing now
        });

        // Focus the popup
        this.popup.focus();
        this.popup.setAlwaysOnTop(true, 'screen-saver');
    }

    startCountdown() {
        if (this.countdownTimer) {
            clearInterval(this.countdownTimer);
        }

        this.countdownTimer = setInterval(() => {
            this.countdownSeconds--;

            if (this.popup && !this.popup.isDestroyed()) {
                this.popup.webContents.send('update-countdown', this.countdownSeconds);
            }

            if (this.countdownSeconds <= 0) {
                console.log('⏰ Idle timeout reached - marking user as inactive');
                this.handleTimeout();
            }
        }, 1000);
    }

    handleTimeout() {
        console.log('🔴 Countdown finished - user is now ON BREAK');

        // Update popup to show "On Break" status instead of hiding
        if (this.popup && !this.popup.isDestroyed()) {
            this.popup.webContents.send('show-on-break-status');
            console.log('📱 Popup updated to show "On Break" - stays visible until user interaction');
        }

        if (this.onTimeoutCallback) {
            this.onTimeoutCallback();
        }

        // DON'T hide popup - keep it visible showing "On Break"
        // this.hide(); // REMOVED - popup stays visible
    }

    handleResume() {
        console.log('🟢 User activity detected - resuming session');

        // IMMEDIATELY close the window
        this.hide();

        if (this.onResumeCallback) {
            this.onResumeCallback();
        }
    }

    hide() {
        console.log('🔥 HIDE() CALLED - Forcing popup to close');

        if (!this.isShowing) {
            console.log('⚠️ Popup not showing, but trying to close anyway');
        }

        // Force cleanup first
        this.cleanup();

        // Force close the window with multiple methods
        if (this.popup && !this.popup.isDestroyed()) {
            console.log('💥 DESTROYING POPUP WINDOW NOW');
            try {
                this.popup.close();
                this.popup.destroy();
            } catch (error) {
                console.error('Error destroying popup:', error);
            }
            this.popup = null;
        } else {
            console.log('⚠️ Popup window already destroyed or null');
        }
    }

    cleanup() {
        console.log('🧹 CLEANUP() called');
        this.isShowing = false;
        this.intentionalClose = false;

        if (this.countdownTimer) {
            console.log('⏰ Clearing countdown timer');
            clearInterval(this.countdownTimer);
            this.countdownTimer = null;
        }

        this.onTimeoutCallback = null;
        this.onResumeCallback = null;
        console.log('✅ Cleanup completed');
    }

    isVisible() {
        return this.isShowing && this.popup && !this.popup.isDestroyed();
    }

    // Handle IPC messages from popup
    setupIpcHandlers() {
        ipcMain.handle('idle-warning-resume', () => {
            console.log('📨 IPC: idle-warning-resume received - CALLING HIDE()');
            this.hide(); // Direct hide call
            if (this.onResumeCallback) this.onResumeCallback();
        });

        ipcMain.handle('idle-warning-continue-idle', () => {
            console.log('📨 IPC: idle-warning-continue-idle received');
            this.handleTimeout();
        });

        // Add immediate activity detection handler
        ipcMain.handle('idle-warning-activity-detected', () => {
            console.log('📨 IPC: idle-warning-activity-detected received - CALLING HIDE()');
            this.hide(); // Direct hide call
            if (this.onResumeCallback) this.onResumeCallback();
        });
    }

    removeIpcHandlers() {
        ipcMain.removeHandler('idle-warning-resume');
        ipcMain.removeHandler('idle-warning-continue-idle');
        ipcMain.removeHandler('idle-warning-activity-detected');
    }
}

module.exports = IdleWarningPopup;
