<?php

namespace App\Controller\Admin;

use App\Entity\Hardware;
use App\Entity\HardwareHistory;
use App\Form\HardwareFormType;
use App\Repository\HardwareHistoryRepository;
use App\Service\Admin\HardwareService;
use App\Traits\ExceptionLoggerTrait;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Csrf\CsrfToken;
use Symfony\Component\Security\Csrf\CsrfTokenManagerInterface;

#[Route('/admin')]
class HardwareController extends AbstractController
{
    use ExceptionLoggerTrait;

    public function __construct(
        ParameterBagInterface                   $parameterBag,
        private readonly EntityManagerInterface $entityManager,
        private readonly HardwareService        $hardwareService,
        private readonly HardwareHistoryRepository  $hardwareHistory,
    )
    {
        $this->initializeLogger($parameterBag, 'hardware_controller');
    }

    #[Route(path: '/hardware', name: 'hardware', methods: ['GET', 'POST'])]
    public function index(Request $request): Response
    {
        $table = $this->hardwareService->createMasterHardwareTable($request);

        if ($table->isCallback()) {
            return $table->getResponse();
        }

        return $this->render('admin/Hardware/index.html.twig', [
            'datatable' => $table,
        ]);
    }

    #[Route('/hardware/create', name: 'hardware_create', methods: ['GET', 'POST'])]
    public function createHardware(Request $request): Response
    {
        $hardware = new Hardware();
        $form = $this->createForm(HardwareFormType::class, $hardware);

        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $this->entityManager->persist($hardware);
            $this->entityManager->flush();

            $this->logHardwareHistory($hardware, 'created', null, $this->serializeHardware($hardware));

            $this->addFlash('success', 'Hardware created successfully.');
            return $this->redirectToRoute('hardware');
        }

        return $this->render('admin/Hardware/new.html.twig', [
            'form' => $form->createView(),
            'mode' => 'create',
        ]);
    }

    #[Route('/hardware/edit/{id}', name: 'hardware_edit', methods: ['GET', 'POST'])]
    public function editHardware(
        Request                $request,
        Hardware               $hardware,
        EntityManagerInterface $entityManager
    ): Response
    {
        $oldData = $this->serializeHardware($hardware);
        $form = $this->createForm(HardwareFormType::class, $hardware);

        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $entityManager->flush();

            $this->logHardwareHistory($hardware, 'updated', $oldData, $this->serializeHardware($hardware));

            $this->addFlash('success', 'Hardware updated successfully.');
            return $this->redirectToRoute('hardware');
        }

        return $this->render('admin/Hardware/new.html.twig', [
            'form' => $form->createView(),
            'hardware' => $hardware,
            'mode' => 'edit',
        ]);
    }

    #[Route('/hardware/delete/{id}', name: 'hardware_delete', methods: ['POST'])]
    public function deleteHardware(
        Request $request,
        Hardware $hardware,
        EntityManagerInterface $entityManager,
        CsrfTokenManagerInterface $csrfTokenManager
    ): Response {
        $token = $request->request->get('_token');

        if (!$csrfTokenManager->isTokenValid(new CsrfToken('delete' . $hardware->getId(), $token))) {
            return $this->json([
                'success' => false,
                'message' => 'Invalid CSRF token.'
            ], Response::HTTP_FORBIDDEN);
        }

        $entityManager->remove($hardware);
        $entityManager->flush();

        return $this->json([
            'success' => true,
            'message' => 'Hardware has been deleted.'
        ], Response::HTTP_OK);
    }

    #[Route('/hardware/view/{id}', name: 'hardware_view', methods: ['GET'])]
    public function show(Hardware $hardware): Response
    {
        $history = $this->hardwareHistory->findBy(['hardware' => $hardware], ['changedAt' => 'DESC']);
        return $this->render('admin/Hardware/new.html.twig', [
            'hardware' => $hardware,
            'mode' => 'view',
            'history' => $history,
        ]);
    }

    private function logHardwareHistory(Hardware $hardware, string $changeType, ?array $oldData, ?array $newData): void
    {
        $history = new HardwareHistory();
        $history->setHardware($hardware);
        $history->setChangedBy($this->getUser() ? $this->getUser()->getUserIdentifier() : 'anonymous');
        $history->setChangeType($changeType);
        $history->setOldData($oldData);
        $history->setNewData($newData);
        $history->setChangedAt(new \DateTime());

        $this->entityManager->persist($history);
        $this->entityManager->flush();
    }

    private function serializeHardware(Hardware $hardware): array
    {
        return [
            'hardwareName' => $hardware->getHardwareName(),
            'hardwareType' => $hardware->getHardwareType()?->getName(),
            'employee' => $hardware->getEmployee()?->getName(),
            'description' => $hardware->getDescription(),
            'showOnDashboard' => $hardware->isShowOnDashboard() ? 'Yes' : 'No',
            'purchaseDate' => $hardware->getPurchaseDate()?->format('Y-m-d'),
            'warrantyEndDate' => $hardware->getWarrantyEndDate()?->format('Y-m-d'),
            'vendorName' => $hardware->getVendorName(),
            'manufacturerName' => $hardware->getManufacturerName(),
            'manufacturerModel' => $hardware->getManufacturerModel(),
            'manufacturerSerial' => $hardware->getManufacturerSerial(),
            'condition' => $hardware->getCondition(),
            'ipAddress' => $hardware->getIpAddress(),
            'macAddress' => $hardware->getMacAddress(),
            'location' => $hardware->getLocation(),
            'pcName' => $hardware->getPcName(),
            'ram' => $hardware->getRam(),
            'rom' => $hardware->getRom(),
        ];
    }
}