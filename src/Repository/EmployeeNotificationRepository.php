<?php

namespace App\Repository;

use App\Entity\EmployeeNotification;
use App\Entity\MasterEmployee;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<EmployeeNotification>
 */
class EmployeeNotificationRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, EmployeeNotification::class);
    }

    public function countNotifiedEmployees(): int
    {
        $yesterday = (new \DateTime('yesterday'))->format('Y-m-d');

        return $this->createQueryBuilder('en')
            ->select('COUNT(DISTINCT en.employee)')
            ->leftJoin('en.employeeHours', 'eh')
            ->where('eh.reportDate = :yesterday')
            ->setParameter('yesterday', $yesterday)
            ->getQuery()
            ->getSingleScalarResult();
    }

    public function countJustificationGiven(): int
    {
        $yesterday = (new \DateTime('yesterday'))->format('Y-m-d');

        return $this->createQueryBuilder('en')
            ->select('COUNT(DISTINCT en.employee)')
            ->leftJoin('en.employeeHours', 'eh')
            ->where('eh.reportDate = :yesterday')
            ->andWhere('en.justifiedAt IS NOT NULL')
            ->setParameter('yesterday', $yesterday)
            ->getQuery()
            ->getSingleScalarResult();
    }


    public function countJustificationNotGiven(): int
    {
        $yesterday = (new \DateTime('yesterday'))->format('Y-m-d');

        return $this->createQueryBuilder('en')
            ->select('COUNT(DISTINCT en.employee)')
            ->leftJoin('en.employeeHours', 'eh')
            ->where('eh.reportDate = :yesterday')
            ->andWhere('en.justifiedAt IS NULL')
            ->setParameter('yesterday', $yesterday)
            ->getQuery()
            ->getSingleScalarResult();
    }

    public function findAllLatestSentAt(): array
    {
        $latestSentAt = $this->createQueryBuilder('en')
            ->select('MAX(en.sentAt)')
            ->getQuery()
            ->getSingleScalarResult();

        return $this->createQueryBuilder('en')
            ->leftJoin('en.employee', 'e')
            ->addSelect('e')
            ->leftJoin('en.employeeHours', 'eh')
            ->addSelect('eh')
            ->where('en.sentAt = :latestSentAt')
            ->setParameter('latestSentAt', $latestSentAt)
            ->getQuery()
            ->getResult();
    }
    public function findPendingJustifications(\DateTimeImmutable $date): array
    {
        return $this->createQueryBuilder('en')
            ->where('en.isApproved IS NULL')
            ->andWhere('en.sentAt <= :date')
            ->andWhere('en.status <= :status')
            ->setParameter('date', $date)
            ->setParameter('status', 0)
            ->getQuery()
            ->getResult();
    }

    public function countNotificationsForEmployee(MasterEmployee $masterEmployee): int
    {
        return (int) $this->createQueryBuilder('en')
            ->select('COUNT(en.id)')
            ->join('en.employee', 'e')
            ->join('e.masterEmployee', 'me')
            ->where('me.id = :masterEmployeeId')
            ->setParameter('masterEmployeeId', $masterEmployee->getId())
            ->getQuery()
            ->getSingleScalarResult();
    }

    public function countJustifiedAndNotJustifiedForEmployee(MasterEmployee $masterEmployee): array
    {
        return $this->createQueryBuilder('en')
            ->select(
                'SUM(CASE WHEN en.status = true THEN 1 ELSE 0 END) as justifiedCount',
                'SUM(CASE WHEN en.status = false THEN 1 ELSE 0 END) as notJustifiedCount'
            )
            ->join('en.employee', 'e')
            ->join('e.masterEmployee', 'me')
            ->where('me.id = :masterEmployeeId')
            ->setParameter('masterEmployeeId', $masterEmployee->getId())
            ->getQuery()
            ->getSingleResult();
    }

}
