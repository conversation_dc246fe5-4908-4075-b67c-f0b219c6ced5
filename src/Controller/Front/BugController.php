<?php

namespace App\Controller\Front;

use App\Entity\Bug;
use App\Entity\BugStatusHistory;
use App\Entity\MasterEmployee;
use App\Entity\Status;
use App\Entity\Task;
use App\Entity\TaskAssignment;
use App\Form\EmployeeBugType;
use App\Message\BugNotificationMessage;
use App\Repository\TaskRepository;
use App\Service\Common\BugService;
use App\Traits\ExceptionLoggerTrait;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Messenger\Exception\ExceptionInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Csrf\CsrfToken;
use Symfony\Component\Security\Csrf\CsrfTokenManagerInterface;


class BugController extends AbstractController
{
    use ExceptionLoggerTrait;
    public function __construct(
        private readonly BugService $bugService,
        private readonly EntityManagerInterface $entityManager,
        private readonly CsrfTokenManagerInterface $csrfTokenManager,
        private readonly MessageBusInterface $messageBus,
    ) {}

    #[Route('/bug', name: 'bug_index', methods: ['GET', 'POST'])]
    public function index(Request $request): Response
    {
        $employee = $this->getUser();
        if (!$employee instanceof MasterEmployee) {
            throw $this->createAccessDeniedException('This can only be accessed by an employee.');
        }
        $table = $this->bugService->createBugTable($request, $employee);
        if ($table->isCallback()) {
            return $table->getResponse();
        }
        $taskId = $request->query->getInt('task');
        $taskAssignmentId = $request->query->getInt('taskAssignmentId');
        if ($taskId && $taskAssignmentId) {
            return new RedirectResponse($this->generateUrl('bug_index', ['taskAssignmentId' => $taskAssignmentId]));
        }
        $task = null;
        $taskAssignment = null;

        if ($taskId) {
            $task = $this->entityManager->getRepository(Task::class)->find($taskId);
            if (!$task) {
                throw $this->createNotFoundException('Task not found.');
            }
            if ($task->getAssignedTo() !== $employee) {
                throw $this->createAccessDeniedException('You are not assigned to this task.');
            }
        }
        if ($taskAssignmentId) {
            $taskAssignment = $this->entityManager->getRepository(TaskAssignment::class)->find($taskAssignmentId);
            if (!$taskAssignment) {
                throw $this->createNotFoundException('Task not found.');
            }
            if ($taskAssignment->getAssignedTo() !== $employee) {
                throw $this->createAccessDeniedException('You are not assigned to this task.');
            }
        }
        $isHistory = ($taskAssignment?->getStatus() === 'COMPLETED') || ($task?->getStatus() === 'COMPLETED');
        return $this->render('Front/Bug/index.html.twig', [
            'datatable' => $table,
            'task' => $task,
            'taskAssignment' => $taskAssignment,
            'isHistory' => $isHistory,
        ]);
    }

    /**
     * @throws \Exception
     * @throws ExceptionInterface
     */
    #[Route('/bug/new', name: 'bug_new', methods: ['GET', 'POST'])]
    public function new(Request $request): Response
    {
        $employee = $this->getUser();
        if (!$employee instanceof MasterEmployee) {
            throw $this->createAccessDeniedException('This can only be accessed by an employee.');
        }
        $taskId = $request->query->getInt('task');
        $taskAssignmentId = $request->query->getInt('taskAssignmentId');

        $task = null;
        $taskAssignment = null;
        if (!$taskId && !$taskAssignmentId) {
            throw $this->createAccessDeniedException('A task or task assignment must be specified to create a bug.');
        }
        $bug = new Bug();
        $bug->setAssignedBy($employee);
        if ($taskId) {
            $task = $this->entityManager->getRepository(Task::class)->find($taskId);
            if (!$task) {
                throw new NotFoundHttpException('Task not found.');
            }
            $project = $task->getProject();
            $bug->setTask($task);
            $bug->setProject($project);
            $bug->setAssignedTo($task->getAssignedBy());
        } elseif ($taskAssignmentId) {
            $taskAssignment = $this->entityManager->getRepository(TaskAssignment::class)->find($taskAssignmentId);
            if (!$taskAssignment) {
                throw new NotFoundHttpException('Task assignment not found.');
            }
            $task = $taskAssignment->getTask();
            $project = $task ? $task->getProject() : null;
            if ($taskAssignment->getAssignedTo() !== $employee) {
                throw $this->createAccessDeniedException('You are not assigned to this task.');
            }
            $bug->setTaskAssignment($taskAssignment);
            $bug->setTask($task);
            $bug->setProject($project);
            $bug->setAssignedTo($taskAssignment->getAssignedBy());
        }

        $form = $this->createForm(EmployeeBugType::class, $bug);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $bug->setCreatedAt(new \DateTime());
            $this->entityManager->persist($bug);
            $this->entityManager->flush();

            $statusHistory = new BugStatusHistory();
            $statusHistory->setBug($bug);
            $statusHistory->setChangedBy($employee);
            $statusHistory->setChangedByAdmin(null);
            $statusHistory->setStatus($bug->getStatus());
            $statusHistory->setChangedAt(new \DateTime());
            $this->entityManager->persist($statusHistory);
            $this->entityManager->flush();
            try {
                $this->messageBus->dispatch(new BugNotificationMessage(
                    $bug->getId(),
                    $bug->getAssignedTo()->getId(),
                    'create',
                    []
                ));
            } catch (\Throwable $e) {
                $this->log('Notification dispatch failed: ' . $e->getMessage());
            }

            $this->addFlash('success', 'Bug created successfully.');
            return $this->redirectToRoute('bug_index', [
                'task' => $taskId ?: null,
                'taskAssignmentId' => $taskAssignmentId ?: null,
            ]);
        }
        $isHistory = ($taskAssignment?->getStatus() === 'COMPLETED') || ($task?->getStatus() === 'COMPLETED');
        return $this->render('Front/Bug/new.html.twig', [
            'form' => $form->createView(),
            'mode' => 'create',
            'task' => $task,
            'taskAssignment' => $taskAssignment,
            'isHistory' => $isHistory,
        ]);
    }

    #[Route('/bug/{id}/edit', name: 'bug_edit', methods: ['GET', 'POST'])]
    public function edit(Request $request, int $id): Response
    {
        $employee = $this->getUser();
        if (!$employee instanceof MasterEmployee) {
            throw $this->createAccessDeniedException('This can only be accessed by an employee.');
        }

        $bug = $this->entityManager->getRepository(Bug::class)->find($id);
        if (!$bug) {
            throw new NotFoundHttpException('Bug not found');
        }

        if ($bug->getAssignedBy() !== $employee) {
            throw $this->createAccessDeniedException('You are not allowed to edit this bug.');
        }

        $form = $this->createForm(EmployeeBugType::class, $bug);
        $originalDescription = $bug->getComments();
        $originalStatus = $bug->getStatus();
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $newStatus = $bug->getStatus();
            $newDescription = $bug->getComments();

            if ($originalStatus !== $newStatus) {
                $statusHistory = new BugStatusHistory();
                $statusHistory->setBug($bug);
                $statusHistory->setChangedBy($employee);
                $statusHistory->setChangedByAdmin(null);
                $statusHistory->setStatus($newStatus);
                $statusHistory->setChangedAt(new \DateTime());
                $this->entityManager->persist($statusHistory);
            }
            $bug->setUpdatedAt(new \DateTime());
            $this->entityManager->persist($bug);
            $this->entityManager->flush();
            try {
                $changes = [];
                if ($originalDescription !== $newDescription) {
                    $changes[] = 'description';
                }
                if ($originalStatus !== $newStatus) {
                    $changes[] = 'status';
                }

                if (!empty($changes)) {
                    $this->messageBus->dispatch(new BugNotificationMessage(
                        $bug->getId(),
                        $bug->getAssignedTo()->getId(),
                        'update',
                        $changes
                    ));
                }
            } catch (\Throwable $e) {
                $this->log('Notification dispatch failed: ' . $e->getMessage());
            }

            $this->addFlash('success', 'Bug updated successfully.');

            return $this->redirectToRoute('bug_index', [
                'task' => $bug->getTask() ? $bug->getTask()->getId() : null,
                'taskAssignmentId' => $bug->getTaskAssignment() ? $bug->getTaskAssignment()->getId() : null,
            ]);
        }
        $task = $bug->getTask();
        $taskAssignment = $bug->getTaskAssignment();
        $isHistory = ($taskAssignment?->getStatus() === 'COMPLETED') || ($task?->getStatus() === 'COMPLETED');
        return $this->render('Front/Bug/new.html.twig', [
            'form' => $form->createView(),
            'mode' => 'edit',
            'task' => $bug->getTask(),
            'taskAssignment' => $bug->getTaskAssignment(),
            'isHistory' => $isHistory,
        ]);
    }


    #[Route('/bug/{id}/update-status', name: 'bug_update_status', methods: ['POST'])]
    public function updateStatus(Request $request, int $id): JsonResponse
    {
        $employee = $this->getUser();
        if (!$employee instanceof MasterEmployee) {
            return $this->json(['success' => false, 'error' => 'Access denied'], 403);
        }

        $bug = $this->entityManager->getRepository(Bug::class)->find($id);
        if (!$bug) {
            return $this->json(['success' => false, 'error' => 'Bug not found'], 404);
        }

        if ($bug->getAssignedTo() !== $employee) {
            return $this->json(['success' => false, 'error' => 'You are not authorized to update this bug'], 403);
        }

        $csrfToken = $request->request->get('_csrf_token');
        if (!$this->csrfTokenManager->isTokenValid(new CsrfToken('update_status_' . $id, $csrfToken))) {
            return $this->json(['success' => false, 'error' => 'Invalid CSRF token'], 400);
        }

        $status = $request->request->get('status');
        $testerComments = $request->request->get('testerComments');

        try {
            $statusEnum = Status::from($status);
            $oldStatus = $bug->getStatus();
            $bug->setStatus($statusEnum);
            $bug->setUpdatedAt(new \DateTime());

            if ($testerComments !== null) {
                $bug->setTesterComments($testerComments);
            }
            if ($oldStatus !== $statusEnum) {
                $statusHistory = new BugStatusHistory();
                $statusHistory->setBug($bug);
                $statusHistory->setStatus($statusEnum);
                $statusHistory->setChangedBy($employee);
                $statusHistory->setChangedByAdmin(null);
                $statusHistory->setChangedAt(new \DateTime());

                $this->entityManager->persist($statusHistory);
            }

            $this->entityManager->persist($bug);
            $this->entityManager->flush();

            return $this->json(['success' => true]);
        } catch (\Throwable $e) {
            return $this->json(['success' => false, 'error' => 'An error occurred'], 500);
        }
    }


    #[Route('/api/tasks', name: 'api_tasks')]
    public function getTasks(Request $request, TaskRepository $taskRepository): JsonResponse
    {
        $projectId = $request->query->get('project');
        $employee = $this->getUser();
        $tasks = $taskRepository->findBy([
            'project' => $projectId,
            'assignedTo' => $employee
        ]);

        $data = array_map(function ($task) {
            return [
                'id' => $task->getId(),
                'title' => $task->getTitle(),
            ];
        }, $tasks);

        return $this->json($data);
    }

    #[Route('/api/task/{id}', name: 'api_task')]
    public function getTask(Task $task): JsonResponse
    {
        return $this->json([
            'id' => $task->getId(),
            'description' => $task->getDescription(),
        ]);
    }
    #[Route('/my-bugs', name: 'my_bugs', methods: ['GET', 'POST'])]
    public function myBugs(Request $request): Response
    {
        $employee = $this->getUser();
        if (!$employee instanceof MasterEmployee) {
            throw $this->createAccessDeniedException('This can only be accessed by an employee.');
        }
        $table = $this->bugService->createMyBugsTable($request, $employee);
        if ($table->isCallback()) {
            return $table->getResponse();
        }
        return $this->render('Front/Bug/my_bugs.html.twig', [
            'datatable' => $table,
            'isHistory' => false,
        ]);
    }

    #[Route('/my-bugs/history', name: 'my_bugs_history', methods: ['GET', 'POST'])]
    public function myBugsHistory(Request $request): Response
    {
        $employee = $this->getUser();
        if (!$employee instanceof MasterEmployee) {
            throw $this->createAccessDeniedException('This can only be accessed by an employee.');
        }
        $table = $this->bugService->createMyBugsHistoryTable($request, $employee);
        if ($table->isCallback()) {
            return $table->getResponse();
        }

        return $this->render('Front/Bug/my_bugs.html.twig', [
            'datatable' => $table,
            'isHistory' => true,
        ]);
    }
}
?>