<?php

namespace App\Form;

use App\Entity\LeaveType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\Length;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\Positive;
use Symfony\Component\Validator\Constraints as Assert;

class LeaveTypeFormType extends AbstractType
{
    private const PROTECTED_LEAVE_NAMES = [
        'Addon Leave',
        'Sick Leave',
        'Casual Leave'
    ];
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $leaveType = $options['data'] ?? null;
        $isProtected = $this->isProtectedLeaveType($leaveType);

        $builder
            ->add('name', TextType::class, [
                'label' => 'Leave Type Name',
                'attr' => [
                    'placeholder' => 'Enter leave type name',
                    'class' => 'form-control',
                    'maxlength' => 50,
                    'data-validation' => 'max:50',
                    'readonly' => $isProtected
                ],
                'constraints' => [
                    new NotBlank([
                        'message' => 'Leave type name cannot be blank',
                    ]),
                    new Length([
                        'min' => 2,
                        'max' => 50,
                        'minMessage' => 'Leave type name must be at least {{ limit }} characters long',
                        'maxMessage' => 'Leave type name cannot be longer than {{ limit }} characters',
                    ]),
                ],
            ])
            ->add('description', TextareaType::class, [
                'label' => 'Description',
                'required' => false,
                'attr' => [
                    'placeholder' => 'Enter leave type description (optional)',
                    'class' => 'form-control',
                    'rows' => 1,
                    'maxlength' => 255,
                    'data-validation' => 'max:255'
                ],
                'constraints' => [
                    new Length([
                        'max' => 255,
                        'maxMessage' => 'Description cannot be longer than {{ limit }} characters',
                    ]),
                ],
            ])
            ->add('maxDays', IntegerType::class, [
                'label' => 'Maximum Days',
                'attr' => [
                    'placeholder' => 'Enter maximum days',
                    'class' => 'form-control',
                    'min' => 1,
                    'max' => 366,
                ],
                'constraints' => [
                    new NotBlank([
                        'message' => 'Maximum days cannot be blank',
                    ]),
                    new Positive([
                        'message' => 'Maximum days must be a positive number',
                    ]),
                    new Assert\LessThanOrEqual([
                        'value' => 366,
                        'message' => 'Maximum days cannot exceed 366 days',
                    ]),
                ],
            ])
            ->add('appliedFrom', ChoiceType::class, [
                'choices' => [
                    'Confirmation Date' => 'CO',
                    'Joining Date' => 'JD',
                ],
                'label' => 'Is Applied From',
            ])
            ->add('isPaid', CheckboxType::class, [
                'label' => 'Paid Leave',
                'required' => false,
                'attr' => [
                    'class' => 'form-check-input'
                ],
                'label_attr' => [
                    'class' => 'form-check-label',
                ],
            ])
            ->add('noticeDaysSingle', IntegerType::class, [
                'label' => 'Notice Days for Single Monday, Friday, or Holiday',
                'required' => false,
                'attr' => [
                    'class' => 'form-control',
                    'min' => 1,
                    'max' => 366,
                    'placeholder' => 'e.g., 15 days',
                ],
                'constraints' => [
                    new Positive([
                        'message' => 'Notice days must be a positive number',
                    ]),
                    new Assert\LessThanOrEqual([
                        'value' => 366,
                        'message' => 'Notice days cannot exceed 366 days',
                    ]),
                ],
            ])
            ->add('noticeDaysLongWeekend', IntegerType::class, [
                'label' => 'Notice Days for Long Weekend or Holiday Sandwich',
                'required' => false,
                'attr' => [
                    'class' => 'form-control',
                    'min' => 1,
                    'max' => 366,
                    'placeholder' => 'e.g., 30 days',
                ],
                'constraints' => [
                    new Positive([
                        'message' => 'Notice days must be a positive number',
                    ]),
                    new Assert\LessThanOrEqual([
                        'value' => 366,
                        'message' => 'Notice days cannot exceed 366 days',
                    ]),
                ],
            ]);
    }

    private function isProtectedLeaveType(?LeaveType $leaveType): bool
    {
        if (!$leaveType || !$leaveType->getName()) {
            return false;
        }

        return in_array($leaveType->getName(), self::PROTECTED_LEAVE_NAMES);
    }
    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => LeaveType::class,
        ]);
    }
}