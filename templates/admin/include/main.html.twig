{% extends 'admin/dashboard/base.html.twig' %}
{% block body %}
<div class="content">
    <div class="animated fadeIn">
        <div class="row">
            <div class="col-lg-3 col-md-6">
                <div class="card">
                    <div class="card-body">
                        <div class="stat-widget-five">
                            <div class="stat-icon dib flat-color-1">
                                <i class="fa fa-users"></i>
                            </div>
                            <div class="stat-content">
                                <div class="text-left dib">
                                    <div class="stat-text"><span class="count">{{ employeeCount }}</span></div>
                                    <div class="stat-heading">Employees</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="card">
                    <div class="card-body">
                        <div class="stat-widget-five">
                            <div class="stat-icon dib flat-color-5">
                                <i class="fa fa-check-circle"></i>
                            </div>
                            <div class="stat-content">
                                <div class="text-left dib">
                                    <div class="stat-text"><span class="count">{{ justificationGivenCount }}</span></div>
                                    <div class="stat-heading">Justified</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="card">
                    <div class="card-body">
                        <div class="stat-widget-five">
                            <div class="stat-icon dib flat-color-6">
                                <i class="fa fa-times-circle"></i>
                            </div>
                            <div class="stat-content">
                                <div class="text-left dib">
                                    <div class="stat-text"><span class="count">{{ justificationNotGivenCount }}</span></div>
                                    <div class="stat-heading">Not Justified</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="card">
                    <div class="card-body">
                        <div class="stat-widget-five">
                            <div class="stat-icon dib flat-color-1">
                                <i class="fa fa-hourglass"></i>
                            </div>
                            <div class="stat-content">
                                <div class="text-left dib">
                                    <div class="stat-text"><span class="count">{{ remainingHours }}</span></div>
                                    <div class="stat-heading">Compensated</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<div class="orders">
    <div class="row">
        <div class="col-xl-12">
            <div class="card">
                <div class="card-body">
                    {% if justifiable is not empty %}
                    <div class="table-stats order-table ov-h text-center">
                        <table class="table">
                            <thead>
                            <tr>
                                <th class="serial font-weight-bold">#</th>
                                <th class="font-weight-bold">Employee</th>
                                <th class="font-weight-bold">Log Hours</th>
                                <th class="font-weight-bold">Log Date</th>
                                <th class="font-weight-bold">Reminder Date</th>
                                <th class="font-weight-bold">Justification</th>
                            </tr>
                            </thead>
                            <tbody>
                            {% for justify in justifiable %}
                            <tr>

                                <th class="serial">{{ loop.index }}</th>
                                <td>{{ justify.employee.masterEmployee.name|default('N/A') }}</td>
                                <td>{{ justify.employeeHours.totalHours | decimal_to_time }}</td>
                                <td>{{ justify.employeeHours.reportDate| custom_date }}</td>
                                <td>{{ justify.sentAt| custom_date }}</td>
                                <td>
                                    {% if justify.justification %}
                                        <button type="button" class="bs-btn-main btn btn-primary btn-sm" data-toggle="modal"
                                                data-target="#justificationModal{{ justify.id }}" data-justification="{{ justify.justification|escape('html') }}">
                                            <i class="bs-fa-main fa fa-eye"></i>
                                        </button>
                                    {% endif %}
                                    <div class="modal fade" id="justificationModal{{ justify.id }}" tabindex="-1" role="dialog" aria-labelledby="justificationModalLabel" aria-hidden="true">
                                        <div class="modal-dialog modal-lg" role="document">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h5 class="modal-title font-weight-bold" id="justificationModalLabel" style="color: #0b2e13;">Justification Details</h5>
                                                </div>
                                                <div class="modal-body">
                                                    <p id="justificationContent" style="color: #0b2e13; font-weight: bold;">{{ justify.justification }}</p>
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                </td>
                            </tr>
                            {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                        <p>No justifiable entries found.</p>
                    {% endif %}
                </div>

            </div>
        </div>
    </div>
</div>
    <div class="orders">
        {% include 'CommonTemplate/dashboard.html.twig'%}
    </div>
</div>
    <div class="clearfix"></div>
    <link rel="stylesheet" href="{{ asset('assets/css/dashboard.css') }}">
{% endblock %}
