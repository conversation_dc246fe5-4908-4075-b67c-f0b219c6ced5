<?php

namespace App\Form;

use App\Entity\Payroll;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints as Assert;

class PayrollFormType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $positive = [new Assert\GreaterThanOrEqual([
            'value' => 0,
            'message' => 'This value must be greater than or equal to 0.'
        ])];

        $optionalPositive = [
            new Assert\Type(['type' => 'numeric', 'message' => 'Must be a number']),
            new Assert\GreaterThanOrEqual(['value' => 0, 'message' => 'Value must be ≥ 0']),
        ];

        $builder
            ->add('salary', NumberType::class, [
                'required' => true,
                'scale' => 2,
                'constraints' => [
                    new Assert\NotBlank(['message' => 'Salary is required.']),
                    ...$positive
                ],
            ])
            ->add('deduction', NumberType::class, [
                'required' => true,
                'scale' => 2,
                'constraints' => [
                    new Assert\NotBlank(['message' => 'Deduction is required.']),
                    ...$positive
                ],
            ])
            ->add('workingDays', NumberType::class, [
                'required' => true,
                'constraints' => [
                    new Assert\NotBlank(['message' => 'Working days required.']),
                    new Assert\Range([
                        'min' => 0,
                        'max' => 31,
                        'notInRangeMessage' => 'Working days must be between {{ min }} and {{ max }}.'
                    ]),
                ],
            ])
            ->add('presentDays', NumberType::class, [
                'required' => true,
                'constraints' => [
                    new Assert\NotBlank(['message' => 'Present days required.']),
                    new Assert\Range([
                        'min' => 0,
                        'max' => 31,
                        'notInRangeMessage' => 'Present days must be between {{ min }} and {{ max }}.'
                    ]),
                ],
            ])
            // Optional Fields Below
            ->add('perDayBasedOnBasic', NumberType::class, [
                'required' => false,
                'scale' => 2,
                'constraints' => $optionalPositive,
            ])
            ->add('totalLeaveDeduction', NumberType::class, [
                'required' => false,
                'scale' => 2,
                'constraints' => $optionalPositive,
            ])
            ->add('extraPay', NumberType::class, [
                'required' => false,
                'scale' => 2,
                'constraints' => $optionalPositive,
            ])
            ->add('salaryAfterLeave', NumberType::class, [
                'required' => false,
                'scale' => 2,
                'constraints' => $optionalPositive,
            ])
            ->add('basicDaSalary', NumberType::class, [
                'required' => false,
                'scale' => 2,
                'constraints' => $optionalPositive,
            ])
            ->add('hra', NumberType::class, [
                'required' => false,
                'scale' => 2,
                'constraints' => $optionalPositive,
            ])
            ->add('conveyanceAllowance', NumberType::class, [
                'required' => false,
                'scale' => 2,
                'constraints' => $optionalPositive,
            ])
            ->add('medicalAllowance', NumberType::class, [
                'required' => false,
                'scale' => 2,
                'constraints' => $optionalPositive,
            ])
            ->add('telephoneAllowance', NumberType::class, [
                'required' => false,
                'scale' => 2,
                'constraints' => $optionalPositive,
            ])
            ->add('specialAllowance', NumberType::class, [
                'required' => false,
                'scale' => 2,
                'constraints' => $optionalPositive,
            ])
            ->add('netSalary', NumberType::class, [
                'required' => false,
                'scale' => 2,
                'constraints' => $optionalPositive,
            ])
            ->add('diff', NumberType::class, [
                'required' => false,
                'scale' => 2,
            ])
            ->add('pt', NumberType::class, [
                'required' => false,
                'scale' => 2,
                'constraints' => $optionalPositive,
            ])
            ->add('esic', NumberType::class, [
                'required' => false,
                'scale' => 2,
                'constraints' => $optionalPositive,
            ])
            ->add('salaryForPFCalculations', NumberType::class, [
                'required' => false,
                'scale' => 2,
                'constraints' => $optionalPositive,
            ])
            ->add('pfEmployee', NumberType::class, [
                'required' => false,
                'scale' => 2,
                'constraints' => $optionalPositive,
            ])
            ->add('tdsDeduction', NumberType::class, [
                'required' => false,
                'scale' => 2,
                'constraints' => $optionalPositive,
            ])
            ->add('loanOtherDeductions', NumberType::class, [
                'required' => false,
                'scale' => 2,
                'constraints' => $optionalPositive,
            ])
            ->add('totalDeductions', NumberType::class, [
                'required' => false,
                'scale' => 2,
                'constraints' => $optionalPositive,
            ])
            ->add('salaryPayable', NumberType::class, [
                'required' => false,
                'scale' => 2,
                'constraints' => $optionalPositive,
            ])
            ->add('pfEmployer', NumberType::class, [
                'required' => false,
                'scale' => 2,
                'constraints' => $optionalPositive,
            ])
            ->add('ctc', NumberType::class, [
                'required' => false,
                'scale' => 2,
                'constraints' => $optionalPositive,
            ])
            ->add('inputCtcForCalculation', NumberType::class, [
                'required' => false,
                'scale' => 2,
                'constraints' => $optionalPositive,
            ]);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => Payroll::class,
        ]);
    }
}