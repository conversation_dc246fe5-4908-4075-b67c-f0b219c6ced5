<?php

namespace App\Repository;

use App\Entity\SalaryStructure;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<SalaryStructure>
 */
class SalaryStructureRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, SalaryStructure::class);
    }
    /**
     * Find SalaryStructure for an employee valid for a given date.
     */
    public function findValidCtcForEmployee(int $employeeId, \DateTimeInterface $date): ?SalaryStructure
    {
        return $this->createQueryBuilder('s')
            ->where('s.employee = :employeeId')
            ->andWhere('s.startDate <= :date')
            ->andWhere('(s.endDate IS NULL OR s.endDate >= :date)')
            ->setParameter('employeeId', $employeeId)
            ->setParameter('date', $date)
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult();
    }

    /**
     * Find all SalaryStructure records for an employee for a given month and year, ordered by startDate ascending.
     * @throws \DateMalformedStringException
     */
    public function findAllForEmployeeMonth(int $employeeId, int $year, int $month): array
    {
        $start = new \DateTimeImmutable(sprintf('%d-%02d-01', $year, $month));
        $end = $start->modify('last day of this month')->setTime(23, 59, 59);
        return $this->createQueryBuilder('s')
            ->where('s.employee = :employeeId')
            ->andWhere('s.startDate <= :end')
            ->andWhere('(s.endDate IS NULL OR s.endDate >= :start)')
            ->setParameter('employeeId', $employeeId)
            ->setParameter('start', $start)
            ->setParameter('end', $end)
            ->orderBy('s.startDate', 'ASC')
            ->getQuery()
            ->getResult();
    }
}
