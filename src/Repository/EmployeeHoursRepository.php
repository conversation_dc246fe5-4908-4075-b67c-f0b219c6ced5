<?php

namespace App\Repository;

use App\Entity\Employee;
use App\Entity\EmployeeHours;
use App\Entity\MasterEmployee;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Doctrine\Common\Collections\ArrayCollection;


/**
 * @extends ServiceEntityRepository<EmployeeHours>
 */
class EmployeeHoursRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, EmployeeHours::class);
    }
    public function findByDateRange(\DateTime $startDate, \DateTime $endDate, ?MasterEmployee $masterEmployee = null): array
    {
        $qb = $this->createQueryBuilder('eh')
            ->join('eh.employee', 'e')
            ->join('e.masterEmployee', 'me')
            ->addSelect('e', 'me')
            ->where('eh.reportDate BETWEEN :startDate AND :endDate')
            ->setParameter('startDate', $startDate->format('Y-m-d'))
            ->setParameter('endDate', $endDate->format('Y-m-d'));

        if ($masterEmployee !== null) {
            $qb->andWhere('me.id = :masterEmployeeId')
                ->setParameter('masterEmployeeId', $masterEmployee->getId());
        }

        return $qb->orderBy('eh.reportDate', 'ASC')
            ->getQuery()
            ->getResult();
    }

    public function findByMasterEmployeeAndPreviousMonth(MasterEmployee $masterEmployee): array
    {
        $lastMonth = new \DateTime('first day of last month');
        $lastMonthEnd = new \DateTime('last day of last month');

        return $this->createQueryBuilder('eh')
            ->join('eh.employee', 'e')
            ->join('e.masterEmployee', 'me')
            ->where('me.id = :masterEmployeeId')
            ->andWhere('eh.reportDate BETWEEN :startDate AND :endDate')
            ->setParameters(new ArrayCollection([
                new \Doctrine\ORM\Query\Parameter('masterEmployeeId', $masterEmployee->getId()),
                new \Doctrine\ORM\Query\Parameter('startDate', $lastMonth),
                new \Doctrine\ORM\Query\Parameter('endDate', $lastMonthEnd),
            ]))
            ->getQuery()
            ->getResult();
    }

    public function getTotalHoursForCurrentMonth(MasterEmployee $masterEmployee): float
    {
        $currentMonthStart = new \DateTime('first day of this month');
        $currentMonthEnd = new \DateTime('last day of this month');

        return (float) $this->createQueryBuilder('eh')
            ->select('SUM(eh.totalHours) as total_hours')
            ->join('eh.employee', 'e')
            ->join('e.masterEmployee', 'me')
            ->where('me.id = :masterEmployeeId')
            ->andWhere('eh.reportDate BETWEEN :startDate AND :endDate')
            ->setParameter('masterEmployeeId', $masterEmployee->getId())
            ->setParameter('startDate', $currentMonthStart->format('Y-m-d'))
            ->setParameter('endDate', $currentMonthEnd->format('Y-m-d'))
            ->getQuery()
            ->getSingleScalarResult();
    }

    public function findByMasterEmployeeAndDateRange(MasterEmployee $masterEmployee, \DateTime $startDate, \DateTime $endDate): array
    {
        $startDate = (clone $startDate)->setTime(0, 0, 0);
        $endDate = (clone $endDate)->setTime(23, 59, 59);

        return $this->createQueryBuilder('eh')
            ->join('eh.employee', 'e')
            ->where('e.masterEmployee = :masterEmployee')
            ->andWhere('eh.reportDate BETWEEN :startDate AND :endDate')
            ->setParameter('masterEmployee', $masterEmployee)
            ->setParameter('startDate', $startDate)
            ->setParameter('endDate', $endDate)
            ->orderBy('eh.reportDate', 'ASC')
            ->getQuery()
            ->getResult();
    }
}
