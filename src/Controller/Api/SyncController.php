<?php

namespace App\Controller\Api;

use App\Entity\TimeLog;
use App\Entity\ScreenshotLog;
use App\Entity\ApplicationUsage;
use App\Entity\WebsiteUsage;
use App\Repository\TimeLogRepository;
use App\Repository\ScreenshotLogRepository;
use App\Repository\ApplicationUsageRepository;
use App\Repository\WebsiteUsageRepository;
use App\Repository\MasterEmployeeRepository;
use App\Service\SyncService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

#[Route('/api/sync', name: 'api_sync_')]
class SyncController extends AbstractController
{
    public function __construct(
        private TimeLogRepository $timeLogRepository,
        private ScreenshotLogRepository $screenshotLogRepository,
        private ApplicationUsageRepository $applicationUsageRepository,
        private WebsiteUsageRepository $websiteUsageRepository,
        private MasterEmployeeRepository $employeeRepository,
        private EntityManagerInterface $entityManager,
        private SyncService $syncService
    ) {}

    #[Route('/bulk', name: 'bulk', methods: ['POST'])]
    public function bulkSync(Request $request): JsonResponse
    {
        try {
            $data = json_decode($request->getContent(), true);

            if (!isset($data['employee_id'])) {
                return new JsonResponse([
                    'success' => false,
                    'error' => 'Employee ID is required'
                ], 400);
            }

            $employee = $this->employeeRepository->find($data['employee_id']);
            if (!$employee) {
                return new JsonResponse([
                    'success' => false,
                    'error' => 'Employee not found'
                ], 404);
            }

            $result = $this->syncService->processBulkSync($data);

            return new JsonResponse([
                'success' => $result['success'],
                'message' => $result['success'] ? 'Bulk sync completed successfully' : 'Bulk sync completed with errors',
                'processed' => $result['processed'],
                'errors' => $result['errors'],
                'details' => $result['details'],
                'timestamp' => new \DateTime()
            ]);

        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => 'Failed to process bulk sync',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    #[Route('/time-logs', name: 'time_logs', methods: ['POST'])]
    public function syncTimeLogs(Request $request): JsonResponse
    {
        try {
            $data = json_decode($request->getContent(), true);
            
            if (!isset($data['employee_id']) || !isset($data['logs'])) {
                return new JsonResponse([
                    'success' => false,
                    'error' => 'Employee ID and logs data are required'
                ], 400);
            }

            $employee = $this->employeeRepository->find($data['employee_id']);
            if (!$employee) {
                return new JsonResponse([
                    'success' => false,
                    'error' => 'Employee not found'
                ], 404);
            }

            $syncedCount = 0;
            $errors = [];

            foreach ($data['logs'] as $logData) {
                try {
                    $timeLog = new TimeLog();
                    $timeLog->setEmployee($employee);
                    $timeLog->setLogDate(new \DateTime($logData['log_date']));
                    $timeLog->setStartTime(new \DateTime($logData['start_time']));
                    
                    if (isset($logData['end_time']) && $logData['end_time']) {
                        $timeLog->setEndTime(new \DateTime($logData['end_time']));
                    }
                    
                    $timeLog->setLogType($logData['log_type'] ?? 'work');
                    $timeLog->setDuration($logData['duration'] ?? null);
                    $timeLog->setDescription($logData['description'] ?? null);
                    $timeLog->setDeviceId($logData['device_id'] ?? null);
                    $timeLog->setMetadata($logData['metadata'] ?? null);
                    $timeLog->setIsSynced(true);

                    $this->entityManager->persist($timeLog);
                    $syncedCount++;
                    
                } catch (\Exception $e) {
                    $errors[] = "Failed to sync log: " . $e->getMessage();
                }
            }

            $this->entityManager->flush();

            return new JsonResponse([
                'success' => true,
                'synced_count' => $syncedCount,
                'errors' => $errors,
                'message' => "Successfully synced {$syncedCount} time logs"
            ]);

        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => 'Failed to sync time logs',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    #[Route('/application-usage', name: 'application_usage', methods: ['POST'])]
    public function syncApplicationUsage(Request $request): JsonResponse
    {
        try {
            $data = json_decode($request->getContent(), true);
            
            if (!isset($data['employee_id']) || !isset($data['usage_data'])) {
                return new JsonResponse([
                    'success' => false,
                    'error' => 'Employee ID and usage data are required'
                ], 400);
            }

            $employee = $this->employeeRepository->find($data['employee_id']);
            if (!$employee) {
                return new JsonResponse([
                    'success' => false,
                    'error' => 'Employee not found'
                ], 404);
            }

            $syncedCount = 0;
            $errors = [];

            foreach ($data['usage_data'] as $usageData) {
                try {
                    $appUsage = new ApplicationUsage();
                    $appUsage->setEmployee($employee);
                    $appUsage->setUsageDate(new \DateTime($usageData['usage_date']));
                    $appUsage->setApplicationName($usageData['application_name']);
                    $appUsage->setApplicationPath($usageData['application_path'] ?? null);
                    $appUsage->setWindowTitle($usageData['window_title'] ?? null);
                    $appUsage->setStartTime(new \DateTime($usageData['start_time']));
                    
                    if (isset($usageData['end_time']) && $usageData['end_time']) {
                        $appUsage->setEndTime(new \DateTime($usageData['end_time']));
                    }
                    
                    $appUsage->setDuration($usageData['duration'] ?? null);
                    $appUsage->setCategory($usageData['category'] ?? null);
                    $appUsage->setSwitchCount($usageData['switch_count'] ?? 1);
                    $appUsage->setDeviceId($usageData['device_id'] ?? null);

                    // Handle metadata - convert JSON string to array if needed
                    $metadata = $usageData['metadata'] ?? null;
                    if (is_string($metadata)) {
                        $metadata = json_decode($metadata, true);
                    }
                    $appUsage->setMetadata($metadata);
                    $appUsage->setIsSynced(true);

                    $this->entityManager->persist($appUsage);
                    $syncedCount++;
                    
                } catch (\Exception $e) {
                    $errors[] = "Failed to sync application usage: " . $e->getMessage();
                }
            }

            $this->entityManager->flush();

            return new JsonResponse([
                'success' => true,
                'synced_count' => $syncedCount,
                'errors' => $errors,
                'message' => "Successfully synced {$syncedCount} application usage records"
            ]);

        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => 'Failed to sync application usage',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    #[Route('/website-usage', name: 'website_usage', methods: ['POST'])]
    public function syncWebsiteUsage(Request $request): JsonResponse
    {
        try {
            $data = json_decode($request->getContent(), true);
            
            if (!isset($data['employee_id']) || !isset($data['usage_data'])) {
                return new JsonResponse([
                    'success' => false,
                    'error' => 'Employee ID and usage data are required'
                ], 400);
            }

            $employee = $this->employeeRepository->find($data['employee_id']);
            if (!$employee) {
                return new JsonResponse([
                    'success' => false,
                    'error' => 'Employee not found'
                ], 404);
            }

            $syncedCount = 0;
            $errors = [];

            foreach ($data['usage_data'] as $usageData) {
                try {
                    $websiteUsage = new WebsiteUsage();
                    $websiteUsage->setEmployee($employee);
                    $websiteUsage->setVisitDate(new \DateTime($usageData['visit_date']));
                    $websiteUsage->setUrl($usageData['url']);
                    $websiteUsage->setDomain($usageData['domain']);
                    $websiteUsage->setPageTitle($usageData['page_title'] ?? null);
                    $websiteUsage->setVisitTime(new \DateTime($usageData['visit_time']));
                    
                    if (isset($usageData['leave_time']) && $usageData['leave_time']) {
                        $websiteUsage->setLeaveTime(new \DateTime($usageData['leave_time']));
                    }
                    
                    $websiteUsage->setDuration($usageData['duration'] ?? null);
                    $websiteUsage->setCategory($usageData['category'] ?? null);
                    $websiteUsage->setProductivityLevel($usageData['productivity_level'] ?? null);
                    $websiteUsage->setBrowserName($usageData['browser_name'] ?? null);
                    $websiteUsage->setVisitCount($usageData['visit_count'] ?? 1);
                    $websiteUsage->setDeviceId($usageData['device_id'] ?? null);

                    // Handle metadata - convert JSON string to array if needed
                    $metadata = $usageData['metadata'] ?? null;
                    if (is_string($metadata)) {
                        $metadata = json_decode($metadata, true);
                    }
                    $websiteUsage->setMetadata($metadata);
                    $websiteUsage->setIsSynced(true);

                    $this->entityManager->persist($websiteUsage);
                    $syncedCount++;
                    
                } catch (\Exception $e) {
                    $errors[] = "Failed to sync website usage: " . $e->getMessage();
                }
            }

            $this->entityManager->flush();

            return new JsonResponse([
                'success' => true,
                'synced_count' => $syncedCount,
                'errors' => $errors,
                'message' => "Successfully synced {$syncedCount} website usage records"
            ]);

        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => 'Failed to sync website usage',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    #[Route('/status', name: 'status', methods: ['GET'])]
    public function getSyncStatus(Request $request): JsonResponse
    {
        try {
            $employeeId = $request->query->get('employee_id');

            if (!$employeeId) {
                return new JsonResponse([
                    'success' => false,
                    'error' => 'Employee ID is required'
                ], 400);
            }

            $employee = $this->employeeRepository->find($employeeId);
            if (!$employee) {
                return new JsonResponse([
                    'success' => false,
                    'error' => 'Employee not found'
                ], 404);
            }

            // Get sync counts for different data types
            $timeLogCount = $this->timeLogRepository->count(['employee' => $employee, 'isSynced' => true]);
            $screenshotCount = $this->screenshotLogRepository->count(['employee' => $employee, 'isSynced' => true]);
            $appUsageCount = $this->applicationUsageRepository->count(['employee' => $employee, 'isSynced' => true]);
            $websiteUsageCount = $this->websiteUsageRepository->count(['employee' => $employee, 'isSynced' => true]);

            // Get pending sync counts
            $pendingTimeLogCount = $this->timeLogRepository->count(['employee' => $employee, 'isSynced' => false]);
            $pendingScreenshotCount = $this->screenshotLogRepository->count(['employee' => $employee, 'isSynced' => false]);
            $pendingAppUsageCount = $this->applicationUsageRepository->count(['employee' => $employee, 'isSynced' => false]);
            $pendingWebsiteUsageCount = $this->websiteUsageRepository->count(['employee' => $employee, 'isSynced' => false]);

            $totalPending = $pendingTimeLogCount + $pendingScreenshotCount + $pendingAppUsageCount + $pendingWebsiteUsageCount;

            return new JsonResponse([
                'success' => true,
                'data' => [
                    'employee_id' => $employeeId,
                    'last_updated' => new \DateTime(),
                    'sync_required' => $totalPending > 0,
                    'synced_counts' => [
                        'time_logs' => $timeLogCount,
                        'screenshots' => $screenshotCount,
                        'application_usage' => $appUsageCount,
                        'website_usage' => $websiteUsageCount,
                        'total' => $timeLogCount + $screenshotCount + $appUsageCount + $websiteUsageCount
                    ],
                    'pending_counts' => [
                        'time_logs' => $pendingTimeLogCount,
                        'screenshots' => $pendingScreenshotCount,
                        'application_usage' => $pendingAppUsageCount,
                        'website_usage' => $pendingWebsiteUsageCount,
                        'total' => $totalPending
                    ],
                    'server_time' => new \DateTime()
                ]
            ]);
            
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => 'Failed to get sync status',
                'message' => $e->getMessage()
            ], 500);
        }
    }
}
