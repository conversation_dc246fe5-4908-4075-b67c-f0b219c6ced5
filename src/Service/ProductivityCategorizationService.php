<?php

namespace App\Service;

class ProductivityCategorizationService
{
    private array $productiveApps = [
        'code' => [
            'visual studio code', 'vscode', 'sublime text', 'atom', 'notepad++', 'vim', 'emacs',
            'intellij idea', 'phpstorm', 'webstorm', 'pycharm', 'android studio', 'xcode',
            'eclipse', 'netbeans', 'brackets', 'nano', 'gedit'
        ],
        'design' => [
            'photoshop', 'illustrator', 'indesign', 'figma', 'sketch', 'canva', 'gimp',
            'inkscape', 'blender', 'maya', '3ds max', 'after effects', 'premiere pro'
        ],
        'office' => [
            'microsoft word', 'excel', 'powerpoint', 'outlook', 'onenote', 'access',
            'google docs', 'google sheets', 'google slides', 'libreoffice', 'openoffice',
            'notion', 'obsidian', 'evernote', 'trello', 'asana', 'monday.com'
        ],
        'communication' => [
            'slack', 'microsoft teams', 'zoom', 'skype', 'discord', 'telegram',
            'whatsapp', 'signal', 'google meet', 'webex', 'gotomeeting'
        ],
        'browser_work' => [
            'chrome', 'firefox', 'safari', 'edge', 'opera', 'brave'
        ],
        'database' => [
            'mysql workbench', 'phpmyadmin', 'pgadmin', 'mongodb compass', 'dbeaver',
            'navicat', 'sequel pro', 'tableplus', 'datagrip'
        ],
        'terminal' => [
            'terminal', 'cmd', 'powershell', 'git bash', 'putty', 'iterm', 'hyper',
            'windows terminal', 'konsole', 'gnome terminal'
        ]
    ];

    private array $neutralApps = [
        'system' => [
            'explorer', 'finder', 'file manager', 'task manager', 'activity monitor',
            'system preferences', 'control panel', 'settings', 'calculator'
        ],
        'utilities' => [
            'notepad', 'textedit', 'calendar', 'clock', 'timer', 'screenshot',
            'snipping tool', 'archive utility', 'winrar', '7zip'
        ]
    ];

    private array $distractingApps = [
        'social' => [
            'facebook', 'twitter', 'instagram', 'tiktok', 'snapchat', 'linkedin',
            'reddit', 'pinterest', 'tumblr', 'youtube'
        ],
        'entertainment' => [
            'netflix', 'spotify', 'apple music', 'amazon prime', 'hulu', 'disney+',
            'twitch', 'steam', 'epic games', 'origin', 'uplay', 'battle.net'
        ],
        'games' => [
            'minecraft', 'fortnite', 'league of legends', 'dota', 'counter-strike',
            'valorant', 'overwatch', 'world of warcraft', 'among us', 'fall guys'
        ]
    ];

    private array $productiveWebsites = [
        'work' => [
            'github.com', 'gitlab.com', 'bitbucket.org', 'stackoverflow.com', 'developer.mozilla.org',
            'w3schools.com', 'codecademy.com', 'freecodecamp.org', 'coursera.org', 'udemy.com',
            'pluralsight.com', 'lynda.com', 'edx.org', 'khanacademy.org'
        ],
        'documentation' => [
            'docs.microsoft.com', 'developer.apple.com', 'docs.google.com', 'aws.amazon.com',
            'cloud.google.com', 'azure.microsoft.com', 'firebase.google.com', 'heroku.com'
        ],
        'productivity' => [
            'trello.com', 'asana.com', 'monday.com', 'notion.so', 'evernote.com',
            'todoist.com', 'any.do', 'wunderlist.com', 'basecamp.com', 'slack.com'
        ],
        'email' => [
            'gmail.com', 'outlook.com', 'yahoo.com', 'protonmail.com', 'mail.google.com'
        ]
    ];

    private array $neutralWebsites = [
        'news' => [
            'bbc.com', 'cnn.com', 'reuters.com', 'ap.org', 'npr.org', 'bloomberg.com',
            'techcrunch.com', 'arstechnica.com', 'theverge.com', 'wired.com'
        ],
        'reference' => [
            'wikipedia.org', 'dictionary.com', 'merriam-webster.com', 'translate.google.com',
            'wolframalpha.com', 'google.com', 'bing.com', 'duckduckgo.com'
        ],
        'shopping' => [
            'amazon.com', 'ebay.com', 'walmart.com', 'target.com', 'bestbuy.com',
            'newegg.com', 'alibaba.com', 'etsy.com', 'shopify.com'
        ]
    ];

    private array $distractingWebsites = [
        'social' => [
            'facebook.com', 'twitter.com', 'instagram.com', 'tiktok.com', 'snapchat.com',
            'linkedin.com', 'reddit.com', 'pinterest.com', 'tumblr.com', 'discord.com'
        ],
        'entertainment' => [
            'youtube.com', 'netflix.com', 'hulu.com', 'disney.com', 'amazon.com/prime',
            'spotify.com', 'apple.com/music', 'soundcloud.com', 'twitch.tv', 'mixer.com'
        ],
        'gaming' => [
            'steam.com', 'epicgames.com', 'origin.com', 'uplay.com', 'battle.net',
            'roblox.com', 'minecraft.net', 'leagueoflegends.com', 'dota2.com'
        ]
    ];

    /**
     * Categorize an application based on its name
     */
    public function categorizeApplication(string $applicationName): array
    {
        $appName = strtolower(trim($applicationName));
        
        // Check productive apps
        foreach ($this->productiveApps as $category => $apps) {
            foreach ($apps as $app) {
                if (str_contains($appName, $app)) {
                    return [
                        'category' => $category,
                        'productivity_level' => 'productive'
                    ];
                }
            }
        }

        // Check neutral apps
        foreach ($this->neutralApps as $category => $apps) {
            foreach ($apps as $app) {
                if (str_contains($appName, $app)) {
                    return [
                        'category' => $category,
                        'productivity_level' => 'neutral'
                    ];
                }
            }
        }

        // Check distracting apps
        foreach ($this->distractingApps as $category => $apps) {
            foreach ($apps as $app) {
                if (str_contains($appName, $app)) {
                    return [
                        'category' => $category,
                        'productivity_level' => 'distracting'
                    ];
                }
            }
        }

        // Default to neutral if not found
        return [
            'category' => 'unknown',
            'productivity_level' => 'neutral'
        ];
    }

    /**
     * Categorize a website based on its domain
     */
    public function categorizeWebsite(string $domain): array
    {
        $domain = strtolower(trim($domain));
        
        // Remove www. prefix if present
        $domain = preg_replace('/^www\./', '', $domain);

        // Check productive websites
        foreach ($this->productiveWebsites as $category => $sites) {
            foreach ($sites as $site) {
                if (str_contains($domain, $site) || str_contains($site, $domain)) {
                    return [
                        'category' => $category,
                        'productivity_level' => 'productive'
                    ];
                }
            }
        }

        // Check neutral websites
        foreach ($this->neutralWebsites as $category => $sites) {
            foreach ($sites as $site) {
                if (str_contains($domain, $site) || str_contains($site, $domain)) {
                    return [
                        'category' => $category,
                        'productivity_level' => 'neutral'
                    ];
                }
            }
        }

        // Check distracting websites
        foreach ($this->distractingWebsites as $category => $sites) {
            foreach ($sites as $site) {
                if (str_contains($domain, $site) || str_contains($site, $domain)) {
                    return [
                        'category' => $category,
                        'productivity_level' => 'distracting'
                    ];
                }
            }
        }

        // Default to neutral if not found
        return [
            'category' => 'unknown',
            'productivity_level' => 'neutral'
        ];
    }

    /**
     * Get productivity statistics for a set of applications
     */
    public function getApplicationProductivityStats(array $applications): array
    {
        $stats = [
            'productive' => ['count' => 0, 'duration' => 0],
            'neutral' => ['count' => 0, 'duration' => 0],
            'distracting' => ['count' => 0, 'duration' => 0]
        ];

        foreach ($applications as $app) {
            $category = $this->categorizeApplication($app['name']);
            $level = $category['productivity_level'];
            
            $stats[$level]['count']++;
            $stats[$level]['duration'] += $app['duration'] ?? 0;
        }

        return $stats;
    }

    /**
     * Get productivity statistics for a set of websites
     */
    public function getWebsiteProductivityStats(array $websites): array
    {
        $stats = [
            'productive' => ['count' => 0, 'duration' => 0],
            'neutral' => ['count' => 0, 'duration' => 0],
            'distracting' => ['count' => 0, 'duration' => 0]
        ];

        foreach ($websites as $website) {
            $category = $this->categorizeWebsite($website['domain']);
            $level = $category['productivity_level'];
            
            $stats[$level]['count']++;
            $stats[$level]['duration'] += $website['duration'] ?? 0;
        }

        return $stats;
    }

    /**
     * Calculate overall productivity score (0-100)
     */
    public function calculateProductivityScore(array $appStats, array $websiteStats): int
    {
        $totalDuration = 0;
        $productiveDuration = 0;
        $distractingDuration = 0;

        // Calculate from applications
        foreach ($appStats as $level => $data) {
            $totalDuration += $data['duration'];
            if ($level === 'productive') {
                $productiveDuration += $data['duration'];
            } elseif ($level === 'distracting') {
                $distractingDuration += $data['duration'];
            }
        }

        // Calculate from websites
        foreach ($websiteStats as $level => $data) {
            $totalDuration += $data['duration'];
            if ($level === 'productive') {
                $productiveDuration += $data['duration'];
            } elseif ($level === 'distracting') {
                $distractingDuration += $data['duration'];
            }
        }

        if ($totalDuration === 0) {
            return 50; // Neutral score if no data
        }

        // Calculate score: productive time adds points, distracting time subtracts
        $productiveRatio = $productiveDuration / $totalDuration;
        $distractingRatio = $distractingDuration / $totalDuration;
        
        $score = 50 + ($productiveRatio * 50) - ($distractingRatio * 30);
        
        return max(0, min(100, (int) round($score)));
    }
}
