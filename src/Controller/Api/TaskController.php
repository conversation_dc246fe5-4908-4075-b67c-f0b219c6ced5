<?php

namespace App\Controller\Api;

use App\Entity\Task;
use App\Repository\TaskRepository;
use App\Repository\ProjectRepository;
use App\Repository\MasterEmployeeRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

#[Route('/api/tasks', name: 'api_tasks_')]
class TaskController extends AbstractController
{
    public function __construct(
        private TaskRepository $taskRepository,
        private ProjectRepository $projectRepository,
        private MasterEmployeeRepository $employeeRepository,
        private EntityManagerInterface $entityManager
    ) {}

    #[Route('', name: 'list', methods: ['GET'])]
    public function getTasks(Request $request): JsonResponse
    {
        try {
            $employeeId = $request->query->get('employee_id');
            $projectId = $request->query->get('project_id');
            $status = $request->query->get('status');
            
            if (!$employeeId) {
                return new JsonResponse([
                    'success' => false,
                    'error' => 'Employee ID is required'
                ], 400);
            }

            $employee = $this->employeeRepository->find($employeeId);
            if (!$employee) {
                return new JsonResponse([
                    'success' => false,
                    'error' => 'Employee not found'
                ], 404);
            }

            $qb = $this->entityManager->createQueryBuilder();
            $qb->select('t')
                ->from(Task::class, 't')
                ->where('t.assignedTo = :employee')
                ->setParameter('employee', $employee)
                ->orderBy('t.createdAt', 'DESC');

            if ($projectId) {
                $project = $this->projectRepository->find($projectId);
                if ($project) {
                    $qb->andWhere('t.project = :project')
                       ->setParameter('project', $project);
                }
            }

            if ($status) {
                $qb->andWhere('t.status = :status')
                   ->setParameter('status', $status);
            }

            $tasks = $qb->getQuery()->getResult();

            $taskData = [];
            foreach ($tasks as $task) {
                $taskData[] = [
                    'id' => $task->getId(),
                    'title' => $task->getTitle(),
                    'description' => $task->getDescription(),
                    'status' => $task->getStatus(),
                    'priority' => $task->getPriority(),
                    'estimated_hours' => $task->getEstimatedHours(),
                    'actual_hours' => $task->getActualHours(),
                    'assigned_at' => $task->getAssignedAt()?->format('Y-m-d H:i:s'),
                    'created_at' => $task->getCreatedAt()?->format('Y-m-d H:i:s'),
                    'updated_at' => $task->getUpdatedAt()?->format('Y-m-d H:i:s'),
                    'project' => [
                        'id' => $task->getProject()?->getId(),
                        'name' => $task->getProject()?->getName(),
                        'project_code' => $task->getProject()?->getProjectCode()
                    ],
                    'assigned_by' => [
                        'id' => $task->getAssignedBy()?->getId(),
                        'name' => $task->getAssignedBy() ? 
                            $task->getAssignedBy()->getFirstName() . ' ' . $task->getAssignedBy()->getLastName() : null
                    ]
                ];
            }

            return new JsonResponse([
                'success' => true,
                'data' => $taskData,
                'count' => count($taskData)
            ]);

        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => 'Failed to fetch tasks',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    #[Route('/{id}', name: 'get', methods: ['GET'])]
    public function getTask(int $id, Request $request): JsonResponse
    {
        try {
            $employeeId = $request->query->get('employee_id');
            
            if (!$employeeId) {
                return new JsonResponse([
                    'success' => false,
                    'error' => 'Employee ID is required'
                ], 400);
            }

            $employee = $this->employeeRepository->find($employeeId);
            if (!$employee) {
                return new JsonResponse([
                    'success' => false,
                    'error' => 'Employee not found'
                ], 404);
            }

            $task = $this->taskRepository->find($id);
            if (!$task) {
                return new JsonResponse([
                    'success' => false,
                    'error' => 'Task not found'
                ], 404);
            }

            // Check if employee has access to this task
            if ($task->getAssignedTo()?->getId() !== $employee->getId()) {
                return new JsonResponse([
                    'success' => false,
                    'error' => 'Access denied to this task'
                ], 403);
            }

            $taskData = [
                'id' => $task->getId(),
                'title' => $task->getTitle(),
                'description' => $task->getDescription(),
                'status' => $task->getStatus(),
                'priority' => $task->getPriority(),
                'estimated_hours' => $task->getEstimatedHours(),
                'actual_hours' => $task->getActualHours(),
                'assigned_at' => $task->getAssignedAt()?->format('Y-m-d H:i:s'),
                'created_at' => $task->getCreatedAt()?->format('Y-m-d H:i:s'),
                'updated_at' => $task->getUpdatedAt()?->format('Y-m-d H:i:s'),
                'project' => [
                    'id' => $task->getProject()?->getId(),
                    'name' => $task->getProject()?->getName(),
                    'project_code' => $task->getProject()?->getProjectCode(),
                    'description' => $task->getProject()?->getDescription()
                ],
                'assigned_by' => [
                    'id' => $task->getAssignedBy()?->getId(),
                    'name' => $task->getAssignedBy() ? 
                        $task->getAssignedBy()->getFirstName() . ' ' . $task->getAssignedBy()->getLastName() : null
                ],
                'assigned_by_admin' => [
                    'id' => $task->getAssignedByAdmin()?->getId(),
                    'name' => $task->getAssignedByAdmin()?->getUsername()
                ]
            ];

            return new JsonResponse([
                'success' => true,
                'data' => $taskData
            ]);

        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => 'Failed to fetch task',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    #[Route('/by-project/{projectId}', name: 'by_project', methods: ['GET'])]
    public function getTasksByProject(int $projectId, Request $request): JsonResponse
    {
        try {
            $employeeId = $request->query->get('employee_id');
            
            if (!$employeeId) {
                return new JsonResponse([
                    'success' => false,
                    'error' => 'Employee ID is required'
                ], 400);
            }

            $employee = $this->employeeRepository->find($employeeId);
            if (!$employee) {
                return new JsonResponse([
                    'success' => false,
                    'error' => 'Employee not found'
                ], 404);
            }

            $project = $this->projectRepository->find($projectId);
            if (!$project) {
                return new JsonResponse([
                    'success' => false,
                    'error' => 'Project not found'
                ], 404);
            }

            $tasks = $this->taskRepository->findBy([
                'project' => $project,
                'assignedTo' => $employee
            ], ['createdAt' => 'DESC']);

            $taskData = [];
            foreach ($tasks as $task) {
                $taskData[] = [
                    'id' => $task->getId(),
                    'title' => $task->getTitle(),
                    'description' => $task->getDescription(),
                    'status' => $task->getStatus(),
                    'priority' => $task->getPriority(),
                    'estimated_hours' => $task->getEstimatedHours(),
                    'actual_hours' => $task->getActualHours(),
                    'assigned_at' => $task->getAssignedAt()?->format('Y-m-d H:i:s'),
                    'created_at' => $task->getCreatedAt()?->format('Y-m-d H:i:s'),
                    'updated_at' => $task->getUpdatedAt()?->format('Y-m-d H:i:s')
                ];
            }

            return new JsonResponse([
                'success' => true,
                'data' => $taskData,
                'count' => count($taskData),
                'project' => [
                    'id' => $project->getId(),
                    'name' => $project->getName(),
                    'project_code' => $project->getProjectCode()
                ]
            ]);

        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => 'Failed to fetch tasks for project',
                'message' => $e->getMessage()
            ], 500);
        }
    }
}
