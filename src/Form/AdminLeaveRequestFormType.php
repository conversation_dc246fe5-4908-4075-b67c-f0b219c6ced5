<?php
namespace App\Form;

use App\Entity\LeaveBalance;
use App\Entity\LeaveRequest;
use App\Entity\MasterEmployee;
use App\Repository\MasterEmployeeRepository;
use Doctrine\ORM\EntityRepository;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\CallbackTransformer;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormEvent;
use Symfony\Component\Form\FormEvents;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\Length;

class AdminLeaveRequestFormType extends AbstractType
{
    public function __construct(
        private readonly MasterEmployeeRepository $masterEmployeeRepository
    )
    {
    }

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('employee', EntityType::class, [
                'class' => MasterEmployee::class,
                'autocomplete'=>true,
                'choices' => $this->masterEmployeeRepository->findBy(['isDelete' => false]),
                'choice_label' => function (MasterEmployee $employee) {
                    return $employee->getName() . ' (' . $employee->getEmail() . ')';
                },
                'placeholder' => 'Select an employee',
                'required' => true,
                'attr' => [
                    'class' => 'form-control',
                ],
            ])
            ->add('startDate', TextType::class, [
                'required' => true,
                'label' => 'Start Date',
                'attr' => [
                    'class' => 'form-control datepicker',
                    'placeholder' => 'Select date',
                    'required' => 'required',
                ],
            ])
            ->add('startHalfDay', ChoiceType::class, [
                'choices' => [
                    'Full Day' => null,
                    'First Half' => LeaveRequest::HALF_DAY_MORNING,
                    'Second Half' => LeaveRequest::HALF_DAY_AFTERNOON,
                ],
                'required' => true,
                'autocomplete'=>true,
            ])
            ->add('endDate', TextType::class, [
                'required' => true,
                'label' => 'End Date',
                'attr' => [
                    'class' => 'form-control datepicker',
                    'placeholder' => 'Select date',
                    'required' => 'required',
                ],
            ])
            ->add('endHalfDay', ChoiceType::class, [
                'choices' => [
                    'Full Day' => null,
                    'First Half' => LeaveRequest::HALF_DAY_MORNING,
                    'Second Half' => LeaveRequest::HALF_DAY_AFTERNOON,
                ],
                'required' => true,
                'autocomplete'=>true,
            ])
            ->add('reason', TextareaType::class, [
                'required' => true,
                'attr' => ['class' => 'form-control', 'rows' => 1, 'maxlength' => 255, 'data-validation' => 'max:255'],
                'constraints' => [
                    new Length(['max' => 255, 'maxMessage' => 'Reason cannot be longer than {{ limit }} characters']),
                ],
            ]);

        $builder->add('leaveType', EntityType::class, [
            'class' => LeaveBalance::class,
            'choice_label' => function (LeaveBalance $leaveBalance) {
                $remainingDays = $leaveBalance->getRemainingDays();
                $remainingDaysText = '';
                if ($remainingDays < 100) {
                    $remainingDaysText = '('.number_format($leaveBalance->getRemainingDays(), 1).' days remaining)';
                }
                return sprintf(
                    '%s %s',
                    $leaveBalance->getLeaveType()->getName(),
                    $remainingDaysText
                );
            },
            'placeholder' => 'Select employee first',
            'required' => true,
            'attr' => ['class' => 'form-control'],
            'choices' => [],
            'query_builder' => function (EntityRepository $er) {
                return $er->createQueryBuilder('lb')
                    ->where('lb.id = 0');
            }
        ]);

        $dateTransformer = new CallbackTransformer(
            function ($dateAsObject) {
                return $dateAsObject instanceof \DateTimeInterface ? $dateAsObject->format('d-m-Y') : '';
            },
            function ($dateAsString) {
                return $dateAsString ? \DateTime::createFromFormat('d-m-Y', $dateAsString) : null;
            }
        );

        $builder->get('startDate')->addModelTransformer($dateTransformer);
        $builder->get('endDate')->addModelTransformer($dateTransformer);

        $builder->addEventListener(FormEvents::PRE_SUBMIT, function (FormEvent $event) {
            $form = $event->getForm();
            $data = $event->getData();
            if (!empty($data['employee'])) {
                $employeeId = $data['employee'];
                $form->add('leaveType', EntityType::class, [
                    'class' => LeaveBalance::class,
                    'placeholder' => 'Select Leave Type',
                    'choice_label' => function (LeaveBalance $leaveBalance) {
                        $remainingDays = $leaveBalance->getRemainingDays();
                        $remainingDaysText = '';
                        if ($remainingDays < 100) {
                            $remainingDaysText = '('.number_format($leaveBalance->getRemainingDays(), 1).' days remaining)';
                        }
                        return sprintf(
                            '%s %s',
                            $leaveBalance->getLeaveType()->getName(),
                            $remainingDaysText
                        );
                    },
                    'query_builder' => function (EntityRepository $er) use ($employeeId) {
                        return $er->createQueryBuilder('lb')
                            ->where('lb.employee = :employeeId')
                            ->setParameter('employeeId', $employeeId);
                    },
                    'required' => true,
                    'attr' => ['class' => 'form-control'],
                ]);
            }
        });
    }
    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => LeaveRequest::class,
        ]);
    }
}