const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipc<PERSON>enderer without exposing the entire object
contextBridge.exposeInMainWorld('idleWarningAPI', {
    // Listen for popup initialization
    onInitPopup: (callback) => ipcRenderer.on('init-popup', callback),
    
    // Listen for countdown updates
    onUpdateCountdown: (callback) => ipcRenderer.on('update-countdown', callback),

    // Listen for "On Break" status
    onShowOnBreakStatus: (callback) => ipcRenderer.on('show-on-break-status', callback),
    
    // User clicked "I'm still here" button
    resumeActivity: () => ipcRenderer.invoke('idle-warning-resume'),
    
    // User clicked "Continue idle" or timeout reached
    continueIdle: () => ipcRenderer.invoke('idle-warning-continue-idle'),

    // Activity detected in popup
    activityDetected: () => ipcRenderer.invoke('idle-warning-activity-detected'),

    // Remove listeners
    removeAllListeners: (channel) => ipcRenderer.removeAllListeners(channel)
});
