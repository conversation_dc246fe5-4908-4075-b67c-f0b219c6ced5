<?php

namespace App\MessageHandler;

use App\Entity\FormSubmission;
use App\Message\FeedbackNotificationMessage;
use App\Repository\MasterEmployeeRepository;
use App\Service\Common\EmailService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Mailer\Exception\TransportExceptionInterface;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use App\Traits\ExceptionLoggerTrait;

#[AsMessageHandler]
class FeedbackNotificationHandler
{
    use ExceptionLoggerTrait;

    public function __construct(
        private readonly MasterEmployeeRepository $employeeRepository,
        private readonly ParameterBagInterface   $parameterBag,
        private readonly EntityManagerInterface  $entityManager,
        private readonly EmailService $emailService,
    ) {
        $this->initializeLogger($parameterBag, 'email_notifications_handler');
    }

    /**
     * @throws TransportExceptionInterface
     */
    public function __invoke(FeedbackNotificationMessage $message): void
    {
        $employee = $this->employeeRepository->find($message->getEmployeeId());
        if (!$employee) {
            $this->log('Employee not found', ['employee_id' => $message->getEmployeeId()]);
            return;
        }

        $latestSubmission = $this->entityManager->getRepository(FormSubmission::class)
            ->findOneBy(['employee' => $employee], ['submissionDate' => 'DESC']);

        $rating = $latestSubmission ? $latestSubmission->getRating() : 'No rating available';
        $submissionDate = $latestSubmission ? $latestSubmission->getSubmissionDate()->format('d-M-Y') : 'N/A';

        $placeholders = [
            '{{employee_name}}' => $employee->getName(),
            '{{rating}}' => $rating,
            '{{submission_date}}' => $submissionDate,
            '{{fullRating_url}}' => rtrim($this->parameterBag->get('app_base_url'), '/'),
        ];

        $emailContent = $this->emailService->generateEmailContent('employee_feedback_notification', $placeholders);
        $this->emailService->sendEmail($employee->getEmail(), 'Performance Review Submission Notification', $emailContent);
    }
}
