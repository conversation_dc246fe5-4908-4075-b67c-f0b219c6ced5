{% extends 'admin/dashboard/base.html.twig' %}

{% block title %}Add Salary Structure{% endblock %}

{% block body %}
    {% include 'admin/partials/breadcrumbs.html.twig' with {
        breadcrumbs: [
            { 'label': 'Dashboard', 'url': url('admin_dashboard') },
            { 'label': 'Employee', 'url': url('master_employee') },
            { 'label': 'Salary Structure', 'url': url('admin_employee_salary', {'employeeId': employee.id}) }
        ],
        active: mode == 'edit' ? 'Edit Salary Structure' : 'Add Salary Structure'
    } %}

    <div class="content">
        <div class="animated fadeIn">
            <div class="row">
                <div class="col-lg-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <strong class="card-title"><i class="fa fa-user mr-2"></i>Employee Information - {{ employee.name }}</strong>
                            <div class="d-flex align-items-center">
                                <a href="{{ path('admin_employee_salary', {'employeeId': employee.id}) }}" class="btn btn-sm btn-outline-secondary mr-2">
                                    <i class="fa fa-list mr-1"></i>Back to List
                                </a>
                            </div>
                        </div>
                        <div class="card-body">
                            {{ form_start(form, { 'attr': { 'class': 'needs-validation use-loader', 'novalidate': 'novalidate' } }) }}
                            <div class="form-section">
                                <h5 class="section-header">
                                    <i class="fa fa-calendar mr-2"></i>Salary Period
                                </h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            {{ form_label(form.startDate, 'Start Date', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text"><i class="fa fa-calendar"></i></span>
                                                </div>
                                                {{ form_widget(form.startDate, {
                                                    'attr': {
                                                        'class': 'form-control datepicker',
                                                        'data-initial-date': salary_structure_form.startDate is defined and salary_structure_form.startDate ? salary_structure_form.startDate|date('d-m-Y') : ''
                                                    }
                                                }) }}
                                            </div>
                                            {{ form_errors(form.startDate) }}
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            {{ form_label(form.endDate, 'End Date', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text"><i class="fa fa-calendar"></i></span>
                                                </div>
                                                {{ form_widget(form.endDate, {
                                                    'attr': {
                                                        'class': 'form-control datepicker',
                                                        'data-initial-date': salary_structure_form.endDate is defined and salary_structure_form.endDate ? salary_structure_form.endDate|date('d-m-Y') : ''
                                                    }
                                                }) }}
                                            </div>
                                            {{ form_errors(form.endDate) }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="form-section">
                                <h5 class="section-header">
                                    <i class="fa fa-money mr-2"></i>Salary Details
                                </h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            {{ form_label(form.ctc, 'CTC (Cost to Company)', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text"><i class="fa fa-inr"></i></span>
                                                </div>
                                                {{ form_widget(form.ctc, { 'attr': { 'class': 'form-control monthly-input', 'placeholder': 'Enter CTC', 'data-annual-target': 'annual-ctc' } }) }}
                                            </div>
                                            {{ form_errors(form.ctc) }}
                                            <small class="form-text text-muted">Total annual cost to company</small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label control-label mb-1">Annual CTC</label>
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text"><i class="fa fa-inr"></i></span>
                                                </div>
                                                <input type="text" class="form-control" id="annual-ctc" readonly>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            {{ form_label(form.deductESIC, 'Deduct ESIC', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                            <span class="input-group-text">
                                                                {{ form_widget(form.deductESIC, { 'attr': { 'class': 'custom-checkbox', 'aria-label': 'Checkbox for Is Required' } }) }}
                                                            </span>
                                                </div>
                                                <div class="form-control checkbox-label">
                                                    {{ form_label(form.deductESIC, null, { 'label_attr': { 'class': 'ml-2' } }) }}
                                                </div>
                                            </div>
                                            {{ form_errors(form.deductESIC) }}
                                            <small class="form-text text-muted">ESIC : IF salary is less than 21000/- compulsory deduction</small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            {{ form_label(form.deductPF, 'Deduct PF', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                            <span class="input-group-text">
                                                                {{ form_widget(form.deductPF, { 'attr': { 'class': 'custom-checkbox', 'aria-label': 'Checkbox for Is Visible' } }) }}
                                                            </span>
                                                </div>
                                                <div class="form-control checkbox-label">
                                                    {{ form_label(form.deductPF, null, { 'label_attr': { 'class': 'ml-2' } }) }}
                                                </div>
                                            </div>
                                            {{ form_errors(form.deductPF) }}
                                            <small class="form-text text-muted">
                                                Employees earning more than ₹15,000/month can opt out only if they never had a UAN or their Aadhaar is not linked to UAN.
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="d-flex justify-content-end mt-4">
                                <a href="{{ path('admin_employee_salary', {'employeeId': employee.id}) }}" class="btn btn-secondary mr-2">Cancel</a>
                                <button type="submit" class="btn btn-primary pl-3 pr-3">{{ mode == 'edit' ? 'Update Salary Structure' : 'Create Salary Structure' }}</button>
                            </div>
                            {{ form_end(form) }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="clearfix"></div>
    <link rel="stylesheet" href="{{ asset('assets/css/Error/error.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/AdminForm/Form.css') }}">
    <script src="{{ asset('assets/js/datatables.min.js') }}"></script>
    <script src="{{ asset('bundles/datatables/js/datatables.js') }}"></script>
    <script src="{{ asset('assets/js/moment.min.js') }}"></script>
    <script src="{{ asset('assets/js/daterangepicker.min.js') }}"></script>
    <script src="{{ asset('assets/js/DatepickerFieldDisable.js') }}"></script>
    <script src="{{ asset('assets/js/admin/salaryStructure.js') }}"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            document.querySelectorAll('.monthly-input').forEach(function (input) {
                input.addEventListener('input', function () {
                    var val = parseFloat(this.value) || 0;
                    var targetId = this.getAttribute('data-annual-target');
                    if (targetId) {
                        document.getElementById(targetId).value = (val * 12).toFixed(2);
                    }
                });
                input.dispatchEvent(new Event('input'));
            });
        });

    </script>
{% endblock %}