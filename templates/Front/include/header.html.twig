<!-- Right Panel -->
{% block body %}
    <div id="right-panel" class="right-panel">
        <!-- Header-->
        <header id="header" class="header">
            <div class="top-left">
                <div class="navbar-header">
                    <a class="navbar-brand" href="{{ url('dashboard_employee') }}"><img src="{{ asset('images/GetMedia.png') }}" alt="Logo"></a>
                    <a id="menuToggle" class="menutoggle"><i class="fa fa-bars"></i></a>
                </div>
            </div>
            <div class="top-right">
                <div class="header-menu">
                    <div class="header-left"></div>
                    <div class="user-area dropdown float-right">
                        <a href="#" class="dropdown-toggle active" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            {% if profilePicture %}
                                <img class="user-avatar rounded-circle profile-avatar-2" style="width:40px; object-fit:cover; height:40px; border: 2px solid #fff;box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);border-radius: 50%;"
                                     src="{{ asset(profilePicture.filePath) }}"
                                     alt="User Avatar">
                            {% else %}
                                <img class="user-avatar rounded-circle"
                                     src="{{ asset('images/admin.jpg') }}"
                                     alt="Default Avatar">
                            {% endif %}
                        </a>

                        <div class="user-menu dropdown-menu">
                            <a class="nav-link" href="{{ url('employee_profile') }}"><i class="fa fa- user"></i>My Profile</a>

                            <a class="nav-link" href="{{ url('employee_update_password') }}"><i class="fa fa -cog"></i>Change Password</a>

                            <a class="nav-link" href="{{ url('app_logout_employee') }}"><i class="fa fa-power -off"></i>Logout</a>
                        </div>
                    </div>

                </div>
            </div>
        </header>
        <!-- /#header -->
    </div>
{% endblock %}