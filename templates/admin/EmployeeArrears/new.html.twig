{% extends 'admin/dashboard/base.html.twig' %}

{% block title %}Add Employee Arrears{% endblock %}

{% block body %}
    {% include 'admin/partials/breadcrumbs.html.twig' with {
        breadcrumbs: [
            { 'label': 'Dashboard', 'url': url('admin_dashboard') },
            { 'label': 'Employee Arrears', 'url': url('admin_employee_arrears') }
        ],
        active: mode == 'edit' ? 'Edit Employee Arrears' : 'Add Employee Arrears'
    } %}

    <div class="content">
        <div class="animated fadeIn">
            <div class="row">
                <div class="col-lg-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <strong class="card-title">
                                {{ mode == 'edit' ? 'Edit Employee Arrears' : 'Add Employee Arrears' }}
                            </strong>
                            <div class="d-flex align-items-center">
                                <a href="{{ path('admin_employee_arrears') }}" class="btn btn-sm btn-outline-secondary mr-2">
                                    <i class="fa fa-list mr-1"></i>Back to List
                                </a>
                            </div>
                        </div>
                        <div class="card-body">
                            {{ form_start(form, { 'attr': { 'class': 'needs-validation', 'novalidate': 'novalidate','data-turbo': 'false' } }) }}
                            <div class="form-section">
                                <h5 class="section-header">
                                    <i class="fa fa-calendar mr-2"></i>Employee Information
                                </h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            {{ form_label(form.employee, 'Employee', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text"><i class="fa fa-user"></i></span>
                                                </div>
                                                {{ form_widget(form.employee, { 'attr': { 'class': 'form-control' } }) }}
                                            </div>
                                            {{ form_errors(form.employee) }}
                                            <small class="form-text text-muted">Select the employee for whom you are adding arrears</small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            {{ form_label(form.totalArrears, 'Total Arrears', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text"><i class="fa fa-rupee"></i></span>
                                                </div>
                                                {{ form_widget(form.totalArrears, { 'attr': { 'class': 'form-control', 'placeholder': 'Total arrears', 'readonly': 'readonly' } }) }}
                                            </div>
                                            {{ form_errors(form.totalArrears) }}
                                            <small class="form-text text-muted">Total arrears amount for the employee</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="form-section">
                                <h5 class="section-header">
                                    <i class="fa fa-calendar mr-2"></i>Arrears Details
                                </h5>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            {{ form_label(form.month, 'Month', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text"><i class="fa fa-calendar"></i></span>
                                                </div>
                                                {{ form_widget(form.month, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter month (1-12)' } }) }}
                                            </div>
                                            {{ form_errors(form.month) }}
                                            <small class="form-text text-muted">Enter the month (1-12)</small>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            {{ form_label(form.year, 'Year', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text"><i class="fa fa-calendar"></i></span>
                                                </div>
                                                {{ form_widget(form.year, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter year' } }) }}
                                            </div>
                                            {{ form_errors(form.year) }}
                                            <small class="form-text text-muted">Enter the year (e.g., 2025)</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            {{ form_label(form.oldSalary, 'Previous Salary', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text"><i class="fa fa-money"></i></span>
                                                </div>
                                                {{ form_widget(form.oldSalary, { 'attr': { 'class': 'form-control', 'placeholder': 'Previous salary' } }) }}
                                            </div>
                                            {{ form_errors(form.oldSalary) }}
                                            <small class="form-text text-muted">Previous monthly salary</small>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            {{ form_label(form.newSalary, 'New Salary', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text"><i class="fa fa-money"></i></span>
                                                </div>
                                                {{ form_widget(form.newSalary, { 'attr': { 'class': 'form-control', 'placeholder': 'New salary' } }) }}
                                            </div>
                                            {{ form_errors(form.newSalary) }}
                                            <small class="form-text text-muted">New monthly salary</small>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            {{ form_label(form.arrearsDays, 'Arrears Days', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text"><i class="fa fa-calendar-o"></i></span>
                                                </div>
                                                {{ form_widget(form.arrearsDays, { 'attr': { 'class': 'form-control', 'placeholder': 'Number of arrears days' } }) }}
                                            </div>
                                            {{ form_errors(form.arrearsDays) }}
                                            <small class="form-text text-muted">Number of working days after salary change (excluding leave)</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="d-flex justify-content-end mt-4">
                                <a href="{{ path('admin_employee_arrears') }}" class="btn btn-secondary mr-2">Cancel</a>
                                <button type="submit" class="btn btn-primary pl-3 pr-3">{{ mode == 'edit' ? 'Update Arrears' : 'Create Arrears' }}</button>
                            </div>
                            {{ form_end(form) }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="clearfix"></div>
    <link rel="stylesheet" href="{{ asset('assets/css/Error/error.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/AdminForm/Form.css') }}">
    <script src="{{ asset('assets/js/datatables.min.js') }}"></script>
    <script src="{{ asset('bundles/datatables/js/datatables.js') }}"></script>
    <script src="{{ asset('assets/js/admin/common.js') }}"></script>
    <script src="{{ asset('assets/js/admin/employee_arrears.js') }}"></script>
{% endblock %} 