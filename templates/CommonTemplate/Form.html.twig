<form method="POST" id="feedbackForm">
    <div class="section-card">
        <div class="section-header">
            <div class="section-icon">
                <i class="fa fa-user"></i>
            </div>
            <span>Employee Selection</span>
        </div>
        <select name="employee_id" id="employee" class="employee-select" required>
            <option value="">-- Select Employee --</option>
            {% for employee in employees %}
                <option value="{{ employee.id }}">{{ employee.name }}</option>
            {% endfor %}
        </select>
    </div>
    <div id="employee-activity-container"
         data-employee-activity="{{ employeeActivityData|json_encode|escape('html_attr') }}">
    <div id="employee-activity-display" class="section-card" style="display: none;">
        <div class="section-header">
            <div class="section-icon">
                <i class="fa fa-line-chart"></i>
            </div>
            <span>Employee Activity Metrics</span>
        </div>
        <div class="activity-metrics">
            <div class="row">
                <div class="col-md-6">
                    <div class="metric-card">
                        <h4>Active Minutes</h4>
                        <div class="progress mb-2">
                            <div id="active-minutes-bar" class="progress-bar bg-success" role="progressbar" style="width: 0%"></div>
                        </div>
                        <p id="active-minutes-text">0%</p>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="metric-card">
                        <h4>Active Seconds</h4>
                        <div class="progress mb-2">
                            <div id="active-seconds-bar" class="progress-bar bg-info" role="progressbar" style="width: 0%"></div>
                        </div>
                        <p id="active-seconds-text">0%</p>
                    </div>
                </div>
            </div>
            <div class="activity-summary mt-3">
                <p id="activity-summary-text" class="text-muted">Select an employee to view their activity metrics.</p>
            </div>
        </div>
    </div>
    </div>
    {% for section in sections %}
        <div class="section-card">
            <div class="section-header">
                <div class="section-icon">
                    <i class="{{ section.icon }}"></i>
                </div>
                <span>{{ section.name }}</span>
            </div>
            {% if section.description %}
                <p class="text-muted mb-4">{{ section.description }}</p>
            {% endif %}

            {% for field in section.sortedFields %}
                {% set fieldName = 'field_' ~ field.id %}
                {% set validationRules = field.validationRules|default({}) %}

                <div class="form-group mb-4 form-field"
                     {% if not field.isVisible %}style="display: none;"{% endif %}
                        {% if field.dependsOn %}data-depends-on="{{ field.dependsOn|json_encode }}"{% endif %}>

                    <label class="form-label d-block mb-2">
                        {{ field.fieldLabel }}
                        {% if field.isRequired %}<span class="required-star">*</span>{% endif %}
                    </label>

                    {% if field.helpText %}
                        <small class="form-text text-muted d-block mb-2">{{ field.helpText }}</small>
                    {% endif %}


                    {% if field.fieldType in ['text', 'email', 'password', 'number', 'date'] %}
                        <label>
                            <input type="{{ field.fieldType }}"
                                   name="{{ fieldName }}"
                                   class="form-control"
                                   placeholder="{{ field.placeholder }}"
                                   value="{{ field.defaultValue|default('') }}"
                                   {% if field.isRequired %}required{% endif %}
                                    {% if validationRules.minLength is defined %}minlength="{{ validationRules.minLength }}"{% endif %}
                                    {% if validationRules.maxLength is defined %}maxlength="{{ validationRules.maxLength }}"{% endif %}
                                    {% if field.fieldType == 'email' and validationRules.pattern is not defined %}
                                        pattern="[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,}$"
                                    {% endif %}
                                    {% if validationRules.pattern is defined %}pattern="{{ validationRules.pattern }}"{% endif %}
                                    {% if field.fieldType == 'number' %}
                                        {% if validationRules.min is defined %}min="{{ validationRules.min }}"{% endif %}
                                        {% if validationRules.max is defined %}max="{{ validationRules.max }}"{% endif %}
                                        {% if validationRules.step is defined %}step="{{ validationRules.step }}"{% endif %}
                                    {% endif %}
                                    {% if field.fieldType == 'date' %}
                                {% if validationRules.minDate is defined %}min="{{ validationRules.minDate }}"{% endif %}
                                {% if validationRules.maxDate is defined %}max="{{ validationRules.maxDate }}"{% endif %}
                                    {% endif %}>
                        </label>

                    {% elseif field.fieldType == 'textarea' %}
                                <textarea name="{{ fieldName }}"
                                          class="form-control"
                                          placeholder="{{ field.placeholder }}"
                                          {% if field.isRequired %}required{% endif %}
                                        {% if validationRules.minLength is defined %}minlength="{{ validationRules.minLength }}"{% endif %}
                                        {% if validationRules.maxLength is defined %}maxlength="{{ validationRules.maxLength }}"{% endif %}>{{ field.defaultValue|default('') }}</textarea>


                    {% elseif field.fieldType == 'select' %}
                        <label>
                            <select name="{{ fieldName }}"
                                    class="form-control"
                                    {% if field.isRequired %}required{% endif %}>
                                <option value="">Select an option</option>
                                {% for option in fieldTypes[field.id].options.options|default([]) %}
                                    <option value="{{ option.value }}"
                                            {% if field.defaultValue == option.value %}selected{% endif %}>
                                        {{ option.label }}
                                    </option>
                                {% endfor %}
                            </select>
                        </label>

                    {% elseif field.fieldType in ['rating', 'radio'] %}
                        <div class="rating-group radio-group-container">
                            {% for option in field.fieldOptions.options|default([]) %}
                                <input type="radio"
                                       class="form-check-input invisible-radio"
                                       name="{{ fieldName }}"
                                       id="{{ fieldName }}_{{ option.value }}"
                                       value="{{ option.value }}"
                                       {% if field.isRequired %}required{% endif %}
                                        {% if field.defaultValue == option.value %}checked{% endif %}>
                                <label class="rating-label" for="{{ fieldName }}_{{ option.value }}">
                                    {{ option.label }}
                                </label>
                            {% endfor %}
                        </div>

                    {% elseif field.fieldType == 'checkbox' %}
                        <div class="d-flex flex-column gap-2">
                            {% for option in fieldTypes[field.id].options.options|default([]) %}
                                <div class="form-check">
                                    <label>
                                        <input class="form-check-input"
                                               type="checkbox"
                                               name="{{ fieldName }}[]"
                                               value="{{ option.value }}"
                                               {% if field.isRequired %}required{% endif %}>
                                    </label>
                                    <label class="form-check-label">{{ option.label }}</label>
                                </div>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>
            {% endfor %}
        </div>
    {% endfor %}
    {% if show %}
        <div class="form-group section-card">
            <label class="section-header">Overall Rating</label>
            <div class="rating-description">
                <p><strong>⭐ 1 – Needs Improvement:</strong> Performance falls below expectations, requiring significant improvement.</p>
                <p><strong>⭐ 2 – Developing:</strong> Shows progress but still needs improvement in key areas.</p>
                <p><strong>⭐ 3 – Meets Expectations:</strong> Performs at the expected level with consistency.</p>
                <p><strong>⭐ 4 – Exceeds Expectations:</strong> Performs above expectations, demonstrating strong skills.</p>
                <p><strong>⭐ 5 – Outstanding:</strong> Consistently exceeds expectations and serves as a role model.</p>
            </div>
            <div class="stars" id="starRating">
                <span class="star" data-value="1">&#9733;</span>
                <span class="star" data-value="2">&#9733;</span>
                <span class="star" data-value="3">&#9733;</span>
                <span class="star" data-value="4">&#9733;</span>
                <span class="star" data-value="5">&#9733;</span>
            </div>
            <div id="overall-rating-display" class="rating-text">Overall Rating: 0.00</div>
            <input type="hidden" id="overall_rating" name="overall_rating" value="0">
        </div>
    {% endif %}
    {% if show %}
    <div class="text-center">
        <button type="submit" class="submit-button">
            <i class="fa fa-paper-plane mr-2"></i> Submit Form
        </button>
    </div>
    {% endif %}
</form>