<?php

namespace App\Service\Admin;

use App\Entity\Employee;
use App\Entity\EmployeeHours;
use App\Traits\ExceptionLoggerTrait;
use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use App\Repository\SettingRepository;

class EmployeeHoursService
{
    use ExceptionLoggerTrait;

    private array $configCache = [];

    public function __construct(
        private readonly HttpClientInterface    $httpClient,
        ParameterBagInterface                   $parameterBag,
        private readonly EntityManagerInterface $entityManager,
        private readonly SettingRepository      $configurationRepository,
    )
    {
        $this->initializeLogger($parameterBag, 'employee_hours_service');
    }

    /**
     * Retrieve configuration values with caching to avoid repetitive repository calls.
     *
     * @param string $fieldName
     * @param string|null $defaultValue
     * @return string|null
     */
    private function getConfigurationValue(string $fieldName, ?string $defaultValue): ?string
    {
        if (!isset($this->configCache[$fieldName])) {
            $configuration = $this->configurationRepository->findOneBy(['fieldName' => $fieldName]);
            $this->configCache[$fieldName] = $configuration ? $configuration->getFieldValue() : $defaultValue;
        }

        return $this->configCache[$fieldName];
    }

    /**
     * Fetch and store daily hours from an external API.
     *
     * @param DateTime $date
     * @return int[]
     * @throws Exception
     */
    public function fetchAndStoreDailyHours(DateTime $date): array
    {
        $startTime = $date->setTime(0, 0)->getTimestamp() * 1000;
        $endTime = $date->setTime(23, 59, 59)->getTimestamp() * 1000;
        $apiUrl = sprintf(
            '%semployee_summary_report?startTime=%s&endTime=%s',
            $this->getConfigurationValue('Base_URL', $_ENV['BASE_URL']),
            $startTime,
            $endTime
        );
        $apiKey = $this->getConfigurationValue('TEAMLOGGER_API_KEY', $_ENV['TEAMLOGGER_API_KEY']);

        $response = $this->httpClient->request('GET', $apiUrl, [
            'headers' => [
                'Authorization' => "Bearer $apiKey",
                'Accept' => 'application/json',
            ],
        ]);

        if ($response->getStatusCode() !== 200) {
            $this->logger->error('Failed to fetch data from API.', [
                'status_code' => $response->getStatusCode(),
                'response_body' => $response->getContent(false),
            ]);
            throw new Exception(sprintf('Failed to fetch data. Status code: %d', $response->getStatusCode()));
        }

        $summaryData = $response->toArray();
        return $this->processEmployeeHours($summaryData, $date);
    }

    /**
     * Process employee hours data and persist to the database.
     *
     * @param array $summaryData
     * @param DateTime $date
     * @return array{success: int, failures: int}
     */
    private function processEmployeeHours(array $summaryData, DateTime $date): array
    {
        $employeeRepository = $this->entityManager->getRepository(Employee::class);
        $employeeHoursRepository = $this->entityManager->getRepository(EmployeeHours::class);

        $successCount = 0;
        $failureCount = 0;
        $batch = [];

        foreach ($summaryData as $data) {
            if (empty($data['id'])) {
                $failureCount++;
                continue;
            }

            $employee = $employeeRepository->findOneBy(['guid' => $data['id']]);
            if (!$employee) {
                $failureCount++;
                continue;
            }

            $employeeHours = $employeeHoursRepository->findOneBy(['employee' => $employee, 'reportDate' => $date]) ?? new EmployeeHours();
            $this->populateEmployeeHours($employeeHours, $employee, $date, $data);

            $batch[] = $employeeHours;
            $successCount++;

            if (count($batch) >= 20) {
                $this->flushBatch($batch);
            }
        }

        $this->flushBatch($batch); // Flush remaining batch

        return ['success' => $successCount, 'failures' => $failureCount];
    }

    /**
     * Populate EmployeeHours entity with data.
     *
     * @param EmployeeHours $employeeHours
     * @param Employee $employee
     * @param DateTime $date
     * @param array $data
     */
    private function populateEmployeeHours(EmployeeHours $employeeHours, Employee $employee, DateTime $date, array $data): void
    {
        $employeeHours->setEmployee($employee);
        $employeeHours->setReportDate($date);
        $employeeHours->setOnComputerHours($data['onComputerHours'] ?? 0);
        $employeeHours->setMeetingHours($data['meetingHours'] ?? 0);
        $employeeHours->setOffComputerHours($data['offComputerHours'] ?? 0);
        $employeeHours->setTotalHours($data['totalHours'] ?? 0);
        $employeeHours->setBreakHours($data['breakHours'] ?? 0);
        $employeeHours->setSpanHours($data['spanHours'] ?? 0);
        $employeeHours->setActiveMinutes($data['activeMinutesRatio'] ?? 0);
        $employeeHours->setActiveSeconds($data['activeSecondsRatio'] ?? 0);
    }

    /**
     * Flush a batch of entities and clear the EntityManager.
     *
     * @param array $batch
     */
    private function flushBatch(array &$batch): void
    {
        if (empty($batch)) {
            return;
        }

        foreach ($batch as $entity) {
            $this->entityManager->persist($entity);
        }

        $this->entityManager->flush();
        $this->entityManager->clear();
        $batch = [];
    }
}