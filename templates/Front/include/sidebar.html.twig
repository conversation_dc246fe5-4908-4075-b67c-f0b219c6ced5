<!-- Left Panel -->
<aside id="left-panel" class="left-panel">
    <nav class="navbar navbar-expand-sm navbar-default">
        <div id="main-menu" class="main-menu collapse navbar-collapse">
            <ul class="nav navbar-nav">
                <li class="{% if app.request.attributes.get('_route') == 'dashboard_employee' %}active{% endif %}">
                <a href="{{ url('dashboard_employee') }}"><i class="menu-icon fa fa-laptop"></i>Dashboard </a>
                </li>
                <li class="menu-title">Services</li>
                {% if app.user and app.user.teamLeader %}
                    <li class="{% if app.request.attributes.get('_route') == 'employee_projects' %}active{% endif %}">
                        <a href="{{ url('employee_projects') }}"><i class="menu-icon fa fa-product-hunt"></i>Projects</a>
                    </li>
                {% endif %}
                <li class="menu-item-has-children dropdown
                    {% if app.request.attributes.get('_route') in ['employee_task','employee_activity_index','employee_other_activity','my_bugs','employee_task_history','my_bugs_history'] %}active show{% endif %}">
                    <a href="#" class="dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"> <i class="menu-icon fa fa-tasks"></i>Tasks</a>
                    <ul class="sub-menu children dropdown-menu
                       {% if app.request.attributes.get('_route') in ['employee_task','employee_activity_index','employee_other_activity','my_bugs','employee_task_history','my_bugs_history'] %}show{% endif %}">
                        <li class="{% if app.request.attributes.get('_route') == 'employee_task' %}active{% endif %}">
                            <i class="fa fa-tasks"></i><a href="{{ url('employee_task') }}">My Tasks</a>
                        </li>
                        <li class="{% if app.request.attributes.get('_route') == 'my_bugs' %}active{% endif %}">
                            <i class="fa fa-bug"></i><a href="{{ url('my_bugs') }}">My Bugs</a>
                        </li>
                        <li class="{% if app.request.attributes.get('_route') == 'employee_activity_index' %}active{% endif %}">
                            <i class="fa fa-file"></i><a href="{{ url('employee_activity_index') }}">Other Activity</a></li>
                        {% if app.user and app.user.teamLeader %}
                            <li class="{% if app.request.attributes.get('_route') == 'employee_other_activity' %}active{% endif %}">
                                <i class="fa fa-file"></i><a href="{{ url('employee_other_activity') }}">Employee Activity</a></li>
                        {% endif %}
                        <li class="{% if app.request.attributes.get('_route') == 'employee_task_history' %}active{% endif %}">
                            <i class="fa fa-history"></i><a href="{{ url('employee_task_history') }}">Task History</a></li>
                        <li class="{% if app.request.attributes.get('_route') == 'my_bugs_history' %}active{% endif %}">
                            <i class="fa fa-history"></i><a href="{{ url('my_bugs_history') }}">Bug History</a></li>
                    </ul>
                </li>
                <li class="menu-item-has-children dropdown
                                    {% if app.request.attributes.get('_route') in ['employee_leave_request_list','employee_leave'] %}active show{% endif %}">
                    <a href="#" class="dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"> <i class="menu-icon fa fa-table"></i>Leaves</a>
                    <ul class="sub-menu children dropdown-menu
                           {% if app.request.attributes.get('_route') in ['employee_leave_request_list','employee_leave'] %}show{% endif %}">
                        <li class="{% if app.request.attributes.get('_route') == 'employee_leave_request_list' %}active{% endif %}">
                            <i class="fa fa-file"></i><a href="{{ url('employee_leave_request_list') }}">My Leaves</a></li>
                        {% if app.user and (app.user.teamLeader or app.user.hrAccount) %}
                            <li class="{% if app.request.attributes.get('_route') == 'employee_leave' %}active{% endif %}">
                                <i class="fa fa-file"></i><a href="{{ url('employee_leave') }}">Team Leaves
                                </a></li>
                        {% endif %}
                    </ul>
                </li>
                <li class="menu-item-has-children dropdown
                                    {% if app.request.attributes.get('_route') in ['team_leader_forms', 'employee_employee_history','employee_history','app_front_self_evaluation','employee_history_self','generate_360_report_form'] %}active show{% endif %}">
                <a href="#" class="dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"> <i class="menu-icon fa fa-table"></i>Forms</a>
                    <ul class="sub-menu children dropdown-menu
                           {% if app.request.attributes.get('_route') in ['team_leader_forms', 'employee_employee_history','employee_history','app_front_self_evaluation','employee_history_self','generate_360_report_form'] %}show{% endif %}">
                    {% if app.user and app.user.teamLeader %}
                        <li class="{% if app.request.attributes.get('_route') == 'team_leader_forms' %}active{% endif %}">
                            <i class="fa fa-file"></i><a href="{{ url('team_leader_forms') }}">Performance Feedback</a></li>
                        <li class="{% if app.request.attributes.get('_route') == 'employee_employee_history' %}active{% endif %}">
                            <i class="fa fa-file"></i><a href="{{ url('employee_employee_history') }}">Employee FeedBack Review</a></li>
                        {% endif %}
                        <li class="{% if app.request.attributes.get('_route') == 'employee_history' %}active{% endif %}">
                            <i class="fa fa-file"></i><a href="{{ url('employee_history') }}">Feedback History</a></li>
                        <li class="{% if app.request.attributes.get('_route') == 'app_front_self_evaluation' %}active{% endif %}">
                            <i class="fa fa-file"></i><a href="{{ url('app_front_self_evaluation') }}">Self Evaluation Form</a></li>
                        <li class="{% if app.request.attributes.get('_route') == 'employee_history_self' %}active{% endif %}">
                            <i class="fa fa-file"></i><a href="{{ url('employee_history_self') }}">Self Evaluation History</a></li>
                        <li class="{% if app.request.attributes.get('_route') == 'generate_360_report_form' %}active{% endif %}">
                            <i class="fa fa-file"></i><a href="{{ url('generate_360_report_form') }}">Employee 360° report</a></li>
                    </ul>
                </li>

                <li class="{% if app.request.attributes.get('_route') in ['employee_screenshots','employee_screenshot_view','employee_screenshot_daily_report'] %}active{% endif %}">
                    <a href="{{ url('employee_screenshots') }}"><i class="menu-icon fa fa-camera"></i>My Screenshots</a>
                </li>

                <li class="menu-item-has-children dropdown
                    {% if app.request.attributes.get('_route') in ['document_list','my_employee_documents'] %}active show{% endif %}">
                    <a href="#" class="dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"> <i class="menu-icon fa fa-building"></i>Company</a>
                    <ul class="sub-menu children dropdown-menu
                       {% if app.request.attributes.get('_route') in ['document_list','my_employee_documents'] %}show{% endif %}">
                        <li class="{% if app.request.attributes.get('_route') == 'document_list' %}active{% endif %}">
                            <i class="fa fa-file"></i><a href="{{ url('document_list') }}">Documents</a></li>
                        <li class="{% if app.request.attributes.get('_route') == 'my_employee_documents' %}active{% endif %}">
                            <i class="fa fa-file"></i><a href="{{ url('my_employee_documents') }}">My Documents</a></li>
                    </ul>
                </li>
            </ul>
        </div>
    </nav>
</aside>
<!-- /#left-panel -->
