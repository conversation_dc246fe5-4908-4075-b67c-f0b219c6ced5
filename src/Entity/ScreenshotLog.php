<?php

namespace App\Entity;

use App\Repository\ScreenshotLogRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: ScreenshotLogRepository::class)]
#[ORM\Table(name: 'screenshot_logs')]
#[ORM\Index(columns: ['employee_id', 'captured_at'], name: 'idx_employee_captured')]
#[ORM\Index(columns: ['capture_type'], name: 'idx_capture_type')]
class ScreenshotLog
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\ManyToOne(targetEntity: MasterEmployee::class)]
    #[ORM\JoinColumn(nullable: false)]
    private ?MasterEmployee $employee = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $capturedAt = null;

    #[ORM\Column(length: 20)]
    private ?string $captureType = null; // 'periodic', 'burst', 'manual', 'verification'

    #[ORM\Column(length: 255)]
    private ?string $filePath = null;

    #[ORM\Column(length: 100, nullable: true)]
    private ?string $fileName = null;

    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    private ?int $fileSize = null; // File size in bytes

    #[ORM\Column(length: 50, nullable: true)]
    private ?string $resolution = null; // e.g., "1920x1080"

    #[ORM\Column(length: 100, nullable: true)]
    private ?string $deviceId = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $activeApplication = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $activeWindow = null;

    #[ORM\Column(type: Types::JSON, nullable: true)]
    private ?array $metadata = null; // Additional capture data

    #[ORM\Column(type: Types::BOOLEAN, options: ['default' => false])]
    private bool $isSynced = false;

    #[ORM\Column(type: Types::BOOLEAN, options: ['default' => false])]
    private bool $isProcessed = false;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $createdAt = null;

    public function __construct()
    {
        $this->capturedAt = new \DateTime();
        $this->createdAt = new \DateTime();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getEmployee(): ?MasterEmployee
    {
        return $this->employee;
    }

    public function setEmployee(?MasterEmployee $employee): static
    {
        $this->employee = $employee;
        return $this;
    }

    public function getCapturedAt(): ?\DateTimeInterface
    {
        return $this->capturedAt;
    }

    public function setCapturedAt(\DateTimeInterface $capturedAt): static
    {
        $this->capturedAt = $capturedAt;
        return $this;
    }

    public function getCaptureType(): ?string
    {
        return $this->captureType;
    }

    public function setCaptureType(string $captureType): static
    {
        $this->captureType = $captureType;
        return $this;
    }

    public function getFilePath(): ?string
    {
        return $this->filePath;
    }

    public function setFilePath(string $filePath): static
    {
        $this->filePath = $filePath;
        return $this;
    }

    public function getFileName(): ?string
    {
        return $this->fileName;
    }

    public function setFileName(?string $fileName): static
    {
        $this->fileName = $fileName;
        return $this;
    }

    public function getFileSize(): ?int
    {
        return $this->fileSize;
    }

    public function setFileSize(?int $fileSize): static
    {
        $this->fileSize = $fileSize;
        return $this;
    }

    public function getResolution(): ?string
    {
        return $this->resolution;
    }

    public function setResolution(?string $resolution): static
    {
        $this->resolution = $resolution;
        return $this;
    }

    public function getDeviceId(): ?string
    {
        return $this->deviceId;
    }

    public function setDeviceId(?string $deviceId): static
    {
        $this->deviceId = $deviceId;
        return $this;
    }

    public function getActiveApplication(): ?string
    {
        return $this->activeApplication;
    }

    public function setActiveApplication(?string $activeApplication): static
    {
        $this->activeApplication = $activeApplication;
        return $this;
    }

    public function getActiveWindow(): ?string
    {
        return $this->activeWindow;
    }

    public function setActiveWindow(?string $activeWindow): static
    {
        $this->activeWindow = $activeWindow;
        return $this;
    }

    public function getMetadata(): ?array
    {
        return $this->metadata;
    }

    public function setMetadata(?array $metadata): static
    {
        $this->metadata = $metadata;
        return $this;
    }

    public function isSynced(): bool
    {
        return $this->isSynced;
    }

    public function setIsSynced(bool $isSynced): static
    {
        $this->isSynced = $isSynced;
        return $this;
    }

    public function isProcessed(): bool
    {
        return $this->isProcessed;
    }

    public function setIsProcessed(bool $isProcessed): static
    {
        $this->isProcessed = $isProcessed;
        return $this;
    }

    public function getCreatedAt(): ?\DateTimeInterface
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTimeInterface $createdAt): static
    {
        $this->createdAt = $createdAt;
        return $this;
    }

    // Helper methods
    public function getFileSizeFormatted(): string
    {
        if (!$this->fileSize) {
            return 'Unknown';
        }

        $bytes = $this->fileSize;
        $units = ['B', 'KB', 'MB', 'GB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    public function getFullPath(): string
    {
        return $this->filePath . '/' . $this->fileName;
    }

    public function exists(): bool
    {
        return file_exists($this->getFullPath());
    }
}
