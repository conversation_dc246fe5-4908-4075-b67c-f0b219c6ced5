<?php

namespace App\Controller\Front;

use App\Entity\EmployeeActivity;
use App\Entity\MasterEmployee;
use App\Form\EmployeeActivityType;
use App\Service\Common\EmployeeActivityService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Csrf\CsrfToken;
use Symfony\Component\Security\Csrf\CsrfTokenManagerInterface;

#[Route('/employee-activity')]
class EmployeeActivityController extends AbstractController
{
    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly EmployeeActivityService $employeeActivityService,
        private readonly CsrfTokenManagerInterface $csrfTokenManager
    ) {
    }

    #[Route('', name: 'employee_activity_index', methods: ['GET', 'POST'])]
    public function index(Request $request): Response
    {
        $employee = $this->getUser();
        if (!$employee instanceof MasterEmployee) {
            throw $this->createAccessDeniedException('This can only be accessed by an employee.');
        }

        $table = $this->employeeActivityService->createEmployeeActivityTable($request, $employee);

        if ($table->isCallback()) {
            return $table->getResponse();
        }

        return $this->render('Front/employeeActivity/index.html.twig', [
            'datatable' => $table,
        ]);
    }

    #[Route('/new', name: 'employee_activity_new', methods: ['GET', 'POST'])]
    public function new(Request $request): Response
    {
        $employee = $this->getUser();
        if (!$employee instanceof MasterEmployee) {
            throw $this->createAccessDeniedException('This can only be accessed by an employee.');
        }

        $employeeActivity = new EmployeeActivity();
        $employeeActivity->setEmployee($employee);
        $form = $this->createForm(EmployeeActivityType::class, $employeeActivity);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $now = new \DateTime();
            $employeeActivity->setCreatedAt($now);
            $employeeActivity->setUpdatedAt($now);

            $this->entityManager->persist($employeeActivity);
            $this->entityManager->flush();

            $this->addFlash('success', 'Employee activity created successfully.');
            return $this->redirectToRoute('employee_activity_index');
        }

        return $this->render('Front/employeeActivity/new.html.twig', [
            'employeeActivity' => $employeeActivity,
            'form' => $form->createView(),
            'mode' => 'create',
        ]);
    }

    #[Route('/{id}/edit', name: 'employee_activity_edit', methods: ['GET', 'POST'])]
    public function edit(Request $request, EmployeeActivity $employeeActivity): Response
    {
        $form = $this->createForm(EmployeeActivityType::class, $employeeActivity);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $employeeActivity->setUpdatedAt(new \DateTime());
            $this->entityManager->flush();

            $this->addFlash('success', 'Employee activity updated successfully.');
            return $this->redirectToRoute('employee_activity_index');
        }

        return $this->render('Front/employeeActivity/new.html.twig', [
            'employeeActivity' => $employeeActivity,
            'form' => $form->createView(),
            'mode' => 'edit',
        ]);
    }

    #[Route('/{id}', name: 'employee_activity_delete', methods: ['POST'])]
    public function delete(Request $request, EmployeeActivity $employeeActivity): Response
    {
        $token = $request->request->get('_token');
        if (!$this->csrfTokenManager->isTokenValid(new CsrfToken('delete' . $employeeActivity->getId(), $token))) {
            return $this->json([
                'success' => false,
                'message' => 'Invalid CSRF token.'
            ], Response::HTTP_FORBIDDEN);
        }

        $this->entityManager->remove($employeeActivity);
        $this->entityManager->flush();

        return $this->json([
            'success' => true,
            'message' => 'Employee activity deleted successfully.'
        ], Response::HTTP_OK);
    }
}