const { app, <PERSON>rowserWindow } = require('electron');
const IdleWarningPopup = require('./src/idle-warning-popup');

let popup;

app.whenReady().then(() => {
    console.log('🚀 App ready, testing popup...');
    
    popup = new IdleWarningPopup();
    popup.setupIpcHandlers();
    
    console.log('📱 Showing popup...');
    popup.show(1, 
        () => console.log('⏰ Timeout callback'),
        () => console.log('✅ Resume callback')
    );
    
    // Test closing after 3 seconds
    setTimeout(() => {
        console.log('🔥 MANUALLY CALLING HIDE()...');
        popup.hide();
        
        // Check if it worked
        setTimeout(() => {
            console.log('🔍 Checking if popup is still visible...');
            if (popup.isVisible()) {
                console.log('❌ FAILED: Popup still visible!');
            } else {
                console.log('✅ SUCCESS: Popup hidden!');
            }
            
            setTimeout(() => app.quit(), 1000);
        }, 2000);
    }, 3000);
});

app.on('window-all-closed', () => {
    console.log('🏁 All windows closed');
    app.quit();
});
