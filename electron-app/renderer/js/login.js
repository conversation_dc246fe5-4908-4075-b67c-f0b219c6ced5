class LoginManager {
    constructor() {
        this.apiBaseUrl = 'http://127.0.0.1:8000'; // Default Symfony dev server
        this.init();
    }

    async init() {
        this.setupEventListeners();
        await this.loadAppInfo();
        await this.checkStoredCredentials();
        await this.checkConnection();
    }

    setupEventListeners() {
        // Form submission
        document.getElementById('loginForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleLogin();
        });

        // Password toggle
        document.getElementById('togglePassword').addEventListener('click', () => {
            this.togglePasswordVisibility();
        });



        // Enter key handling
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                const form = document.getElementById('loginForm');
                if (document.activeElement && form.contains(document.activeElement)) {
                    e.preventDefault();
                    this.handleLogin();
                }
            }
        });
    }

    async loadAppInfo() {
        try {
            // Load app version
            const version = await window.electronAPI.getAppVersion();
            document.getElementById('appVersion').textContent = `v${version}`;

            // Load machine ID (truncated for display)
            const machineId = await window.electronAPI.getMachineId();
            if (machineId) {
                const truncatedId = machineId.substring(0, 8) + '...';
                document.getElementById('machineId').textContent = `ID: ${truncatedId}`;
            }
        } catch (error) {
            console.error('Failed to load app info:', error);
        }
    }

    async checkStoredCredentials() {
        try {
            const storedEmail = await window.electronAPI.storeGet('userEmail');
            const rememberMe = await window.electronAPI.storeGet('rememberMe');
            
            if (storedEmail && rememberMe) {
                document.getElementById('email').value = storedEmail;
                document.getElementById('rememberMe').checked = true;
            }

            // Check for stored token
            const token = await window.electronAPI.storeGet('authToken');
            if (token) {
                // Validate token with server
                await this.validateStoredToken(token);
            }
        } catch (error) {
            console.error('Failed to check stored credentials:', error);
        }
    }

    async validateStoredToken(token) {
        try {
            this.showLoading(true);
            
            const response = await this.makeApiRequest('/api/validate-token', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                data: {}
            });

            if (response.valid) {
                // Token is valid, proceed to dashboard
                await this.loginSuccess(response.user, token);
            } else {
                // Token is invalid, clear it
                await window.electronAPI.storeDelete('authToken');
            }
        } catch (error) {
            console.error('Token validation failed:', error);
            await window.electronAPI.storeDelete('authToken');
        } finally {
            this.showLoading(false);
        }
    }

    async checkConnection() {
        const statusElement = document.getElementById('connectionStatus');
        const statusText = document.getElementById('statusText');

        try {
            statusElement.className = 'status-indicator checking';
            statusText.textContent = 'Checking connection...';

            const response = await fetch(`${this.apiBaseUrl}/api/health`, {
                method: 'GET',
                timeout: 5000
            });

            if (response.ok) {
                statusElement.className = 'status-indicator online';
                statusText.textContent = 'Connected to server';
            } else {
                throw new Error('Server responded with error');
            }
        } catch (error) {
            statusElement.className = 'status-indicator offline';
            statusText.textContent = 'Offline mode - Limited functionality';
            console.warn('Connection check failed:', error);
        }
    }

    togglePasswordVisibility() {
        const passwordInput = document.getElementById('password');
        const toggleIcon = document.querySelector('#togglePassword i');

        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            toggleIcon.className = 'fas fa-eye-slash';
        } else {
            passwordInput.type = 'password';
            toggleIcon.className = 'fas fa-eye';
        }
    }

    async handleLogin() {
        const email = document.getElementById('email').value.trim();
        const password = document.getElementById('password').value;
        const rememberMe = document.getElementById('rememberMe').checked;

        if (!email || !password) {
            this.showError('Please fill in all fields');
            return;
        }

        try {
            this.showLoading(true);
            this.clearMessages();

            // Get machine ID for device identification
            const machineId = await window.electronAPI.getMachineId();

            const response = await this.makeApiRequest('/api/employee/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                data: {
                    email: email,
                    password: password,
                    deviceId: machineId,
                    rememberMe: rememberMe
                }
            });

            if (response.success) {
                await this.loginSuccess(response.user, response.token);
                
                // Store credentials if remember me is checked
                if (rememberMe) {
                    await window.electronAPI.storeSet('userEmail', email);
                    await window.electronAPI.storeSet('rememberMe', true);
                } else {
                    await window.electronAPI.storeDelete('userEmail');
                    await window.electronAPI.storeDelete('rememberMe');
                }
            } else {
                this.showError(response.message || 'Login failed');
            }
        } catch (error) {
            console.error('Login error:', error);
            
            // Try offline login if online login fails
            if (await this.tryOfflineLogin(email, password)) {
                this.showSuccess('Logged in offline mode');
                setTimeout(() => {
                    window.electronAPI.loginSuccess();
                }, 1500);
            } else {
                this.showError('Login failed. Please check your credentials and connection.');
            }
        } finally {
            this.showLoading(false);
        }
    }

    async loginSuccess(user, token) {
        // Store authentication data
        await window.electronAPI.storeSet('authToken', token);
        await window.electronAPI.storeSet('currentUser', user);
        await window.electronAPI.storeSet('loginTime', new Date().toISOString());
        await window.electronAPI.storeSet('isLoggedIn', true);

        // Store user data in local database
        await this.storeUserDataLocally(user, token);

        // Log the login activity
        await this.logLoginActivity(user);



        this.showSuccess('Login successful! Redirecting...');

        setTimeout(() => {
            window.electronAPI.loginSuccess();
        }, 1500);
    }



    async tryOfflineLogin(email, password) {
        try {
            const result = await window.electronAPI.offlineAuthenticate(email, password);

            if (result.success) {
                // Store offline user data
                await window.electronAPI.storeSet('authToken', result.token);
                await window.electronAPI.storeSet('currentUser', result.user);
                await window.electronAPI.storeSet('loginTime', new Date().toISOString());
                await window.electronAPI.storeSet('isLoggedIn', true);
                await window.electronAPI.storeSet('offlineMode', true);

                return true;
            }
        } catch (error) {
            console.error('Offline login failed:', error);
        }
        return false;
    }

    async storeUserDataLocally(user, token) {
        try {
            // Store user data
            await window.electronAPI.dbRun(`
                INSERT OR REPLACE INTO users (
                    id, email, username, name, roles, employee_code,
                    is_team_leader, is_hr_account, last_sync
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            `, [
                user.id,
                user.email,
                user.username,
                user.name,
                JSON.stringify(user.roles),
                user.employeeCode,
                user.isTeamLeader ? 1 : 0,
                user.isHrAccount ? 1 : 0,
                new Date().toISOString()
            ]);

            // Store token with expiration
            const machineId = await window.electronAPI.getMachineId();
            const expiresAt = new Date();
            expiresAt.setDate(expiresAt.getDate() + 30); // 30 days from now

            await window.electronAPI.dbRun(`
                INSERT OR REPLACE INTO tokens (
                    user_id, token, device_id, expires_at
                ) VALUES (?, ?, ?, ?)
            `, [
                user.id,
                token,
                machineId,
                expiresAt.toISOString()
            ]);

        } catch (error) {
            console.error('Failed to store user data locally:', error);
        }
    }

    async logLoginActivity(user) {
        try {
            await window.electronAPI.dbRun(`
                INSERT INTO activity_log (user_id, action, details)
                VALUES (?, ?, ?)
            `, [
                user.id,
                'Logged in successfully',
                `Login from device at ${new Date().toLocaleString()}`
            ]);
        } catch (error) {
            console.error('Failed to log login activity:', error);
        }
    }

    async makeApiRequest(endpoint, options = {}) {
        const config = {
            url: `${this.apiBaseUrl}${endpoint}`,
            timeout: 10000,
            ...options
        };

        try {
            const response = await window.electronAPI.apiRequest(config);
            return response;
        } catch (error) {
            if (error.response) {
                // Server responded with error status
                throw new Error(`HTTP ${error.response.status}: ${error.response.statusText}`);
            } else if (error.request) {
                // Request was made but no response received
                throw new Error('No response from server');
            } else {
                // Something else happened
                throw new Error(error.message || 'Request failed');
            }
        }
    }

    showLoading(show) {
        const overlay = document.getElementById('loadingOverlay');
        const loginBtn = document.getElementById('loginBtn');
        
        if (show) {
            overlay.style.display = 'flex';
            loginBtn.disabled = true;
        } else {
            overlay.style.display = 'none';
            loginBtn.disabled = false;
        }
    }

    showError(message) {
        const errorDiv = document.getElementById('errorMessage');
        const errorText = document.getElementById('errorText');
        
        errorText.textContent = message;
        errorDiv.style.display = 'flex';
        
        setTimeout(() => {
            errorDiv.style.display = 'none';
        }, 5000);
    }

    showSuccess(message) {
        const successDiv = document.getElementById('successMessage');
        const successText = document.getElementById('successText');

        successText.textContent = message;
        successDiv.style.display = 'flex';

        setTimeout(() => {
            successDiv.style.display = 'none';
        }, 3000);
    }

    showInfo(message) {
        // Use success styling for info messages
        this.showSuccess(message);
    }

    showWarning(message) {
        // Use error styling for warning messages
        this.showError(message);
    }

    clearMessages() {
        document.getElementById('errorMessage').style.display = 'none';
        document.getElementById('successMessage').style.display = 'none';
    }
}

// Initialize login manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new LoginManager();
});
