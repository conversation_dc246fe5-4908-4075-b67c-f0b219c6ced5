<?php

namespace App\Repository;

use App\Entity\ProjectTeam;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<ProjectTeam>
 */
class ProjectTeamRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, ProjectTeam::class);
    }

    /**
     * Check if a team member is already assigned to a project
     */
    public function isTeamMemberAlreadyAssigned(int $projectId, int $teamMemberId): bool
    {
        $result = $this->createQueryBuilder('pt')
            ->select('COUNT(pt.id)')
            ->where('pt.project = :project_id')
            ->andWhere('pt.teamMember = :team_member_id')
            ->setParameter('project_id', $projectId)
            ->setParameter('team_member_id', $teamMemberId)
            ->getQuery()
            ->getSingleScalarResult();

        return $result > 0;
    }

    /**
     * Get all team member IDs already assigned to a project
     */
    public function getAssignedTeamMemberIds(int $projectId): array
    {
        $result = $this->createQueryBuilder('pt')
            ->select('IDENTITY(pt.teamMember) as team_member_id')
            ->where('pt.project = :project_id')
            ->setParameter('project_id', $projectId)
            ->getQuery()
            ->getArrayResult();

        return array_column($result, 'team_member_id');
    }
}
