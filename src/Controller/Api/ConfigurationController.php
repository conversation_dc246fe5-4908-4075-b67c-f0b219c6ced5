<?php

namespace App\Controller\Api;

use App\Repository\TrackingConfigurationRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

#[Route('/api/configuration', name: 'api_configuration_')]
class ConfigurationController extends AbstractController
{
    public function __construct(
        private TrackingConfigurationRepository $configRepository,
        private EntityManagerInterface $entityManager
    ) {}

    #[Route('/tracking', name: 'tracking', methods: ['GET'])]
    public function getTrackingConfiguration(Request $request): JsonResponse
    {
        try {
            // Get employee ID from query parameter (for future employee-specific configs)
            $employeeId = $request->query->get('employee_id');

            // For now, we only use global configurations (employee_id = null in database)
            // Get global configurations where employee field is NULL
            $globalConfigs = $this->configRepository->findBy([
                'configType' => 'global',
                'employee' => null,  // This is the key fix - global configs have null employee
                'isActive' => true
            ]);

            // Future: Get employee-specific configurations if needed
            // $employeeConfigs = [];
            // if ($employeeId) {
            //     $employeeConfigs = $this->configRepository->findBy([
            //         'configType' => 'employee',
            //         'employee' => $employeeId,
            //         'isActive' => true
            //     ]);
            // }

            // Organize configurations (only global for now)
            $config = $this->organizeConfigurations($globalConfigs, []);

            return new JsonResponse([
                'success' => true,
                'data' => $config,
                'timestamp' => new \DateTime(),
                'version' => '1.0'
            ]);

        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => 'Failed to fetch configuration',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    #[Route('/tracking/categories', name: 'tracking_categories', methods: ['GET'])]
    public function getTrackingCategories(): JsonResponse
    {
        try {
            $categories = [
                'idle_detection' => [
                    'idle_timeout_enabled' => true,
                    'idle_timeout_minutes' => 5
                ],
                'time_tracking' => [
                    'silent_mode_enabled' => true,
                    'auto_start_tracking' => true,
                    'max_daily_hours' => 8,
                    'min_session_duration' => 60,
                    'max_session_duration' => 14400,
                    'working_hours_start' => 9,
                    'working_hours_end' => 18
                ],
                'screenshots' => [
                    'screenshot_enabled' => true,
                    'screenshot_interval_seconds' => 300,
                    'screenshot_quality' => 80,
                    'screenshot_burst_mode' => false,
                    'screenshot_random_interval' => true
                ],

                'application_tracking' => [
                    'app_tracking_enabled' => true,
                    'track_window_titles' => true,
                    'productivity_categorization' => true
                ],
                'website_tracking' => [
                    'website_tracking_enabled' => true,
                    'track_browser_tabs' => true,
                    'website_categorization' => true
                ],
                'productivity' => [
                    'productivity_analysis_enabled' => true,
                    'break_reminder_interval' => 60,
                    'focus_time_threshold' => 25
                ]
            ];
            
            return new JsonResponse([
                'success' => true,
                'data' => $categories
            ]);
            
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => 'Failed to fetch categories'
            ], 500);
        }
    }

    #[Route('/sync-status', name: 'sync_status', methods: ['GET'])]
    public function getSyncStatus(Request $request): JsonResponse
    {
        try {
            $employeeId = $request->query->get('employee_id');

            if (!$employeeId) {
                return new JsonResponse([
                    'success' => false,
                    'error' => 'Employee ID is required'
                ], 400);
            }

            // Get sync status for different data types
            $timeLogCount = $this->entityManager->getRepository(\App\Entity\TimeLog::class)
                ->count(['employee' => $employeeId, 'isSynced' => true]);

            $screenshotCount = $this->entityManager->getRepository(\App\Entity\ScreenshotLog::class)
                ->count(['employee' => $employeeId, 'isSynced' => true]);

            $appUsageCount = $this->entityManager->getRepository(\App\Entity\ApplicationUsage::class)
                ->count(['employee' => $employeeId, 'isSynced' => true]);

            $websiteUsageCount = $this->entityManager->getRepository(\App\Entity\WebsiteUsage::class)
                ->count(['employee' => $employeeId, 'isSynced' => true]);

            return new JsonResponse([
                'success' => true,
                'data' => [
                    'last_updated' => new \DateTime(),
                    'config_version' => '1.0',
                    'sync_required' => false,
                    'sync_counts' => [
                        'time_logs' => $timeLogCount,
                        'screenshots' => $screenshotCount,
                        'application_usage' => $appUsageCount,
                        'website_usage' => $websiteUsageCount
                    ],
                    'server_time' => new \DateTime()
                ]
            ]);
            
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => 'Failed to get sync status'
            ], 500);
        }
    }

    private function organizeConfigurations(array $globalConfigs, array $employeeConfigs = []): array
    {
        $config = [];
        
        // Process global configurations
        foreach ($globalConfigs as $globalConfig) {
            $key = $globalConfig->getConfigKey();
            $value = $this->parseConfigValue($globalConfig->getConfigValue());
            $config[$key] = $value;
        }
        
        // Override with employee-specific configurations
        foreach ($employeeConfigs as $employeeConfig) {
            $key = $employeeConfig->getConfigKey();
            $value = $this->parseConfigValue($employeeConfig->getConfigValue());
            $config[$key] = $value;
        }
        
        // Ensure all required configurations exist with defaults
        $defaults = $this->getDefaultConfigurations();
        foreach ($defaults as $key => $defaultValue) {
            if (!isset($config[$key])) {
                $config[$key] = $defaultValue;
            }
        }
        
        // Organize by categories for easier consumption
        return [
            'idle_detection' => [
                'idle_timeout_enabled' => (bool)($config['idle_timeout_enabled'] ?? true),
                'idle_timeout_minutes' => (int)($config['idle_timeout_minutes'] ?? 5)
            ],
            'time_tracking' => [
                'silent_mode_enabled' => (bool)($config['silent_mode_enabled'] ?? true),
                'auto_start_tracking' => (bool)($config['auto_start_tracking'] ?? true),
                'max_daily_hours' => (int)($config['max_daily_hours'] ?? 8),
                'min_session_duration' => (int)($config['min_session_duration'] ?? 60),
                'max_session_duration' => (int)($config['max_session_duration'] ?? 14400),
                'working_hours_start' => (int)($config['working_hours_start'] ?? 9),
                'working_hours_end' => (int)($config['working_hours_end'] ?? 18)
            ],
            'screenshots' => [
                'screenshot_enabled' => (bool)($config['screenshot_enabled'] ?? true),
                'screenshot_interval_seconds' => (int)($config['screenshot_interval_seconds'] ?? 300),
                'screenshot_quality' => (int)($config['screenshot_quality'] ?? 80),
                'screenshot_burst_mode' => (bool)($config['screenshot_burst_mode'] ?? false),
                'screenshot_random_interval' => (bool)($config['screenshot_random_interval'] ?? true)
            ],

            'application_tracking' => [
                'app_tracking_enabled' => (bool)($config['app_tracking_enabled'] ?? true),
                'track_window_titles' => (bool)($config['track_window_titles'] ?? true),
                'productivity_categorization' => (bool)($config['productivity_categorization'] ?? true)
            ],
            'website_tracking' => [
                'website_tracking_enabled' => (bool)($config['website_tracking_enabled'] ?? true),
                'track_browser_tabs' => (bool)($config['track_browser_tabs'] ?? true),
                'website_categorization' => (bool)($config['website_categorization'] ?? true)
            ],
            'productivity' => [
                'productivity_analysis_enabled' => (bool)($config['productivity_analysis_enabled'] ?? true),
                'break_reminder_interval' => (int)($config['break_reminder_interval'] ?? 60),
                'focus_time_threshold' => (int)($config['focus_time_threshold'] ?? 25)
            ]
        ];
    }

    private function parseConfigValue(string $value): mixed
    {
        // Try to parse as JSON first
        $decoded = json_decode($value, true);
        if (json_last_error() === JSON_ERROR_NONE) {
            return $decoded;
        }
        
        // Parse boolean values
        if (in_array(strtolower($value), ['true', '1', 'yes', 'on'])) {
            return true;
        }
        if (in_array(strtolower($value), ['false', '0', 'no', 'off'])) {
            return false;
        }
        
        // Parse numeric values
        if (is_numeric($value)) {
            return strpos($value, '.') !== false ? (float)$value : (int)$value;
        }
        
        // Return as string
        return $value;
    }

    private function getDefaultConfigurations(): array
    {
        return [
            'idle_timeout_enabled' => true,
            'idle_timeout_minutes' => 5,
            'silent_mode_enabled' => true,
            'auto_start_tracking' => true,
            'max_daily_hours' => 8,
            'min_session_duration' => 60,
            'max_session_duration' => 14400,
            'working_hours_start' => 9,
            'working_hours_end' => 18,
            'screenshot_enabled' => true,
            'screenshot_interval_seconds' => 300,
            'screenshot_quality' => 80,
            'screenshot_burst_mode' => false,
            'screenshot_random_interval' => true,

            'app_tracking_enabled' => true,
            'track_window_titles' => true,
            'productivity_categorization' => true,
            'website_tracking_enabled' => true,
            'track_browser_tabs' => true,
            'website_categorization' => true,
            'productivity_analysis_enabled' => true,
            'break_reminder_interval' => 60,
            'focus_time_threshold' => 25
        ];
    }
}
