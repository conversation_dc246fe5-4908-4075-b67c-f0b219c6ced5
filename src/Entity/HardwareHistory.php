<?php

namespace App\Entity;

use App\Repository\HardwareHistoryRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: HardwareHistoryRepository::class)]
class HardwareHistory
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    /** @phpstan-ignore-next-line */
    private ?int $id = null;

    #[ORM\ManyToOne(targetEntity: Hardware::class)]
    #[ORM\JoinColumn(name: "hardwareId", referencedColumnName: "id", nullable: false, onDelete: "CASCADE")]
    private ?Hardware $hardware = null;

    #[ORM\Column(length: 255)]
    private ?string $changedBy = null;

    #[ORM\Column(length: 50)]
    private ?string $changeType = null;

    #[ORM\Column(type: "json", nullable: true)]
    private ?array $oldData = null;

    #[ORM\Column(type: "json", nullable: true)]
    private ?array $newData = null;

    #[ORM\Column(type: "datetime")]
    private ?\DateTimeInterface $changedAt = null;


    public function getId(): ?int
    {
        return $this->id;
    }

    public function getHardware(): ?Hardware
    {
        return $this->hardware;
    }

    public function setHardware(?Hardware $hardware): void
    {
        $this->hardware = $hardware;
    }

    public function getChangedBy(): ?string
    {
        return $this->changedBy;
    }

    public function setChangedBy(?string $changedBy): void
    {
        $this->changedBy = $changedBy;
    }

    public function getChangeType(): ?string
    {
        return $this->changeType;
    }

    public function setChangeType(?string $changeType): void
    {
        $this->changeType = $changeType;
    }

    public function getOldData(): ?array
    {
        return $this->oldData;
    }

    public function setOldData(?array $oldData): void
    {
        $this->oldData = $oldData;
    }

    public function getNewData(): ?array
    {
        return $this->newData;
    }

    public function setNewData(?array $newData): void
    {
        $this->newData = $newData;
    }

    public function getChangedAt(): ?\DateTimeInterface
    {
        return $this->changedAt;
    }

    public function setChangedAt(?\DateTimeInterface $changedAt): void
    {
        $this->changedAt = $changedAt;
    }
}