<?php

namespace App\Form;

use App\Entity\Document;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\FileType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\Length;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\File;

class DocumentType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('title', TextType::class, [
                'label' => 'Document Title',
                'required' => true,
                'constraints' => [
                    new NotBlank(['message' => 'Document title is required.']),
                    new Length([
                        'min' => 2,
                        'max' => 70,
                        'minMessage' => 'Document title must be at least {{ limit }} characters long',
                        'maxMessage' => 'Document title cannot be longer than {{ limit }} characters',
                    ]),
                ],
                'attr' => [
                    'maxlength' => 70,
                    'class' => 'form-control',
                    'data-validation' => 'required|min:2|max:70',
                    'placeholder' => 'Enter document title',
                ],
            ])
            ->add('file', FileType::class, [
                'label' => 'Upload Document',
                'mapped' => false,
                'required' => $options['is_create'],
                'constraints' => [
                    new File([
                        'maxSize' => '5m',
                        'mimeTypes' => ['application/pdf', 'image/jpeg', 'image/png'],
                        'mimeTypesMessage' => 'Please upload a valid PDF, JPEG, or PNG file.',
                    ]),
                ],
                'attr' => [
                    'class' => 'form-control',
                    'data-validation' => 'required',
                    'accept' => '.pdf,.jpg,.jpeg,.png',
                ],
            ]);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => Document::class,
            'is_create' => true,
        ]);
    }
}