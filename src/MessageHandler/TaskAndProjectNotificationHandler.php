<?php

namespace App\MessageHandler;

use App\Entity\Project;
use App\Entity\Task;
use App\Entity\TaskAssignment;
use App\Message\TaskAndProjectNotificationMessage;
use App\Repository\ProjectRepository;
use App\Repository\SettingRepository;
use App\Service\Common\EmailService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Mailer\Exception\TransportExceptionInterface;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use App\Traits\ExceptionLoggerTrait;

#[AsMessageHandler]
class TaskAndProjectNotificationHandler
{
    use ExceptionLoggerTrait;

    public function __construct(
        private readonly ProjectRepository        $projectRepository,
        private readonly ParameterBagInterface    $parameterBag,
        private readonly EntityManagerInterface   $entityManager,
        private readonly SettingRepository        $settingRepository,
        private readonly EmailService             $emailService,
    )
    {
        $this->initializeLogger($parameterBag, 'task_project_notifications_handler');
    }

    private function isNotificationEnabled(string $type): bool
    {
        $setting = $this->settingRepository->findOneBy(['fieldName' => $type]);
        return $setting ? filter_var($setting->getFieldValue(), FILTER_VALIDATE_BOOLEAN) : false;
    }

    /**
     * @throws TransportExceptionInterface
     */
    public function __invoke(TaskAndProjectNotificationMessage $message): void
    {
        $entityType = $message->getEntityType();
        $entityId = $message->getEntityId();
        $action = $message->getAction();
        $changes = $message->getChanges() ?? [];

        if ($entityType === 'task') {
            $task = $this->entityManager->getRepository(Task::class)->find($entityId);
            if (!$task) {
                $this->log('Task not found', ['task_id' => $entityId]);
                return;
            }

            $assignedTo = $task->getAssignedTo();
            if (!$assignedTo) {
                $this->log('No employee assigned to task', ['task_id' => $entityId]);
                return;
            }

            $placeholders = [
                '{{employee_name}}' => $assignedTo->getName(),
                '{{task_title}}' => $task->getTitle(),
                '{{project_name}}' => $task->getProject()->getName(),
                '{{action}}' => strtoupper(trim($action)),
                '{{assigned_date}}' => $task->getAssignedAt() ? $task->getAssignedAt()->format('d-M-Y') : 'N/A',
                '{{app_base_url}}' => rtrim($this->parameterBag->get('app_base_url'), '/') . "/employee/task",
            ];

            if ($action === 'create' && $this->isNotificationEnabled('TASK_NOTIFICATION_ENABLED')) {
                $templateIdentifier = 'task_assignment_notification';
                $subject = 'New Task Assigned';
                $emailContent = $this->emailService->generateEmailContent($templateIdentifier, $placeholders);
                $this->emailService->sendEmail($assignedTo->getEmail(), $subject, $emailContent);
            } elseif ($action === 'update' && !empty($changes)) {
                $shouldSend = false;
                foreach ($changes as $change) {
                    if ($change === 'assignment' && $this->isNotificationEnabled('TASK_ASSIGNMENT_UPDATE_ENABLED')) {
                        $shouldSend = true;
                    } elseif ($change === 'status' && $this->isNotificationEnabled('TASK_STATUS_UPDATE_ENABLED')) {
                        $shouldSend = true;
                    } elseif ($change === 'description' && $this->isNotificationEnabled('TASK_DESCRIPTION_UPDATE_ENABLED')) {
                        $shouldSend = true;
                    }
                }
                if ($shouldSend) {
                    $templateIdentifier = 'task_update_notification';
                    $subject = 'Task Assignment Updated';
                    $emailContent = $this->emailService->generateEmailContent($templateIdentifier, $placeholders);
                    $this->emailService->sendEmail($assignedTo->getEmail(), $subject, $emailContent);
                } else {
                    $this->log('Task update notifications skipped due to settings', ['task_id' => $entityId, 'changes' => $changes]);
                }
            } else {
                $this->log('Task notifications are disabled or no relevant changes', ['task_id' => $entityId, 'action' => $action]);
            }
        }elseif ($entityType === 'task_assignment') {
            $taskAssignment = $this->entityManager->getRepository(TaskAssignment::class)->find($entityId);
            if (!$taskAssignment) {
                $this->log('Task assignment not found', ['task_assignment_id' => $entityId]);
                return;
            }

            $assignedTo = $taskAssignment->getAssignedTo();
            if (!$assignedTo) {
                $this->log('No employee assigned to task assignment', ['task_assignment_id' => $entityId]);
                return;
            }

            $placeholders = [
                '{{employee_name}}' => $assignedTo->getName(),
                '{{task_title}}' => $taskAssignment->getTask()->getTitle(),
                '{{project_name}}' => $taskAssignment->getTask()->getProject()->getName(),
                '{{sub_task_category}}' => $taskAssignment->getCategory(),
                '{{action}}' => strtoupper(trim($action)),
                '{{assigned_date}}' => $taskAssignment->getCreatedAt()->format('d-M-Y'),
                '{{app_base_url}}' => rtrim($this->parameterBag->get('app_base_url'), '/') . "/employee/task",
            ];

            if ($action === 'create' && $this->isNotificationEnabled('TASK_NOTIFICATION_ENABLED')) {
                $templateIdentifier = 'subTask_assignment_notification';
                $subject = 'New Sub-Task Assigned';
                $emailContent = $this->emailService->generateEmailContent($templateIdentifier, $placeholders);
                $this->emailService->sendEmail($assignedTo->getEmail(), $subject, $emailContent);
            } elseif ($action === 'update' && !empty($changes)) {
                $shouldSend = false;
                foreach ($changes as $change) {
                    if ($change === 'assignment' && $this->isNotificationEnabled('TASK_ASSIGNMENT_UPDATE_ENABLED')) {
                        $shouldSend = true;
                    } elseif ($change === 'status' && $this->isNotificationEnabled('TASK_STATUS_UPDATE_ENABLED')) {
                        $shouldSend = true;
                    } elseif ($change === 'description' && $this->isNotificationEnabled('TASK_DESCRIPTION_UPDATE_ENABLED')) {
                        $shouldSend = true;
                    }
                }
                if ($shouldSend) {
                    $templateIdentifier = 'subTask_update_notification';
                    $subject = 'Sub-Task Assignment Updated';
                    $emailContent = $this->emailService->generateEmailContent($templateIdentifier, $placeholders);
                    $this->emailService->sendEmail($assignedTo->getEmail(), $subject, $emailContent);
                } else {
                    $this->log('Task assignment update notifications skipped due to settings', ['task_assignment_id' => $entityId, 'changes' => $changes]);
                }
            } else {
                $this->log('Task assignment notifications are disabled or no relevant changes', ['task_assignment_id' => $entityId, 'action' => $action]);
            }
        } elseif ($entityType === 'project') {
            if (!$this->isNotificationEnabled('PROJECT_NOTIFICATION_ENABLED')) {
                $this->log('Project notifications are disabled', ['project_id' => $entityId]);
                return;
            }

            $project = $this->projectRepository->find($entityId);
            if (!$project) {
                $this->log('Project not found', ['project_id' => $entityId]);
                return;
            }
            if ($action === 'no-change') {
                $this->log('No project manager change, skipping notification', ['project_id' => $entityId]);
                return;
            }
            if ($action === 'create') {
                $this->sendProjectNotification($project, $action);
                return;
            }
            if ($action === 'update') {
                $this->sendProjectNotification($project, $action);
                return;
            }
            $this->log('Unknown action type', ['action' => $action, 'entity_type' => $entityType]);
        } else {
            $this->log('Invalid entity type', ['entity_type' => $entityType]);
        }
    }

    /**
     * @throws TransportExceptionInterface
     */
    private function sendProjectNotification(Project $project, string $action): void
    {
        $recipients = [$project->getProjectManager()];
        foreach ($project->getProjectTeams() as $projectTeam) {
            if ($employee = $projectTeam->getEmployee()) {
                $recipients[] = $employee;
            }
        }

        $placeholders = [
            '{{project_name}}' => $project->getName(),
            '{{action}}' => strtoupper(trim($action)),
            '{{start_date}}' => $project->getStartDate()->format('d-M-Y'),
            '{{app_base_url}}' => rtrim($this->parameterBag->get('app_base_url'), '/') . "/employee/projects",
        ];

        $templateIdentifier = $action === 'create' ? 'project_assignment_notification' : 'project_update_notification';
        $subject = $action === 'create' ? 'New Project Assigned' : 'Project Manager Updated';

        foreach ($recipients as $employee) {
            $placeholders['{{employee_name}}'] = $employee->getName();
            $emailContent = $this->emailService->generateEmailContent($templateIdentifier, $placeholders);
            $this->emailService->sendEmail($employee->getEmail(), $subject, $emailContent);
        }
    }
}