<?php

namespace App\Entity;

use App\Repository\FormFieldRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: FormFieldRepository::class)]
class FormField
{
    public const INACTIVE = false;
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private ?int $id = null;

    #[ORM\ManyToOne(targetEntity: FormSection::class, inversedBy: 'fields')]
    #[ORM\JoinColumn(nullable: false)]
    private FormSection $section;

    #[ORM\Column(type: 'string', length: 255)]
    private string $fieldLabel;

    #[ORM\Column(type: 'string', length: 15 , nullable: true)]
    private ?string $fieldKey = null;

    #[ORM\Column(type: 'string', length: 50)]
    private string $fieldType = '';
    #[ORM\Column(type: 'integer')]
    private int $fieldOrder;

    #[ORM\Column(type: 'string', length: 255, nullable: true)]
    private ?string $placeholder = null;

    #[ORM\Column(type: 'string', length: 255, nullable: true)]
    private ?string $helpText = null;

    #[ORM\Column(type: 'string', length: 255, nullable: true)]
    private ?string $defaultValue = null;

    #[ORM\Column(type: 'json', nullable: true)]
    private ?array $fieldOptions = null;

    #[ORM\Column(type: 'boolean')]
    private bool $isRequired = false;

    #[ORM\Column(type: 'boolean')]
    private bool $isVisible = true;

    #[ORM\Column(type: 'json', nullable: true)]
    private ?array $dependsOn = null;

    #[ORM\Column(type: 'json', nullable: true)]
    private ?array $validationRules = null;

    #[ORM\Column(type: 'datetime')]
    private \DateTimeInterface $createdAt;

    #[ORM\Column(type: 'datetime')]
    private \DateTimeInterface $updatedAt;

    #[ORM\Column(type: Types::BOOLEAN, options: ["default" => false])]
    private ?bool $isDelete = self::INACTIVE;

    public function __construct()
    {
        $this->createdAt = new \DateTime();
        $this->updatedAt = new \DateTime();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): void
    {
        $this->id = $id;
    }

    public function getSection(): FormSection
    {
        return $this->section;
    }

    public function setSection(FormSection $section): void
    {
        $this->section = $section;
    }

    public function getFieldLabel(): string
    {
        return $this->fieldLabel;
    }

    public function setFieldLabel(string $fieldLabel): void
    {
        $this->fieldLabel = $fieldLabel;
    }

    public function getFieldKey(): string
    {
        return $this->fieldKey;
    }

    public function setFieldKey(string $fieldKey): void
    {
        $this->fieldKey = $fieldKey;
    }

    public function getFieldType(): string
    {
        return $this->fieldType;
    }

    public function setFieldType(string $fieldType): void
    {
        $this->fieldType = $fieldType;
    }

    public function getFieldOrder(): int
    {
        return $this->fieldOrder;
    }

    public function setFieldOrder(int $fieldOrder): void
    {
        $this->fieldOrder = $fieldOrder;
    }

    public function getPlaceholder(): ?string
    {
        return $this->placeholder;
    }

    public function setPlaceholder(?string $placeholder): void
    {
        $this->placeholder = $placeholder;
    }

    public function getHelpText(): ?string
    {
        return $this->helpText;
    }

    public function setHelpText(?string $helpText): void
    {
        $this->helpText = $helpText;
    }

    public function getDefaultValue(): ?string
    {
        return $this->defaultValue;
    }

    public function setDefaultValue(?string $defaultValue): void
    {
        $this->defaultValue = $defaultValue;
    }

    public function getFieldOptions(): ?array
    {
        return $this->fieldOptions;
    }

    public function setFieldOptions(?array $fieldOptions): void
    {
        $this->fieldOptions = $fieldOptions;
    }

    public function isRequired(): bool
    {
        return $this->isRequired;
    }

    public function setIsRequired(bool $isRequired): void
    {
        $this->isRequired = $isRequired;
    }

    public function isVisible(): bool
    {
        return $this->isVisible;
    }

    public function setIsVisible(bool $isVisible): void
    {
        $this->isVisible = $isVisible;
    }

    public function getDependsOn(): ?array
    {
        return $this->dependsOn;
    }

    public function setDependsOn(?array $dependsOn): void
    {
        $this->dependsOn = $dependsOn;
    }

    public function getValidationRules(): ?array
    {
        return $this->validationRules;
    }

    public function setValidationRules(?array $validationRules): void
    {
        $this->validationRules = $validationRules;
    }

    public function getCreatedAt(): \DateTimeInterface
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTimeInterface $createdAt): void
    {
        $this->createdAt = $createdAt;
    }

    public function getUpdatedAt(): \DateTimeInterface
    {
        return $this->updatedAt;
    }

    public function setUpdatedAt(\DateTimeInterface $updatedAt): void
    {
        $this->updatedAt = $updatedAt;
    }

    public function getIsDelete(): ?bool
    {
        return $this->isDelete;
    }

    public function setIsDelete(?bool $isDelete): void
    {
        $this->isDelete = $isDelete;
    }
}