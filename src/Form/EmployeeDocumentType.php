<?php

namespace App\Form;

use App\Entity\EmployeeDocument;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\FileType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\File;

class EmployeeDocumentType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('documentType', ChoiceType::class, [
                'label' => 'Document Type',
                'choices' => [
                    'PAN' => 'PAN',
                    'Aadhaar' => 'AADHAAR',
                    'Offer Letter' => 'OFFER_LETTER',
                    'Resume' => 'RESUME',
                    'Passport' => 'PASSPORT',
                    'Driving License' => 'DRIVING_LICENSE',
                    'Education Certificate' => 'EDUCATION_CERTIFICATE',
                    'Experience Letter' => 'EXPERIENCE_LETTER',
                    'Profile Picture' => 'PROFILE_PICTURE',
                    'Other' => 'OTHER',
                ],
                'required' => true,
                'placeholder' => 'Select Document Type',
            ])
            ->add('file', FileType::class, [
                'label' => 'Upload Document',
                'required' => $options['is_new'],
                'mapped' => false,
                'constraints' => [
                    new File([
                        'maxSize' => '5M',
                        'mimeTypes' => [ 'application/pdf', 'image/jpeg', 'image/png', 'image/gif', 'text/plain'],
                        'mimeTypesMessage' => 'Please upload a valid PDF, JPG, or PNG file',
                    ]),
                ],
            ])
            ->add('notes', TextareaType::class, [
                'label' => 'Notes (Optional)',
                'required' => false,
                'attr' => ['rows' => 4],
            ]);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => EmployeeDocument::class,
            'is_new' => true,
        ]);
    }
}