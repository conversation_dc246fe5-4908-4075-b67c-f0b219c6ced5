<?php

namespace App\Service\Admin;

use App\Entity\LeaveBalance;
use App\Entity\MasterEmployee;
use App\Entity\LeaveType;
use App\Helper\SettingHelper;
use Doctrine\ORM\EntityManagerInterface;

class LeaveBalanceManagerService
{
    private const ADDON_LEAVE_NAME = 'Addon Leave';
    private const SICK_LEAVE_NAME = 'Sick Leave';
    private const CASUAL_LEAVE_NAME = 'Casual Leave';
    private const DEFAULT_RENEWAL_MONTH = 'April';
    private const MONTHS_PER_YEAR = 12;

    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly SettingHelper          $settingHelper,
    ) {
    }

    /**
     * @throws \DateMalformedStringException
     */
    public function populateAllLeaveBalances(): int
    {
        $employeeRepo = $this->entityManager->getRepository(MasterEmployee::class);
        $leaveTypeRepo = $this->entityManager->getRepository(LeaveType::class);
        $leaveBalanceRepo = $this->entityManager->getRepository(LeaveBalance::class);

        $employees = $employeeRepo->findBy(['isDelete' => false]);
        $leaveTypes = $leaveTypeRepo->findBy(['isDelete' => false]);
        $settings = $this->settingHelper->getSettingConfiguration();
        $renewalMonth = $settings['renewMonth'] ?? self::DEFAULT_RENEWAL_MONTH;
        $today = new \DateTimeImmutable();
        $renewalDate = \DateTimeImmutable::createFromFormat(
            'F j Y',
            "{$renewalMonth} 1 {$today->format('Y')}"
        );

        if ($today < $renewalDate) {
            $renewalDate = $renewalDate->modify('-1 year');
        }
        $currentLeaveYear = (int) $renewalDate->format('Y');

        $createdCount = 0;

        foreach ($employees as $employee) {
            foreach ($leaveTypes as $leaveType) {
                $leaveBalance = $leaveBalanceRepo->findOneBy([
                    'employee' => $employee,
                    'leaveType' => $leaveType,
                    'year' => $currentLeaveYear,
                ]);

                if (!$leaveBalance) {
                    $totalDays = $this->calculateLeaveDays($employee, $leaveType);

                    if ($totalDays > 0.0) {
                        $leaveBalance = new LeaveBalance();
                        $leaveBalance->setEmployee($employee);
                        $leaveBalance->setLeaveType($leaveType);
                        $leaveBalance->setYear($currentLeaveYear);

                        $leaveBalance->setTotalDays($totalDays);
                        $leaveBalance->setUsedDays(0.0);
                        $leaveBalance->updateRemainingDays();

                        $this->entityManager->persist($leaveBalance);
                        $createdCount++;
                    }
                }
            }
        }

        if ($createdCount > 0) {
            $this->entityManager->flush();
        }

        return $createdCount;
    }

    /**
     * @throws \DateMalformedStringException
     */
    private function calculateLeaveDays(MasterEmployee $employee, LeaveType $leaveType): float
    {
        $settings = $this->settingHelper->getSettingConfiguration();
        $renewalMonth = $settings['renewMonth'] ?? self::DEFAULT_RENEWAL_MONTH;
        $today = new \DateTimeImmutable();
        $renewalDate = \DateTimeImmutable::createFromFormat(
            'F j Y',
            "{$renewalMonth} 1 {$today->format('Y')}"
        );

        if ($today < $renewalDate) {
            $renewalDate = $renewalDate->modify('-1 year');
        }

        if ($leaveType->getName() === self::ADDON_LEAVE_NAME) {
            return $this->calculateAddonLeaves($employee, $leaveType->getMaxDays(), $leaveType, $renewalDate);
        }

        if (in_array($leaveType->getName(), [self::SICK_LEAVE_NAME, self::CASUAL_LEAVE_NAME])) {
            return $this->calculateSLorCLLeaves($employee, $leaveType, $renewalDate);
        }

        return (float) $leaveType->getMaxDays();
    }

    private function calculateAddonLeaves(MasterEmployee $employee, int $maxDays, LeaveType $leaveType, \DateTimeImmutable $renewalDate): float
    {
        $appliedFrom = $leaveType->getAppliedFrom();

        if ($appliedFrom === 'JD') {
            $joiningDate = $employee->getJoiningDate();
        } else {
            $joiningDate = $employee->getConfirmationDate();
        }
        if (!$joiningDate instanceof \DateTimeInterface) {
            return 0.0;
        }

        $yearsOfService = $renewalDate->diff($joiningDate)->y;
        return min($yearsOfService, $maxDays);
    }

    /**
     * @throws \DateMalformedStringException
     */
    private function calculateSLorCLLeaves(MasterEmployee $employee, LeaveType $leaveType, \DateTimeImmutable $renewalDate): float
    {
        $appliedFrom = $leaveType->getAppliedFrom();

        if ($appliedFrom === 'JD') {
            $referenceDate = $employee->getJoiningDate();
        } else {
            $referenceDate = $employee->getConfirmationDate();
        }

        if (!$referenceDate instanceof \DateTimeInterface) {
            return 0.0;
        }

        $yearsSinceReference = $renewalDate->diff($referenceDate)->y;
        if ($yearsSinceReference < 1) {
            $nextRenewalDate = $renewalDate->modify('+1 year');

            $dayOfReferenceDate = (int) $referenceDate->format('d');
            $includeCurrentMonth = $dayOfReferenceDate <= 15;

            $startDateForCalculation = $referenceDate;
            if (!$includeCurrentMonth) {
                /** @var \DateTime $referenceDate */
                $startDateForCalculation = $referenceDate->modify('first day of next month');
            }

            if ($startDateForCalculation > $renewalDate) {
                $interval = $startDateForCalculation->diff($nextRenewalDate);
                $remainingMonths = $interval->m + ($interval->y * self::MONTHS_PER_YEAR);

                $leavePerMonth = $leaveType->getMaxDays() / self::MONTHS_PER_YEAR;
                $totalDays = $remainingMonths * $leavePerMonth;
                return min($totalDays, $leaveType->getMaxDays());
            }

            return $leaveType->getMaxDays();
        }

        return $leaveType->getMaxDays();
    }
}