<!-- Left Panel -->
<aside id="left-panel" class="left-panel">
    <nav class="navbar navbar-expand-sm navbar-default">
        <div id="main-menu" class="main-menu collapse navbar-collapse">
            <ul class="nav navbar-nav">
                <li class="{% if app.request.attributes.get('_route') == 'admin_dashboard' %}active{% endif %}">
                    <a href="{{ url('admin_dashboard') }}"><i class="menu-icon fa fa-tachometer"></i>Dashboard </a>
                </li>

                <li class="menu-item-has-children dropdown
                    {% if app.request.attributes.get('_route') in ['report', 'remaining','api_teamlogger_manual_entries'] %}active show{% endif %}">
                    <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                        <i class="menu-icon fa fa-table"></i>Productivity Tracker
                    </a>
                    <ul class="sub-menu children dropdown-menu
                                        {% if app.request.attributes.get('_route') in ['report', 'remaining','api_teamlogger_manual_entries'] %}show{% endif %}">
{#                    <li class="{% if app.request.attributes.get('_route') == 'employee' %}active{% endif %}">#}
{#                            <i class="fa fa-users"></i><a href="{{ path('employee') }}">Employee </a>#}
{#                        </li>#}
                        <li class="{% if app.request.attributes.get('_route') == 'report' %}active{% endif %}">
                            <i class="fa fa-list"></i><a href="{{ url('report') }}">Log Hours </a></li>
                        <li class="{% if app.request.attributes.get('_route') == 'remaining' %}active{% endif %}">
                            <i class="fa fa-times-circle"></i><a href="{{ url('remaining') }}">Reject Log
                            </a></li>
                        <li class="{% if app.request.attributes.get('_route') == 'api_teamlogger_manual_entries' %}active{% endif %}">
                            <i class="fa fa-edit"></i><a href="{{ url('api_teamlogger_manual_entries') }}">Manual Request</a></li>
                    </ul>
                </li>
                <li class="menu-item-has-children dropdown
                    {% if app.request.attributes.get('_route') in ['setting', 'email_config_page','email_template_list','report_config_page','hardware_type','leave_policy','notification_config_page','admin_tracking_config_index'] %}active show{% endif %}">
                    <a href="#" class="dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"> <i class="menu-icon fa fa-cog"></i>Setting</a>
                    <ul class="sub-menu children dropdown-menu
                             {% if app.request.attributes.get('_route') in ['setting', 'email_config_page','email_template_list','report_config_page','hardware_type','leave_policy','notification_config_page','admin_tracking_config_index'] %}show{% endif %}">
                        <li class="{% if app.request.attributes.get('_route') == 'setting' %}active{% endif %}">
                            <i class="fa fa-table"></i><a href="{{ url('setting') }}">Teamlogger</a></li>
                        <li class="{% if app.request.attributes.get('_route') == 'email_config_page' %}active{% endif %}">
                            <i class="fa fa-envelope"></i><a href="{{ url('email_config_page') }}">Mailer </a></li>
                        <li class="{% if app.request.attributes.get('_route') == 'email_template_list' %}active{% endif %}">
                            <i class="fa fa-file-text"></i><a href="{{ url('email_template_list') }}">Email Template</a></li>
                        <li class="{% if app.request.attributes.get('_route') == 'report_config_page' %}active{% endif %}">
                            <i class="fa fa-bell"></i><a href="{{ url('report_config_page') }}">Notification</a></li>
                        <li class="{% if app.request.attributes.get('_route') == 'hardware_type' %}active{% endif %}">
                            <i class="fa fa-microchip"></i><a href="{{ url('hardware_type') }}">Hardware Types</a></li>
                        <li class="{% if app.request.attributes.get('_route') == 'notification_config_page' %}active{% endif %}">
                            <i class="fa fa-tasks"></i><a href="{{ url('notification_config_page') }}">Task Mailer</a></li>
                        <li class="{% if app.request.attributes.get('_route') == 'leave_policy' %}active{% endif %}">
                            <i class="fa fa-bell"></i><a href="{{ url('leave_policy') }}">Leave policy</a></li>
                        <li class="{% if app.request.attributes.get('_route') == 'admin_tracking_config_index' %}active{% endif %}">
                            <i class="fa fa-desktop"></i><a href="{{ url('admin_tracking_config_index') }}">Employee Tracking</a></li>
                    </ul>
                </li>
                <li class="menu-item-has-children dropdown
                 {% if app.request.attributes.get('_route') in ['summary','admin_employee_activity','admin_generate_360_report_form','admin_attendance_report'] %}active show{% endif %}">
                    <a href="#" class="dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"> <i class="menu-icon fa fa-file"></i>Reports</a>
                    <ul class="sub-menu children dropdown-menu
                         {% if app.request.attributes.get('_route') in ['summary','admin_employee_activity','admin_generate_360_report_form','admin_attendance_report'] %}show{% endif %}">
{#                        <li class="{% if app.request.attributes.get('_route') == 'report' %}active{% endif %}">#}
{#                            <i class="fa fa-list"></i><a href="{{ url('report') }}">Work hours Justification</a></li>#}
{#                        <li class="{% if app.request.attributes.get('_route') == 'remaining' %}active{% endif %}">#}
{#                            <i class="fa fa-times-circle"></i><a href="{{ url('remaining') }}">Rejected Justification#}
{#                            </a></li>#}
{#                        <li class="{% if app.request.attributes.get('_route') == 'api_teamlogger_manual_entries' %}active{% endif %}">#}
{#                            <i class="fa fa-edit"></i><a href="{{ url('api_teamlogger_manual_entries') }}">Manual Request</a></li>#}
                        <li class="{% if app.request.attributes.get('_route') == 'summary' %}active{% endif %}">
                            <i class="fa fa-clock-o"></i><a href="{{ path('summary') }}"> Employee Report</a>
                        </li>
                        <li class="{% if app.request.attributes.get('_route') == 'admin_employee_activity' %}active{% endif %}">
                            <i class="fa fa-file"></i><a href="{{ path('admin_employee_activity') }}"> Employee Activity</a>
                        </li>
                        <li class="{% if app.request.attributes.get('_route') == 'admin_generate_360_report_form' %}active{% endif %}">
                            <i class="fa fa-file"></i><a href="{{ path('admin_generate_360_report_form') }}"> Employee 360° report </a>
                        </li>
                        <li class="{% if app.request.attributes.get('_route') == 'admin_attendance_report' %}active{% endif %}">
                            <i class="fa fa-file"></i><a href="{{ path('admin_attendance_report') }}"> Attendance report </a>
                        </li>
{#                        <li class="{% if app.request.attributes.get('_route') == 'weekly_hours' %}active{% endif %}">#}
{#                            <i class="fa fa-calendar"></i><a href="{{ url('weekly_hours') }}">Weekly Report</a></li>#}
                    </ul>
                </li>

                <li class="menu-item-has-children dropdown
                    {% if app.request.attributes.get('_route') in ['master_employee', 'department'] %}active show{% endif %}">
                <a href="#" class="dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"> <i class="menu-icon fa fa-user-plus"></i>User</a>
                    <ul class="sub-menu children dropdown-menu
                       {% if app.request.attributes.get('_route') in  ['master_employee', 'department'] %}show{% endif %}">
                    <li class="{% if app.request.attributes.get('_route') == 'master_employee' %}active{% endif %}">
                             <i class="fa fa-user"></i><a href="{{ url('master_employee') }}">Employee</a></li>
                        <li class="{% if app.request.attributes.get('_route') == 'department' %}active{% endif %}">
                             <i class="fa fa-building"></i><a href="{{ url('department') }}">Department</a></li>
                    </ul>
                </li>

                <li class="menu-item-has-children dropdown
                    {% if app.request.attributes.get('_route') in ['admin_payroll_index','admin_extrapay','admin_employee_arrears','admin_payroll_pdf'] %}active show{% endif %}">
                    <a href="#" class="dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"> <i class="menu-icon fa fa-rupee"></i>Payroll</a>
                    <ul class="sub-menu children dropdown-menu
                       {% if app.request.attributes.get('_route') in ['admin_payroll_index','admin_extrapay','admin_employee_arrears','admin_payroll_pdf'] %}show{% endif %}">
                        <li class="{% if app.request.attributes.get('_route') == 'admin_payroll_index' %}active{% endif %}">
                            <i class="fa fa-gear"></i><a href="{{ url('admin_payroll_index') }}">Generate Payroll</a></li>
                        <li class="{% if app.request.attributes.get('_route') == 'admin_extrapay' %}active{% endif %}">
                            <i class="fa fa-envelope"></i><a href="{{ url('admin_extrapay') }}">Extra Pay</a></li>
                        <li class="{% if app.request.attributes.get('_route') == 'admin_employee_arrears' %}active{% endif %}">
                            <i class="fa fa-envelope"></i><a href="{{ url('admin_employee_arrears') }}">Arrears Pay</a></li>
                        <li class="{% if app.request.attributes.get('_route') == 'admin_payroll_pdf' %}active{% endif %}">
                            <i class="fa fa-envelope"></i><a href="{{ url('admin_payroll_pdf') }}">Payroll PDF</a></li>
                    </ul>
                </li>

                <li class="menu-item-has-children dropdown
                    {% if app.request.attributes.get('_route') in ['form_template', 'admin_employee_history','admin_employee_history_self_evaluation'] %}active show{% endif %}">
                <a href="#" class="dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"> <i class="menu-icon fa fa-wpforms"></i>Forms</a>
                    <ul class="sub-menu children dropdown-menu
                       {% if app.request.attributes.get('_route') in ['form_template', 'admin_employee_history','admin_employee_history_self_evaluation'] %}show{% endif %}">
                    <li class="{% if app.request.attributes.get('_route') == 'form_template' %}active{% endif %}">
                               <i class="fa fa-comment"></i><a href="{{ url('form_template') }}">Performance Feedback</a></li>
                        <li class="{% if app.request.attributes.get('_route') == 'admin_employee_history' %}active{% endif %}">
                               <i class="fa fa-history"></i><a href="{{ url('admin_employee_history') }}">FeedBack Review</a></li>
                        <li class="{% if app.request.attributes.get('_route') == 'admin_employee_history_self_evaluation' %}active{% endif %}">
                            <i class="fa fa-history"></i><a href="{{ url('admin_employee_history_self_evaluation') }}">Self Evaluation Review</a></li>
                    </ul>
                </li>

                <li class="menu-item-has-children dropdown
                    {% if app.request.attributes.get('_route') in ['holiday_list','leave_type_list','leave_balance_list','admin_leave_request_list','leave_list'] %}active show{% endif %}">
                    <a href="#" class="dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"> <i class="menu-icon fa fa-user-plus"></i>Leave</a>
                    <ul class="sub-menu children dropdown-menu
                       {% if app.request.attributes.get('_route') in  ['holiday_list','leave_type_list','leave_balance_list','admin_leave_request_list','leave_list'] %}show{% endif %}">
                        <li class="{% if app.request.attributes.get('_route') == 'holiday_list' %}active{% endif %}">
                            <i class="fa fa-user"></i><a href="{{ url('holiday_list') }}">Holiday</a></li>
                        <li class="{% if app.request.attributes.get('_route') == 'leave_type_list' %}active{% endif %}">
                            <i class="fa fa-user"></i><a href="{{ url('leave_type_list') }}">Leave Type</a></li>
                        <li class="{% if app.request.attributes.get('_route') == 'leave_balance_list' %}active{% endif %}">
                            <i class="fa fa-user"></i><a href="{{ url('leave_balance_list') }}">Leave Balance Logs</a></li>
                        <li class="{% if app.request.attributes.get('_route') == 'admin_leave_request_list' %}active{% endif %}">
                            <i class="fa fa-user"></i><a href="{{ url('admin_leave_request_list') }}">Leave Request</a></li>
                        <li class="{% if app.request.attributes.get('_route') == 'leave_list' %}active{% endif %}">
                            <i class="fa fa-user"></i><a href="{{ url('leave_list') }}">Leave Balance</a></li>
                    </ul>
                </li>


                <li class="menu-item-has-children dropdown
                    {% if app.request.attributes.get('_route') in ['admin_document_index','admin_announcement','social_media_post_list'] %}active show{% endif %}">
                    <a href="#" class="dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"> <i class="menu-icon fa fa-building"></i>Company</a>
                    <ul class="sub-menu children dropdown-menu
                       {% if app.request.attributes.get('_route') in ['admin_document_index','admin_announcement','social_media_post_list'] %}show{% endif %}">
                        <li class="{% if app.request.attributes.get('_route') == 'admin_document_index' %}active{% endif %}">
                            <i class="fa fa-file"></i><a href="{{ url('admin_document_index') }}">Documents</a></li>
                        <li class="{% if app.request.attributes.get('_route') == 'admin_announcement' %}active{% endif %}">
                            <i class="fa fa-bullhorn"></i><a href="{{ url('admin_announcement') }}">Announcement</a></li>
                        <li class="{% if app.request.attributes.get('_route') == 'social_media_post_list' %}active{% endif %}">
                            <i class="fa fa-instagram"></i><a href="{{ url('social_media_post_list') }}">Social Media</a></li>
                    </ul>
                </li>
                <li class="{% if app.request.attributes.get('_route') == 'hardware' %}active{% endif %}">
                    <a href="{{ url('hardware') }}"><i class="menu-icon fa fa-microchip"></i>Hardware </a>
                </li>
                <li class="{% if app.request.attributes.get('_route') == 'project' %}active{% endif %}">
                    <a href="{{ url('project') }}"><i class="menu-icon fa fa-product-hunt"></i>Project </a>
                </li>
                <li class="{% if app.request.attributes.get('_route') == 'admin_bug_index' %}active{% endif %}">
                    <a href="{{ url('admin_bug_index') }}"><i class="menu-icon fa fa-bug"></i>Bugs</a>
                </li>
                <li class="menu-item-has-children dropdown
                    {% if app.request.attributes.get('_route') in ['admin_screenshot_report','admin_screenshot_view','admin_screenshot_full_report'] %}active show{% endif %}">
                    <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                        <i class="menu-icon fa fa-table"></i>Teamlogger
                    </a>
                    <ul class="sub-menu children dropdown-menu
                         {% if app.request.attributes.get('_route') in ['admin_screenshot_report','admin_screenshot_view','admin_screenshot_full_report'] %}show{% endif %}">
                        <li class="{% if app.request.attributes.get('_route') == 'admin_screenshot_report' %}active{% endif %}">
                            <i class="fa fa-camera"></i><a href="{{ path('admin_screenshot_report') }}">Screenshots</a>
                        </li>
                    </ul>
                </li>
            </ul>
        </div>
    </nav>
</aside>
<!-- /#left-panel -->
