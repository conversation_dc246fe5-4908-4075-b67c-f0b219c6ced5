{% extends 'admin/dashboard/base.html.twig' %}

{% block title %}Form Sections - {{ template.name }}{% endblock %}
{% block body %}
    {% include 'admin/partials/breadcrumbs.html.twig' with {
        title: 'Forms',
        breadcrumbs: [
            { 'label': 'Dashboard', 'url': url('admin_dashboard') },
            { 'label': 'Performance Feedback', 'url': path('form_template') },
            { 'label': template.name ~ ' Form', 'url': path('form_section_index', { 'template_id': template.id }) }
        ],
        active: section.id is defined ? 'Edit Form Section' : 'Create Form Section'
    } %}

    <div class="content">
        <div class="animated fadeIn">
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <strong class="card-title">
                                {{ section.id is defined ? 'Edit Form Section' : 'Create Form Section' }}
                            </strong>
                            <div class="d-flex align-items-center">
                                <a href="{{ path('form_section_index', { 'template_id': template.id }) }}" class="btn btn-sm btn-outline-secondary mr-2">
                                    <i class="fa fa-list mr-1"></i>Back to List
                                </a>
                                {% if section.id is defined and form is not defined %}
                                    <a href="{{ path('form_section_edit', { 'id': section.id, 'template_id': template.id }) }}" class="btn btn-sm btn-primary">
                                        <i class="fa fa-edit mr-1"></i>Edit Form Section
                                    </a>
                                {% endif %}
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="section-form">
                                {% if form is defined %}
                                    {{ form_start(form, { 'attr': { 'class': 'needs-validation use-loader', 'novalidate': 'novalidate' } }) }}
                                    <div class="form-section">
{#                                        <h5 class="section-header">#}
{#                                            <i class="fa fa-list-alt mr-2"></i>Form Section Information#}
{#                                        </h5>#}
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.name, 'Section Name', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-list"></i></span>
                                                        </div>
                                                        {{ form_widget(form.name, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter section name', 'required': 'required' } }) }}
                                                    </div>
                                                    {{ form_errors(form.name) }}
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.description, 'Description', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-info-circle"></i></span>
                                                        </div>
                                                        {{ form_widget(form.description, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter description' } }) }}
                                                    </div>
                                                    {{ form_errors(form.description) }}
                                                    <small class="form-text text-muted">Provide a brief description of the section</small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.status, 'Status', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-toggle-on"></i></span>
                                                        </div>
                                                        {{ form_widget(form.status, { 'attr': { 'class': 'form-control', 'aria-label': 'Select Status' } }) }}
                                                    </div>
                                                    {{ form_errors(form.status) }}
                                                    <small class="form-text text-muted">Select the section status (Active/Inactive)</small>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.orderNumber, 'Order Number', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-sort-numeric-asc"></i></span>
                                                        </div>
                                                        {{ form_widget(form.orderNumber, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter order number' } }) }}
                                                    </div>
                                                    {{ form_errors(form.orderNumber) }}
                                                    <small class="form-text text-muted">Set the display order of the section</small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
{#                                            <div class="col-md-6">#}
{#                                                <div class="mb-3">#}
{#                                                    {{ form_label(form.isMandatory, 'Required Section', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}#}
{#                                                    <div class="input-group">#}
{#                                                        <div class="input-group-prepend">#}
{#                                                            <span class="input-group-text">#}
{#                                                                {{ form_widget(form.isMandatory, { 'attr': { 'class': 'custom-checkbox', 'aria-label': 'Checkbox for Required Section' } }) }}#}
{#                                                            </span>#}
{#                                                        </div>#}
{#                                                        <div class="form-control checkbox-label">#}
{#                                                            {{ form_label(form.isMandatory, null, { 'label_attr': { 'class': 'ml-2' } }) }}#}
{#                                                        </div>#}
{#                                                    </div>#}
{#                                                    {{ form_errors(form.isMandatory) }}#}
{#                                                    <small class="form-text text-muted">Check if this section is mandatory</small>#}
{#                                                </div>#}
{#                                            </div>#}
                                            <div class="col-md-6">
                                            <div class="mb-3">
                                                    <label class="form-label control-label">Section Icon</label>
                                                    <div class="input-group">
                                                        {{ form_widget(form.icon, { 'attr': { 'class': 'form-control icon-select' } }) }}
                                                        <span class="input-group-text" style="background-color: #f8f9fa; border: 1px solid #ced4da; display: flex; align-items: center; justify-content: center; font-size: 1.2rem; padding: 8px 12px; min-width: 45px;">
                                                            <i id="icon-preview"></i>
                                                        </span>
                                                    </div>
                                                    {{ form_errors(form.icon) }}
                                                    <small class="form-text text-muted">Select an icon for the section (e.g., fa-list, fa-star)</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="action-footer mt-4">
                                        <a href="{{ path('form_section_index', { 'template_id': template.id }) }}" class="btn btn-secondary mr-2">
                                            Cancel
                                        </a>
                                        <button type="submit" class="btn btn-primary pr-3 pl-3">
                                            {{ section.id is defined ? 'Update Form Section' : 'Create Form Section' }}
                                        </button>
                                    </div>
                                    {{ form_end(form) }}
                                {% else %}
                                    <!-- View Mode -->
                                    <div class="form-section employee-details-section">
                                        <div class="details-section">
                                            <h5 class="section-header">
                                                <i class="fa fa-list-alt mr-2"></i>Form Section Information
                                            </h5>
                                            <div class="details-container">
                                                <div class="detail-row">
                                                    <div class="detail-item col-md-4"><strong class="detail-label">Section Name:</strong></div>
                                                    <div class="detail-item col-md-8">{{ section.name ?: 'N/A' }}</div>
                                                </div>
                                                <div class="detail-row">
                                                    <div class="detail-item col-md-4"><strong class="detail-label">Description:</strong></div>
                                                    <div class="detail-item col-md-8">{{ section.description ?: 'N/A' }}</div>
                                                </div>
                                                <div class="detail-row">
                                                    <div class="detail-item col-md-4"><strong class="detail-label">Status:</strong></div>
                                                    <div class="detail-item col-md-8">{{ section.status ? 'Active' : 'Inactive' }}</div>
                                                </div>
                                                <div class="detail-row">
                                                    <div class="detail-item col-md-4"><strong class="detail-label">Order Number:</strong></div>
                                                    <div class="detail-item col-md-8">{{ section.orderNumber ?: 'N/A' }}</div>
                                                </div>
                                                <div class="detail-row">
                                                    <div class="detail-item col-md-4"><strong class="detail-label">Required Section:</strong></div>
                                                    <div class="detail-item col-md-8">{{ section.isMandatory ? 'Yes' : 'No' }}</div>
                                                </div>
                                                <div class="detail-row">
                                                    <div class="detail-item col-md-4"><strong class="detail-label">Section Icon:</strong></div>
                                                    <div class="detail-item col-md-8">
                                                        <i class="{{ section.icon ?: 'fa-question' }}"></i> {{ section.icon ?: 'N/A' }}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="action-footer mt-4">
                                            <a href="{{ path('form_section_index', { 'template_id': template.id }) }}" class="btn btn-secondary mr-2">
                                                <i class="fa fa-list mr-1"></i>Back to List
                                            </a>
                                            <a href="{{ path('form_section_edit', { 'id': section.id, 'template_id': template.id }) }}" class="btn btn-primary">
                                                <i class="fa fa-edit mr-1"></i>Edit Form Section
                                            </a>
                                        </div>
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div> <!-- .card -->
                </div><!--/.col-->
            </div>
        </div>
    </div>
    <div class="clearfix"></div>
    <link rel="stylesheet" href="{{ asset('assets/css/Error/error.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/AdminForm/Form.css') }}">
    <script src="{{ asset('assets/js/admin/formSection.js') }}"></script>
{% endblock %}