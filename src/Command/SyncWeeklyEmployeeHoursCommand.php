<?php

namespace App\Command;

use App\Service\Admin\EmployeeHoursService;
use App\Traits\ExceptionLoggerTrait;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;

class SyncWeeklyEmployeeHoursCommand extends Command
{
    use ExceptionLoggerTrait;

    protected static $defaultName = 'app:fetch-weekly-hours';

    public function __construct(
        private readonly EmployeeHoursService $employeeHoursService,
        ParameterBagInterface $parameterBag
    ) {
        parent::__construct(self::$defaultName);
        $this->initializeLogger($parameterBag, 'weekly_employee_hours_command');
    }

    protected function configure(): void
    {
        $this
            ->setDescription('Fetch employee hours for the past week and update the database.')
            ->setHelp('This command retrieves employee hours for the past 7 days from the Teamlogger API and updates the database to ensure all data, including late-approved manual requests, is synced.')
            ->addOption(
                'start-date',
                's',
                InputOption::VALUE_OPTIONAL,
                'Start date for the week (format: YYYY-MM-DD). Defaults to 7 days ago from today.'
            );
    }

    /**
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     * @throws ClientExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $io->title('Fetching Employee Weekly Hours');

        try {
            $startDateInput = $input->getOption('start-date');
            $startDate = $startDateInput
                ? new \DateTime($startDateInput)
                : (new \DateTime())->modify('-7 days');

            $io->note(sprintf('Fetching hours from %s to today.', $startDate->format('Y-m-d')));

            $totalSuccess = 0;
            $totalFailures = 0;

            $currentDate = clone $startDate;
            $today = new \DateTime();
            while ($currentDate <= $today) {
                $io->section(sprintf('Processing date: %s', $currentDate->format('Y-m-d')));
                $result = $this->employeeHoursService->fetchAndStoreDailyHours($currentDate);

                $totalSuccess += $result['success'];
                $totalFailures += $result['failures'];

                $io->text(sprintf(
                    'Date %s - Success: %d, Failures: %d',
                    $currentDate->format('Y-m-d'),
                    $result['success'],
                    $result['failures']
                ));

                $currentDate->modify('+1 day');
            }

            $io->success(sprintf(
                'Weekly processing complete. Total Success: %d, Total Failures: %d.',
                $totalSuccess,
                $totalFailures
            ));

            return Command::SUCCESS;

        } catch (\Exception $e) {
            $this->log(
                'Error fetching and syncing weekly employee hours.',
                [
                    'error_message' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                    'trace' => $e->getTraceAsString(),
                ]
            );
            $io->error(sprintf('Failed to fetch or process weekly data. Error: %s', $e->getMessage()));
            return Command::FAILURE;
        }
    }
}