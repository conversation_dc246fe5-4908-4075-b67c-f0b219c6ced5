<?php

namespace App\Form;

use App\Entity\Department;
use App\Entity\MasterEmployee;
use App\Entity\Project;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\CallbackTransformer;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\Length;
use Symfony\Component\Validator\Constraints\PositiveOrZero;
use Symfony\Component\Validator\Constraints\Choice;
use Symfony\Component\Validator\Constraints\Regex;

class ProjectFormType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('name', TextType::class, [
                'label' => 'Project Name',
                'attr' => ['placeholder' => 'Enter project name', 'required' => 'required'],
                'constraints' => [
                    new NotBlank(['message' => 'Project name is required.']),
                    new Length([
                        'min' => 2,
                        'max' => 255,
                        'minMessage' => 'Project name must be at least {{ limit }} characters long.',
                        'maxMessage' => 'Project name cannot be longer than {{ limit }} characters.',
                    ]),
                ],
            ])
            ->add('description', TextType::class, [
                'label' => 'Description',
                'attr' => ['placeholder' => 'Enter project description'],
                'constraints' => [
                    new NotBlank(['message' => 'Description is required.']),
                    new Length([
                        'min' => 2,
                        'max' => 1000,
                        'minMessage' => 'Description must be at least {{ limit }} characters long.',
                        'maxMessage' => 'Description cannot be longer than {{ limit }} characters.',
                    ]),
                ],
            ])
            ->add('department', EntityType::class, [
                'class' => Department::class,
                'choice_label' => 'depname',
                'label' => 'Department',
                'autocomplete'=>true,
                'required' => true,
                'placeholder' => 'Select a department',
                'constraints' => [
                    new NotBlank(['message' => 'Department is required.']),
                ],
                'query_builder' => function (\Doctrine\ORM\EntityRepository $er) {
                    return $er->createQueryBuilder('d')
                        ->where('d.isDelete = :is_delete')
                        ->andWhere('d.depStatus = :dep_status')
                        ->setParameter('is_delete', false)
                        ->setParameter('dep_status', true)
                        ->orderBy('d.depName', 'ASC');
                },
            ])
            ->add('projectManager', EntityType::class, [
                'class' => MasterEmployee::class,
                'choice_label' => 'name',
                'label' => 'Project Manager',
                'autocomplete'=>true,
                'required' => true,
                'constraints' => [
                    new NotBlank(['message' => 'Project Manager is required.']),
                ],
                'query_builder' => function (\Doctrine\ORM\EntityRepository $er) {
                    return $er->createQueryBuilder('m')
                        ->where('m.teamLeader = :is_team_leader')
                        ->setParameter('is_team_leader', true)
                        ->orderBy('m.name', 'ASC');
                },
            ])
            ->add('bde', EntityType::class, [
                'class' => MasterEmployee::class,
                'choice_label' => 'name',
                'label' => 'Business Development Executive',
                'required' => true,
                'autocomplete'=>true,
                'constraints' => [
                    new NotBlank(['message' => 'Business Development Executive is required.']),
                ],
                'query_builder' => function (\Doctrine\ORM\EntityRepository $er) {
                    return $er->createQueryBuilder('m')
                        ->where('m.isDelete = :is_delete')
                        ->setParameter('is_delete', false)
                        ->orderBy('m.name', 'ASC');
                },
            ])
            ->add('clientName', TextType::class, [
                'label' => 'Client Name',
                'required' => false,
                'attr' => ['placeholder' => 'Enter client name'],
                'constraints' => [
                    new Length([
                        'max' => 255,
                        'maxMessage' => 'Client name cannot be longer than {{ limit }} characters.',
                    ]),
                ],
            ])
            ->add('clientContact', TextType::class, [
                'label' => 'Client Contact',
                'required' => false,
                'attr' => ['placeholder' => 'Enter client email or phone'],
                'constraints' => [
                    new Length([
                        'max' => 255,
                        'maxMessage' => 'Client contact cannot be longer than {{ limit }} characters.',
                    ]),
                ],
            ])
            ->add('projectCode', TextType::class, [
                'label' => 'Project Code',
                'attr' => ['placeholder' => 'e.g., PROJ-2025-001'],
                'constraints' => [
                    new NotBlank(['message' => 'Project code is required.']),
                    new Length([
                        'min' => 2,
                        'max' => 50,
                        'minMessage' => 'Project code must be at least {{ limit }} characters long.',
                        'maxMessage' => 'Project code cannot be longer than {{ limit }} characters.',
                    ]),
                    new Regex([
                        'pattern' => '/^[A-Z0-9\-]+$/',
                        'message' => 'Project code can only contain uppercase letters, numbers, and hyphens.',
                    ]),
                ],
            ])
            ->add('projectType', ChoiceType::class, [
                'label' => 'Project Type',
                'choices' => [
                    'Internal' => 'Internal',
                    'External' => 'External',
                    'R&D' => 'R&D',
                    'Client Service' => 'Client Service',
                ],
                'autocomplete'=>true,
                'constraints' => [
                    new NotBlank(['message' => 'Project type is required.']),
                    new Choice([
                        'choices' => ['Internal', 'External', 'R&D', 'Client Service'],
                        'message' => 'Please select a valid project type.',
                    ]),
                ],
            ])
            ->add('startDate', TextType::class, [
                'label' => 'Start Date',
                'required' => true,
                'attr' => [
                    'class' => 'form-control datepicker',
                    'placeholder' => 'Select purchase date',
                    'required' => 'required',
                ],
                'constraints' => [ new NotBlank(['message' => 'Start date is required.'])],
            ])
            ->add('endDate', TextType::class, [
                'label' => 'End Date',
                'attr' => [
                    'class' => 'form-control datepicker',
                    'placeholder' => 'Select purchase date',
                    'required' => 'required',
                ],
                'required' => true,
                'constraints' => [
                    new NotBlank(['message' => 'End date is required.']),
                ],
            ])
            ->add('deadline', TextType::class, [
                'label' => 'Deadline',
                'attr' => [
                    'class' => 'form-control datepicker',
                    'placeholder' => 'Select purchase date',
                    'required' => 'required',
                ],
                'required' => true,
                'constraints' => [
                    new NotBlank(['message' => 'Deadline is required.']),
                ],
            ])
            ->add('priority', ChoiceType::class, [
                'label' => 'Priority',
                'choices' => [
                    'Low' => 'Low',
                    'Medium' => 'Medium',
                    'High' => 'High',
                    'Urgent' => 'Urgent',
                ],
                'autocomplete'=>true,
                'constraints' => [
                    new NotBlank(['message' => 'Priority is required.']),
                    new Choice([
                        'choices' => ['Low', 'Medium', 'High', 'Urgent'],
                        'message' => 'Please select a valid priority.',
                    ]),
                ],
            ])
            ->add('budget', NumberType::class, [
                'label' => 'Budget',
                'required' => false,
                'attr' => ['placeholder' => 'Enter budget amount'],
                'constraints' => [
                    new PositiveOrZero(['message' => 'Budget must be a positive number or zero.']),
                ],
            ])
            ->add('budgetCurrency', ChoiceType::class, [
                'label' => 'Budget Currency',
                'choices' => [
                    'Select Currency' => '',
                    'USD' => 'USD',
                    'EUR' => 'EUR',
                    'GBP' => 'GBP',
                    'INR' => 'INR',
                ],
                'autocomplete'=>true,
                'required' => false,
                'attr' => ['class' => 'form-control'],
                'constraints' => [
                    new Choice([
                        'choices' => ['', 'USD', 'EUR', 'GBP', 'INR'],
                        'message' => 'Please select a valid currency.',
                    ]),
                ],
            ])
            ->add('status', ChoiceType::class, [
                'label' => 'Status',
                'choices' => [
                    'Not Started' => 'Not Started',
                    'Active' => 'Active',
                    'On Hold' => 'On Hold',
                    'Completed' => 'Completed',
                    'Cancelled' => 'Cancelled',
                ],
                'autocomplete'=>true,
                'constraints' => [
                    new NotBlank(['message' => 'Status is required.']),
                    new Choice([
                        'choices' => ['Not Started', 'Active', 'On Hold', 'Completed', 'Cancelled'],
                        'message' => 'Please select a valid status.',
                    ]),
                ],
            ]);

        foreach (['startDate', 'endDate', 'deadline'] as $field) {
            $builder->get($field)
                ->addModelTransformer(new CallbackTransformer(
                    function ($dateAsObject) {
                        return $dateAsObject instanceof \DateTimeInterface ? $dateAsObject->format('d-m-Y') : '';
                    },
                    function ($dateAsString) {
                        return $dateAsString ? \DateTime::createFromFormat('d-m-Y', $dateAsString) : null;
                    }
                ));
        }
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => Project::class,
        ]);
    }
}