<div class="content">
    <div class="animated fadeIn">
        <div class="row">
            <div id="flash-container"></div>
            <div id="order-loader">
                <img src="{{ asset('images/loader.svg') }}" alt="loader">
            </div>
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <strong class="card-title">Project Team</strong>
                        {% if is_granted('ROLE_ADMIN') %}
                        <a href="{{ path('admin_project_team_new', {'project_id': project_id}) }}" class="btn btn-sm btn-primary">
                            <i class="fa fa-plus mr-1"></i>Add Team Member
                        </a>
                        {% elseif is_granted('ROLE_EMPLOYEE') %}
                            <a href="{{ path('employee_project_team_new', {'project_id': project_id}) }}" class="btn btn-sm btn-primary">
                                <i class="fa fa-plus mr-1"></i>Add Team Member
                            </a>
                        {% endif %}
                    </div>
                    <div class="card-body">
                        <div class="table-stats order-table ov-h text-center">
                            <div id="project-team" class="datatable-main-wrapper">Loading...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="clearfix"></div>
<script src="{{ asset('assets/js/datatables.min.js') }}"></script>
<script src="{{ asset('bundles/datatables/js/datatables.js') }}"></script>
<script>
    $(function () {
        recordsTable = $('#project-team').initDataTables({{ datatable_settings(datatable) }});
    });
</script>
<script src="{{ asset('assets/js/datatableclearsearch.js') }}"></script>
<script src="{{ asset('assets/js/admin/common.js') }}"></script>