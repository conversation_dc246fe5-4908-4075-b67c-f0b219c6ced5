<?php

namespace App\Traits;

use Monolog\Handler\StreamHandler;
use Monolog\Level;
use Monolog\Logger;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Throwable;

trait ExceptionLoggerTrait
{
    private ?LoggerInterface $logger = null;

    /**
     * Initialize the logger with a custom log file.
     *
     * @param ParameterBagInterface $parameterBag
     * @param string $loggerName
     */
    public function initializeLogger(
        ParameterBagInterface $parameterBag,
        string $loggerName = 'brainstream'
    ): void
    {
        $logDir = $parameterBag->get('kernel.logs_dir');
        $logFilePath = sprintf('%s/%s', $logDir, $loggerName . '.log');

        $this->logger = new Logger($loggerName);
        $this->logger->pushHandler(new StreamHandler($logFilePath, Level::Error));
    }

    /**
     * Generic log method for logging messages with context.
     *
     * @param string $message
     * @param array $context
     * @return void
     */
    public function log(string $message, array $context = []): void
    {
        $this->logger?->error($message, $context);
    }

}