<?php

namespace App\Message;

readonly class TaskAndProjectNotificationMessage
{
    public function __construct(
        private string $entityType,
        private int $entityId,
        private string $action,
        private array $changes
    ) {}

    public function getEntityType(): string
    {
        return $this->entityType;
    }

    public function getEntityId(): int
    {
        return $this->entityId;
    }

    public function getAction(): string
    {
        return $this->action;
    }
    public function getChanges(): ?array
    {
        return $this->changes;
    }

}