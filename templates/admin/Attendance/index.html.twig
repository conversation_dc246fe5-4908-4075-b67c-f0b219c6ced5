{% extends 'admin/dashboard/base.html.twig' %}

{% block title %}Attendance Report{% endblock %}

{% block body %}
    {% include 'admin/partials/breadcrumbs.html.twig' with {
        breadcrumbs: [
            { 'label': 'Dashboard', 'url': path('admin_dashboard') },
        ],
        active: 'Attendance Report'
    } %}
    <div class="content">
        <div class="animated fadeIn">
            <div class="row">
                <div id="flash-container"></div>
                <div id="order-loader">
                    <img src="{{ asset('images/loader.svg') }}" alt="loader">
                </div>
                <div class="col-md-12">
                    <div id="flash-container"></div>
                    <div class="card">
                        <div class="card-header d-flex justify-content-between">
                            <strong class="card-title">Attendance Report</strong>
                            <form method="get" action="{{ path('admin_attendance_report') }}" class="form-inline use-loader">
                                {% set now = date() %}
                                {% set currentYear = now.format('Y')|number_format(0, '.', '') %}
                                {% set currentMonth = now.format('n')|number_format(0, '.', '') %}

                                <div class="form-group mr-2">
                                    <label for="year" class="mr-1">Year:</label>
                                    <select name="year" id="year" class="form-control">
                                        {% for y in range(currentYear - 5, currentYear) %}
                                            <option value="{{ y }}"
                                                    {% if filters.year is defined and filters.year == y %}
                                                        selected
                                                    {% elseif filters.year is not defined and y == currentYear %}
                                                        selected
                                                    {% endif %}
                                            >{{ y }}</option>
                                        {% endfor %}
                                    </select>
                                </div>

                                <div class="form-group mr-2">
                                    <label for="month" class="mr-1">Month:</label>
                                    <select name="month" id="month" class="form-control">
                                        {% for m in 1..currentMonth %}
                                            <option value="{{ m }}"
                                                    {% if filters.month is defined and filters.month == m %}
                                                        selected
                                                    {% elseif filters.month is not defined and m == currentMonth %}
                                                        selected
                                                    {% endif %}
                                            >
                                                {{ date(currentYear ~ '-' ~ '%02d'|format(m) ~ '-01')|date('F') }}
                                            </option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <button type="submit" class="btn btn-primary pr-3 pl-3 mr-2">Filter</button>
                                <a href="{{ path('admin_attendance_report', {'keep_filters': 0}) }}" class="btn btn-primary pr-3 pl-3">Reset</a>
                            </form>

                        </div>
                        <div class="card-body">
                            <div class="table-stats order-table ov-h text-center">
                                <div id="attendance" class="datatable-main-wrapper">Loading...</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="clearfix"></div>
    <link rel="stylesheet" href="{{ asset('assets/css/task-description.css') }}">
    <script src="{{ asset('assets/js/datatables.min.js') }}"></script>
    <script src="{{ asset('bundles/datatables/js/datatables.js') }}"></script>
<style>
    .attendance-calendar {
        font-family: Arial, sans-serif;
        margin-bottom: 20px;
    }

    .calendar-week {
        display: flex;
        border-bottom: 1px solid #eee;
    }

    .calendar-day, .calendar-day-header {
        flex: 1;
        padding: 8px;
        text-align: center;
        min-height: 50px;
        display: flex;
        flex-direction: column;
        justify-content: center;
    }

    .calendar-day-header {
        font-weight: bold;
        background-color: #f8f9fa;
    }

    .calendar-day {
        border-right: 1px solid #eee;
        position: relative;
    }

    .calendar-day:last-child {
        border-right: none;
    }

    .calendar-day.present {
        background-color: rgba(40, 167, 69, 0.1);
    }

    .calendar-day.absent {
        background-color: rgba(220, 53, 69, 0.1);
    }

    .calendar-day.no-record {
        background-color: rgba(108, 117, 125, 0.1);
    }

    .day-number {
        font-size: 12px;
        margin-bottom: 5px;
    }

    .day-status {
        font-size: 16px;
    }

    .calendar-legend {
        display: flex;
        justify-content: center;
        gap: 20px;
    }

    .calendar-legend .indicator {
        display: inline-block;
        width: 15px;
        height: 15px;
        border-radius: 50%;
        margin-right: 5px;
    }

    .calendar-legend .present {
        background-color: #28a745;
    }

    .calendar-legend .absent {
        background-color: #dc3545;
    }

    .calendar-legend .no-record {
        background-color: #6c757d;
    }
</style>
    <script>
        $(function () {
            recordsTable = $('#attendance').initDataTables({{ datatable_settings(datatable) }});
        });
    </script>
    <script src="{{ asset('assets/js/datatableclearsearch.js') }}"></script>
    <script src="{{ asset('assets/js/admin/common.js') }}"></script>
    <script src="{{ asset('assets/js/admin/attendence.js') }}"></script>
    <script>

        function loadCalendarData(employeeId, year, month, modalId) {
            const container = $(`#${modalId} .calendar-container`);
            container.html('<div class="text-center"><i class="fa fa-spinner fa-spin"></i> Loading calendar...</div>');

            $.get(`/admin/attendance-table/calendar-data?employeeId=${employeeId}&year=${year}&month=${month}`, function(data) {
                if (data.error) {
                    container.html(`<div class="alert alert-danger">${data.error}</div>`);
                    return;
                }
                renderCalendar(data, container);
            }).fail(function() {
                container.html('<div class="alert alert-danger">Failed to load calendar data</div>');
            });
        }

        function renderCalendar(data, container) {
            const daysInMonth = data.length;
            const firstDate = new Date(data[0].date);
            const firstDayOfWeek = firstDate.getDay(); // 0=Sun, 1=Mon...

            let html = '<div class="attendance-calendar">';
            html += '<div class="calendar-week calendar-header">';
            ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].forEach(day => {
                html += `<div class="calendar-day-header">${day}</div>`;
            });
            html += '</div>';

            let dayIndex = 0;
            while (dayIndex < data.length) {
                html += '<div class="calendar-week">';

                for (let i = 0; i < 7; i++) {
                    if (dayIndex === 0 && i < firstDayOfWeek) {
                        html += '<div class="calendar-day empty"></div>';
                        continue;
                    }

                    const day = data[dayIndex];
                    if (day) {
                        const date = new Date(day.date);
                        const dayNumber = date.getDate();
                        const indicator = getStatusIndicator(day.status);
                        const formattedDate = `${dayNumber.toString().padStart(2, '0')}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getFullYear()}`;
                        let tooltip = `${formattedDate}:\nStatus: ${day.status}`;
                        if (Array.isArray(day.details)) {
                            day.details.forEach(entry => {
                                tooltip += `\n- ${entry.status}: ${entry.time}`;
                            });
                        }
                        html += `
                    <div class="calendar-day ${day.status}" title="${tooltip}">
                        <div class="day-number">${dayNumber}</div>
                        <div class="day-status">${indicator}</div>
                    </div>`;
                        dayIndex++;
                    } else {
                        html += '<div class="calendar-day empty"></div>';
                    }
                }

                html += '</div>';
            }

            html += '</div>';

            // Legend
            html += `
        <div class="calendar-legend mt-3">
            <div><span class="indicator present"></span> Present</div>
            <div><span class="indicator absent"></span> Absent</div>
            <div><span class="indicator no-record"></span> Weekend/Holiday</div>
        </div>
    `;

            container.html(html);
        }

        function getStatusIndicator(status) {
            switch (status) {
                case 'present': return '🟢';
                case 'absent': return '🔴';
                case 'no-record': return '⚪';
                default: return '';
            }
        }
        $(document).on('shown.bs.modal', '.modal', function() {
            const container = $(this).find('.calendar-container');
            if (container.length) {
                const employeeId = container.data('employee-id');
                const year = container.data('year');
                const month = container.data('month');
                const modalId = $(this).attr('id');

                if (employeeId && year && month) {
                    loadCalendarData(employeeId, year, month, modalId);
                }
            }
        });
    </script>
{% endblock %}