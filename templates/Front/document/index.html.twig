{% extends 'Front/dashboard/base.html.twig' %}

{% block title %}Documents{% endblock %}

{% block body %}
    {% include 'Front/partials/breadcrumbs.html.twig' with {
        breadcrumbs: [
            { 'label': 'Dashboard', 'url': url('dashboard_employee') },
        ],
        active: 'Documents'
    } %}

    <div class="content">
        <div class="animated fadeIn">
            <div class="row">
                <div class="col-md-12">
                    <div id="flash-container"></div>
                    <div class="card">
                        <div class="card-header d-flex justify-content-between">
                            <strong class="card-title">Documents</strong>
                        </div>
                        <div class="card-body">
                            {% if documents is not empty %}
                                <table class="table">
                                    <thead>
                                    <tr class="text-center">
                                        <th class="serial">#</th>
                                        <th>Title</th>
                                        <th>Actions</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {% for document in documents %}
                                        <tr class="text-center">
                                            <th class="serial">{{ loop.index }}</th>
                                            <td class="align-middle">{{ document.title }}</td>
                                            <td class="align-middle">
                                                <a href="#"
                                                   class="bs-btn-main btn btn-primary btn-sm"
                                                   onclick="openPdfModal('{{ document.filePath | e('js') }}', '{{ document.title | e('js') }}'); return false;"
                                                   title="View">
                                                    <i class="fa fa-eye"></i>
                                                </a>
                                                <a href="{{ path('document_download', {'id': document.id}) }}"
                                                   class="bs-btn-main btn btn-primary btn-sm"
                                                   title="Download">
                                                    <i class="fa fa-download"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                    </tbody>
                                </table>
                            {% else %}
                                <p>No documents found.</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="clearfix"></div>

    <div class="modal fade" id="pdfViewerModal" tabindex="-1" aria-labelledby="pdfViewerModalLabel" aria-hidden="true">
        {% include 'CommonTemplate/modalBox.html.twig' %}
    </div>
    <link rel="stylesheet" href="{{ asset('assets/css/document/document.css') }}">
    <script src="{{ asset('assets/js/front/document.js') }}"></script>
{% endblock %}