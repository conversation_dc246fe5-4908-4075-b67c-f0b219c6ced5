<?php

namespace App\Controller\Admin;

use App\Entity\Document;
use App\Form\DocumentType;
use App\Service\Admin\DocumentService;
use App\Traits\ExceptionLoggerTrait;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Csrf\CsrfToken;
use Symfony\Component\Security\Csrf\CsrfTokenManagerInterface;

#[Route('/admin')]
class DocumentController extends AbstractController
{
    use ExceptionLoggerTrait;

    public function __construct(
        private readonly ParameterBagInterface $params,
        private readonly EntityManagerInterface $entityManager,
        private readonly DocumentService $documentService,
        private readonly CsrfTokenManagerInterface $csrfTokenManager
    ) {
        $this->initializeLogger($params, 'document_controller');
    }

    #[Route('/documents', name: 'admin_document_index', methods: ['GET', 'POST'])]
    public function index(Request $request): Response
    {
        $table = $this->documentService->createMasterDocumentTable($request);

        if ($table->isCallback()) {
            return $table->getResponse();
        }

        return $this->render('admin/document/index.html.twig', [
            'datatable' => $table,
        ]);
    }

    #[Route('/documents/new', name: 'admin_document_new', methods: ['GET', 'POST'])]
    public function new(Request $request): Response
    {
        $document = new Document();
        $form = $this->createForm(DocumentType::class, $document);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $file = $form->get('file')->getData();
            if ($file) {
                $fileName = uniqid('', true) . '.' . $file->guessExtension();
                $uploadDir = $this->params->get('uploads_directory');
                $file->move($uploadDir, $fileName);
                $document->setFilePath('/uploads/documents/' . $fileName);
            }

            $this->entityManager->persist($document);
            $this->entityManager->flush();

            $this->addFlash('success', 'Document created successfully.');
            return $this->redirectToRoute('admin_document_index');
        }

        return $this->render('admin/document/new.html.twig', [
            'form' => $form->createView(),
            'mode' => 'create',
        ]);
    }

    #[Route('/documents/edit/{id}', name: 'admin_document_edit', methods: ['GET', 'POST'])]
    public function edit(Request $request, Document $document): Response
    {
        $form = $this->createForm(DocumentType::class, $document);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $file = $form->get('file')->getData();
            if ($file) {
                // Delete old file if exists
                if ($document->getFilePath()) {
                    $oldFile = $this->params->get('uploads_directory') . '/' . basename($document->getFilePath());
                    if (file_exists($oldFile)) {
                        unlink($oldFile);
                    }
                }
                $fileName = uniqid('', true) . '.' . $file->guessExtension();
                $uploadDir = $this->params->get('uploads_directory');
                $file->move($uploadDir, $fileName);
                $document->setFilePath('/uploads/documents/' . $fileName);
            }

            $this->entityManager->flush();
            $this->addFlash('success', 'Document updated successfully.');
            return $this->redirectToRoute('admin_document_index');
        }

        return $this->render('admin/document/new.html.twig', [
            'form' => $form->createView(),
            'document' => $document,
            'mode' => 'edit',
        ]);
    }

    #[Route('/documents/view/{id}', name: 'admin_document_view', methods: ['GET'])]
    public function view(Document $document): Response
    {
        return $this->render('admin/document/new.html.twig', [
            'document' => $document,
            'mode' => 'view',
        ]);
    }

    #[Route('/documents/delete/{id}', name: 'admin_document_delete', methods: ['POST'])]
    public function delete(Request $request, Document $document): Response
    {
        $token = $request->request->get('_token');
        if (!$this->csrfTokenManager->isTokenValid(new CsrfToken('delete' . $document->getId(), $token))) {
            return $this->json([
                'success' => false,
                'message' => 'Invalid CSRF token.'
            ], Response::HTTP_FORBIDDEN);
        }
        if ($document->getFilePath()) {
            $filePath = $this->params->get('uploads_directory') . '/' . basename($document->getFilePath());
            if (file_exists($filePath)) {
                unlink($filePath);
            }
        }
        $this->entityManager->remove($document);
        $this->entityManager->flush();

        return $this->json([
            'success' => true,
            'message' => 'Document has been deleted.'
        ], Response::HTTP_OK);
    }
}