<?php
namespace App\Form;

use App\Entity\MasterEmployee;
use App\Entity\Project;
use App\Entity\ProjectTeam;
use App\Entity\Task;
use Doctrine\ORM\EntityRepository;
use FOS\CKEditorBundle\Form\Type\CKEditorType;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Validator\Constraints\Length;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\PositiveOrZero;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class TaskType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        if ($options['self_assignment'] ?? false) {
            $employee = $options['employee'] ?? null;

            if (!$employee instanceof MasterEmployee) {
                throw new \InvalidArgumentException('Employee is required for self-assignment form.');
            }

            $builder->add('project', EntityType::class, [
                'label' => 'Project',
                'class' => Project::class,
                'choice_label' => 'name',
                'required' => true,
                'query_builder' => function (EntityRepository $er) use ($employee) {
                    return $er->createQueryBuilder('p')
                        ->innerJoin(ProjectTeam::class, 'pt', 'WITH', 'pt.project = p')
                        ->andWhere('pt.teamMember = :employee')
                        ->setParameter('employee', $employee)
                        ->orderBy('p.name', 'ASC');
                },
                'attr' => [
                    'class' => 'form-control',
                ],
            ]);
        }
        $builder
            ->add('title', TextType::class, [
                'label' => 'Task Title',
                'required' => true,
                'constraints' => [
                    new NotBlank([
                        'message' => 'Task title is required.',
                    ]),
                    new Length([
                        'min' => 2,
                        'max' => 255,
                        'minMessage' => 'Task title must be at least {{ limit }} characters long',
                        'maxMessage' => 'Task title cannot be longer than {{ limit }} characters',
                    ]),
                ],
                'attr' => [
                    'maxlength' => 255,
                    'class' => 'form-control',
                    'data-validation' => 'required|min:2|max:255',
                    'placeholder' => 'Enter task title',
                ],
            ])
            ->add('description', CKEditorType::class, [
                'constraints' => [new NotBlank()],
                'row_attr' => [
                    'class' => 'col-md-12'
                ],
                'attr' => ['rows' => '10'],
            ])
            ->add('priority', ChoiceType::class, [
                'label' => 'Priority',
                'autocomplete'=>true,
                'choices' => [
                    'Low' => 'LOW',
                    'Medium' => 'MEDIUM',
                    'High' => 'HIGH',
                ],
                'required' => true,
                'constraints' => [
                    new NotBlank([
                        'message' => 'Please select a priority.',
                    ]),
                ],
                'attr' => [
                    'class' => 'form-control',
                    'data-validation' => 'required',
                ],
            ])
            ->add('status', ChoiceType::class, [
                'label' => 'Status',
                'autocomplete'=>true,
                'choices' => [
                    'New' => 'NEW',
                    'In Progress' => 'IN_PROGRESS',
                    'Completed' => 'COMPLETED',
                ],
                'required' => true,
                'constraints' => [
                    new NotBlank([
                        'message' => 'Please select a status.',
                    ]),
                ],
                'attr' => [
                    'class' => 'form-control',
                    'data-validation' => 'required',
                ],
            ])
            ->add('estimatedHours', NumberType::class, [
                'label' => 'Estimated Hours',
                'required' => true,
                'scale' => 2,
                'constraints' => [
                    new NotBlank([
                        'message' => 'Estimated hours are required.',
                    ]),
                    new PositiveOrZero([
                        'message' => 'Estimated hours must be zero or positive.',
                    ]),
                ],
                'attr' => [
                    'class' => 'form-control',
                    'data-validation' => 'required|min:0',
                    'placeholder' => 'Enter estimated hours',
                    'type' => 'number',
                    'step' => '0.1'
                ],
            ]);
       if (!($options['self_assignment'] ?? false)) {
           $builder->add('assignedTo', EntityType::class, [
               'label' => 'Assigned To',
               'class' => MasterEmployee::class,
               'choice_label' => 'name',
               'autocomplete'=>true,
               'required' => false,
               'placeholder' => 'Select an employee',
               'query_builder' => function (\Doctrine\ORM\EntityRepository $er) {
                   return $er->createQueryBuilder('m')
                       ->where('m.isDelete = :is_delete')
                       ->setParameter('is_delete', false)
                       ->orderBy('m.name', 'ASC');
               },
               'attr' => [
                   'class' => 'form-control',
               ],
           ]);
       }
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => Task::class,
            'self_assignment' => false,
            'employee' => null,
        ]);
    }
}