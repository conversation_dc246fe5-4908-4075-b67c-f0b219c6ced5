<?php

namespace App\Repository;

use App\Entity\Attendance;
use App\Entity\MasterEmployee;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Attendance>
 */
class AttendanceRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Attendance::class);
    }

    public function findByEmployeeAndDateRange(MasterEmployee $employee, \DateTime $startDate, \DateTime $endDate): array
    {
        return $this->createQueryBuilder('a')
            ->where('a.employee = :employee')
            ->andWhere('a.timestamp >= :startDate')
            ->andWhere('a.timestamp <= :endDate')
            ->setParameter('employee', $employee)
            ->setParameter('startDate', $startDate->format('Y-m-d H:i:s'))
            ->setParameter('endDate', $endDate->format('Y-m-d H:i:s'))
            ->getQuery()
            ->getResult();
    }

    public function findTodayPunch(MasterEmployee $employee, string $status): ?Attendance
    {
        $todayStart = (new \DateTimeImmutable('today'))->setTime(0, 0);
        $todayEnd = (new \DateTimeImmutable('tomorrow'))->setTime(0, 0);

        return $this->createQueryBuilder('a')
            ->where('a.employee = :employee')
            ->andWhere('a.status = :status')
            ->andWhere('a.timestamp BETWEEN :start AND :end')
            ->setParameter('employee', $employee)
            ->setParameter('status', $status)
            ->setParameter('start', $todayStart)
            ->setParameter('end', $todayEnd)
            ->getQuery()
            ->getOneOrNullResult();
    }
    public function findTodayOfficeInPunch(MasterEmployee $employee): ?Attendance
    {
        return $this->findTodayPunch($employee, 'Office In');
    }

    public function findTodayAttendance(MasterEmployee $employee): array
    {
        $today = new \DateTimeImmutable('today');
        $tomorrow = new \DateTimeImmutable('tomorrow');

        return $this->createQueryBuilder('a')
            ->where('a.employee = :employee')
            ->andWhere('a.timestamp >= :today')
            ->andWhere('a.timestamp < :tomorrow')
            ->setParameter('employee', $employee)
            ->setParameter('today', $today)
            ->setParameter('tomorrow', $tomorrow)
            ->orderBy('a.timestamp', 'DESC')
            ->getQuery()
            ->getResult();
    }

    public function hasPunchedOfficeInToday(MasterEmployee $employee): bool
    {
        $todayStart = new \DateTimeImmutable('today');
        $todayEnd = new \DateTimeImmutable('tomorrow');

        return $this->createQueryBuilder('a')
                ->select('1')
                ->where('a.employee = :employee')
                ->andWhere('a.status = :status')
                ->andWhere('a.timestamp >= :start AND a.timestamp < :end')
                ->setParameter('employee', $employee)
                ->setParameter('status', 'Office In')
                ->setParameter('start', $todayStart)
                ->setParameter('end', $todayEnd)
                ->setMaxResults(1)
                ->getQuery()
                ->getOneOrNullResult() !== null;
    }

    public function findAttendanceByEmployeeAndDateRange(MasterEmployee $employee, \DateTimeInterface $dateStart, \DateTimeInterface $dateEnd): array
    {
        return $this->createQueryBuilder('a')
            ->where('a.employee = :employee')
            ->andWhere('a.timestamp BETWEEN :start AND :end')
            ->setParameter('employee', $employee)
            ->setParameter('start', $dateStart)
            ->setParameter('end', $dateEnd)
            ->getQuery()
            ->getResult();
    }
    
}