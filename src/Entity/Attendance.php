<?php

namespace App\Entity;

use App\Repository\AttendanceRepository;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity(repositoryClass: AttendanceRepository::class)]
class Attendance
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\ManyToOne(targetEntity: MasterEmployee::class, inversedBy: 'attendances')]
    #[ORM\JoinColumn(nullable: false)]
    #[Assert\NotNull(message: "Employee is required")]
    private ?MasterEmployee $employee = null;

    #[ORM\Column(type: "datetime")]
    #[Assert\NotNull(message: "Timestamp is required")]
    private ?\DateTimeInterface $timestamp = null;

    #[ORM\Column(length: 50)]
    #[Assert\NotNull(message: "Status is required")]
    #[Assert\Choice(choices: ["Office In", "Going out for Lunch", "Back from Lunch", "Leaving for the Day"], message: "Invalid attendance status")]
    private ?string $status = null;

    #[ORM\Column(type: "datetime", nullable: true)]
    private ?\DateTimeInterface $exitTimestamp = null;


    public function getId(): ?int
    {
        return $this->id;
    }

    public function getEmployee(): ?MasterEmployee
    {
        return $this->employee;
    }

    public function setEmployee(?MasterEmployee $employee): self
    {
        $this->employee = $employee;
        return $this;
    }

    public function getTimestamp(): ?\DateTimeInterface
    {
        return $this->timestamp;
    }

    public function setTimestamp(\DateTimeInterface $timestamp): self
    {
        $this->timestamp = $timestamp;
        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;
        return $this;
    }

    public function getExitTimestamp(): ?\DateTimeInterface
    {
        return $this->exitTimestamp;
    }

    public function setExitTimestamp(?\DateTimeInterface $exitTimestamp): self
    {
        $this->exitTimestamp = $exitTimestamp;
        return $this;
    }

}