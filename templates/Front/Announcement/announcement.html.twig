<div class="card shadow-sm border-0 rounded-lg">
    <div class="card-header border-0" style="background-color: #f78739; color: white;">
        <h5 class="card-title mb-0">
            <i class="fa fa-bullhorn me-2"></i>Important Announcements
        </h5>
    </div>
    <div class="card-body p-0">
        <div class="scrollable-events" style="max-height: 140px; overflow-y: auto;">
            {% if announcements|length > 0 %}
                <ul class="list-group list-group-flush">
                    {% for announcement in announcements %}
                        <li class="list-group-item border-0 py-3">
                            <div class="d-flex justify-content-between align-items-start">
                                <div class="d-flex align-items-start">
                                    <div class="announcement-icon me-3 rounded-circle" style="width: 45px; height: 45px; background-color: rgba(247, 135, 57, 0.15); color: #f78739; display: flex; align-items: center; justify-content: center;flex-shrink: 0;">
                                        <i class="fa {% if announcement.priority == '2' %}fa-exclamation-circle{% elseif announcement.priority == '1' %}fa-map-pin{% else %}fa-bell{% endif %}"></i>
                                    </div>
                                    <div style="padding:0px 8px">
                                        <h6 class="mb-1 d-flex align-items-start">
                                            <span title="{{ announcement.title }}"
                                                  style="display: -webkit-box; -webkit-line-clamp: 3; -webkit-box-orient: vertical; overflow: hidden; text-overflow: ellipsis; white-space: normal; max-height: 4.5em;">
                                                {{ announcement.title }}
                                            </span>
                                            {% if announcement.isImportant %}
                                                <span class="badge badge-danger ml-2" style="font-size: 0.75rem;">Imp</span>
                                            {% endif %}
                                        </h6>
                                        <small class="text-muted d-block mb-2">{{ announcement.startDate|date('M d') }}</small>
                                        <div class="announcement-meta">
                                            <small class="text-muted d-inline-block mr-3">
                                                <i class="fa fa-signal mr-1"></i>
                                                <span class="badge" style="background-color: {% if announcement.priority == '2' %}#ffcdd2{% elseif announcement.priority == '1' %}#fff3cd{% else %}#e3f2fd{% endif %}; color: {% if announcement.priority == '2' %}#d32f2f{% elseif announcement.priority == '1' %}#856404{% else %}#0d47a1{% endif %};">
                                                            {% if announcement.priority == '2' %}
                                                                High
                                                            {% elseif announcement.priority == '1' %}
                                                                Pinned
                                                            {% else %}
                                                                Normal
                                                            {% endif %}
                                                        </span>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                                <button type="button" class="bs-btn-main btn btn-primary btn-sm" style="flex-shrink: 0;" data-toggle="modal" data-target="#announcementModal{{ announcement.id }}">
                                    <i class="bs-fa-main fa fa-eye"></i>
                                </button>
                            </div>
                        </li>
                    {% endfor %}
                </ul>
            {% else %}
                <div class="text-center py-4">
                    <div class="empty-state-icon mb-3">
                        <i class="fa fa-calendar-o fa-3x text-muted"></i>
                    </div>
                    <p class="text-muted">No important announcements at this time.</p>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Modals for Announcements -->
{% for announcement in announcements %}
    <div class="modal fade" id="announcementModal{{ announcement.id }}" tabindex="-1" role="dialog" aria-labelledby="announcementModalLabel{{ announcement.id }}" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header" style="background-color: #f78739; color: white; border-bottom: none;">
                    <h5 class="modal-title" id="announcementModalLabel{{ announcement.id }}">
                        <i class="fa fa-bullhorn mr-2"></i>
                        {{ announcement.title }}
                    </h5>
                </div>
                <div class="modal-body">
                    <div class="announcement-meta-section mb-4 pb-3" style="border-bottom: 1px solid #f0f2f5;">
                        <div class="d-flex align-items-center mb-3">
                                    <span class="badge mr-2 px-2 py-1" style="background-color: {% if announcement.priority == '2' %}#ffcdd2{% elseif announcement.priority == '1' %}#fff3cd{% else %}#e3f2fd{% endif %}; color: {% if announcement.priority == '2' %}#d32f2f{% elseif announcement.priority == '1' %}#856404{% else %}#0d47a1{% endif %};">
                                        {% if announcement.priority == '2' %}
                                            <i class="fa fa-exclamation-circle mr-1"></i> High Priority
                                        {% elseif announcement.priority == '1' %}
                                            <i class="fa fa-thumbtack mr-1"></i> Pinned
                                        {% else %}
                                            <i class="fa fa-info-circle mr-1"></i> Normal
                                        {% endif %}
                                    </span>
                            {% if announcement.isImportant %}
                                <span class="badge badge-danger px-2 py-1">
                                            <i class="fa fa-exclamation-triangle mr-1"></i> Important
                                        </span>
                            {% endif %}
                        </div>
                        <!-- Dates Section -->
                        <div class="announcement-dates-section mb-4 p-3" style="background-color: #f8f9fb; border-radius: 10px;">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center">
                                        <div class="date-icon mr-3" style="width: 48px; height: 48px; background-color: rgba(247, 135, 57, 0.15); color: #f78739; border-radius: 10px; display: flex; align-items: center; justify-content: center;">
                                            <i class="fa fa-calendar-plus-o fa-lg"></i>
                                        </div>
                                        <div>
                                            <div style="font-size: 0.85rem; color: #6c757d; text-transform: uppercase; letter-spacing: 0.5px;">Start Date</div>
                                            <div style="font-weight: 600; font-size: 1.1rem;">{{ announcement.startDate|date('M d, Y') }}</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 mt-3 mt-md-0">
                                    <div class="d-flex align-items-center">
                                        <div class="date-icon mr-3" style="width: 48px; height: 48px; background-color: rgba(247, 135, 57, 0.15); color: #f78739; border-radius: 10px; display: flex; align-items: center; justify-content: center;">
                                            <i class="fa fa-calendar-check-o fa-lg"></i>
                                        </div>
                                        <div>
                                            <div style="font-size: 0.85rem; color: #6c757d; text-transform: uppercase; letter-spacing: 0.5px;">End Date</div>
                                            <div style="font-weight: 600; font-size: 1.1rem;">{{ announcement.endDate|date('M d, Y') }}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Audience Section -->
                        <div class="audience-section mb-1">
                            <div class="d-flex flex-wrap">
                            </div>
                        </div>
                    </div>
                    <div class="announcement-content-section">
                        <h6 style="font-size: 0.9rem; text-transform: uppercase; letter-spacing: 0.8px; color: #495057; border-bottom: 2px solid #f78739; display: inline-block; padding-bottom: 5px; margin-bottom: 15px;">
                            <i class="fa fa-file-text mr-2" style="color: #f78739;"></i>Announcement Details
                        </h6>
                        <div class="p-4 bg-light rounded" style="border-left: 4px solid #f78739; background-color: #f8f9fb !important;">
                            {{ announcement.content|raw }}
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
{% endfor %}
<style>
    .empty-state-icon i {
        color: #d6d6d6;
    }
    .announcement-meta-section .badge {
        font-weight: normal;
    }
    .modal-content {
        border: none;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0,0,0,0.15);
    }
    .modal-header .close:hover {
        opacity: 1;
    }
    .badge {
        border-radius: 4px;
        font-weight: 500;
    }
    @media (max-width: 576px) {
        .date-icon {
            width: 40px !important;
            height: 40px !important;
        }
    }
</style>