{% extends 'admin/dashboard/base.html.twig' %}

{% block title %}Form Sections - {{ section.name }}{% endblock %}

{% block body %}
    {% include 'admin/partials/breadcrumbs.html.twig' with {
        title: section.name ,
        breadcrumbs: [
            { 'label': 'Dashboard', 'url': url('admin_dashboard') },
            { 'label': 'Performance Feedback', 'url': path('form_template') },
            { 'label': template.name ~ ' Sections', 'url': path('form_section_index', { 'template_id': template.id }) },
        ],
        active: section.name ~ ' Section'
    } %}

    <div class="content">
        <div class="animated fadeIn">
            <div class="row">
                <div class="col-md-12">
                    <div id="flash-container"></div>
                    <div class="card">
                        <div class="card-header d-flex justify-content-between">
                            <strong class="card-title"> {{ section.name }} Fields</strong>
                            <a href="{{ path('form_field_new', {'sections_id': section.id,'template_id': template.id}) }}" class="btn btn-primary">Add New Field</a>
                        </div>
                        <div class="card-body">
                            <div class="table-stats order-table ov-h text-center ">
                                <div id="formField" class="datatable-main-wrapper">Loading...</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="clearfix"></div>
    <script src="{{ asset('assets/js/datatables.min.js') }}"></script>
    <script src="{{ asset('bundles/datatables/js/datatables.js') }}"></script>
    <script src="{{ asset('assets/js/toggle.js') }}"></script>
    <script>
        $(document).ready(function() {
             recordsTable = $('#formField').initDataTables({{ datatable_settings(datatable) | raw }})
                .then(function(dt) {
                    initializeToggleButtons();
                    dt.on('draw', function() {
                        initializeToggleButtons();
                    });
                    initializeSearch(dt);

                    return dt;
                })
                .catch(function(error) {
                    console.error('Error initializing DataTable:', error);
                });
        });
    </script>
    <script src="{{ asset('assets/js/admin/common.js') }}"></script>
{% endblock %}
