<?php

namespace App\Helper;

use App\Repository\EmployeeHoursRepository;

class TeamloggerActivePercentage
{
    public function getEmployeeActivityData(EmployeeHoursRepository $repository, $employees): array
    {
        $activityData = [];

        foreach ($employees as $employee) {
            $employeeHours = $repository->findByMasterEmployeeAndPreviousMonth($employee);
            $totalActiveMinutes = 0;
            $totalActiveSeconds = 0;
            $daysCount = count($employeeHours);

            foreach ($employeeHours as $hours) {
                $totalActiveMinutes += $hours->getActiveMinutes();
                $totalActiveSeconds += $hours->getActiveSeconds();
            }

            $averageActiveMinutes = $daysCount > 0 ? ($totalActiveMinutes / $daysCount) * 100 : 0;
            $averageActiveSeconds = $daysCount > 0 ? ($totalActiveSeconds / $daysCount) * 100 : 0;

            $activityData[$employee->getId()] = [
                'activeMinutesPercentage' => number_format($averageActiveMinutes, 2),
                'activeSecondsPercentage' => number_format($averageActiveSeconds, 2),
                'rawTotalMinutes' => $totalActiveMinutes,
                'rawTotalSeconds' => $totalActiveSeconds,
                'daysTracked' => $daysCount
            ];
        }
        return $activityData;
    }
}
