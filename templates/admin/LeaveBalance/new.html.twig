{% extends 'admin/dashboard/base.html.twig' %}

{% block body %}
    {% include 'admin/partials/breadcrumbs.html.twig' with {
        title: 'Edit Leave Balances',
        breadcrumbs: [
            { 'label': 'Dashboard', 'url': path('admin_dashboard') },
            { 'label': 'Leave Balances', 'url': path('leave_list') }
        ],
        active: 'Edit Leave Balances'
    } %}

    <div class="content">
        <div class="animated fadeIn">
            <div class="row">
                <div class="col-lg-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            {% if mode == 'edit' %}
                                <strong class="card-title"> Edit Leave Balances</strong>
                            {% endif %}
                            <div class="d-flex align-items-center">
                                <a href="{{ path('leave_list') }}" class="btn btn-sm btn-outline-secondary mr-2">
                                    <i class="fa fa-list mr-1"></i>Back to List
                                </a>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="leave-balance-form">
                                {{ form_start(form, {
                                    'action': path('leave_edit', {'employeeId': employee.id}),
                                    'method': 'POST',
                                    'attr': { 'class': 'needs-validation use-loader', 'novalidate': 'novalidate' }
                                }) }}
                                <div class="form-section">
                                    <table class="table table-bordered">
                                        <thead>
                                        <tr>
                                            <th>Leave Type</th>
                                            <th>Used Days</th>
                                            <th>Total Days</th>
                                            <th>Remaining Days</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        {% for leaveBalance in form.leaveBalances %}
                                            <tr>
                                                <td>
                                                    {{ form_label(leaveBalance.leaveType, null, { 'label_attr': { 'class': 'form-label' } }) }}
                                                    {{ form_widget(leaveBalance.leaveType, { 'attr': { 'class': 'form-control', 'disabled': 'disabled' } }) }}
                                                    {{ form_errors(leaveBalance.leaveType) }}
                                                </td>
                                                <td>
                                                    {{ form_label(leaveBalance.usedDays, null, { 'label_attr': { 'class': 'form-label' } }) }}
                                                    {{ form_widget(leaveBalance.usedDays, { 'attr': { 'class': 'form-control' } }) }}
                                                    {{ form_errors(leaveBalance.usedDays) }}
                                                </td>
                                                <td>
                                                    {{ form_label(leaveBalance.totalDays, null, { 'label_attr': { 'class': 'form-label' } }) }}
                                                    {{ form_widget(leaveBalance.totalDays, { 'attr': { 'class': 'form-control' } }) }}
                                                    {{ form_errors(leaveBalance.totalDays) }}
                                                </td>
                                                <td>
                                                    {{ form_label(leaveBalance.remainingDays, null, { 'label_attr': { 'class': 'form-label' } }) }}
                                                    {{ form_widget(leaveBalance.remainingDays, { 'attr': { 'class': 'form-control', 'readonly': 'readonly' } }) }}
                                                    {{ form_errors(leaveBalance.remainingDays) }}
                                                </td>
                                            </tr>
                                        {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                                <div class="d-flex justify-content-end mt-4">
                                    <a href="{{ path('leave_list') }}" class="btn btn-secondary mr-2">Cancel</a>
                                    <button type="submit" class="btn btn-primary pr-3 pl-3">Update Leave Balances</button>
                                </div>
                                {{ form_end(form) }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="clearfix"></div>
        <link rel="stylesheet" href="{{ asset('assets/css/Error/error.css') }}">
        <link rel="stylesheet" href="{{ asset('assets/css/AdminForm/Form.css') }}">
        <script src="{{ asset('assets/js/datatables.min.js') }}"></script>
        <script src="{{ asset('bundles/datatables/js/datatables.js') }}"></script>
        <script src="{{ asset('assets/js/moment.min.js') }}"></script>
        <script src="{{ asset('assets/js/daterangepicker.min.js') }}"></script>
        <script src="{{ asset('assets/js/admin/LeaveBalanceValidation.js') }}"></script>
{% endblock %}