<?php

namespace App\Controller\Admin;

use App\Entity\FormTemplate;
use App\Entity\User;
use App\Form\FormTemplateType;
use App\Message\FeedbackNotificationMessage;
use App\Repository\EmployeeHoursRepository;
use App\Repository\FormTemplateRepository;
use App\Repository\MasterEmployeeRepository;
use App\Service\Admin\FormTemplateService;
use App\Service\Common\FormFillService;
use App\Traits\ExceptionLoggerTrait;
use Doctrine\ORM\EntityManagerInterface;
use http\Exception;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Messenger\Exception\ExceptionInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Csrf\CsrfToken;
use Symfony\Component\Security\Csrf\CsrfTokenManagerInterface;

#[Route('/admin')]
class FormTemplateController extends AbstractController
{
    use ExceptionLoggerTrait;

    public function __construct(
        ParameterBagInterface                       $parameterBag,
        private readonly EntityManagerInterface     $entityManager,
        private readonly FormTemplateService        $formTemplate,
    )
    {
        $this->initializeLogger($parameterBag, 'form_template_controller');
    }

    /**
     * @param Request $request
     * @return Response
     */
    #[Route(path: '/form-template', name: 'form_template', methods: ['GET', 'POST'])]
    public function index(Request $request): Response
    {
        $table = $this->formTemplate->createMasterFormTemplateTable($request);

        if ($table->isCallback()) {
            return $table->getResponse();
        }

        return $this->render('admin/FormTemplate/index.html.twig', [
            'datatable' => $table,
        ]);
    }

    #[Route('/form-template/create', name: 'form_template_create', methods: ['GET', 'POST'])]
    public function createFormTemplate(Request $request): Response
    {
        $formTemplate = new FormTemplate();
        $form = $this->createForm(FormTemplateType::class, $formTemplate);

        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $user = $this->getUser();
            if ($user instanceof User) {
                $formTemplate->setCreatedBy($user);
            }
            $this->entityManager->persist($formTemplate);
            $this->entityManager->flush();

            $this->addFlash('success', 'Form Template created successfully.');
            return $this->redirectToRoute('form_template');
        }
        return $this->render('admin/FormTemplate/new.html.twig', [
            'form' => $form->createView(),
            'mode' => 'create',
        ]);
    }


    #[Route('/form-template/edit/{id}', name: 'form_template_edit', methods: ['GET', 'POST'])]
    public function editFormTemplate(
        Request                $request,
        FormTemplate           $formTemplate,
        EntityManagerInterface $entityManager
    ): Response
    {
        $form = $this->createForm(FormTemplateType::class, $formTemplate);

        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $entityManager->flush();

            $this->addFlash('success', 'Form Template updated successfully.');
            return $this->redirectToRoute('form_template');
        }

        return $this->render('admin/FormTemplate/new.html.twig', [
            'form' => $form->createView(),
            'formTemplate' => $formTemplate,
            'mode' => 'edit',
        ]);
    }
    #[Route('/form-template/delete/{id}', name: 'form_template_delete', methods: ['POST'])]
    public function deleteFormTemplate(
        Request                $request,
        FormTemplate           $formTemplate,
        EntityManagerInterface $entityManager,
        CsrfTokenManagerInterface $csrfTokenManager
    ): Response
    {
        $token = $request->request->get('_token');

        if (!$csrfTokenManager->isTokenValid(new CsrfToken('delete' . $formTemplate->getId(), $token))) {
            return $this->json([
                'success' => false,
                'message' => 'Invalid CSRF token.'
            ], Response::HTTP_FORBIDDEN);
        }

        $formTemplate->setIsDelete(true);
        $entityManager->persist($formTemplate);
        $entityManager->flush();

        return $this->json([
            'success' => true,
            'message' => 'Template has been deleted.'
        ], Response::HTTP_OK);
    }

    #[Route('/form-template/view/{id}', name: 'form_template_view', methods: ['GET'])]
    public function show(FormTemplate $formTemplate): Response
    {
        return $this->render('admin/FormTemplate/new.html.twig', [
            'formTemplate' => $formTemplate,
            'mode' => 'view',
        ]);
    }

    #[Route('/form-template/toggle-status/{id}', name: 'form_template_toggle_status', methods: ['POST'])]
    public function toggleStatus(Request $request, FormTemplate $formTemplate): Response
    {
        if (!$request->isXmlHttpRequest()) {
            return $this->json([
                'success' => false,
                'message' => 'This endpoint only accepts AJAX requests.'
            ], 400);
        }

        if (!$this->isCsrfTokenValid('toggle-status' . $formTemplate->getId(), $request->request->get('_token'))) {
            return $this->json([
                'success' => false,
                'message' => 'Invalid CSRF token.'
            ], 400);
        }

        try {
            $formTemplate->setStatus(!$formTemplate->isStatus());
            $formTemplate->setUpdatedAt(new \DateTime());
            $this->entityManager->flush();

            return $this->json([
                'success' => true,
                'message' => 'Template visibility status updated successfully.',
                'isVisible' => $formTemplate->isStatus()
            ]);
        } catch (\Exception $e) {
            return $this->json([
                'success' => false,
                'message' => 'Error updating status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * @throws ExceptionInterface
     * @throws \JsonException
     */
    #[Route('/form-template/{templateId}/preview', name: 'fill_preview', methods: ['GET', 'POST'])]
    public function fillForm(
        int                      $templateId,
        Request                  $request,
        FormTemplateRepository   $formTemplateRepository,
        MasterEmployeeRepository $employeeRepository,
        EntityManagerInterface   $entityManager,
        EmployeeHoursRepository  $employeeHoursRepository,
        FormFillService          $formFillService,
        MessageBusInterface      $messageBus
    ): Response|JsonResponse
    {
        $admin = $this->getUser();
        if (!$admin instanceof User) {
            throw new \LogicException('User must be an Admin');
        }
        $template = $formTemplateRepository->find($templateId);

        if (!$template || !$template->isStatus() | $template->getIsDelete()) {
            throw $this->createAccessDeniedException("This template is inactive or does not exist.");
        }
        $employees = $employeeRepository->findBy(['isDelete' => false]);
        $sections = $template->getSections()->filter(fn($section) => $section->isStatus() && !$section->getIsDelete()
                && count($formFillService->getSortedFields($section)) > 0)->toArray();
        usort($sections, fn($a, $b) => $a->getOrderNumber() <=> $b->getOrderNumber());
        $fieldTypes = $formFillService->processSections($sections);

        if ($request->isMethod('POST')) {
            $employeeId = $request->request->get('employee_id');
            $employee = $employeeRepository->find($employeeId);

            if (!$employee) {
                $this->addFlash('error', 'Invalid employee selected.');
                return $this->redirectToRoute('fill_form', ['templateId' => $templateId]);
            }

            // Calculate overall rating
            $ratingData = $formFillService->calculateOverallRating($sections, $fieldTypes, $request);
            $overallRating = $ratingData['ratingCount'] > 0
                ? number_format($ratingData['totalRating'] / $ratingData['ratingCount'], 2)
                : null;
            $activityData = $formFillService->getEmployeeActivityData($employeeHoursRepository, $employee);
            $submission = $formFillService->createSubmission(
                $template,
                $employee,
                $admin,
                $overallRating,
                $activityData['averageActiveMinutes'],
                $activityData['averageActiveSeconds'],
                true
            );
            $formFillService->processFormResponses(
                $submission,
                $sections,
                $fieldTypes,
                $request,
                $admin,
                true
            );

            $entityManager->flush();

            $message = new FeedbackNotificationMessage($employee->getId());
            $messageBus->dispatch($message);

            $this->addFlash('success', 'Form submitted successfully.');
            return new JsonResponse(['redirectUrl' => $this->generateUrl('form_template')], 200);
        }

        return $this->render('admin/TemplatePreview.html.twig', [
            'template' => $template,
            'employees' => $employees,
            'sections' => $sections,
            'fieldTypes' => $fieldTypes,
            'employeeActivityData' => $formFillService->getEmployeesActivityData($employeeHoursRepository, $employees),
        ]);
    }
}