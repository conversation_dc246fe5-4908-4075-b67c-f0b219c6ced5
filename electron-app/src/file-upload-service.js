const fs = require('fs').promises;
const path = require('path');
const FormData = require('form-data');
const axios = require('axios');

class FileUploadService {
    constructor(dbService, store) {
        this.dbService = dbService;
        this.store = store;
        
        // Configuration
        this.config = {
            serverUrl: 'http://127.0.0.1:8000',
            uploadEndpoint: '/api/files/upload',
            maxFileSize: 10 * 1024 * 1024, // 10MB
            allowedTypes: ['image/jpeg', 'image/png', 'image/webp'],
            retryAttempts: 3,
            retryDelay: 5000, // 5 seconds
            batchSize: 5, // Upload 5 files at a time
            compressionQuality: 0.8
        };
        
        // State
        this.uploadQueue = [];
        this.isUploading = false;
        this.uploadStats = {
            totalUploaded: 0,
            totalFailed: 0,
            totalBytes: 0
        };
        
        this.init();
    }

    async init() {
        console.log('📤 Initializing File Upload Service...');
        
        try {
            // Load configuration
            await this.loadConfiguration();
            
            // Start upload queue processor
            this.startUploadProcessor();
            
            console.log('✅ File Upload Service initialized');
        } catch (error) {
            console.error('❌ Failed to initialize File Upload Service:', error);
        }
    }

    async loadConfiguration() {
        try {
            const storedConfig = await this.store.get('fileUploadConfig');
            if (storedConfig) {
                this.config = { ...this.config, ...storedConfig };
            }
            
            console.log('⚙️ File upload configuration loaded');
        } catch (error) {
            console.error('Failed to load file upload configuration:', error);
        }
    }

    async updateConfiguration(newConfig) {
        try {
            this.config = { ...this.config, ...newConfig };
            await this.store.set('fileUploadConfig', this.config);
            
            console.log('⚙️ File upload configuration updated');
            return true;
        } catch (error) {
            console.error('Failed to update file upload configuration:', error);
            return false;
        }
    }

    startUploadProcessor() {
        // Process upload queue every 30 seconds
        setInterval(async () => {
            if (!this.isUploading && this.uploadQueue.length > 0) {
                await this.processUploadQueue();
            }
        }, 30000);
    }

    async queueFileForUpload(filePath, fileType, metadata = {}) {
        try {
            // Validate file
            const stats = await fs.stat(filePath);
            if (stats.size > this.config.maxFileSize) {
                throw new Error(`File too large: ${stats.size} bytes`);
            }

            const uploadItem = {
                id: Date.now() + Math.random(),
                filePath,
                fileType,
                fileSize: stats.size,
                metadata,
                attempts: 0,
                queuedAt: new Date().toISOString()
            };

            this.uploadQueue.push(uploadItem);
            console.log(`📤 Queued file for upload: ${path.basename(filePath)}`);
            
            return uploadItem.id;
        } catch (error) {
            console.error('Failed to queue file for upload:', error);
            return null;
        }
    }

    async processUploadQueue() {
        if (this.isUploading || this.uploadQueue.length === 0) {
            return;
        }

        this.isUploading = true;
        console.log(`📤 Processing upload queue: ${this.uploadQueue.length} files`);

        try {
            // Process files in batches
            const batch = this.uploadQueue.splice(0, this.config.batchSize);
            
            for (const item of batch) {
                try {
                    await this.uploadFile(item);
                    this.uploadStats.totalUploaded++;
                    this.uploadStats.totalBytes += item.fileSize;
                } catch (error) {
                    console.error(`Failed to upload file: ${item.filePath}`, error);
                    
                    item.attempts++;
                    if (item.attempts < this.config.retryAttempts) {
                        // Re-queue for retry
                        this.uploadQueue.push(item);
                        console.log(`📤 Re-queued file for retry: ${path.basename(item.filePath)} (attempt ${item.attempts})`);
                    } else {
                        this.uploadStats.totalFailed++;
                        console.error(`📤 Failed to upload file after ${this.config.retryAttempts} attempts: ${item.filePath}`);
                    }
                }
            }
        } catch (error) {
            console.error('Error processing upload queue:', error);
        } finally {
            this.isUploading = false;
        }
    }

    async uploadFile(uploadItem) {
        try {
            const { filePath, fileType, metadata } = uploadItem;
            
            // Check if file still exists
            try {
                await fs.access(filePath);
            } catch (error) {
                throw new Error(`File not found: ${filePath}`);
            }

            // Read file
            const fileBuffer = await fs.readFile(filePath);
            const fileName = path.basename(filePath);

            // Create form data
            const formData = new FormData();
            formData.append('file', fileBuffer, {
                filename: fileName,
                contentType: this.getMimeType(fileName)
            });
            formData.append('file_type', fileType);
            formData.append('metadata', JSON.stringify(metadata));

            // Get current user for authentication
            const currentUser = await this.store.get('currentUser');
            if (currentUser) {
                formData.append('employee_id', currentUser.id.toString());
            }

            // Upload file
            const response = await axios.post(
                `${this.config.serverUrl}${this.config.uploadEndpoint}`,
                formData,
                {
                    headers: {
                        ...formData.getHeaders(),
                        'Content-Length': formData.getLengthSync()
                    },
                    timeout: 60000, // 60 seconds timeout
                    maxContentLength: this.config.maxFileSize,
                    maxBodyLength: this.config.maxFileSize
                }
            );

            if (response.status === 200 && response.data.success) {
                console.log(`✅ File uploaded successfully: ${fileName}`);
                
                // Update database record if applicable
                await this.updateFileRecord(uploadItem, response.data);
                
                // Delete local file if configured to do so
                if (this.config.deleteAfterUpload) {
                    await this.deleteLocalFile(filePath);
                }
                
                return response.data;
            } else {
                throw new Error(`Upload failed: ${response.data.error || 'Unknown error'}`);
            }

        } catch (error) {
            console.error(`Upload failed for ${uploadItem.filePath}:`, error.message);
            throw error;
        }
    }

    async updateFileRecord(uploadItem, uploadResponse) {
        try {
            const { fileType, filePath } = uploadItem;
            const fileName = path.basename(filePath);

            if (fileType === 'screenshot') {
                await this.dbService.run(`
                    UPDATE screenshot_logs
                    SET is_synced = 1, metadata = json_set(metadata, '$.upload_url', ?)
                    WHERE file_name = ?
                `, [uploadResponse.file_url, fileName]);
            }

            console.log(`📤 Updated database record for uploaded file: ${fileName}`);
        } catch (error) {
            console.error('Failed to update file record:', error);
        }
    }

    async deleteLocalFile(filePath) {
        try {
            await fs.unlink(filePath);
            console.log(`🗑️ Deleted local file: ${path.basename(filePath)}`);
        } catch (error) {
            console.error(`Failed to delete local file: ${filePath}`, error);
        }
    }

    getMimeType(fileName) {
        const ext = path.extname(fileName).toLowerCase();
        switch (ext) {
            case '.jpg':
            case '.jpeg':
                return 'image/jpeg';
            case '.png':
                return 'image/png';
            case '.webp':
                return 'image/webp';
            default:
                return 'application/octet-stream';
        }
    }

    async queueScreenshotsForUpload() {
        try {
            const unsynced = await this.dbService.all(`
                SELECT * FROM screenshot_logs 
                WHERE is_synced = 0 
                ORDER BY captured_at ASC 
                LIMIT 20
            `);

            for (const screenshot of unsynced) {
                if (screenshot.file_path && await this.fileExists(screenshot.file_path)) {
                    await this.queueFileForUpload(
                        screenshot.file_path,
                        'screenshot',
                        {
                            screenshot_id: screenshot.id,
                            captured_at: screenshot.captured_at,
                            capture_type: screenshot.capture_type,
                            employee_id: screenshot.employee_id,
                            original_filename: screenshot.file_name,
                            resolution: screenshot.resolution,
                            device_id: screenshot.device_id,
                            active_application: screenshot.active_application,
                            active_window: screenshot.active_window,
                            captured_date: screenshot.captured_at
                        }
                    );
                }
            }

            console.log(`📤 Queued ${unsynced.length} screenshots for upload`);
        } catch (error) {
            console.error('Failed to queue screenshots for upload:', error);
        }
    }



    async fileExists(filePath) {
        try {
            await fs.access(filePath);
            return true;
        } catch {
            return false;
        }
    }

    getUploadStats() {
        return {
            ...this.uploadStats,
            queueLength: this.uploadQueue.length,
            isUploading: this.isUploading
        };
    }

    async clearUploadQueue() {
        this.uploadQueue = [];
        console.log('📤 Upload queue cleared');
    }

    async destroy() {
        console.log('🔄 Destroying file upload service...');
        this.uploadQueue = [];
        this.isUploading = false;
        console.log('✅ File upload service destroyed');
    }
}

module.exports = FileUploadService;
