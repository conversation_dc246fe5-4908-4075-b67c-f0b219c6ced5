{% extends 'admin/dashboard/base.html.twig' %}

{% block title %}Payroll Management{% endblock %}

{% block body %}
    {% include 'admin/partials/breadcrumbs.html.twig' with {
        breadcrumbs: [
            { 'label': 'Dashboard', 'url': url('admin_dashboard') },
            { 'label': 'Employee', 'url': url('master_employee') }
        ],
        active: 'Payroll Management'
    } %}

    <div class="content">
        <div class="animated fadeIn">
            <div class="row">
                <div class="col-lg-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <strong class="card-title"><i class="fa fa-money mr-2"></i>Payroll Management</strong>
                        </div>
                        <div class="card-body">
                            <!-- Year and Month Filter Form -->
                            <form method="get" action="{{ path('admin_payroll_index') }}" class="mb-4">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label for="year" class="form-label control-label mb-1">Year</label>
                                            <select name="year" id="year" class="form-control">
                                                {% set currentYear = "now"|date("Y") + 0 %}
                                                {% for y in range(2020, currentYear + 2) %}
                                                    <option value="{{ y }}" {% if y == year %}selected{% endif %}>{{ y }}</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label for="month" class="form-label control-label mb-1">Month</label>
                                            <select name="month" id="month" class="form-control">
                                                <option value="">Select Month</option>
                                                {% for i in 1..12 %}
                                                    <option value="{{ i }}"
                                                            {% if selectedMonth is defined and selectedMonth == i %}selected{% endif %}>
                                                        {{ ('2025-' ~ "%02d"|format(i) ~ '-01')|date('F') }}
                                                    </option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-3 align-self-end">
                                        <div class="mb-3">
                                            <button type="submit" class="btn btn-primary">Show</button>
                                        </div>
                                    </div>
                                </div>
                            </form>

                            <!-- Working Days Information -->
                            <p><strong>Working Days (excluding weekends & holidays):</strong> {{ workingDays }}</p>

                            <!-- Payroll Form -->
{#                            <div class="mb-3">#}
{#                                <input type="text" id="payroll-table-search" class="form-control" placeholder="Search payroll table...">#}
{#                            </div>#}
                            {{ form_start(form, { 'attr': { 'class': 'needs-validation use-loader', 'novalidate': 'novalidate' ,'data-turbo': 'false'} }) }}
                            <div class="table-responsive" style="max-width: 100%; overflow-x: auto;">
                                <table id="payroll-table" class="table table-bordered" style="font-size: 14px; min-width: 1500px;">
                                    <thead>
                                    <tr>
                                        <th style="min-width: 150px;">Name</th>
                                        <th style="min-width: 120px;"><span class="required-field">Salary</span></th>
                                        <th style="min-width: 120px;"><span class="required-field">Deduction</span></th>
                                        <th style="min-width: 120px;"><span class="required-field">Working Days</span></th>
                                        <th style="min-width: 120px;"><span class="required-field">Present Days</span></th>
                                        <th style="min-width: 120px;">Per Day Basic</th>
                                        <th style="min-width: 150px;">Total Leave Deduction</th>
                                        <th style="min-width: 120px;">Extra Pay</th>
                                        <th style="min-width: 150px;">Salary After Leave</th>
                                        <th style="min-width: 150px;">Basic + DA Salary</th>
                                        <th style="min-width: 120px;">HRA</th>
                                        <th style="min-width: 150px;">Conveyance Allowance</th>
                                        <th style="min-width: 150px;">Medical Allowance</th>
                                        <th style="min-width: 150px;">Telephone Allowance</th>
                                        <th style="min-width: 150px;">Special Allowance</th>
                                        <th style="min-width: 120px;">Net Salary</th>
                                        <th style="min-width: 120px;">Diff</th>
                                        <th style="min-width: 120px;">PT</th>
                                        <th style="min-width: 120px;">ESIC</th>
                                        <th style="min-width: 150px;">Salary For PF Calculations</th>
                                        <th style="min-width: 120px;">PF Employee</th>
                                        <th style="min-width: 120px;">TDS Deduction</th>
                                        <th style="min-width: 150px;">Loan & Other Deductions</th>
                                        <th style="min-width: 150px;">Total Deductions</th>
                                        <th style="min-width: 150px;">Salary Payable</th>
                                        <th style="min-width: 120px;">PF Employer</th>
                                        <th style="min-width: 120px;">CTC</th>
                                        <th style="min-width: 150px;">Input CTC For Calculation</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {% for employee in employees %}
                                        {% set payroll = payrollMap[employee.id] ?? null %}
                                        {% set flags = deductionFlags[employee.id] ?? {'pf': true, 'esic': true} %}
                                        <tr 
                                            data-employee-id="{{ employee.id }}"
                                            data-deduct-pf="{{ flags.pf ? '1' : '0' }}"
                                            data-deduct-esic="{{ flags.esic ? '1' : '0' }}"
                                        >
                                            <td style="padding: 10px;">{{ employee.name }}</td>
                                            <td style="padding: 10px;">
                                                <div class="input-group" style="margin: 5px 0;">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text"><i class="fa fa-inr"></i></span>
                                                    </div>
                                                    {{ form_widget(form.payrolls[employee.id].salary, { 'attr': { 'class': 'form-control', 'step': '0.01', 'style': 'padding: 8px;' } }) }}
                                                </div>
                                                {{ form_errors(form.payrolls[employee.id].salary) }}
                                            </td>
                                            <td style="padding: 10px;">
                                                <div class="input-group" style="margin: 5px 0;">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text"><i class="fa fa-inr"></i></span>
                                                    </div>
                                                    {{ form_widget(form.payrolls[employee.id].deduction, { 'attr': { 'class': 'form-control', 'step': '0.01', 'value': payroll.deduction ?? lwpMap[employee.id] ?? '', 'style': 'padding: 8px;' } }) }}
                                                </div>
                                                {{ form_errors(form.payrolls[employee.id].deduction) }}
                                            </td>
                                            <td style="padding: 10px;">
                                                {{ form_widget(form.payrolls[employee.id].workingDays, { 'attr': { 'class': 'form-control', 'value': payroll.workingDays ?? workingDays, 'style': 'padding: 8px;' } }) }}
                                                {{ form_errors(form.payrolls[employee.id].workingDays) }}
                                            </td>
                                            <td style="padding: 10px;">
                                                {{ form_widget(form.payrolls[employee.id].presentDays, {
                                                    'attr': {
                                                        'class': 'form-control',
                                                        'value': payroll.presentDays ?? (workingDays - (leaveDaysMap[employee.id] ?? 0)),
                                                        'style': 'padding: 8px;'
                                                    }
                                                }) }}
                                                {{ form_errors(form.payrolls[employee.id].presentDays) }}
                                            </td>
                                            <td style="padding: 10px;">
                                                <div class="input-group" style="margin: 5px 0;">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text"><i class="fa fa-inr"></i></span>
                                                    </div>
                                                    {{ form_widget(form.payrolls[employee.id].perDayBasedOnBasic, { 'attr': { 'class': 'form-control', 'step': '0.01', 'value': payroll.perDayBasedOnBasic ?? '', 'style': 'padding: 8px;' } }) }}
                                                </div>
                                                {{ form_errors(form.payrolls[employee.id].perDayBasedOnBasic) }}
                                            </td>
                                            <td style="padding: 10px;">
                                                <div class="input-group" style="margin: 5px 0;">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text"><i class="fa fa-inr"></i></span>
                                                    </div>
                                                    {{ form_widget(form.payrolls[employee.id].totalLeaveDeduction, { 'attr': { 'class': 'form-control', 'step': '0.01', 'value': payroll.totalLeaveDeduction ?? '', 'style': 'padding: 8px;' } }) }}
                                                </div>
                                                {{ form_errors(form.payrolls[employee.id].totalLeaveDeduction) }}
                                            </td>
                                            <td style="padding: 10px;">
                                                <div class="input-group" style="margin: 5px 0;">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text"><i class="fa fa-inr"></i></span>
                                                    </div>
                                                    {{ form_widget(form.payrolls[employee.id].extraPay, { 'attr': { 'class': 'form-control', 'step': '0.01', 'value': payroll.extraPay ?? '', 'style': 'padding: 8px;' } }) }}
                                                </div>
                                                {{ form_errors(form.payrolls[employee.id].extraPay) }}
                                            </td>
                                            <td style="padding: 10px;">
                                                <div class="input-group" style="margin: 5px 0;">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text"><i class="fa fa-inr"></i></span>
                                                    </div>
                                                    {{ form_widget(form.payrolls[employee.id].salaryAfterLeave, { 'attr': { 'class': 'form-control', 'step': '0.01', 'value': payroll.salaryAfterLeave ?? '', 'style': 'padding: 8px;' } }) }}
                                                </div>
                                                {{ form_errors(form.payrolls[employee.id].salaryAfterLeave) }}
                                            </td>
                                            <td style="padding: 10px;">
                                                <div class="input-group" style="margin: 5px 0;">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text"><i class="fa fa-inr"></i></span>
                                                    </div>
                                                    {{ form_widget(form.payrolls[employee.id].basicDaSalary, { 'attr': { 'class': 'form-control', 'step': '0.01', 'value': payroll.basicDaSalary ?? '', 'style': 'padding: 8px;' } }) }}
                                                </div>
                                                {{ form_errors(form.payrolls[employee.id].basicDaSalary) }}
                                            </td>
                                            <td style="padding: 10px;">
                                                <div class="input-group" style="margin: 5px 0;">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text"><i class="fa fa-inr"></i></span>
                                                    </div>
                                                    {{ form_widget(form.payrolls[employee.id].hra, { 'attr': { 'class': 'form-control', 'step': '0.01', 'value': payroll.hra ?? '', 'style': 'padding: 8px;' } }) }}
                                                </div>
                                                {{ form_errors(form.payrolls[employee.id].hra) }}
                                            </td>
                                            <td style="padding: 10px;">
                                                <div class="input-group" style="margin: 5px 0;">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text"><i class="fa fa-inr"></i></span>
                                                    </div>
                                                    {{ form_widget(form.payrolls[employee.id].conveyanceAllowance, { 'attr': { 'class': 'form-control', 'step': '0.01', 'value': payroll.conveyanceAllowance ?? '', 'style': 'padding: 8px;' } }) }}
                                                </div>
                                                {{ form_errors(form.payrolls[employee.id].conveyanceAllowance) }}
                                            </td>
                                            <td style="padding: 10px;">
                                                <div class="input-group" style="margin: 5px 0;">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text"><i class="fa fa-inr"></i></span>
                                                    </div>
                                                    {{ form_widget(form.payrolls[employee.id].medicalAllowance, { 'attr': { 'class': 'form-control', 'step': '0.01', 'value': payroll.medicalAllowance ?? '', 'style': 'padding: 8px;' } }) }}
                                                </div>
                                                {{ form_errors(form.payrolls[employee.id].medicalAllowance) }}
                                            </td>
                                            <td style="padding: 10px;">
                                                <div class="input-group" style="margin: 5px 0;">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text"><i class="fa fa-inr"></i></span>
                                                    </div>
                                                    {{ form_widget(form.payrolls[employee.id].telephoneAllowance, { 'attr': { 'class': 'form-control', 'step': '0.01', 'value': payroll.telephoneAllowance ?? '', 'style': 'padding: 8px;' } }) }}
                                                </div>
                                                {{ form_errors(form.payrolls[employee.id].telephoneAllowance) }}
                                            </td>
                                            <td style="padding: 10px;">
                                                <div class="input-group" style="margin: 5px 0;">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text"><i class="fa fa-inr"></i></span>
                                                    </div>
                                                    {{ form_widget(form.payrolls[employee.id].specialAllowance, { 'attr': { 'class': 'form-control', 'step': '0.01', 'value': payroll.specialAllowance ?? '', 'style': 'padding: 8px;' } }) }}
                                                </div>
                                                {{ form_errors(form.payrolls[employee.id].specialAllowance) }}
                                            </td>
                                            <td style="padding: 10px;">
                                                <div class="input-group" style="margin: 5px 0;">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text"><i class="fa fa-inr"></i></span>
                                                    </div>
                                                    {{ form_widget(form.payrolls[employee.id].netSalary, { 'attr': { 'class': 'form-control', 'step': '0.01', 'value': payroll.netSalary ?? '', 'style': 'padding: 8px;' } }) }}
                                                </div>
                                                {{ form_errors(form.payrolls[employee.id].netSalary) }}
                                            </td>
                                            <td style="padding: 10px;">
                                                <div class="input-group" style="margin: 5px 0;">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text"><i class="fa fa-inr"></i></span>
                                                    </div>
                                                    {{ form_widget(form.payrolls[employee.id].diff, { 'attr': { 'class': 'form-control', 'step': '0.01', 'value': payroll.diff ?? '', 'style': 'padding: 8px;' } }) }}
                                                </div>
                                                {{ form_errors(form.payrolls[employee.id].diff) }}
                                            </td>
                                            <td style="padding: 10px;">
                                                <div class="input-group" style="margin: 5px 0;">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text"><i class="fa fa-inr"></i></span>
                                                    </div>
                                                    {{ form_widget(form.payrolls[employee.id].pt, { 'attr': { 'class': 'form-control', 'step': '0.01', 'value': payroll.pt ?? '', 'style': 'padding: 8px;' } }) }}
                                                </div>
                                                {{ form_errors(form.payrolls[employee.id].pt) }}
                                            </td>
                                            <td style="padding: 10px;">
                                                <div class="input-group" style="margin: 5px 0;">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text"><i class="fa fa-inr"></i></span>
                                                    </div>
                                                    {{ form_widget(form.payrolls[employee.id].esic, { 'attr': { 'class': 'form-control', 'step': '0.01', 'value': payroll.esic ?? '', 'style': 'padding: 8px;' } }) }}
                                                </div>
                                                {{ form_errors(form.payrolls[employee.id].esic) }}
                                            </td>
                                            <td style="padding: 10px;">
                                                <div class="input-group" style="margin: 5px 0;">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text"><i class="fa fa-inr"></i></span>
                                                    </div>
                                                    {{ form_widget(form.payrolls[employee.id].salaryForPFCalculations, { 'attr': { 'class': 'form-control', 'step': '0.01', 'value': payroll.salaryForPFCalculations ?? '', 'style': 'padding: 8px;' } }) }}
                                                </div>
                                                {{ form_errors(form.payrolls[employee.id].salaryForPFCalculations) }}
                                            </td>
                                            <td style="padding: 10px;">
                                                <div class="input-group" style="margin: 5px 0;">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text"><i class="fa fa-inr"></i></span>
                                                    </div>
                                                    {{ form_widget(form.payrolls[employee.id].pfEmployee, { 'attr': { 'class': 'form-control', 'step': '0.01', 'value': payroll.pfEmployee ?? '', 'style': 'padding: 8px;' } }) }}
                                                </div>
                                                {{ form_errors(form.payrolls[employee.id].pfEmployee) }}
                                            </td>
                                            <td style="padding: 10px;">
                                                <div class="input-group" style="margin: 5px 0;">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text"><i class="fa fa-inr"></i></span>
                                                    </div>
                                                    {{ form_widget(form.payrolls[employee.id].tdsDeduction, { 'attr': { 'class': 'form-control', 'step': '0.01', 'value': payroll.tdsDeduction ?? '', 'style': 'padding: 8px;' } }) }}
                                                </div>
                                                {{ form_errors(form.payrolls[employee.id].tdsDeduction) }}
                                            </td>
                                            <td style="padding: 10px;">
                                                <div class="input-group" style="margin: 5px 0;">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text"><i class="fa fa-inr"></i></span>
                                                    </div>
                                                    {{ form_widget(form.payrolls[employee.id].loanOtherDeductions, { 'attr': { 'class': 'form-control', 'step': '0.01', 'value': payroll.loanOtherDeductions ?? '', 'style': 'padding: 8px;' } }) }}
                                                </div>
                                                {{ form_errors(form.payrolls[employee.id].loanOtherDeductions) }}
                                            </td>
                                            <td style="padding: 10px;">
                                                <div class="input-group" style="margin: 5px 0;">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text"><i class="fa fa-inr"></i></span>
                                                    </div>
                                                    {{ form_widget(form.payrolls[employee.id].totalDeductions, { 'attr': { 'class': 'form-control', 'step': '0.01', 'value': payroll.totalDeductions ?? '', 'style': 'padding: 8px;' } }) }}
                                                </div>
                                                {{ form_errors(form.payrolls[employee.id].totalDeductions) }}
                                            </td>
                                            <td style="padding: 10px;">
                                                <div class="input-group" style="margin: 5px 0;">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text"><i class="fa fa-inr"></i></span>
                                                    </div>
                                                    {{ form_widget(form.payrolls[employee.id].salaryPayable, { 'attr': { 'class': 'form-control', 'step': '0.01', 'value': payroll.salaryPayable ?? '', 'style': 'padding: 8px;' } }) }}
                                                </div>
                                                {{ form_errors(form.payrolls[employee.id].salaryPayable) }}
                                            </td>
                                            <td style="padding: 10px;">
                                                <div class="input-group" style="margin: 5px 0;">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text"><i class="fa fa-inr"></i></span>
                                                    </div>
                                                    {{ form_widget(form.payrolls[employee.id].pfEmployer, { 'attr': { 'class': 'form-control', 'step': '0.01', 'value': payroll.pfEmployer ?? '', 'style': 'padding: 8px;' } }) }}
                                                </div>
                                                {{ form_errors(form.payrolls[employee.id].pfEmployer) }}
                                            </td>
                                            <td style="padding: 10px;">
                                                <div class="input-group" style="margin: 5px 0;">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text"><i class="fa fa-inr"></i></span>
                                                    </div>
                                                    {{ form_widget(form.payrolls[employee.id].ctc, { 'attr': { 'class': 'form-control', 'step': '0.01', 'value': payroll.ctc ?? ctcMap[employee.id] ?? '', 'style': 'padding: 8px;' } }) }}
                                                </div>
                                                {{ form_errors(form.payrolls[employee.id].ctc) }}
                                            </td>
                                            <td style="padding: 10px;">
                                                <div class="input-group" style="margin: 5px 0;">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text"><i class="fa fa-inr"></i></span>
                                                    </div>
                                                    {{ form_widget(form.payrolls[employee.id].inputCtcForCalculation, { 'attr': { 'class': 'form-control', 'step': '0.01', 'value': payroll.inputCtcForCalculation ?? ctcMap[employee.id] ?? '', 'style': 'padding: 8px;' } }) }}
                                                </div>
                                                {{ form_errors(form.payrolls[employee.id].inputCtcForCalculation) }}
                                            </td>
                                        </tr>
                                    {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            <div class="d-flex justify-content-end mt-4">
                                <a href="{{ path('master_employee') }}" class="btn btn-secondary mr-2">Cancel</a>
                                <button type="submit" class="btn btn-primary pl-3 pr-3" >Save Payroll</button>
                            </div>
                            {{ form_end(form) }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="clearfix"></div>

    <!-- Include CSS and JS for styling and validation -->
    <link rel="stylesheet" href="{{ asset('assets/css/Error/error.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/AdminForm/Form.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/payroll.css') }}">
    <script src="{{ asset('assets/js/datatables.min.js') }}"></script>
    <script src="{{ asset('bundles/datatables/js/datatables.js') }}"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const form = document.querySelector('.needs-validation');
            form.addEventListener('submit', function (event) {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
            const payrollTable = $('#payroll-table').DataTable({
                scrollX: true,
                paging: true,
                pageLength: 25,
                lengthMenu: [10, 25, 50, 100],
                ordering: true,
                info: true,
                searching: true,
                fixedHeader: true
            });
            // document.getElementById('payroll-table-search').addEventListener('keyup', function() {
            //     payrollTable.search(this.value).draw();
            // });
        });
    </script>
    <script>
        window.payrollDeductionFlags = {
        {% for employee in employees %}
        {{ employee.id }}: {
            pf: {{ (deductionFlags[employee.id].pf ? 'true' : 'false') }},
            esic: {{ (deductionFlags[employee.id].esic ? 'true' : 'false') }}
        }{{ not loop.last ? ',' : '' }}
        {% endfor %}
        };
    </script>
    <script src="{{ asset('assets/js/admin/payrollCalculator.js') }}"></script>
{% endblock %}