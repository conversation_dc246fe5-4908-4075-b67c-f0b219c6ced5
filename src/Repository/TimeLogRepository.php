<?php

namespace App\Repository;

use App\Entity\TimeLog;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<TimeLog>
 *
 * @method TimeLog|null find($id, $lockMode = null, $lockVersion = null)
 * @method TimeLog|null findOneBy(array $criteria, array $orderBy = null)
 * @method TimeLog[]    findAll()
 * @method TimeLog[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class TimeLogRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, TimeLog::class);
    }

    /**
     * Get time logs for a specific employee and date range
     */
    public function findByEmployeeAndDateRange($employeeId, \DateTime $startDate, \DateTime $endDate): array
    {
        return $this->createQueryBuilder('tl')
            ->where('tl.employee = :employeeId')
            ->andWhere('tl.logDate BETWEEN :startDate AND :endDate')
            ->setParameter('employeeId', $employeeId)
            ->setParameter('startDate', $startDate->format('Y-m-d'))
            ->setParameter('endDate', $endDate->format('Y-m-d'))
            ->orderBy('tl.logDate', 'DESC')
            ->addOrderBy('tl.startTime', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Get today's time logs for an employee
     */
    public function findTodayByEmployee($employeeId): array
    {
        $today = new \DateTime();
        
        return $this->createQueryBuilder('tl')
            ->where('tl.employee = :employeeId')
            ->andWhere('tl.logDate = :today')
            ->setParameter('employeeId', $employeeId)
            ->setParameter('today', $today->format('Y-m-d'))
            ->orderBy('tl.startTime', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Get active (ongoing) session for an employee
     */
    public function findActiveSession($employeeId): ?TimeLog
    {
        return $this->createQueryBuilder('tl')
            ->where('tl.employee = :employeeId')
            ->andWhere('tl.endTime IS NULL')
            ->setParameter('employeeId', $employeeId)
            ->orderBy('tl.startTime', 'DESC')
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult();
    }

    /**
     * Get unsynced time logs
     */
    public function findUnsyncedLogs(): array
    {
        return $this->createQueryBuilder('tl')
            ->where('tl.isSynced = false')
            ->orderBy('tl.createdAt', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Get daily statistics for an employee
     */
    public function getDailyStats($employeeId, \DateTime $date): array
    {
        $result = $this->createQueryBuilder('tl')
            ->select('tl.logType, SUM(tl.duration) as totalDuration, COUNT(tl.id) as sessionCount')
            ->where('tl.employee = :employeeId')
            ->andWhere('tl.logDate = :date')
            ->andWhere('tl.duration IS NOT NULL')
            ->setParameter('employeeId', $employeeId)
            ->setParameter('date', $date->format('Y-m-d'))
            ->groupBy('tl.logType')
            ->getQuery()
            ->getResult();

        // Organize results
        $stats = [
            'work' => ['duration' => 0, 'sessions' => 0],
            'break' => ['duration' => 0, 'sessions' => 0],
            'idle' => ['duration' => 0, 'sessions' => 0]
        ];

        foreach ($result as $row) {
            $type = $row['logType'];
            if (isset($stats[$type])) {
                $stats[$type]['duration'] = (int) $row['totalDuration'];
                $stats[$type]['sessions'] = (int) $row['sessionCount'];
            }
        }

        return $stats;
    }

    /**
     * Get weekly statistics for an employee
     */
    public function getWeeklyStats($employeeId, \DateTime $weekStart): array
    {
        $weekEnd = clone $weekStart;
        $weekEnd->modify('+6 days');

        return $this->createQueryBuilder('tl')
            ->select('tl.logDate, tl.logType, SUM(tl.duration) as totalDuration, COUNT(tl.id) as sessionCount')
            ->where('tl.employee = :employeeId')
            ->andWhere('tl.logDate BETWEEN :weekStart AND :weekEnd')
            ->andWhere('tl.duration IS NOT NULL')
            ->setParameter('employeeId', $employeeId)
            ->setParameter('weekStart', $weekStart->format('Y-m-d'))
            ->setParameter('weekEnd', $weekEnd->format('Y-m-d'))
            ->groupBy('tl.logDate, tl.logType')
            ->orderBy('tl.logDate', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Mark logs as synced
     */
    public function markAsSynced(array $logIds): void
    {
        $this->createQueryBuilder('tl')
            ->update()
            ->set('tl.isSynced', 'true')
            ->where('tl.id IN (:logIds)')
            ->setParameter('logIds', $logIds)
            ->getQuery()
            ->execute();
    }
}
