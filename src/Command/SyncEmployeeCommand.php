<?php

namespace App\Command;

use App\Service\Admin\EmployeeService;
use App\Traits\ExceptionLoggerTrait;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\DecodingExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;

class SyncEmployeeCommand extends Command
{
    use ExceptionLoggerTrait;

    protected static $defaultName = 'app:fetch-teamlogger-users';

    public function __construct(private readonly EmployeeService $employeeService, ParameterBagInterface $parameterBag)
    {
        parent::__construct(self::$defaultName);
        $this->initializeLogger($parameterBag, 'employee_command');
    }

    protected function configure(): void
    {
        $this
            ->setDescription('Fetch users from the Teamlogger API and store them in the database')
            ->setHelp('This command fetches all users from the Teamlogger API and stores them in the database.');
    }

    /**
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     * @throws ClientExceptionInterface
     * @throws DecodingExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $output->writeln('<info>Fetching data from Teamlogger API...</info>');

        try {
            $this->employeeService->fetchAndSyncEmployees();
            $output->writeln('<info>Users successfully fetched and stored.</info>');
            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->log(
                'Error fetching and syncing employees.',
                [
                    'error_message' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                    'trace' => $e->getTraceAsString(),
                ]
            );
            $output->writeln('<error>Error fetching data: ' . $e->getMessage() . '</error>');
            return Command::FAILURE;
        }
    }
}
