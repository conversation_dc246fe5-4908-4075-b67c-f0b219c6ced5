{% extends 'Front/dashboard/base.html.twig' %}

{% block title %}Edit Personal Info{% endblock %}

{% block body %}
    {% include 'admin/partials/breadcrumbs.html.twig' with {
        title: 'Edit Personal Information',
        breadcrumbs: [
            { 'label': 'Dashboard', 'url': url('dashboard_employee') },
            { 'label': 'Profile', 'url': url('employee_profile') }
        ],
        active: 'Edit Personal Information'
    } %}

    <div class="content">
        <div class="animated fadeIn">
            <div class="row">
                <div class="col-lg-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <strong class="card-title">Edit Personal Information</strong>
                            <div class="d-flex align-items-center">
                                <a href="{{ path('employee_profile') }}" class="btn btn-sm btn-outline-secondary mr-2">
                                    <i class="fa fa-list mr-1"></i>Back to Profile
                                </a>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="personal-info-form">
                                {{ form_start(form, { 'attr': { 'class': 'needs-validation use-loader', 'novalidate': 'novalidate' ,'data-turbo': 'false'} }) }}
                                <div class="form-section">
                                    <h5 class="section-header">
                                        <i class="fa fa-info-circle mr-2"></i>Personal Information
                                    </h5>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                {{ form_label(form.birthDate, 'Birth Date', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text"><i class="fa fa-calendar"></i></span>
                                                    </div>
                                                    {{ form_widget(form.birthDate, {
                                                        'attr': {
                                                            'class': 'form-control datepicker',
                                                            'data-initial-date': employee.birthDate is defined and employee.birthDate ? employee.birthDate|date('d-m-Y') : '',
                                                            'placeholder': 'Select date'
                                                        }
                                                    }) }}
                                                </div>
                                                {{ form_errors(form.birthDate) }}
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                {{ form_label(form.gender, 'Gender', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text"><i class="fa fa-venus-mars"></i></span>
                                                    </div>
                                                    {{ form_widget(form.gender, { 'attr': { 'class': 'form-control' } }) }}
                                                </div>
                                                {{ form_errors(form.gender) }}
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                {{ form_label(form.maritalStatus, 'Marital Status', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text"><i class="fa fa-life-ring"></i></span>
                                                    </div>
                                                    {{ form_widget(form.maritalStatus, { 'attr': { 'class': 'form-control' } }) }}
                                                </div>
                                                {{ form_errors(form.maritalStatus) }}
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Contact Information Section -->
                                <div class="form-section">
                                    <h5 class="section-header">
                                        <i class="fa fa-address-book mr-2"></i>Contact Information
                                    </h5>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                {{ form_label(form.personalPhoneNumber, 'Primary Phone', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text"><i class="fa fa-phone"></i></span>
                                                    </div>
                                                    {{ form_widget(form.personalPhoneNumber, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter primary phone number' } }) }}
                                                </div>
                                                {{ form_errors(form.personalPhoneNumber) }}
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                {{ form_label(form.alternativePhoneNumber, 'Alternative Phone', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text"><i class="fa fa-phone-square"></i></span>
                                                    </div>
                                                    {{ form_widget(form.alternativePhoneNumber, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter alternative phone number' } }) }}
                                                </div>
                                                {{ form_errors(form.alternativePhoneNumber) }}
                                                <small class="form-text text-muted">Emergency contact number</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                {{ form_label(form.currentAddress, 'Current Address', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text"><i class="fa fa-home"></i></span>
                                                    </div>
                                                    {{ form_widget(form.currentAddress, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter current address' } }) }}
                                                </div>
                                                {{ form_errors(form.currentAddress) }}
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                {{ form_label(form.permanentAddress, 'Permanent Address', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text"><i class="fa fa-map-marker"></i></span>
                                                    </div>
                                                    {{ form_widget(form.permanentAddress, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter permanent address' } }) }}
                                                </div>
                                                {{ form_errors(form.permanentAddress) }}
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="d-flex justify-content-end mt-4">
                                    <a href="{{ path('employee_profile') }}" class="btn btn-secondary mr-2">Cancel</a>
                                    <button type="submit" class="btn btn-primary pl-3 pr-3">Save Changes</button>
                                </div>

                                {{ form_end(form) }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="clearfix"></div>
    <link rel="stylesheet" href="{{ asset('assets/css/Error/error.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/AdminForm/Form.css') }}">
    <script src="{{ asset('assets/js/datatables.min.js') }}"></script>
    <script src="{{ asset('bundles/datatables/js/datatables.js') }}"></script>
    <script src="{{ asset('assets/js/moment.min.js') }}"></script>
    <script src="{{ asset('assets/js/daterangepicker.min.js') }}"></script>
    <script src="{{ asset('assets/js/front/edit_personal_info.js') }}"></script>
{% endblock %}