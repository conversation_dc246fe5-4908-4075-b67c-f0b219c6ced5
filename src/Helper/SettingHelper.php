<?php

namespace App\Helper;

use App\Repository\SettingRepository;

class SettingHelper
{
    public function __construct(
        private readonly SettingRepository       $settingRepository
    )
    {
    }

    public function getSettingConfiguration(): array
    {
        $dsnSetting = $this->settingRepository->findOneBy(['fieldName' => 'MAILER_DSN']);
        $senderSetting = $this->settingRepository->findOneBy(['fieldName' => 'MAILER_FROM_ADDRESS']);
        $resendSetting=$this->settingRepository->findOneBy(['fieldName' => 'Justification_Reminder_days']);
        $reasonSetting=$this->settingRepository->findOneBy(['fieldName' => 'DisApproval_Reason']);
        $remainingSetting=$this->settingRepository->findOneBy(['fieldName' => 'REMAINING_JUSTIFICATION']);
        $totalWorkingSetting=$this->settingRepository->findOneBy(['fieldName' => 'Total_Working_Hours']);
        $MinimumHours=$this->settingRepository->findOneBy(['fieldName' => 'Minimum_Hours_Notification']);
        $renewalMonth=$this->settingRepository->findOneBy(['fieldName' => 'LEAVE_RENEWAL_MONTH']);
        if (!$dsnSetting || !$senderSetting || !$resendSetting || !$reasonSetting || !$remainingSetting ||
            !$totalWorkingSetting || !$MinimumHours  || !$renewalMonth) {
            throw new \RuntimeException('Setting configuration is incomplete.');
        }

        return [
            'dsn' => $dsnSetting->getFieldValue(),
            'sender' => $senderSetting->getFieldValue(),
            'resend' => $resendSetting->getFieldValue(),
            'reason' => $reasonSetting->getFieldValue(),
            'remain' => $remainingSetting->getFieldValue(),
            'hours' => $totalWorkingSetting->getFieldValue(),
            'MinHours' => $MinimumHours->getFieldValue(),
            'renewMonth' => $renewalMonth->getFieldValue(),
        ];
    }
}
