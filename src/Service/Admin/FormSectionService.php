<?php
namespace App\Service\Admin;

use App\Entity\FormSection;
use App\Helper\ModalboxHelper;
use Doctrine\ORM\QueryBuilder;
use Omines\DataTablesBundle\Adapter\Doctrine\ORMAdapter;
use Omines\DataTablesBundle\Column\TextColumn;
use Omines\DataTablesBundle\DataTable;
use Omines\DataTablesBundle\DataTableFactory;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Request;
use App\Traits\ExceptionLoggerTrait;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Security\Csrf\CsrfTokenManagerInterface;

class FormSectionService
{
    use ExceptionLoggerTrait;

    public function __construct(
        private readonly DataTableFactory $dataTableFactory,
        private readonly RouterInterface $router,
        ParameterBagInterface $parameterBag,
        private readonly CsrfTokenManagerInterface $csrfTokenManager,
        private readonly ModalboxHelper $modalboxHelper
    ) {
        $this->initializeLogger($parameterBag, 'FormSectionService');
    }

    public function createMasterFormSectionTable(Request $request, int $templateId): DataTable
    {
        $dataTable = $this->dataTableFactory->create();

        foreach ($this->getColumns() as $key => $column) {
            $dataTable->add($key, $column['className'], array_filter($column, fn($k) => $k !== 'className', ARRAY_FILTER_USE_KEY));
        }

        return $dataTable->createAdapter(ORMAdapter::class, [
            'entity' => FormSection::class,
            'query' => fn(QueryBuilder $qb) => $this->buildQuery($qb, $templateId),
        ])->handleRequest($request);
    }

    private function getColumns(): array
    {
        return [
            'name' => [
                'label' => 'Name',
                'field' => 'f.name',
                'className' => TextColumn::class,
                'orderable' => false,
            ],
            'description' => [
                'label' => 'Description',
                'field' => 'f.description',
                'className' => TextColumn::class,
                'orderable' => false,
            ],
            'orderNumber' => [
                'label' => 'Order',
                'field' => 'f.orderNumber',
                'orderable' => false,
                'className' => TextColumn::class,
            ],
            'status' => [
                'label' => 'Status',
                'field' => 'f.status',
                'className' => TextColumn::class,
                'render' => fn($value) => $this->renderStatusBadge($value),
                'orderable' => false,
            ],
            'actions' => [
                'label' => 'Actions',
                'field' => 'f.id',
                'className' => TextColumn::class,
                'orderable' => false,
                'render' => fn($value, $context) => $this->renderActionColumn($context),
            ],
        ];
    }

    private function renderStatusBadge(bool $status): string
    {
        return $status
            ? '<span class="badge bg-success">Active</span>'
            : '<span class="badge bg-danger">Inactive</span>';
    }


    private function renderActionColumn(FormSection $formSection): string
    {
        $entityId = $formSection->getId();
        $templateId = $formSection->getTemplate()->getId();
        $editUrl = $this->router->generate('form_section_edit', ['template_id' => $templateId, 'id' => $entityId]);
        $toggleStatusUrl = $this->router->generate('form_section_toggle_status', ['id' => $entityId]);
        $reorderUrl = $this->router->generate('form_section_reorder', ['id' => $entityId]);
        $csrfToken = $this->csrfTokenManager->getToken('toggle-status' . $entityId)->getValue(); // Adjusted to get value
        $sectionsUrl = $this->router->generate('form_field_index', ['template_id' => $templateId, 'sections_id' => $entityId]);
        $deleteUrl = $this->router->generate('form_section_delete', ['template_id' => $templateId, 'id' => $entityId]);
        $deleteToken = $this->csrfTokenManager->getToken('delete' . $entityId)->getValue();

        $modalId = 'deleteModal_' . $entityId;
        $modalBody = sprintf(
            'Are you sure you want to delete the section "%s"?<br><br>' .
            '<strong>Warning:</strong> This action will also delete all connected fields associated with this section. This action cannot be undone.',
            htmlspecialchars($formSection->getName())
        );

        $footerButtons = sprintf(
            '<button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>' .
            '<button type="button" class="btn btn-primary delete-form-section-btn"  data-delete="true" data-url="%s" data-token="%s">Delete</button>',
            $deleteUrl,
            $deleteToken
        );

        $deleteModal = $this->modalboxHelper->renderModal(
            $modalId,
            'Confirm Section Deletion',
            $modalBody,
            $footerButtons
        );

        return sprintf(
            '<div class="d-inline-flex gap-2">
            <a href="%s" class="bs-btn btn btn-primary btn-sm bs-m" title="Edit"><i class="bs-fa fa fa-edit"></i></a>
            <button class="bs-btn btn btn-primary btn-sm bs-m toggle-status-btn" 
                    data-id="%s" 
                    data-token="%s" 
                    data-url="%s" 
                    title="Toggle Status">
                <i class="bs-fa fa fa-toggle-on"></i>
            </button>
            <a href="%s?direction=up" class="bs-btn btn btn-primary btn-sm bs-m" title="Move Up"><i class="bs-fa fa fa-arrow-up"></i></a>
            <a href="%s?direction=down" class="bs-btn btn btn-primary btn-sm bs-m" title="Move Down"><i class="bs-fa fa fa-arrow-down"></i></a>
            <a href="%s" class="bs-btn btn btn-primary btn-sm bs-m" title="Manage Fields"><i class="bs-fa fa fa-list"></i></a>
            <button type="button" class="bs-btn btn btn-primary btn-sm bs-m" data-toggle="modal" data-target="#%s" title="Deactivate">
                <i class="bs-fa fa fa-trash"></i>
            </button>
        </div>%s',
            $editUrl,
            $entityId,
            $csrfToken,
            $toggleStatusUrl,
            $reorderUrl,
            $reorderUrl,
            $sectionsUrl,
            $modalId,
            $deleteModal
        );
    }

    public function buildQuery(QueryBuilder $qb, int $templateId): QueryBuilder
    {
        return $qb->select('f')
            ->from(FormSection::class, 'f')
            ->where('f.template = :templateId')
            ->andWhere('f.isDelete = :isDeleted')
            ->setParameter('templateId', $templateId)
            ->setParameter('isDeleted', false)
            ->orderBy('f.orderNumber', 'ASC');
    }
}
