<div class="content">
    {% if is_granted('ROLE_ADMIN') %}
    <div class="card">
        {% include 'admin/partials/filters.html.twig'%}
    </div>
    {% endif %}
    <div class="animated fadeIn">
        {% if show %}
            {% if is_granted('ROLE_EMPLOYEE') %}
                <div class="leave-box-container">
                    {% set colors = ['#f78739'] %}
                    {% set protectedIcons = {
                        'ADDON LEAVE': 'fa fa-plus-square',
                        'SICK LEAVE': 'fa fa-medkit',
                        'CASUAL LEAVE': 'fa fa-shopping-cart',
                    } %}

                    {% set defaultIcons = ['fa fa-plus', 'fa fa-circle', 'fa fa-star'] %}
                    {% set defaultIndex = 0 %}

                    {% for leaveType, remainingDays in leaveData %}
                        {% set upperType = (leaveType ~ '')|upper %}
                        {% set icon = protectedIcons[upperType] ?? defaultIcons[defaultIndex % defaultIcons|length] %}
                        {% set color = colors[loop.index0 % colors|length] %}
                        {% set defaultIndex = icon in protectedIcons ? defaultIndex : defaultIndex + 1 %}

                        <div class="leave-box-card" style="border-left-color: {{ color }};">
                            <div class="leave-box-inner">
                                <div class="leave-box-icon"
                                     style="background: linear-gradient(135deg, {{ color }}, {{ color }}80);">
                                    <i class="{{ icon }}"></i>
                                </div>
                                <div>
                                    <div class="leave-box-days">{{ remainingDays }}</div>
                                    <div class="leave-box-type">{{ leaveType|lower }}</div>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% endif %}
        {% endif %}
        <div class="row">
            <div id="order-loader">
                <img src="{{ asset('images/loader.svg') }}" alt="loader">
            </div>
            <div class="col-md-12">
                <div id="flash-container"></div>
                <div class="card">
                    <div class="card-header d-flex justify-content-between ">
                        {% if show %}
                            {% if is_granted('ROLE_ADMIN') %}
                                <strong class="card-title">Leave Request</strong>
                                <a href="{{ path('admin_leave_request_create') }}" class="btn btn-primary">Request New
                                    Leave</a>
                            {% elseif is_granted('ROLE_EMPLOYEE') %}
                                <strong class="card-title">My Leaves</strong>
                                <a href="{{ path('employee_leave_request_create') }}" class="btn btn-primary">Request
                                    New Leave</a>
                            {% endif %}
                        {% endif %}
                    </div>
                    <div class="card-body">
                        <div class="table-stats order-table ov-h text-center">
                            <div id="formTemplate" class="datatable-main-wrapper">Loading...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="clearfix"></div>
<script src="{{ asset('assets/js/datatables.min.js') }}"></script>
<script src="{{ asset('bundles/datatables/js/datatables.js') }}"></script>
<script src="{{ asset('assets/js/toggle.js') }}"></script>
<script src="{{ asset('assets/js/admin/employee_task_filter.js') }}"></script>
<script src="{{ asset('assets/js/moment.min.js') }}"></script>
<script src="{{ asset('assets/js/daterangepicker.min.js') }}"></script>
<script src="{{ asset('assets/js/datatableclearsearch.js') }}"></script>
<script>
    $(document).ready(function () {
        recordsTable = $('#formTemplate').initDataTables({{ datatable_settings(datatable) | raw }})
            .then(function (dt) {
                initializeToggleButtons();
                dt.on('draw', function () {
                    initializeToggleButtons();
                });
                initializeSearch(dt);

                return dt;
            })
            .catch(function (error) {
                console.error('Error initializing DataTable:', error);
            });
    });
</script>
