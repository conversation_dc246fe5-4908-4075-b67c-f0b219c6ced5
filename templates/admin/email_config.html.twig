{% extends 'admin/dashboard/base.html.twig' %}

{% block body %}
    {% include 'admin/partials/breadcrumbs.html.twig' with {
        breadcrumbs: [
            { 'label': 'Dashboard', 'url': url('admin_dashboard') },
        ],
        active: 'Mailer'
    } %}

    <div class="content">
        <div class="animated fadeIn">
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <strong class="card-title">Mailer</strong>
                        </div>
                        <div class="card-body">
                            <form id="update-settings-form" novalidate>
                                <div class="form-group">
                                    <label for="Host" class="control-label required">Host:</label>
                                    <input
                                            id="Host"
                                            name="Host"
                                            type="text"
                                            class="form-control"
                                            placeholder="e.g., smtppro.zoho.com"
                                            value="{{ settings['Host']|default('') }}"
                                            required>
                                    <small class="form-text text-muted">Enter the SMTP host address</small>
                                    <div class="invalid-feedback" id="host-error"></div>
                                </div>
                                <div class="form-group">
                                    <label for="Port" class="control-label required">Port:</label>
                                    <input
                                            id="Port"
                                            name="Port"
                                            type="number"
                                            class="form-control"
                                            placeholder="e.g., 465"
                                            value="{{ settings['Port']|default('') }}"
                                            min="1"
                                            max="65535"
                                            required>
                                    <small class="form-text text-muted">Enter a valid port number (1-65535)</small>
                                    <div class="invalid-feedback" id="port-error"></div>
                                </div>
                                <div class="form-group">
                                    <label for="Username" class="control-label required">Username:</label>
                                    <input
                                            id="Username"
                                            name="Username"
                                            type="text"
                                            class="form-control"
                                            value="{{ settings['Username']|default('') }}"
                                            required>
                                    <small class="form-text text-muted">Enter your email username</small>
                                    <div class="invalid-feedback" id="username-error"></div>
                                </div>
                                <div class="form-group">
                                    <label for="Password" class="control-label required">Password:</label>
                                    <input
                                            id="Password"
                                            name="Password"
                                            type="password"
                                            class="form-control"
                                            value="{{ settings['Password']|default('') }}"
                                            required>
                                    <small class="form-text text-muted">Enter your email password</small>
                                    <div class="invalid-feedback" id="password-error"></div>
                                </div>
                                <div class="form-group">
                                    <label for="Sender_Address" class="control-label required">Sender Address:</label>
                                    <input
                                            id="Sender_Address"
                                            name="Sender_Address"
                                            type="email"
                                            class="form-control"
                                            placeholder="e.g., <EMAIL>"
                                            value="{{ settings['Sender_Address']|default('') }}"
                                            required>
                                    <small class="form-text text-muted">Enter a valid email address</small>
                                    <div class="invalid-feedback" id="sender-address-error"></div>
                                </div>
                                <div>
                                    <button
                                            id="email-config"
                                            type="submit"
                                            class="btn btn-block btn-primary"
                                            disabled>
                                        Update Settings
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="clearfix"></div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script src="{{ asset('assets/js/admin/emailconfig.js') }}"></script>
    <script>
        const csrfToken = '{{ csrf_token('setting_update') }}';
    </script>

{% endblock %}