const axios = require('axios');

class OfflineService {
    constructor(dbService, store) {
        this.dbService = dbService;
        this.store = store || { get: () => null, set: () => {}, delete: () => {} }; // Fallback for testing
        this.isOnline = false;
        this.syncQueue = [];
        this.lastConnectionCheck = null;
        this.connectionCheckInterval = null;
        this.syncInterval = null;
    }

    async initialize() {
        await this.checkConnection();
        this.startConnectionMonitoring();
        this.startSyncService();
    }

    async checkConnection() {
        try {
            const apiBaseUrl = this.store.get('apiBaseUrl') || 'http://127.0.0.1:8000';
            const response = await axios.get(`${apiBaseUrl}/api/health`, {
                timeout: 5000
            });
            
            if (response.data && response.data.status === 'ok') {
                this.isOnline = true;
                this.lastConnectionCheck = new Date();
                
                // If we just came back online, trigger sync
                if (!this.wasOnline) {
                    this.wasOnline = true;
                    await this.syncPendingData();
                }
                
                return true;
            }
        } catch (error) {
            this.isOnline = false;
            this.wasOnline = false;
        }
        
        this.lastConnectionCheck = new Date();
        return this.isOnline;
    }

    startConnectionMonitoring() {
        // Check connection every 30 seconds
        this.connectionCheckInterval = setInterval(async () => {
            await this.checkConnection();
        }, 30000);
    }

    startSyncService() {
        // Sync data every 5 minutes when online
        this.syncInterval = setInterval(async () => {
            if (this.isOnline) {
                await this.syncPendingData();
            }
        }, 5 * 60 * 1000);
    }

    async authenticateOffline(email, password) {
        try {
            // Check if user exists in local database
            const user = await this.dbService.get(
                'SELECT * FROM users WHERE email = ? OR username = ?',
                [email, email]
            );

            if (!user) {
                return { success: false, message: 'User not found in offline storage' };
            }

            // Check if there's a valid token
            const validToken = await this.dbService.get(
                'SELECT * FROM tokens WHERE user_id = ? AND expires_at > datetime("now")',
                [user.id]
            );

            if (!validToken) {
                return { success: false, message: 'No valid offline session found' };
            }

            // Log offline login
            await this.dbService.logActivity(user.id, 'Logged in offline', 'Offline authentication successful');

            return {
                success: true,
                user: {
                    id: user.id,
                    email: user.email,
                    username: user.username,
                    name: user.name,
                    roles: JSON.parse(user.roles || '[]'),
                    employeeCode: user.employee_code,
                    isTeamLeader: user.is_team_leader === 1,
                    isHrAccount: user.is_hr_account === 1
                },
                token: validToken.token,
                offline: true
            };
        } catch (error) {
            console.error('Offline authentication failed:', error);
            return { success: false, message: 'Offline authentication failed' };
        }
    }

    async queueForSync(action, data) {
        try {
            const syncItem = {
                id: Date.now() + Math.random(),
                action: action,
                data: data,
                timestamp: new Date().toISOString(),
                attempts: 0,
                maxAttempts: 3
            };

            this.syncQueue.push(syncItem);

            // Store in database for persistence
            await this.dbService.run(
                'INSERT INTO sync_queue (id, action, data, timestamp, attempts) VALUES (?, ?, ?, ?, ?)',
                [syncItem.id, syncItem.action, JSON.stringify(syncItem.data), syncItem.timestamp, syncItem.attempts]
            );

            console.log('Queued for sync:', action);
        } catch (error) {
            console.error('Failed to queue for sync:', error);
        }
    }

    async syncPendingData() {
        if (!this.isOnline) {
            console.log('Cannot sync - offline');
            return;
        }

        try {
            // Load pending sync items from database
            const pendingItems = await this.dbService.all(
                'SELECT * FROM sync_queue ORDER BY timestamp ASC'
            );

            for (const item of pendingItems) {
                try {
                    await this.processSyncItem(item);
                    
                    // Remove from database on success
                    await this.dbService.run('DELETE FROM sync_queue WHERE id = ?', [item.id]);
                    
                } catch (error) {
                    console.error('Failed to sync item:', item, error);
                    
                    // Increment attempts
                    const newAttempts = item.attempts + 1;
                    if (newAttempts >= 3) {
                        // Max attempts reached, remove from queue
                        await this.dbService.run('DELETE FROM sync_queue WHERE id = ?', [item.id]);
                        console.log('Max sync attempts reached for item:', item.id);
                    } else {
                        // Update attempts count
                        await this.dbService.run(
                            'UPDATE sync_queue SET attempts = ? WHERE id = ?',
                            [newAttempts, item.id]
                        );
                    }
                }
            }

            console.log('Sync completed');
        } catch (error) {
            console.error('Sync process failed:', error);
        }
    }

    async processSyncItem(item) {
        const apiBaseUrl = this.store.get('apiBaseUrl') || 'http://127.0.0.1:8000';
        const token = this.store.get('authToken');

        if (!token) {
            throw new Error('No auth token available for sync');
        }

        const data = JSON.parse(item.data);

        switch (item.action) {
            case 'update_profile':
                await axios.put(`${apiBaseUrl}/api/employee/profile`, data, {
                    headers: { 'Authorization': `Bearer ${token}` }
                });
                break;

            case 'log_activity':
                await axios.post(`${apiBaseUrl}/api/employee/activity`, data, {
                    headers: { 'Authorization': `Bearer ${token}` }
                });
                break;

            case 'attendance_punch':
                await axios.post(`${apiBaseUrl}/api/attendance/punch`, data, {
                    headers: { 'Authorization': `Bearer ${token}` }
                });
                break;

            case 'sync_time_logs':
                await axios.post(`${apiBaseUrl}/api/sync/time-logs`, data, {
                    headers: { 'Content-Type': 'application/json' }
                });
                break;

            case 'sync_screenshots':
                await axios.post(`${apiBaseUrl}/api/screenshots/sync`, data, {
                    headers: { 'Content-Type': 'application/json' }
                });
                break;

            case 'sync_application_usage':
                await axios.post(`${apiBaseUrl}/api/sync/application-usage`, data, {
                    headers: { 'Content-Type': 'application/json' }
                });
                break;

            case 'sync_website_usage':
                await axios.post(`${apiBaseUrl}/api/sync/website-usage`, data, {
                    headers: { 'Content-Type': 'application/json' }
                });
                break;

            case 'bulk_sync':
                await axios.post(`${apiBaseUrl}/api/sync/bulk`, data, {
                    headers: { 'Content-Type': 'application/json' }
                });
                break;

            case 'sync_webcam_captures':
                await axios.post(`${apiBaseUrl}/api/webcam-captures/sync`, data, {
                    headers: { 'Authorization': `Bearer ${token}` }
                });
                break;

            default:
                console.warn('Unknown sync action:', item.action);
        }
    }

    async getOfflineData(type, params = {}) {
        try {
            switch (type) {
                case 'user_profile':
                    return await this.dbService.get(
                        'SELECT * FROM users WHERE id = ?',
                        [params.userId]
                    );

                case 'recent_activity':
                    return await this.dbService.all(
                        'SELECT * FROM activity_log WHERE user_id = ? ORDER BY timestamp DESC LIMIT ?',
                        [params.userId, params.limit || 10]
                    );

                case 'app_settings':
                    return await this.dbService.all('SELECT * FROM app_settings');

                default:
                    throw new Error('Unknown offline data type');
            }
        } catch (error) {
            console.error('Failed to get offline data:', error);
            throw error;
        }
    }

    async storeOfflineData(type, data) {
        try {
            switch (type) {
                case 'user_profile':
                    await this.dbService.saveUser(data);
                    break;

                case 'activity':
                    await this.dbService.logActivity(data.userId, data.action, data.details);
                    break;

                case 'app_setting':
                    await this.dbService.setSetting(data.key, data.value);
                    break;

                default:
                    throw new Error('Unknown offline data type');
            }
        } catch (error) {
            console.error('Failed to store offline data:', error);
            throw error;
        }
    }

    async clearExpiredData() {
        try {
            // Clear expired tokens
            await this.dbService.clearExpiredTokens();

            // Clear old activity logs (keep last 1000 entries)
            await this.dbService.run(`
                DELETE FROM activity_log 
                WHERE id NOT IN (
                    SELECT id FROM activity_log 
                    ORDER BY timestamp DESC 
                    LIMIT 1000
                )
            `);

            // Clear old sync queue items (older than 7 days)
            const weekAgo = new Date();
            weekAgo.setDate(weekAgo.getDate() - 7);
            
            await this.dbService.run(
                'DELETE FROM sync_queue WHERE timestamp < ?',
                [weekAgo.toISOString()]
            );

            console.log('Expired data cleared');
        } catch (error) {
            console.error('Failed to clear expired data:', error);
        }
    }

    getConnectionStatus() {
        return {
            isOnline: this.isOnline,
            lastCheck: this.lastConnectionCheck,
            pendingSyncItems: this.syncQueue.length
        };
    }

    async destroy() {
        if (this.connectionCheckInterval) {
            clearInterval(this.connectionCheckInterval);
        }
        if (this.syncInterval) {
            clearInterval(this.syncInterval);
        }
    }
}

module.exports = OfflineService;
