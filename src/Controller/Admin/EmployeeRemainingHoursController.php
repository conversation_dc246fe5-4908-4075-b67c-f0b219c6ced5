<?php

namespace App\Controller\Admin;

use App\Entity\EmployeeNotification;
use App\Entity\EmployeeRemainingHours;
use App\Message\EmployeeNotificationMessage;
use App\Service\Admin\RemainingHoursService;
use App\Traits\ExceptionLoggerTrait;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Routing\Annotation\Route;

#[Route('/admin')]
class EmployeeRemainingHoursController extends AbstractController
{
    use ExceptionLoggerTrait;

    public function __construct(
        ParameterBagInterface                  $parameterBag,
        private readonly RemainingHoursService $remainingHoursService,
        private readonly MessageBusInterface   $messageBus,
        private readonly EntityManagerInterface $entityManager,
    )
    {
        $this->initializeLogger($parameterBag, 'employee_controller');
    }

    /**
     * @param Request $request
     * @return Response
     */
    #[Route(path: '/remaining', name: 'remaining', methods: ['GET', 'POST'])]
    public function index(Request $request): Response
    {
        $table = $this->remainingHoursService->createRemainingHoursTable($request);

        if ($table->isCallback()) {
            return $table->getResponse();
        }

        return $this->render('admin/remaininghours.html.twig', [
            'datatable' => $table,
        ]);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    #[Route(path: '/remaining/employee-notification/{id}', name: 'remaining_notification', methods: ['GET', 'POST'])]
    public function remainingNotify(
        Request $request,
        EmployeeNotification $notification
    ): JsonResponse {
        try {
            $remainingHours = $this->entityManager
                ->getRepository(EmployeeRemainingHours::class)
                ->findOneBy(['employeeNotification' => $notification]);

            if (!$remainingHours) {
                return new JsonResponse([
                    'success' => false,
                    'message' => 'EmployeeRemainingHours not found for this notification.'
                ], Response::HTTP_NOT_FOUND);
            }

            $data = json_decode($request->getContent(), true, 512, JSON_THROW_ON_ERROR);
            $reason = $data['emailContentBody'] ?? '';

            if (empty($reason)) {
                return new JsonResponse([
                    'success' => false,
                    'message' => 'Disapproval reason is required.'
                ], Response::HTTP_BAD_REQUEST);
            }

            // Save email content
            $remainingHours->addEmailContent($reason);
            $this->entityManager->flush();

            $employeeHours = $notification->getEmployeeHours();
            try {
                $message = new EmployeeNotificationMessage(
                    $employeeHours->getEmployee()->getId(),
                    $employeeHours->getTotalHours(),
                    $employeeHours->getId(),
                    $employeeHours->getReportDate()->format('Y-m-d'),
                    false,
                    null,
                    false,
                    $reason
                );
                $this->messageBus->dispatch($message);
            }catch (\Exception $e){
                $this->log('Notification dispatch failed: ' . $e->getMessage());
            }
            return new JsonResponse([
                'success' => true,
                'message' => 'Message sent successfully.'
            ]);
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'message' => 'Error sending mail: ' . $e->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

}