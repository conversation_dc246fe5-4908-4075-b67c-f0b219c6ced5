<?php

namespace App\Repository;

use App\Entity\Payroll;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Payroll>
 */
class PayrollRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Payroll::class);
    }

    /**
     * @param int $employeeId
     * @return Payroll[]
     */
    public function findByEmployeeOrderedByYearMonth(int $employeeId): array
    {
        return $this->createQueryBuilder('p')
            ->andWhere('p.employee = :employeeId')
            ->setParameter('employeeId', $employeeId)
            ->orderBy('p.year', 'DESC')
            ->addOrderBy('p.month', 'DESC')
            ->getQuery()
            ->getResult();
    }
}
