<?php

namespace App\Entity;

use App\Repository\PayrollRepository;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity(repositoryClass: PayrollRepository::class)]
#[ORM\Table(name: 'payroll')]
class Payroll
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private ?int $id = null;

    #[ORM\ManyToOne(targetEntity: MasterEmployee::class)]
    #[ORM\JoinColumn(nullable: false)]
    private ?MasterEmployee $employee = null;

    #[ORM\Column(type: 'integer')]
    #[Assert\Range(min: 1, max: 12)]
    private int $month;

    #[ORM\Column(type: 'integer')]
    #[Assert\Range(min: 2000, max: 2100)]
    private int $year;

    #[ORM\Column(type: 'decimal', precision: 12, scale: 2)]
    private ?float $salary = null;

    #[ORM\Column(type: 'decimal', precision: 12, scale: 2)]
    private ?float $deduction = null;

    #[ORM\Column(type: 'integer')]
    private ?int $workingDays = null;

    #[ORM\Column(type: 'decimal', precision: 5, scale: 2)]
    private ?float $presentDays = null;

    #[ORM\Column(type: 'decimal', precision: 12, scale: 2)]
    private ?float $perDayBasedOnBasic = null;

    #[ORM\Column(type: 'decimal', precision: 12, scale: 2, nullable: true)]
    private ?float $totalLeaveDeduction = null;

    #[ORM\Column(type: 'decimal', precision: 12, scale: 2, nullable: true)]
    private ?float $extraPay = null;

    #[ORM\Column(type: 'decimal', precision: 12, scale: 2)]
    private ?float $salaryAfterLeave = null;

    #[ORM\Column(type: 'decimal', precision: 12, scale: 2)]
    private ?float $basicDaSalary = null;

    #[ORM\Column(type: 'decimal', precision: 12, scale: 2)]
    private ?float $hra = null;

    #[ORM\Column(type: 'decimal', precision: 12, scale: 2)]
    private ?float $conveyanceAllowance = null;

    #[ORM\Column(type: 'decimal', precision: 12, scale: 2)]
    private ?float $medicalAllowance = null;

    #[ORM\Column(type: 'decimal', precision: 12, scale: 2)]
    private ?float $telephoneAllowance = null;

    #[ORM\Column(type: 'decimal', precision: 12, scale: 2)]
    private ?float $specialAllowance = null;

    #[ORM\Column(type: 'decimal', precision: 12, scale: 2)]
    private ?float $netSalary = null;

    #[ORM\Column(type: 'decimal', precision: 12, scale: 2, nullable: true)]
    private ?float $diff = null;

    #[ORM\Column(type: 'decimal', precision: 12, scale: 2, nullable: true)]
    private ?float $pt = null;

    #[ORM\Column(type: 'decimal', precision: 12, scale: 2, nullable: true)]
    private ?float $esic = null;

    #[ORM\Column(type: 'decimal', precision: 12, scale: 2, nullable: true)]
    private ?float $salaryForPFCalculations = null;

    #[ORM\Column(type: 'decimal', precision: 12, scale: 2, nullable: true)]
    private ?float $pfEmployee = null;

    #[ORM\Column(type: 'decimal', precision: 12, scale: 2, nullable: true)]
    private ?float $tdsDeduction = null;

    #[ORM\Column(type: 'decimal', precision: 12, scale: 2, nullable: true)]
    private ?float $loanOtherDeductions = null;

    #[ORM\Column(type: 'decimal', precision: 12, scale: 2, nullable: true)]
    private ?float $totalDeductions = null;

    #[ORM\Column(type: 'decimal', precision: 12, scale: 2)]
    private ?float $salaryPayable = null;

    #[ORM\Column(type: 'decimal', precision: 12, scale: 2, nullable: true)]
    private ?float $pfEmployer = null;

    #[ORM\Column(type: 'decimal', precision: 12, scale: 2)]
    private ?float $ctc = null;

    #[ORM\Column(type: 'decimal', precision: 12, scale: 2)]
    private ?float $inputCtcForCalculation = null;


    public function getId(): ?int
    {
        return $this->id;
    }

    public function getEmployee(): ?MasterEmployee
    {
        return $this->employee;
    }

    public function setEmployee(?MasterEmployee $employee): self
    {
        $this->employee = $employee;
        return $this;
    }

    public function getMonth(): int
    {
        return $this->month;
    }

    public function setMonth(int $month): self
    {
        $this->month = $month;
        return $this;
    }

    public function getYear(): int
    {
        return $this->year;
    }

    public function setYear(int $year): self
    {
        $this->year = $year;
        return $this;
    }

    public function getSalary(): ?float { return $this->salary; }
    public function setSalary(?float $salary): self { $this->salary = $salary; return $this; }

    public function getDeduction(): ?float { return $this->deduction; }
    public function setDeduction(?float $deduction): self { $this->deduction = $deduction; return $this; }

    public function getWorkingDays(): ?int { return $this->workingDays; }
    public function setWorkingDays(?int $workingDays): self { $this->workingDays = $workingDays; return $this; }

    public function getPresentDays(): ?float { return $this->presentDays; }
    public function setPresentDays(?float $presentDays): self { $this->presentDays = $presentDays; return $this; }

    public function getPerDayBasedOnBasic(): ?float { return $this->perDayBasedOnBasic; }
    public function setPerDayBasedOnBasic(?float $value): self { $this->perDayBasedOnBasic = $value; return $this; }

    public function getTotalLeaveDeduction(): ?float { return $this->totalLeaveDeduction; }
    public function setTotalLeaveDeduction(?float $value): self { $this->totalLeaveDeduction = $value; return $this; }

    public function getExtraPay(): ?float { return $this->extraPay; }
    public function setExtraPay(?float $value): self { $this->extraPay = $value; return $this; }

    public function getSalaryAfterLeave(): ?float { return $this->salaryAfterLeave; }
    public function setSalaryAfterLeave(?float $value): self { $this->salaryAfterLeave = $value; return $this; }

    public function getBasicDaSalary(): ?float { return $this->basicDaSalary; }
    public function setBasicDaSalary(?float $value): self { $this->basicDaSalary = $value; return $this; }

    public function getHra(): ?float { return $this->hra; }
    public function setHra(?float $value): self { $this->hra = $value; return $this; }

    public function getConveyanceAllowance(): ?float { return $this->conveyanceAllowance; }
    public function setConveyanceAllowance(?float $value): self { $this->conveyanceAllowance = $value; return $this; }

    public function getMedicalAllowance(): ?float { return $this->medicalAllowance; }
    public function setMedicalAllowance(?float $value): self { $this->medicalAllowance = $value; return $this; }

    public function getTelephoneAllowance(): ?float { return $this->telephoneAllowance; }
    public function setTelephoneAllowance(?float $value): self { $this->telephoneAllowance = $value; return $this; }

    public function getSpecialAllowance(): ?float { return $this->specialAllowance; }
    public function setSpecialAllowance(?float $value): self { $this->specialAllowance = $value; return $this; }

    public function getNetSalary(): ?float { return $this->netSalary; }
    public function setNetSalary(?float $value): self { $this->netSalary = $value; return $this; }

    public function getDiff(): ?float { return $this->diff; }
    public function setDiff(?float $value): self { $this->diff = $value; return $this; }

    public function getPt(): ?float { return $this->pt; }
    public function setPt(?float $value): self { $this->pt = $value; return $this; }

    public function getEsic(): ?float { return $this->esic; }
    public function setEsic(?float $value): self { $this->esic = $value; return $this; }

    public function getSalaryForPFCalculations(): ?float { return $this->salaryForPFCalculations; }
    public function setSalaryForPFCalculations(?float $value): self { $this->salaryForPFCalculations = $value; return $this; }

    public function getPfEmployee(): ?float { return $this->pfEmployee; }
    public function setPfEmployee(?float $value): self { $this->pfEmployee = $value; return $this; }

    public function getTdsDeduction(): ?float { return $this->tdsDeduction; }
    public function setTdsDeduction(?float $value): self { $this->tdsDeduction = $value; return $this; }

    public function getLoanOtherDeductions(): ?float { return $this->loanOtherDeductions; }
    public function setLoanOtherDeductions(?float $value): self { $this->loanOtherDeductions = $value; return $this; }

    public function getTotalDeductions(): ?float { return $this->totalDeductions; }
    public function setTotalDeductions(?float $value): self { $this->totalDeductions = $value; return $this; }

    public function getSalaryPayable(): ?float { return $this->salaryPayable; }
    public function setSalaryPayable(?float $value): self { $this->salaryPayable = $value; return $this; }

    public function getPfEmployer(): ?float { return $this->pfEmployer; }
    public function setPfEmployer(?float $value): self { $this->pfEmployer = $value; return $this; }

    public function getCtc(): ?float { return $this->ctc; }
    public function setCtc(?float $value): self { $this->ctc = $value; return $this; }

    public function getInputCtcForCalculation(): ?float { return $this->inputCtcForCalculation; }
    public function setInputCtcForCalculation(?float $value): self { $this->inputCtcForCalculation = $value; return $this; }
}
