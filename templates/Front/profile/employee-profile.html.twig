{% extends 'Front/dashboard/base.html.twig' %}

{% block title %}Employee Profile{% endblock %}

{% block body %}
    <div class="profile-container">
        <div class="animated fadeIn">
            <div class="profile-card">
                <div class="card-header">
                    <h2>Employee Profile</h2>
                </div>
                <div class="profile-header">
                    <div class="profile-avatar">
                        <a href="#" class="dropdown-toggle active" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            {% if profilePicture %}
                                <img class="user-avatar rounded-circle"
                                     src="{{ asset(profilePicture.filePath) }}"
                                     alt="User Avatar">
                            {% else %}
                                <img class="user-avatar rounded-circle"
                                     src="{{ asset('images/admin.jpg') }}"
                                     alt="Default Avatar">
                            {% endif %}
                        </a>
                    </div>
                    <div class="profile-info">
                        <h5>{{ employee.name }}</h5>
                        <span>{{ employee.email }}</span>
                    </div>
                </div>

                <div class="tabs">
                    <div class="tab active" data-target="personal-info">Personal Info</div>
                    <div style="text-align: right; margin-bottom: 10px;">
                        <a href="{{ path('employee_profile_edit_personal_info') }}" class="btn btn-primary btn-sm"><i class="bs-fa fa fa-pencil"></i></a>
                    </div>
                    <div class="tab" data-target="work-info">Work Info</div>
                    <div class="tab" data-target="bank-info">Bank Details</div>
                    <div class="tab" data-target="hardware-info">Hardware</div>
                </div>

                <div class="tab-content active" id="personal-info">
                    <div class="info-grid">
                        <div class="info-item">
                            <strong>Username</strong>
                            <span>{{ employee.username }}</span>
                        </div>
                        <div class="info-item">
                            <strong>Phone Number</strong>
                            <span>{{ employee.personalPhoneNumber ?: 'N/A' }}</span>
                        </div>
                        <div class="info-item">
                            <strong>Alternative Phone</strong>
                            <span>{{ employee.alternativePhoneNumber ?: 'N/A' }}</span>
                        </div>
                        <div class="info-item">
                            <strong>Current Address</strong>
                            <span>{{ employee.currentAddress ?: 'N/A' }}</span>
                        </div>
                        <div class="info-item">
                            <strong>Permanent Address</strong>
                            <span>{{ employee.permanentAddress ?: 'N/A' }}</span>
                        </div>
                        <div class="info-item">
                            <strong>Birth Date</strong>
                            <span>{{ employee.birthDate ? employee.birthDate|custom_date : 'N/A' }}</span>
                        </div>
                        <div class="info-item">
                            <strong>Marital Status</strong>
                            <span>{{ employee.maritalStatus ? employee.maritalStatus|capitalize : 'N/A' }}</span>
                        </div>
                        <div class="info-item">
                            <strong>Gender</strong>
                            <span>{{ employee.gender ? employee.gender|capitalize : 'N/A' }}</span>
                        </div>
                    </div>
                </div>

                <div class="tab-content" id="work-info">
                    <div class="info-grid">
                        <div class="info-item">
                            <strong>Employee Code</strong>
                            <span>{{ employee.employeeCode ?: 'N/A' }}</span>
                        </div>
                        <div class="info-item">
                            <strong>Joining Date</strong>
                            <span>{{ employee.joiningDate ? employee.joiningDate|custom_date : 'N/A' }}</span>
                        </div>
                        <div class="info-item">
                            <strong>Total Experience</strong>
                            <span>{{ employee.totalExperience ?: 'N/A' }}</span>
                            <div class="experience-bar">
                                <div class="experience-fill"></div>
                            </div>
                        </div>
                        <div class="info-item">
                            <strong>Previous Company</strong>
                            <span>{{ employee.previousCompanyName ?: 'N/A' }}</span>
                        </div>
                        <div class="info-item">
                            <strong>Confirmation Date</strong>
                            <span>{{ employee.confirmationDate ? employee.confirmationDate|custom_date : 'N/A' }}</span>
                        </div>
                    </div>
                    <h5 class="section-title">Departments</h5>
                    <ul class="report">
                        {% for department in employee.departments %}
                            <li>{{ department.depName }}</li>
                        {% else %}
                            <li>No department assigned</li>
                        {% endfor %}
                    </ul>
                    <h5 class="section-title">Reports To</h5>
                    <ul class="report">
                        {% for report in employee.reportsTo %}
                            <li>{{ report.teamLeader.name }}</li>
                        {% else %}
                            <li>No reporting leader assigned</li>
                        {% endfor %}
                    </ul>
                </div>

                <div class="tab-content" id="bank-info">
                    <div class="info-grid">
                        <div class="info-item">
                            <strong>Bank Name</strong>
                            <span>{{ employee.bankName ?: 'N/A' }}</span>
                        </div>
                        <div class="info-item">
                            <strong>Bank Account</strong>
                            <span>{{ employee.bankAccountNumber ?: 'N/A' }}</span>
                        </div>
                        <div class="info-item">
                            <strong>Pan Number</strong>
                            <span>{{ employee.panNumber ?: 'N/A' }}</span>
                        </div>
                        <div class="info-item">
                            <strong>Aadhaar Number</strong>
                            <span>{{ employee.aadhaarNumber ?: 'N/A' }}</span>
                        </div>
                        <div class="info-item">
                            <strong>ESI Number</strong>
                            <span>{{ employee.esiNumber ?: 'N/A' }}</span>
                        </div>
                        <div class="info-item">
                            <strong>PF Number</strong>
                            <span>{{ employee.pfNumber ?: 'N/A' }}</span>
                        </div>
                        <div class="info-item">
                            <strong>UAN Number</strong>
                            <span>{{ employee.uan ?: 'N/A' }}</span>
                        </div>
                        <div class="info-item">
                            <strong>IFSC Code</strong>
                            <span>{{ employee.ifscCode ?: 'N/A' }}</span>
                        </div>
                    </div>
                </div>

                <div class="tab-content" id="hardware-info">
                    <h5 class="section-title">Assigned Hardware</h5>
                    {% if hardware is defined and hardware|length > 0 %}
                        <div class="hardware-table">
                            <table>
                                <thead>
                                <tr>
                                    <th>Serial</th>
                                    <th>Type</th>
                                    <th>Name</th>
                                    <th>Assign Date</th>
                                    <th>Location</th>
                                    <th>MFR / Model</th>
                                    <th>IP / MAC</th>
                                    <th>Condition</th>
                                </tr>
                                </thead>
                                <tbody>
                                {% for item in hardware %}
                                    <tr>
                                        <td>{{ item.manufacturerSerial ?: 'N/A' }}</td>
                                        <td>{{ item.hardwareType ? item.hardwareType.name : 'N/A' }}</td>
                                        <td>{{ item.hardwareName ?: 'N/A' }}</td>
                                        <td>{{ item.purchaseDate ? item.purchaseDate|custom_date : 'N/A' }}</td>
                                        <td>{{ item.location ?: 'N/A' }}</td>
                                        <td>{{ item.manufacturerName ?: 'N/A' }} / {{ item.manufacturerModel ?: 'N/A' }}</td>
                                        <td>{{ item.ipAddress ?: 'N/A' }} / {{ item.macAddress ?: 'N/A' }}</td>
                                        <td>
                                            {% if item.condition == 'New' %}
                                                <span class="hardware-status status-new">✓ New</span>
                                            {% elseif item.condition == 'Used' %}
                                                <span class="hardware-status status-used">● Used</span>
                                            {% elseif item.condition == 'Refurbished' %}
                                                <span class="hardware-status status-refurbished">↻ Refurbished</span>
                                            {% elseif item.condition == 'Damaged' %}
                                                <span class="hardware-status status-damaged">✕ Damaged</span>
                                            {% else %}
                                                <span class="hardware-status">N/A</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="hardware-empty">
                            <i class="fa fa-laptop"></i>
                            <p>No hardware assigned to this employee.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    <div class="clearfix"></div>
    <link rel="stylesheet" href="{{ asset('assets/css/Profile/profile.css') }}">
    <script src="{{ asset('assets/js/profile/profile.js') }}"></script>
    <script>
        const experienceText = '{{ employee.totalExperience ?: "0" }}';
    </script>
{% endblock %}