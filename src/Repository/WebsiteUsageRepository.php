<?php

namespace App\Repository;

use App\Entity\WebsiteUsage;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<WebsiteUsage>
 *
 * @method WebsiteUsage|null find($id, $lockMode = null, $lockVersion = null)
 * @method WebsiteUsage|null findOneBy(array $criteria, array $orderBy = null)
 * @method WebsiteUsage[]    findAll()
 * @method WebsiteUsage[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class WebsiteUsageRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, WebsiteUsage::class);
    }

    /**
     * Get website usage for a specific employee and date range
     */
    public function findByEmployeeAndDateRange($employeeId, \DateTime $startDate, \DateTime $endDate): array
    {
        return $this->createQueryBuilder('wu')
            ->where('wu.employee = :employeeId')
            ->andWhere('wu.visitDate BETWEEN :startDate AND :endDate')
            ->setParameter('employeeId', $employeeId)
            ->setParameter('startDate', $startDate->format('Y-m-d'))
            ->setParameter('endDate', $endDate->format('Y-m-d'))
            ->orderBy('wu.visitDate', 'DESC')
            ->addOrderBy('wu.visitTime', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Get top websites by usage time
     */
    public function getTopWebsitesByUsage($employeeId, \DateTime $date, int $limit = 10): array
    {
        return $this->createQueryBuilder('wu')
            ->select('wu.domain, SUM(wu.duration) as totalDuration, SUM(wu.visitCount) as totalVisits')
            ->where('wu.employee = :employeeId')
            ->andWhere('wu.visitDate = :date')
            ->andWhere('wu.duration IS NOT NULL')
            ->setParameter('employeeId', $employeeId)
            ->setParameter('date', $date->format('Y-m-d'))
            ->groupBy('wu.domain')
            ->orderBy('totalDuration', 'DESC')
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();
    }

    /**
     * Get productivity statistics for websites
     */
    public function getWebsiteProductivityStats($employeeId, \DateTime $date): array
    {
        $result = $this->createQueryBuilder('wu')
            ->select('wu.productivityLevel, SUM(wu.duration) as totalDuration, COUNT(wu.id) as visitCount')
            ->where('wu.employee = :employeeId')
            ->andWhere('wu.visitDate = :date')
            ->andWhere('wu.duration IS NOT NULL')
            ->andWhere('wu.productivityLevel IS NOT NULL')
            ->setParameter('employeeId', $employeeId)
            ->setParameter('date', $date->format('Y-m-d'))
            ->groupBy('wu.productivityLevel')
            ->getQuery()
            ->getResult();

        $stats = [
            'productive' => ['duration' => 0, 'visits' => 0],
            'neutral' => ['duration' => 0, 'visits' => 0],
            'distracting' => ['duration' => 0, 'visits' => 0]
        ];

        foreach ($result as $row) {
            $level = $row['productivityLevel'];
            if (isset($stats[$level])) {
                $stats[$level]['duration'] = (int) $row['totalDuration'];
                $stats[$level]['visits'] = (int) $row['visitCount'];
            }
        }

        return $stats;
    }

    /**
     * Get website categories statistics
     */
    public function getCategoryStats($employeeId, \DateTime $date): array
    {
        return $this->createQueryBuilder('wu')
            ->select('wu.category, SUM(wu.duration) as totalDuration, COUNT(wu.id) as visitCount')
            ->where('wu.employee = :employeeId')
            ->andWhere('wu.visitDate = :date')
            ->andWhere('wu.duration IS NOT NULL')
            ->andWhere('wu.category IS NOT NULL')
            ->setParameter('employeeId', $employeeId)
            ->setParameter('date', $date->format('Y-m-d'))
            ->groupBy('wu.category')
            ->orderBy('totalDuration', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Get unsynced website usage records
     */
    public function findUnsyncedRecords(): array
    {
        return $this->createQueryBuilder('wu')
            ->where('wu.isSynced = false')
            ->orderBy('wu.visitTime', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Mark records as synced
     */
    public function markAsSynced(array $recordIds): void
    {
        $this->createQueryBuilder('wu')
            ->update()
            ->set('wu.isSynced', 'true')
            ->where('wu.id IN (:recordIds)')
            ->setParameter('recordIds', $recordIds)
            ->getQuery()
            ->execute();
    }
}
