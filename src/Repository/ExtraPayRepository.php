<?php

namespace App\Repository;

use App\Entity\ExtraPay;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<ExtraPay>
 */
class ExtraPayRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, ExtraPay::class);
    }
    /**
     * Get the sum of totalPay for an employee for a given month and year.
     */
    public function getTotalPayForEmployeeMonthYear($employee, int $month, int $year): float
    {
        $qb = $this->createQueryBuilder('e')
            ->select('SUM(e.totalPay) as totalPaySum')
            ->where('e.employee = :employee')
            ->andWhere('e.month = :month')
            ->andWhere('e.year = :year')
            ->setParameter('employee', $employee)
            ->setParameter('month', $month)
            ->setParameter('year', $year);

        $result = $qb->getQuery()->getSingleScalarResult();
        return (float) $result;
    }
}
