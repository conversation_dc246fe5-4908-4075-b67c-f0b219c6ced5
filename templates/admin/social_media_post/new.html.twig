{% extends 'admin/dashboard/base.html.twig' %}

{% block body %}
    {% include 'admin/partials/breadcrumbs.html.twig' with {
        title: 'Social Media Posts',
        breadcrumbs: [
            { 'label': 'Dashboard', 'url': url('admin_dashboard') },
            { 'label': 'Social Media Posts', 'url': url('social_media_post_list') }
        ],
        active: mode == 'edit' ? 'Edit Social Media Post' : 'Create Social Media Post'
    } %}

    <div class="content">
        <div class="animated fadeIn">
            <div class="row">
                <div class="col-lg-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <strong class="card-title">
                                {{ mode == 'edit' ? 'Edit Social Media Post' : 'Create Social Media Post' }}
                            </strong>
                            <div class="d-flex align-items-center">
                                <a href="{{ path('social_media_post_list') }}" class="btn btn-sm btn-outline-secondary mr-2">
                                    <i class="fa fa-list mr-1"></i>Back to List
                                </a>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="social-media-post-form">
                                {{ form_start(form, {
                                    'action': mode == 'edit' ? path('social_media_post_edit') : path('social_media_post_create'),
                                    'method': 'POST',
                                    'attr': { 'class': 'needs-validation use-loader', 'novalidate': 'novalidate' },
                                    'data-turbo': 'false'
                                }) }}
                                {% if mode == 'edit' and socialMediaPost.id is defined %}
                                    <input type="hidden" name="id" value="{{ socialMediaPost.id }}">
                                {% endif %}
                                <div class="form-section">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                {{ form_label(form.title, 'Post Title', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text"><i class="fa fa-hand-o-right"></i></span>
                                                    </div>
                                                    {{ form_widget(form.title, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter post title', 'required': 'required' } }) }}
                                                </div>
                                                {{ form_errors(form.title) }}
                                                <small class="form-text text-muted">Enter the title of the social media post</small>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                {{ form_label(form.description, 'Description', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text"><i class="fa fa-info-circle"></i></span>
                                                    </div>
                                                    {{ form_widget(form.description, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter post description' } }) }}
                                                </div>
                                                {{ form_errors(form.description) }}
                                                <small class="form-text text-muted">Provide a brief description of the post (optional)</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                {{ form_label(form.postDate, 'Post Date', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text"><i class="fa fa-calendar-check-o"></i></span>
                                                    </div>
                                                    {{ form_widget(form.postDate, {
                                                        'attr': {
                                                            'class': 'form-control datepicker',
                                                            'data-initial-date': socialMediaPost.postDate is defined and socialMediaPost.postDate ? socialMediaPost.postDate|date('d-m-Y') : ''
                                                        }
                                                    }) }}
                                                </div>
                                                {{ form_errors(form.postDate) }}
                                                <small class="form-text text-muted">Select the date of the post</small>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                {{ form_label(form.mediaType, 'Media Type', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text"><i class="fa fa-file"></i></span>
                                                    </div>
                                                    {{ form_widget(form.mediaType, { 'attr': { 'class': 'form-control' } }) }}
                                                </div>
                                                {{ form_errors(form.mediaType) }}
                                                <small class="form-text text-muted">Select the type of media</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="d-flex justify-content-end mt-4">
                                    <a href="{{ path('social_media_post_list') }}" class="btn btn-secondary mr-2">
                                        Cancel
                                    </a>
                                    <button type="submit" class="btn btn-primary pr-3 pl-3">
                                        {{ mode == 'edit' ? 'Update Post' : 'Create Post' }}
                                    </button>
                                </div>
                                {{ form_end(form) }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="clearfix"></div>
    <link rel="stylesheet" href="{{ asset('assets/css/Error/error.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/AdminForm/Form.css') }}">
    <script src="{{ asset('assets/js/datatables.min.js') }}"></script>
    <script src="{{ asset('bundles/datatables/js/datatables.js') }}"></script>
    <script src="{{ asset('assets/js/HolidayDatepicker.js') }}"></script>
    <script src="{{ asset('assets/js/moment.min.js') }}"></script>
    <script src="{{ asset('assets/js/daterangepicker.min.js') }}"></script>
    <script src="{{ asset('assets/js/admin/social_media_post.js') }}"></script>
{% endblock %}