{% extends 'admin/dashboard/base.html.twig' %}

{% block body %}
    {% include 'admin/partials/breadcrumbs.html.twig' with {
        breadcrumbs: [
            { 'label': 'Dashboard', 'url': url('admin_dashboard') },
            { 'label': 'Leave Type', 'url': url('leave_type_list') }
        ],
        active: mode == 'edit' ? 'Edit Leave Type' : 'Create Leave Type'
    } %}

    <div class="content">
        <div class="animated fadeIn">
            <div class="row">
                <div class="col-lg-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <strong class="card-title">
                                {{ mode == 'edit' ? 'Edit Leave Type' : 'Create Leave Type' }}
                            </strong>
                            <div class="d-flex align-items-center">
                                <a href="{{ path('leave_type_list') }}" class="btn btn-sm btn-outline-secondary mr-2">
                                    <i class="fa fa-list mr-1"></i>Back to List
                                </a>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="leave-type-form">
                                {{ form_start(form, {
                                    'action': mode == 'edit' ? path('leave_type_edit') : path('leave_type_create'),
                                    'method': 'POST',
                                    'attr': { 'class': 'needs-validation use-loader', 'novalidate': 'novalidate' },
                                    'data-turbo': 'false'
                                }) }}
                                {% if mode == 'edit' and leaveType.id is defined %}
                                    <input type="hidden" name="id" value="{{ leaveType.id }}">
                                {% endif %}
                                <div class="form-section">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                {{ form_label(form.name, 'Leave Type Name', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text"><i class="fa fa-tag"></i></span>
                                                    </div>
                                                    {{ form_widget(form.name, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter leave type name', 'required': 'required' } }) }}
                                                </div>
                                                {{ form_errors(form.name) }}
                                                <small class="form-text text-muted">Enter the name of the leave type (unique)</small>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                {{ form_label(form.description, 'Description', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text"><i class="fa fa-info-circle"></i></span>
                                                    </div>
                                                    {{ form_widget(form.description, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter leave type description' } }) }}
                                                </div>
                                                {{ form_errors(form.description) }}
                                                <small class="form-text text-muted">Provide a brief description of the leave type (optional)</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                {{ form_label(form.maxDays, 'Maximum Days', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text"><i class="fa fa-clock-o"></i></span>
                                                    </div>
                                                    {{ form_widget(form.maxDays, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter maximum days', 'required': 'required' } }) }}
                                                </div>
                                                {{ form_errors(form.maxDays) }}
                                                <small class="form-text text-muted">Specify the maximum number of days allowed per year</small>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                {{ form_label(form.isPaid, 'Paid Leave', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text">
                                                            {{ form_widget(form.isPaid, { 'attr': { 'class': 'custom-checkbox', 'aria-label': 'Checkbox for Paid Leave' } }) }}
                                                        </span>
                                                    </div>
                                                    <div class="form-control checkbox-label">
                                                        {{ form_label(form.isPaid, null, { 'label_attr': { 'class': 'ml-2' } }) }}
                                                    </div>
                                                </div>
                                                {{ form_errors(form.isPaid) }}
                                                <small class="form-text text-muted">Check if this is a paid leave type</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                {{ form_label(form.noticeDaysSingle, 'Notice Days (Single Monday, Friday, or Holiday)', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text"><i class="fa fa-calendar"></i></span>
                                                    </div>
                                                    {{ form_widget(form.noticeDaysSingle, { 'attr': { 'class': 'form-control', 'placeholder': 'e.g., 15 days' } }) }}
                                                </div>
                                                {{ form_errors(form.noticeDaysSingle) }}
                                                <small class="form-text text-muted">Days of notice required for a single Monday, Friday, or public holiday to avoid penalty (optional)</small>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                {{ form_label(form.noticeDaysLongWeekend, 'Notice Days (Long Weekend or Sandwich)', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text"><i class="fa fa-calendar"></i></span>
                                                    </div>
                                                    {{ form_widget(form.noticeDaysLongWeekend, { 'attr': { 'class': 'form-control', 'placeholder': 'e.g., 30 days' } }) }}
                                                </div>
                                                {{ form_errors(form.noticeDaysLongWeekend) }}
                                                <small class="form-text text-muted">Days of notice required for long weekends or sandwich leaves involving holidays to avoid penalty (optional)</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                {{ form_label(form.appliedFrom, 'Is Applied From', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text"><i class="fa fa-calendar-check-o"></i></span>
                                                    </div>
                                                    {{ form_widget(form.appliedFrom, { 'attr': { 'class': 'form-control' } }) }}
                                                </div>
                                                {{ form_errors(form.appliedFrom) }}
                                                <small class="form-text text-muted">Select if applied from Confirmation or Joining date</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="d-flex justify-content-end mt-4">
                                    <a href="{{ path('leave_type_list') }}" class="btn btn-secondary mr-2">
                                        Cancel
                                    </a>
                                    <button type="submit" class="btn btn-primary pr-3 pl-3">
                                        {{ mode == 'edit' ? 'Update Leave Type' : 'Create Leave Type' }}
                                    </button>
                                </div>
                                {{ form_end(form) }}
                            </div>
                        </div>
                    </div> <!-- .card -->
                </div><!--/.col-->
            </div>
        </div>
    </div>
    <div class="clearfix"></div>
    <link rel="stylesheet" href="{{ asset('assets/css/Error/error.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/AdminForm/Form.css') }}">
    <script src="{{ asset('assets/js/admin/leaveType.js') }}"></script>
{% endblock %}