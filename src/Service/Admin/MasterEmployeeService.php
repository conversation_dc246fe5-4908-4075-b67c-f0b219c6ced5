<?php

namespace App\Service\Admin;

use App\Entity\MasterEmployee;
use App\Helper\ModalboxHelper;
use Doctrine\ORM\QueryBuilder;
use Omines\DataTablesBundle\Adapter\Doctrine\ORMAdapter;
use Omines\DataTablesBundle\Column\DateTimeColumn;
use Omines\DataTablesBundle\Column\TextColumn;
use Omines\DataTablesBundle\DataTable;
use Omines\DataTablesBundle\DataTableFactory;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Request;
use App\Traits\ExceptionLoggerTrait;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Security\Csrf\CsrfTokenManagerInterface;

class MasterEmployeeService
{
    use ExceptionLoggerTrait;

    public function __construct(
        private readonly DataTableFactory $dataTableFactory,
        ParameterBagInterface $parameterBag,
        private readonly  RouterInterface $router,
        private readonly CsrfTokenManagerInterface $csrfTokenManager,
        private readonly ModalboxHelper            $modalboxHelper
    )
    {
        $this->initializeLogger($parameterBag, 'employee_Table_service');
    }

    public function createMasterEmployeeTable(Request $request): DataTable
    {
        $dataTable = $this->dataTableFactory->create();

        foreach ($this->getColumns() as $key => $column) {
            $dataTable->add($key, $column['className'], array_filter($column, fn($k) => $k !== 'className', ARRAY_FILTER_USE_KEY));
        }

        return $dataTable->createAdapter(ORMAdapter::class, [
            'entity' => MasterEmployee::class,
            'query' => fn(QueryBuilder $qb) => $this->buildQuery($qb),
        ])
            ->addOrderBy('name', 'asc')
            ->handleRequest($request);
    }

    private function getColumns(): array
    {
        return [
            'name' => [
                'label' => 'Name',
                'field' => 'e.name',
                'className' => TextColumn::class,
            ],
            'email' => [
                'label' => 'Email',
                'field' => 'e.email',
                'className' => TextColumn::class,
            ],
            'joiningDate' => [
                'label' => 'Joining Date',
                'field' => 'e.joiningDate',
                'format' => 'd-m-Y',
                'searchable' => false,
                'className' => DateTimeColumn::class,
            ],
            'totalExperience' => [
                'label' => 'Actions',
                'field' => 'e.totalExperience',
                'className' => TextColumn::class,
                'orderable' => false,
                'render' => fn($value, $context) => $this->renderActionColumn($context),
            ],
        ];
    }

    private function renderActionColumn($context): string
    {
        $entityId = $context->getId();
        $editUrl = $this->router->generate('master_employee_edit', ['id' => $entityId]);
        $viewUrl = $this->router->generate('master_employee_view', ['id' => $entityId]);
        $documentsUrl = $this->router->generate('admin_employee_documents', ['employeeId' => $entityId]);
        $SalaryUrl = $this->router->generate('admin_employee_salary', ['employeeId' => $entityId]);
        $deleteUrl = $this->router->generate('master_employee_delete', ['id' => $entityId]);
        $csrfToken = $this->csrfTokenManager->getToken('delete' . $entityId)->getValue();

        $modalId = 'deleteModal_' . $entityId;
        $modalBody = 'Are you sure you want to delete this employee?<br><br><strong>Warning:</strong> This action cannot be undone.';
        $footerButtons = sprintf(
            '<button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>' .
            '<button type="button" class="btn btn-primary delete-employee-btn"  data-delete="true" data-url="%s" data-token="%s">Delete</button>',
            $deleteUrl,
            $csrfToken
        );

        $deleteModal = $this->modalboxHelper->renderModal(
            $modalId,
            'Confirm Employee Deletion',
            $modalBody,
            $footerButtons
        );

        return sprintf(
            '<div class="d-inline-flex gap-2">
            <a href="%s" class="bs-btn btn btn-primary btn-sm" style="margin: 2px;" title="View"><i class="bs-fa fa fa-eye"></i></a>
            <a href="%s" class="bs-btn btn btn-primary btn-sm" style="margin: 2px;" title="Edit"><i class="bs-fa fa fa-edit"></i></a>
             <a href="%s" class="bs-btn btn btn-primary btn-sm" style="margin: 2px;" title="Manage Documents"><i class="bs-fa fa fa-file"></i></a>
             <a href="%s" class="bs-btn btn btn-primary btn-sm" style="margin: 2px;" title="Salary Structure"><i class="bs-fa fa fa-rupee"></i></a>
            <button type="button" class="bs-btn btn btn-primary btn-sm" style="margin: 2px;" title="Delete" data-toggle="modal" data-target="#%s">
                <i class="bs-fa fa fa-trash"></i>
            </button>
        </div>%s',
            $viewUrl,
            $editUrl,
            $documentsUrl,
            $SalaryUrl,
            $modalId,
            $deleteModal
        );
    }


    private function buildQuery(QueryBuilder $qb): void
    {
        $qb->select('e')
            ->from(MasterEmployee::class, 'e')
            ->where('e.isDelete = :isDelete')
            ->setParameter('isDelete', false);
    }

}