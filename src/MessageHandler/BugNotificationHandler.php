<?php

namespace App\MessageHandler;

use App\Entity\Bug;
use App\Message\BugNotificationMessage;
use App\Repository\MasterEmployeeRepository;
use App\Repository\SettingRepository;
use App\Service\Common\EmailService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\Mailer\Exception\TransportExceptionInterface;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;
use App\Traits\ExceptionLoggerTrait;

#[AsMessageHandler]
class BugNotificationHandler
{
    use ExceptionLoggerTrait;

    public function __construct(
        private readonly MasterEmployeeRepository $employeeRepository,
        private readonly ParameterBagInterface $parameterBag,
        private readonly EntityManagerInterface $entityManager,
        private readonly EmailService $emailService,
        private readonly SettingRepository $settingRepository,
    ) {
        $this->initializeLogger($parameterBag, 'bug_notifications_handler');
    }

    private function isNotificationEnabled(string $type): bool
    {
        $setting = $this->settingRepository->findOneBy(['fieldName' => $type]);
        return $setting ? filter_var($setting->getFieldValue(), FILTER_VALIDATE_BOOLEAN) : false;
    }

    public function __invoke(BugNotificationMessage $message): void
    {
        $employee = $this->employeeRepository->find($message->getEmployeeId());
        if (!$employee) {
            $this->log('Employee not found', ['employee_id' => $message->getEmployeeId()]);
            return;
        }

        $bug = $this->entityManager->getRepository(Bug::class)->find($message->getBugId());
        if (!$bug) {
            $this->log('Bug not found', ['bug_id' => $message->getBugId()]);
            return;
        }

        $action = $message->getAction();
        $changes = $message->getChanges() ?? [];

        $placeholders = [
            '{{employee_name}}' => $employee->getName(),
            '{{project_name}}' => $bug->getProject()?->getName() ?? 'N/A',
            '{{task_description}}' => $bug->getTaskDescription() ?? 'No description available',
            '{{priority}}' => $bug->getPriority()?->value ?? 'N/A',
            '{{severity}}' => $bug->getSeverity()?->value ?? 'N/A',
            '{{category}}' => $bug->getCategory()?->value ?? 'N/A',
            '{{status}}' => $bug->getStatus()?->value ?? 'N/A',
            '{{created_at}}' => $bug->getCreatedAt()->format('d-m-Y'),
            '{{app_base_url}}' => rtrim($this->parameterBag->get('app_base_url'), '/') . "/my-bugs",
        ];

        if ($action === 'create' && $this->isNotificationEnabled('BUG_NOTIFICATION_ENABLED')) {
            $this->sendEmail('bug_notification', 'New Bug Assigned', $employee, $placeholders);
        } elseif ($action === 'update' && !empty($changes)) {
            $shouldSend = false;
            foreach ($changes as $change) {
              if ($change === 'status' && $this->isNotificationEnabled('BUG_STATUS_UPDATE_ENABLED')) {
                    $shouldSend = true;
                } elseif ($change === 'description' && $this->isNotificationEnabled('BUG_DESCRIPTION_UPDATE_ENABLED')) {
                    $shouldSend = true;
                }
            }

            if ($shouldSend) {
                $this->sendEmail('bug_update_notification', 'Bug Updated', $employee, $placeholders);
            } else {
                $this->log('Bug update notifications skipped due to settings', [
                    'bug_id' => $bug->getId(),
                    'changes' => $changes
                ]);
            }
        } else {
            $this->log('Bug notifications disabled or no relevant changes', [
                'bug_id' => $bug->getId(),
                'action' => $action
            ]);
        }
    }

    private function sendEmail(string $templateIdentifier, string $subject, $employee, array $placeholders): void
    {
        if (!$employee || !$employee->getEmail()) {
            $this->log('Employee has no email', ['employee_id' => $employee?->getId()]);
            return;
        }

        try {
            $placeholders['{{employee_name}}'] = $employee->getName();
            $emailContent = $this->emailService->generateEmailContent($templateIdentifier, $placeholders);
            $this->emailService->sendEmail($employee->getEmail(), $subject, $emailContent);
        } catch (TransportExceptionInterface|\Exception $e) {
            $this->log('Failed to send bug notification email', [
                'recipient' => $employee->getEmail(),
                'error' => $e->getMessage(),
            ]);
        }
    }
}