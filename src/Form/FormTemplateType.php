<?php
namespace App\Form;

use App\Entity\FormTemplate;
use App\Entity\MasterEmployee;
use Doctrine\ORM\EntityRepository;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\Length;
use Symfony\Component\Validator\Constraints\NotBlank;

class FormTemplateType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('name', TextType::class, [
                'label' => false,
                'required' => true,
                'constraints' => [
                    new NotBlank([
                        'message' => 'Template name is required.',
                    ]),
                    new Length([
                        'min' => 2,
                        'max' => 255,
                        'minMessage' => 'Field label must be at least {{ limit }} characters long',
                        'maxMessage' => 'Field label cannot be longer than {{ limit }} characters',
                    ]),
                ],
                'attr' => [
                    'maxlength' => 255,
                    'class' => 'form-control',
                    'data-validation' => 'required|min:2|max:255'
                ],
            ])
            ->add('category', TextType::class, [
                'label' => false,
                'required' => false,
                'constraints' => [
                    new Length([
                        'max' => 255,
                        'minMessage' => 'Category must be at least {{ limit }} characters long',
                        'maxMessage' => 'Category cannot be longer than {{ limit }} characters',
                    ]),
                ],
                'attr' => [
                    'maxlength' => 255,
                    'class' => 'form-control',
                    'data-validation' => 'max:255'
                ],
            ])
            ->add('frequency', ChoiceType::class, [
                'choices' => [
                    'Monthly' => 'monthly',
                    'Quarterly' => 'quarterly',
                    'Annual' => 'annual',
                    'Custom' => 'custom',
                ],
            ])
            ->add('description', TextareaType::class, [
                'label' => false,
                'attr' => ['class' => 'form-control', 'rows' => 1, 'placeholder' => 'Enter form template description', 'maxlength' => 255,'data-validation' => 'max:255'],
                'required' => false,
                'constraints' => [
                    new Length([
                        'max' => 255,
                        'minMessage' => 'Category must be at least {{ limit }} characters long',
                        'maxMessage' => 'Category cannot be longer than {{ limit }} characters',
                    ]),
                ],
            ])
            ->add('teamLeaders', EntityType::class, [
                'label' => false,
                'class' => MasterEmployee::class,
                'choice_label' => 'name',
                'multiple' => true,
                'expanded' => true,
                'required' => false,
                'query_builder' => function (EntityRepository $er) {
                    return $er->createQueryBuilder('m')
                        ->where('m.teamLeader = :teamLeader')
                        ->setParameter('teamLeader', true);
                },
            ])
            ->add('assignToAll',CheckboxType ::class, [
                'label' => 'Assign To All',
                'required' => false,
            ])
            ->add('status', ChoiceType::class, [
                'label' => 'Status',
                'choices' => [
                    'Active' => true,
                    'Inactive' => false,
                ],
            ]);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => FormTemplate::class,
        ]);
    }
}