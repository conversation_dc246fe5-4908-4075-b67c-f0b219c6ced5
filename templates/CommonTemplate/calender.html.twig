<div class="card-header btn-primary text-white border-0">
    <h5 class="card-title mb-0">
        <i class="fa fa-calendar me-2"></i>Attendance Calendar
    </h5>
</div>
<div id="calendar" class="p-3"></div>

{{ render_modal('attendanceModal', 'Attendance Details', '',
    '<button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>') }}
{% block stylesheets %}
    <style>
        .fc {
            --fc-border-color: #e6e6e6;
            --fc-day-border-color: #f0f0f0;
            --fc-event-bg-color: #f78739;
            --fc-event-border-color: #e06c1f;
            --fc-today-bg-color: #fff9f5;
            max-height: 500px;
        }

        .fc-theme-standard .fc-scrollgrid {
            border-radius: 4px;
            overflow: hidden;
        }

        .fc-theme-standard td, .fc-theme-standard th {
            border: 1px solid var(--fc-border-color);
        }

        .fc-daygrid-body {
            width: 373px !important;
        }

        .fc-col-header {
            width: 373px !important;
        }

        .fc-scrollgrid-sync-table {
            width: 373px !important;
        }

        .fc-daygrid-day-number {
            font-size: 14px;
            font-weight: 500;
            color: #444;
            padding: 8px;
            cursor: pointer;
        }

        .fc-day-today {
            background-color: #fff9f5 !important;
        }

        .fc-day-today .fc-daygrid-day-number {
            background-color: #f78739;
            color: white;
            border-radius: 50%;
            width: 28px;
            height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 2px;
        }

        .fc-col-header-cell {
            background-color: #fcf2eb;
            padding: 8px 0;
        }

        .fc-col-header-cell-cushion {
            font-weight: 600;
            color: #f78739;
            padding: 6px;
            text-decoration: none !important;
        }

        .fc-day-other .fc-daygrid-day-number {
            color: #aaa;
        }

        .fc-toolbar-title {
            font-size: 18px !important;
            font-weight: 600;
            color: #444;
        }

        .fc-toolbar-chunk .fc-button-group {
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            border-radius: 6px;
            overflow: hidden;
        }

        .fc-button-primary {
            background-color: white !important;
            color: #666 !important;
            border: 1px solid #e0e0e0 !important;
            transition: all 0.2s ease;
        }

        .fc-button-primary:hover {
            background-color: #f6f6f6 !important;
        }

        .fc-button-primary:not(:disabled).fc-button-active,
        .fc-button-primary:not(:disabled):active {
            background-color: #f78739 !important;
            color: white !important;
            border-color: #f78739 !important;
        }

        .fc-button-primary:disabled {
            background-color: #f1f1f1 !important;
            color: #aaa !important;
        }

        .fc-daygrid-day:hover {
            background-color: #fcf8f5;
        }

        .fc-daygrid-day-events {
            padding: 0 2px;
        }

        .fc-event {
            border-radius: 4px;
            padding: 2px 4px;
            font-size: 12px;
            cursor: pointer;
        }

        .fc-event:hover {
            filter: brightness(95%);
        }

        .fc-daygrid-event-dot {
            border-color: #f78739 !important;
        }

        .fc-list-event-dot {
            border-color: #f78739;
        }

        .fc-list-event:hover td {
            background-color: #fcf8f5;
        }

        .fc-list-day-cushion {
            background-color: #fcf2eb !important;
        }

        .fc-today-button {
            text-transform: uppercase;
            font-size: 13px !important;
            font-weight: 600 !important;
        }

        .fc-today-button:not(:disabled) {
            background-color: #f78739 !important;
            border-color: #f78739 !important;
        }

        .fc-today-button:not(:disabled):hover {
            background-color: #e06c1f !important;
        }

        .fc-view-harness {
            min-height: 350px;
        }

        .timeline-item {
            position: relative;
            margin-bottom: 20px;
            padding: 10px 15px;
            background: #f8f9fa;
            border-radius: 5px;
            border-left: 3px solid #f78739;
        }

        .timeline-time {
            font-weight: bold;
            color: #f78739;
            font-size: 14px;
        }

        .timeline-status {
            margin-top: 5px;
            font-size: 13px;
            color: #666;
        }

        .attendance-stats {
            display: flex;
            justify-content: space-around;
            margin-bottom: 20px;
        }

        .stat-item {
            text-align: center;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
            flex: 1;
            margin: 0 5px;
        }

        .stat-value {
            font-size: 18px;
            font-weight: bold;
            color: #f78739;
        }

        .stat-label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }

        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
            text-transform: uppercase;
        }

        .status-present {
            background: #d4edda;
            color: #155724;
        }

        .status-absent {
            background: #f8d7da;
            color: #721c24;
        }

        .status-incomplete {
            background: #fff3cd;
            color: #856404;
        }

        .status-progress {
            background: #d1ecf1;
            color: #0c5460;
        }

        .modal-loading {
            text-align: center;
            padding: 40px;
        }

        .spinner-border-sm {
            width: 1rem;
            height: 1rem;
        }

        @media (max-width: 768px) {
            .fc-toolbar {
                flex-direction: column;
                gap: 10px;
            }

            .fc-toolbar-chunk {
                display: flex;
                justify-content: center;
            }

            .attendance-stats {
                flex-direction: column;
            }

            .stat-item {
                margin: 5px 0;
            }
        }

        .fc-holiday {
            background: #f78739 !important;
            color: white !important;
            font-weight: 500;
        }

        .fc-holiday:hover {
            filter: brightness(90%);
        }
    </style>
{% endblock %}
{% block javascripts %}
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            var calendarEl = document.getElementById('calendar');

            {% if events is defined %}
            var calendarEvents = {{ events|raw }};
            {% else %}
            var calendarEvents = [];
            {% endif %}

            function shouldSkipPopup(dateStrOrDateObj, attendanceCount = 0, isHoliday = false) {
                if (isHoliday) return true;
                const dateObj = typeof dateStrOrDateObj === 'string' ? new Date(dateStrOrDateObj) : dateStrOrDateObj;
                const today = new Date();
                today.setHours(0, 0, 0, 0);
                const dayOfWeek = dateObj.getDay();
                const isWeekend = (dayOfWeek === 0 || dayOfWeek === 6);
                const isFuture = dateObj > today;
                return (isFuture || isWeekend) && attendanceCount === 0;
            }

            var calendar = new FullCalendar.Calendar(calendarEl, {
                initialView: 'dayGridMonth',
                initialDate: '{{ "now"|date("Y-m-d") }}',
                headerToolbar: {
                    left: 'prev,next today',
                    center: 'title',
                    right: 'dayGridMonth,listWeek'
                },
                buttonText: {
                    today: 'Today',
                    month: 'Month',
                    list: 'List'
                },
                events: calendarEvents,
                dayMaxEvents: 2,
                eventTimeFormat: {
                    hour: '2-digit',
                    minute: '2-digit',
                    meridiem: false
                },
                firstDay: 1,
                fixedWeekCount: false,
                showNonCurrentDates: true,
                height: 'auto',
                eventClick: function(info) {
                    if (shouldSkipPopup(info.event.start, info.event.extendedProps.attendanceCount, info.event.extendedProps.isHoliday)) {
                        return;
                    }
                    loadAttendanceDetails(info.event.id);
                },
                dateClick: function(info) {
                    if (props.isHoliday) isHoliday = true;
                    if (shouldSkipPopup(info.dateStr)) {
                        return;
                    }
                    loadAttendanceDetails(info.dateStr);
                },
                datesSet: function(info) {
                    loadCalendarData(info.start, info.end);
                }
            });

            calendar.render();

            function loadAttendanceDetails(date) {
                const modal = new bootstrap.Modal(document.getElementById('attendanceModal'));
                const modalBody = document.querySelector('#attendanceModal .modal-body');
                const modalTitle = document.querySelector('#attendanceModal .modal-title');

                modalBody.innerHTML = `
                    <div class="modal-loading">
                        <div class="spinner-border spinner-border-sm text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Loading attendance details...</p>
                    </div>
                `;
                modalTitle.textContent = 'Attendance Details - ' + formatDate(date);
                modal.show();

                fetch(`{{ path('attendance_date_details', {date: 'DATE_PLACEHOLDER'}) }}`.replace('DATE_PLACEHOLDER', date))
                    .then(response => response.json())
                    .then(data => {
                        if (data.error) {
                            modalBody.innerHTML = `
                                <div class="alert alert-danger">
                                    <i class="fa fa-exclamation-triangle"></i> ${data.error}
                                </div>
                            `;
                            return;
                        }

                        modalBody.innerHTML = renderAttendanceDetails(data);
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        modalBody.innerHTML = `
                            <div class="alert alert-danger">
                                <i class="fa fa-exclamation-triangle"></i> Failed to load attendance details
                            </div>
                        `;
                    });
            }

            function loadCalendarData(start, end) {
                const startDate = start.toISOString().split('T')[0];
                const endDate = end.toISOString().split('T')[0];

                fetch(`{{ path('attendance_calendar_data') }}?start=${startDate}&end=${endDate}`)
                    .then(response => response.json())
                    .then(events => {
                        calendar.removeAllEvents();
                        calendar.addEventSource(events);
                    })
                    .catch(error => {
                        console.error('Error loading calendar data:', error);
                    });
            }

            function renderAttendanceDetails(data) {
                const statusBadgeClass = {
                    'Present': 'status-present',
                    'Absent': 'status-absent',
                    'Incomplete': 'status-incomplete',
                    'In Progress': 'status-progress'
                };

                let html = `
                    <div class="attendance-stats">
                        <div class="stat-item">
                            <div class="stat-value">${data.status}</div>
                            <div class="stat-label">Status</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">${data.totalHours || '0:00'}</div>
                            <div class="stat-label">Total Hours (Teamlogger)</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">${data.attendanceCount}</div>
                            <div class="stat-label">Punches</div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <span class="status-badge ${statusBadgeClass[data.status] || 'status-absent'}">${data.status}</span>
                    </div>
                `;

                if (data.workDuration) {
                    html += `
                        <div class="row mb-3">
                            <div class="col-6">
                                <strong>Office In:</strong> ${data.officeInTime || 'Not recorded'}
                            </div>
                            <div class="col-6">
                                <strong>Leaving:</strong> ${data.leavingTime || 'Not recorded'}
                            </div>
                        </div>
                        <div class="mb-3">
                            <strong>Work Duration (HRM):</strong> ${data.workDuration}
                        </div>
                    `;
                }

                if (data.details && data.details.length > 0) {
                    html += `
                        <h6 class="mb-3">Timeline</h6>
                        <div class="attendance-timeline">
                    `;

                    data.details.forEach(detail => {
                        html += `
                            <div class="timeline-item">
                                <div class="timeline-time">${detail.time}</div>
                                <div class="timeline-status">${detail.status}</div>
                            </div>
                        `;
                    });

                    html += `</div>`;
                }

                if (data.breaks && data.breaks.length > 0) {
                    html += `
                        <h6 class="mb-3 mt-4">Breaks</h6>
                        <div class="row">
                    `;

                    data.breaks.forEach(breakItem => {
                        html += `
                            <div class="col-md-6 mb-2">
                                <small class="text-muted">${breakItem.time}</small><br>
                                <span class="badge btn-primary">${breakItem.status}</span>
                            </div>
                        `;
                    });

                    html += `</div>`;
                }

                if (data.status === 'Absent') {
                    html += `
                        <div class="alert alert-warning mt-3">
                            <i class="fa fa-info-circle"></i> No attendance recorded for this date.
                        </div>
                    `;
                }

                return html;
            }
            function formatDate(dateStr) {
                const date = new Date(dateStr);
                return date.toLocaleDateString('en-IN', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                });
            }
        });
    </script>
{% endblock %}