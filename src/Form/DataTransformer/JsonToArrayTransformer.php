<?php

namespace App\Form\DataTransformer;

use Symfony\Component\Form\DataTransformerInterface;
use Symfony\Component\Form\Exception\TransformationFailedException;

class JsonToArrayTransformer implements DataTransformerInterface
{
    /**
     * Transforms the database value to the form value
     * Database (array) -> Form (string)
     */
    public function transform($value): ?string
    {
        if ($value === null) {
            return null;
        }

        // If it's already a string, validate it's JSO<PERSON> and return
        if (is_string($value)) {
            try {
                // Verify it's valid JSO<PERSON>
                json_decode($value, true, 512, JSON_THROW_ON_ERROR);
                return $value;
            } catch (\JsonException) {
                return null;
            }
        }

        // If it's an array, convert to JSON string
        if (is_array($value)) {
            try {
                return json_encode($value, JSON_THROW_ON_ERROR | JSON_PRETTY_PRINT);
            } catch (\JsonException) {
                return null;
            }
        }

        return null;
    }

    /**
     * Transforms the form value to the database value
     * Form (string) -> Database (array)
     */
    public function reverseTransform($value): ?array
    {
        if (empty($value)) {
            return null;
        }

        // If already an array, return as is
        if (is_array($value)) {
            return $value;
        }

        // If string, decode to array
        if (is_string($value)) {
            try {
                $decoded = json_decode($value, true, 512, JSON_THROW_ON_ERROR);
                return is_array($decoded) ? $decoded : null;
            } catch (\JsonException) {
                return null;
            }
        }

        return null;
    }
}