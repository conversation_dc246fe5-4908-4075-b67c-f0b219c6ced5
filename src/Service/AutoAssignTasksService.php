<?php
namespace App\Service;

use App\Entity\Holiday;
use App\Entity\MasterEmployee;
use App\Entity\Project;
use App\Entity\SocialMediaPost;
use App\Entity\Task;
use App\Entity\User;
use App\Service\Common\EmailService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Mailer\Exception\TransportExceptionInterface;

class AutoAssignTasksService
{
    private EntityManagerInterface $entityManager;
    private EmailService $emailService;

    public function __construct(EntityManagerInterface $entityManager, EmailService $emailService)
    {
        $this->entityManager = $entityManager;
        $this->emailService = $emailService;
    }

    /**
     * @throws \DateMalformedStringException
     * @throws TransportExceptionInterface
     */
    public function generateUpcomingTasks(Project $project, ?User $assignedByAdmin = null): void
    {
        $targetDate = (new \DateTime('today'))->modify('+15 days');
        $startDate = clone $targetDate;
        $endDate = clone $targetDate;
        
        $designers = $this->entityManager
            ->getRepository(MasterEmployee::class)
            ->findNonDeletedDesigners();

        if (empty($designers)) {
            throw new \RuntimeException('No designers found to assign tasks.');
        }

        $employees = $this->entityManager->getRepository(MasterEmployee::class)->findAll();
        $events = [];

        foreach ($employees as $employee) {
            $birthDate = $employee->getBirthDate();
            $joiningDate = $employee->getJoiningDate();

            if ($birthDate) {
                $birthdayThisYear = (clone $startDate)->setDate(
                    (int)$startDate->format('Y'),
                    (int)$birthDate->format('m'),
                    (int)$birthDate->format('d')
                );
                if ($birthdayThisYear >= $startDate && $birthdayThisYear <= $endDate) {
                    $events[] = [
                        'type' => 'birthday',
                        'title' => "Birthday Post for {$employee->getName()}",
                        'description' => sprintf(
                            "Create a post for %s's birthday (%s).",
                            $employee->getName(),
                            $employee->getBirthDate()?->format('d-m-Y')
                        ),
                        'date' => $birthdayThisYear,
                        'mediaType' => 'image',
                    ];
                }
            }

            if ($joiningDate) {
                $anniversaryThisYear = (clone $startDate)->setDate(
                    (int)$startDate->format('Y'),
                    (int)$joiningDate->format('m'),
                    (int)$joiningDate->format('d')
                );

                if ($anniversaryThisYear >= $startDate && $anniversaryThisYear <= $endDate) {
                    $years = (int)$startDate->format('Y') - (int)$joiningDate->format('Y');
                    $mediaType = $years >= 5 ? 'video' : 'image';

                    $events[] = [
                        'type' => 'anniversary',
                        'title' => "Anniversary Post for {$employee->getName()}",
                        'description' => sprintf(
                            "Create a post for %s's %d year work anniversary (%s).",
                            $employee->getName(),
                            $years,
                            $employee->getJoiningDate()?->format('d-m-Y')
                        ),
                        'date' => $anniversaryThisYear,
                        'mediaType' => $mediaType,
                    ];
                }
            }
        }

        $holidays = $this->entityManager->getRepository(Holiday::class)->findByDateRange($startDate, $endDate);
        foreach ($holidays as $holiday) {
            $events[] = [
                'type' => 'Festival',
                'title' => "Festival Post for {$holiday->getName()}",
                'description' => sprintf(
                    "Create a post for %s (%s).",
                    $holiday->getName(),
                    $holiday->getDate()?->format('d-m-Y')
                ),
                'date' => $holiday->getDate(),
                'mediaType' => 'image',
            ];
        }

        $scheduledPosts = $this->entityManager
            ->getRepository(SocialMediaPost::class)
            ->findByDateRange($startDate, $endDate);

        foreach ($scheduledPosts as $post) {
            $events[] = [
                'type' => 'Social Media',
                'title' => sprintf('Social Media Post for %s', $post->getTitle()),
                'description' => $post->getDescription() ?: sprintf(
                    "Create a social media post for %s (%s) type(%s).",
                    $post->getTitle(),
                    $post->getPostDate()?->format('d-m-Y'),
                    $post->getMediaType()
                ),
                'date' => $post->getPostDate(),
                'mediaType' => $post->getMediaType(),
            ];
        }

        $tasksByDesigner = [];
        foreach ($events as $index => $event) {
            $task = new Task();
            $task->setTitle($event['title']);
            $task->setDescription($event['description']);
            $task->setPriority('MEDIUM');
            $task->setStatus('NEW');
            $task->setEstimatedHours(4.0);
            $task->setCreatedAt(new \DateTime());
            $task->setUpdatedAt(new \DateTime());
            $task->setAssignedAt(new \DateTime());

            $designer = $designers[$index % count($designers)];
            $task->setAssignedTo($designer);
            $task->setProject($project);
            $task->setAssignedByAdmin($assignedByAdmin);
            $this->entityManager->persist($task);

            $tasksByDesigner[$designer->getEmail()][] = [
                'title' => $task->getTitle(),
                'description' => $task->getDescription(),
                'dueDate' => $event['date']->format('d-m-Y'),
            ];
        }

        $this->entityManager->flush();

        $this->sendTaskNotifications($tasksByDesigner);
    }

    /**
     * @throws TransportExceptionInterface
     */
    private function sendTaskNotifications(array $tasksByDesigner): void
    {
        foreach ($tasksByDesigner as $email => $tasks) {
            $taskItems = '';
            foreach ($tasks as $task) {
                $taskItems .= '
            <tr>
                <td style="padding: 15px; border-bottom: 1px solid #eeeeee;">
                    <h3 style="margin: 0 0 5px 0; color: #f78739;">'.$task['title'].'</h3>
                    <p style="margin: 0 0 5px 0; color: #666666;">'.$task['description'].'</p>
                    <p style="margin: 0; font-weight: bold;">
                        <span style="color: #333333;">Due Date:</span> 
                        <span style="color: #f78739;">'.$task['dueDate'].'</span>
                    </p>
                </td>
            </tr>';
            }

            $emailContent = '
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Task Assignments</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333333; margin: 0; padding: 0; background-color: #f9f9f9; }
                .container { max-width: 600px; margin: 20px auto; background: #ffffff; border-radius: 8px; overflow: hidden; box-shadow: 0 0 10px rgba(0,0,0,0.1); }
                .header { background-color: #f78739; color: white; padding: 25px; text-align: center; }
                .content { padding: 0 20px; }
                .task-table { width: 100%; border-collapse: collapse; }
                .footer { background-color: #f4f4f4; padding: 15px; text-align: center; font-size: 12px; color: #777777; }
                .priority { display: inline-block; padding: 3px 8px; border-radius: 4px; font-size: 12px; font-weight: bold; }
                .priority-medium { background-color: #fff3cd; color: #856404; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1 style="margin: 0; font-size: 24px;">Task Assignments</h1>
                    <p style="margin: 5px 0 0 0; font-size: 16px; opacity: 0.9;">Upcoming 15 Days Schedule</p>
                </div>
                
                <div class="content">
                    <table class="task-table">
                        '.$taskItems.'
                    </table>
                </div>
                
                <div class="footer">
                    <p>Need help with these tasks? Contact your manager for assistance.</p>
                    <p>&copy; '.date('Y').' Brainstream Technolabs. All rights reserved.</p>
                </div>
            </div>
        </body>
        </html>';

            $this->emailService->sendEmail(
                $email,
                'Your Monthly  Task Assignments',
                $emailContent,
                true
            );
        }
    }
}
