<?php

namespace App\Controller\Api;

use App\Repository\ApplicationUsageRepository;
use App\Repository\WebsiteUsageRepository;
use App\Repository\MasterEmployeeRepository;
use App\Service\ProductivityCategorizationService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

#[Route('/api/productivity', name: 'api_productivity_')]
class ProductivityController extends AbstractController
{
    public function __construct(
        private ApplicationUsageRepository $applicationUsageRepository,
        private WebsiteUsageRepository $websiteUsageRepository,
        private MasterEmployeeRepository $employeeRepository,
        private ProductivityCategorizationService $productivityService,
        private EntityManagerInterface $entityManager
    ) {}

    #[Route('/insights', name: 'insights', methods: ['GET'])]
    public function getProductivityInsights(Request $request): JsonResponse
    {
        try {
            $employeeId = $request->query->get('employee_id');
            $date = $request->query->get('date', date('Y-m-d'));
            
            if (!$employeeId) {
                return new JsonResponse([
                    'success' => false,
                    'error' => 'Employee ID is required'
                ], 400);
            }

            $employee = $this->employeeRepository->find($employeeId);
            if (!$employee) {
                return new JsonResponse([
                    'success' => false,
                    'error' => 'Employee not found'
                ], 404);
            }

            $targetDate = new \DateTime($date);

            // Get application usage for the date
            $applications = $this->applicationUsageRepository->findByEmployeeAndDateRange(
                $employeeId, 
                $targetDate, 
                $targetDate
            );

            // Get website usage for the date
            $websites = $this->websiteUsageRepository->findByEmployeeAndDateRange(
                $employeeId, 
                $targetDate, 
                $targetDate
            );

            // Prepare data for categorization
            $appData = [];
            foreach ($applications as $app) {
                $appData[] = [
                    'name' => $app->getApplicationName(),
                    'duration' => $app->getDuration() ?? 0
                ];
            }

            $websiteData = [];
            foreach ($websites as $website) {
                $websiteData[] = [
                    'domain' => $website->getDomain(),
                    'duration' => $website->getDuration() ?? 0
                ];
            }

            // Get productivity statistics
            $appStats = $this->productivityService->getApplicationProductivityStats($appData);
            $websiteStats = $this->productivityService->getWebsiteProductivityStats($websiteData);
            
            // Calculate overall productivity score
            $productivityScore = $this->productivityService->calculateProductivityScore($appStats, $websiteStats);

            // Get top applications by category
            $topApps = $this->getTopApplicationsByCategory($applications);
            $topWebsites = $this->getTopWebsitesByCategory($websites);

            return new JsonResponse([
                'success' => true,
                'data' => [
                    'date' => $date,
                    'employee_id' => $employeeId,
                    'productivity_score' => $productivityScore,
                    'application_stats' => $appStats,
                    'website_stats' => $websiteStats,
                    'top_applications' => $topApps,
                    'top_websites' => $topWebsites,
                    'summary' => [
                        'total_applications' => count($applications),
                        'total_websites' => count($websites),
                        'total_app_duration' => array_sum(array_column($appData, 'duration')),
                        'total_website_duration' => array_sum(array_column($websiteData, 'duration'))
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => 'Failed to get productivity insights',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    #[Route('/categorize/application', name: 'categorize_app', methods: ['POST'])]
    public function categorizeApplication(Request $request): JsonResponse
    {
        try {
            $data = json_decode($request->getContent(), true);
            
            if (!isset($data['application_name'])) {
                return new JsonResponse([
                    'success' => false,
                    'error' => 'Application name is required'
                ], 400);
            }

            $category = $this->productivityService->categorizeApplication($data['application_name']);

            return new JsonResponse([
                'success' => true,
                'data' => [
                    'application_name' => $data['application_name'],
                    'category' => $category['category'],
                    'productivity_level' => $category['productivity_level']
                ]
            ]);

        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => 'Failed to categorize application',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    #[Route('/categorize/website', name: 'categorize_website', methods: ['POST'])]
    public function categorizeWebsite(Request $request): JsonResponse
    {
        try {
            $data = json_decode($request->getContent(), true);
            
            if (!isset($data['domain'])) {
                return new JsonResponse([
                    'success' => false,
                    'error' => 'Domain is required'
                ], 400);
            }

            $category = $this->productivityService->categorizeWebsite($data['domain']);

            return new JsonResponse([
                'success' => true,
                'data' => [
                    'domain' => $data['domain'],
                    'category' => $category['category'],
                    'productivity_level' => $category['productivity_level']
                ]
            ]);

        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => 'Failed to categorize website',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    #[Route('/weekly-report', name: 'weekly_report', methods: ['GET'])]
    public function getWeeklyProductivityReport(Request $request): JsonResponse
    {
        try {
            $employeeId = $request->query->get('employee_id');
            $weekStart = $request->query->get('week_start', date('Y-m-d', strtotime('monday this week')));
            
            if (!$employeeId) {
                return new JsonResponse([
                    'success' => false,
                    'error' => 'Employee ID is required'
                ], 400);
            }

            $employee = $this->employeeRepository->find($employeeId);
            if (!$employee) {
                return new JsonResponse([
                    'success' => false,
                    'error' => 'Employee not found'
                ], 404);
            }

            $startDate = new \DateTime($weekStart);
            $endDate = clone $startDate;
            $endDate->modify('+6 days');

            // Get data for the week
            $applications = $this->applicationUsageRepository->findByEmployeeAndDateRange(
                $employeeId, 
                $startDate, 
                $endDate
            );

            $websites = $this->websiteUsageRepository->findByEmployeeAndDateRange(
                $employeeId, 
                $startDate, 
                $endDate
            );

            // Group by day
            $dailyData = [];
            for ($i = 0; $i < 7; $i++) {
                $currentDate = clone $startDate;
                $currentDate->modify("+{$i} days");
                $dateStr = $currentDate->format('Y-m-d');
                
                $dayApps = array_filter($applications, function($app) use ($currentDate) {
                    return $app->getUsageDate()->format('Y-m-d') === $currentDate->format('Y-m-d');
                });
                
                $dayWebsites = array_filter($websites, function($website) use ($currentDate) {
                    return $website->getVisitDate()->format('Y-m-d') === $currentDate->format('Y-m-d');
                });

                $appData = array_map(function($app) {
                    return ['name' => $app->getApplicationName(), 'duration' => $app->getDuration() ?? 0];
                }, $dayApps);

                $websiteData = array_map(function($website) {
                    return ['domain' => $website->getDomain(), 'duration' => $website->getDuration() ?? 0];
                }, $dayWebsites);

                $appStats = $this->productivityService->getApplicationProductivityStats($appData);
                $websiteStats = $this->productivityService->getWebsiteProductivityStats($websiteData);
                $score = $this->productivityService->calculateProductivityScore($appStats, $websiteStats);

                $dailyData[$dateStr] = [
                    'date' => $dateStr,
                    'day_name' => $currentDate->format('l'),
                    'productivity_score' => $score,
                    'application_stats' => $appStats,
                    'website_stats' => $websiteStats,
                    'total_duration' => array_sum(array_column($appData, 'duration')) + array_sum(array_column($websiteData, 'duration'))
                ];
            }

            // Calculate weekly averages
            $weeklyScore = array_sum(array_column($dailyData, 'productivity_score')) / 7;
            $totalWeeklyDuration = array_sum(array_column($dailyData, 'total_duration'));

            return new JsonResponse([
                'success' => true,
                'data' => [
                    'week_start' => $weekStart,
                    'week_end' => $endDate->format('Y-m-d'),
                    'employee_id' => $employeeId,
                    'weekly_average_score' => round($weeklyScore, 1),
                    'total_weekly_duration' => $totalWeeklyDuration,
                    'daily_data' => array_values($dailyData)
                ]
            ]);

        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => 'Failed to get weekly productivity report',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    private function getTopApplicationsByCategory(array $applications): array
    {
        $categorized = [];
        
        foreach ($applications as $app) {
            $category = $this->productivityService->categorizeApplication($app->getApplicationName());
            $level = $category['productivity_level'];
            
            if (!isset($categorized[$level])) {
                $categorized[$level] = [];
            }
            
            $categorized[$level][] = [
                'name' => $app->getApplicationName(),
                'duration' => $app->getDuration() ?? 0,
                'category' => $category['category']
            ];
        }

        // Sort each category by duration and take top 5
        foreach ($categorized as $level => &$apps) {
            usort($apps, function($a, $b) {
                return $b['duration'] <=> $a['duration'];
            });
            $apps = array_slice($apps, 0, 5);
        }

        return $categorized;
    }

    private function getTopWebsitesByCategory(array $websites): array
    {
        $categorized = [];
        
        foreach ($websites as $website) {
            $category = $this->productivityService->categorizeWebsite($website->getDomain());
            $level = $category['productivity_level'];
            
            if (!isset($categorized[$level])) {
                $categorized[$level] = [];
            }
            
            $categorized[$level][] = [
                'domain' => $website->getDomain(),
                'duration' => $website->getDuration() ?? 0,
                'category' => $category['category']
            ];
        }

        // Sort each category by duration and take top 5
        foreach ($categorized as $level => &$sites) {
            usort($sites, function($a, $b) {
                return $b['duration'] <=> $a['duration'];
            });
            $sites = array_slice($sites, 0, 5);
        }

        return $categorized;
    }
}
