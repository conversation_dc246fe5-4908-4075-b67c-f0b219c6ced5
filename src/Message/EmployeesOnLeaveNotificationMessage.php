<?php

namespace App\Message;

readonly class EmployeesOnLeaveNotificationMessage
{
    public function __construct(
        private array              $employeeIds,
        private array              $leaveRequestIds,
        private \DateTimeInterface $date
    ) {
    }

    public function getEmployeeIds(): array
    {
        return $this->employeeIds;
    }

    public function getLeaveRequestIds(): array
    {
        return $this->leaveRequestIds;
    }

    public function getDate(): \DateTimeInterface
    {
        return $this->date;
    }
}