<?php

namespace App\Service\Admin;

use App\Entity\Document;
use App\Helper\ModalboxHelper;
use Doctrine\ORM\QueryBuilder;
use Omines\DataTablesBundle\Adapter\Doctrine\ORMAdapter;
use Omines\DataTablesBundle\Column\DateTimeColumn;
use Omines\DataTablesBundle\Column\TextColumn;
use Omines\DataTablesBundle\DataTable;
use Omines\DataTablesBundle\DataTableFactory;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Request;
use App\Traits\ExceptionLoggerTrait;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Security\Csrf\CsrfTokenManagerInterface;

class DocumentService
{
    use ExceptionLoggerTrait;

    public function __construct(
        private readonly DataTableFactory $dataTableFactory,
        private readonly RouterInterface $router,
        ParameterBagInterface $parameterBag,
        private readonly CsrfTokenManagerInterface $csrfTokenManager,
        private readonly ModalboxHelper $modalboxHelper
    ) {
        $this->initializeLogger($parameterBag, 'document_table_service');
    }

    public function createMasterDocumentTable(Request $request): DataTable
    {
        $dataTable = $this->dataTableFactory->create();

        foreach ($this->getColumns() as $key => $column) {
            $dataTable->add($key, $column['className'], array_filter($column, fn($k) => $k !== 'className', ARRAY_FILTER_USE_KEY));
        }

        return $dataTable->createAdapter(ORMAdapter::class, [
            'entity' => Document::class,
            'query' => fn(QueryBuilder $qb) => $this->buildQuery($qb),
        ])
            ->addOrderBy('title', 'asc')
            ->handleRequest($request);
    }

    private function getColumns(): array
    {
        return [
            'title' => [
                'label' => 'Document Title',
                'field' => 'd.title',
                'className' => TextColumn::class,
            ],
            'filePath' => [
                'label' => 'File',
                'field' => 'd.filePath',
                'className' => TextColumn::class,
                'render' => fn($value) => sprintf('<a href="%s" target="_blank" style="color:#007bff;">Download</a>', $value),
            ],
            'createdAt' => [
                'label' => 'Created Date',
                'field' => 'd.createdAt',
                'format' => 'd-m-Y',
                'className' => DateTimeColumn::class,
                'searchable' => false,
            ],
            'actions' => [
                'label' => 'Actions',
                'field' => 'd.id',
                'className' => TextColumn::class,
                'orderable' => false,
                'render' => fn($value, $context) => $this->renderActionColumn($context),
            ],
        ];
    }

    private function renderActionColumn($context): string
    {
        $entityId = $context->getId();
        $editUrl = $this->router->generate('admin_document_edit', ['id' => $entityId]);
        $deleteUrl = $this->router->generate('admin_document_delete', ['id' => $entityId]);
        $csrfToken = $this->csrfTokenManager->getToken('delete' . $entityId)->getValue();

        $modalId = 'deleteModal_' . $entityId;
        $modalBody = 'Are you sure you want to permanently delete this document?<br><br><strong>Warning:</strong> This action cannot be undone.';
        $footerButtons = sprintf(
            '<button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>' .
            '<button type="button" class="btn btn-primary delete-document-btn"  data-delete="true" data-url="%s" data-token="%s">Delete</button>',
            $deleteUrl,
            $csrfToken
        );

        $deleteModal = $this->modalboxHelper->renderModal(
            $modalId,
            'Confirm Document Deletion',
            $modalBody,
            $footerButtons
        );

        return sprintf(
            '<div class="d-inline-flex gap-2">
                <a href="%s" class="bs-btn btn btn-primary btn-sm" style="margin: 2px;"><i class="bs-fa fa fa-edit"></i></a>
                <button type="button" class="bs-btn btn btn-primary btn-sm" style="margin: 2px;" data-toggle="modal" data-target="#%s">
                    <i class="bs-fa fa fa-trash"></i>
                </button>
            </div>%s',
            $editUrl,
            $modalId,
            $deleteModal
        );
    }

    private function buildQuery(QueryBuilder $qb): void
    {
        $qb->select('d')
            ->from(Document::class, 'd');
    }
}