{% extends 'admin/dashboard/base.html.twig' %}
{% block body %}
    {% include 'admin/partials/breadcrumbs.html.twig' with {
        title: 'Employees',
        breadcrumbs: [
            { 'label': 'Dashboard', 'url': url('admin_dashboard') },
            { 'label': 'Employees', 'url': url('master_employee') }
        ],
        active: mode == 'view' ? 'View Employee' : (mode == 'edit' ? 'Edit Employee' : 'Create Employee')
    } %}

    <div class="content">
        <div class="animated fadeIn">
            <div class="row">
                <div class="col-lg-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <strong class="card-title">
                                {{ mode == 'view' ? 'View Employee' : (mode == 'edit' ? 'Edit Employee' : 'Create Employee') }}
                            </strong>
                            <div class="d-flex align-items-center">
                                <a href="{{ path('master_employee') }}" class="btn btn-sm btn-outline-secondary mr-2">
                                    <i class="fa fa-list mr-1"></i>Back to List
                                </a>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="employee-form">
                                {% if form is defined %}
                                    {{ form_start(form, { 'attr': { 'class': 'needs-validation use-loader', 'novalidate': 'novalidate' } }) }}
                                    {{ form_widget(form.id) }}
                                    <!-- Account Information Section -->
                                    <div class="form-section">
                                        <h5 class="section-header">
                                            <i class="fa fa-user mr-2"></i>Account Information
                                        </h5>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.email, 'Email Address', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-envelope"></i></span>
                                                        </div>
                                                        {{ form_widget(form.email, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter email address' } }) }}
                                                    </div>
                                                    {{ form_errors(form.email) }}
                                                    <small class="form-text text-muted">Will be used for login and communications</small>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.username, 'Username', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-user"></i></span>
                                                        </div>
                                                        {{ form_widget(form.username, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter unique username' } }) }}
                                                    </div>
                                                    {{ form_errors(form.username) }}
                                                    <small class="form-text text-muted">Must be unique across the system</small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.name, 'Full Name', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-id-card"></i></span>
                                                        </div>
                                                        {{ form_widget(form.name, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter employee full name' } }) }}
                                                    </div>
                                                    {{ form_errors(form.name) }}
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.employeeCode, 'Employee ID', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-id-badge"></i></span>
                                                        </div>
                                                        {{ form_widget(form.employeeCode, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter employee ID code' } }) }}
                                                    </div>
                                                    {{ form_errors(form.employeeCode) }}
                                                    <small class="form-text text-muted">Unique identifier for the employee</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Professional Experience Section -->
                                    <div class="form-section">
                                        <h5 class="section-header">
                                            <i class="fa fa-briefcase mr-2"></i>Professional Experience
                                        </h5>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.joiningDate, 'Joining Date', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-calendar"></i></span>
                                                        </div>
                                                        {{ form_widget(form.joiningDate, {
                                                            'attr': {
                                                                'class': 'form-control datepicker',
                                                                'data-initial-date': master_employee.joiningDate is defined and master_employee.joiningDate ? master_employee.joiningDate|date('d-m-Y') : ''
                                                            }
                                                        }) }}
                                                    </div>
                                                    {{ form_errors(form.joiningDate) }}
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.totalExperience, 'Total Experience (Years)', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-history"></i></span>
                                                        </div>
                                                        {{ form_widget(form.totalExperience, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter experience in years' } }) }}
                                                    </div>
                                                    {{ form_errors(form.totalExperience) }}
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.confirmationDate, 'Confirmation Date', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-check-circle"></i></span>
                                                        </div>
                                                        {{ form_widget(form.confirmationDate, {
                                                            'attr': {
                                                                'class': 'form-control datepicker',
                                                                'data-initial-date': master_employee.confirmationDate is defined and master_employee.confirmationDate ? master_employee.confirmationDate|date('d-m-Y') : ''
                                                            }
                                                        }) }}
                                                    </div>
                                                    {{ form_errors(form.confirmationDate) }}
                                                    <small class="form-text text-muted">Date when probation period ended</small>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.previousCompanyName, 'Previous Company', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-building"></i></span>
                                                        </div>
                                                        {{ form_widget(form.previousCompanyName, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter previous company name' } }) }}
                                                    </div>
                                                    {{ form_errors(form.previousCompanyName) }}
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Contact Information Section -->
                                    <div class="form-section">
                                        <h5 class="section-header">
                                            <i class="fa fa-address-book mr-2"></i>Contact Information
                                        </h5>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.personalPhoneNumber, 'Primary Phone', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-phone"></i></span>
                                                        </div>
                                                        {{ form_widget(form.personalPhoneNumber, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter primary phone number' } }) }}
                                                    </div>
                                                    {{ form_errors(form.personalPhoneNumber) }}
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.alternativePhoneNumber, 'Alternative Phone', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-phone-square"></i></span>
                                                        </div>
                                                        {{ form_widget(form.alternativePhoneNumber, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter alternative phone number' } }) }}
                                                    </div>
                                                    {{ form_errors(form.alternativePhoneNumber) }}
                                                    <small class="form-text text-muted">Emergency contact number</small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.currentAddress, 'Current Address', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-home"></i></span>
                                                        </div>
                                                        {{ form_widget(form.currentAddress, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter current address' } }) }}
                                                    </div>
                                                    {{ form_errors(form.currentAddress) }}
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.permanentAddress, 'Permanent Address', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-map-marker"></i></span>
                                                        </div>
                                                        {{ form_widget(form.permanentAddress, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter permanent address' } }) }}
                                                    </div>
                                                    {{ form_errors(form.permanentAddress) }}
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Personal Information Section -->
                                    <div class="form-section">
                                        <h5 class="section-header">
                                            <i class="fa fa-info-circle mr-2"></i>Personal Information
                                        </h5>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.birthDate, 'Birth Date', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-check-circle"></i></span>
                                                        </div>
                                                        {{ form_widget(form.birthDate, {
                                                            'attr': {
                                                                'class': 'form-control datepicker',
                                                                'data-initial-date': master_employee.birthDate is defined and master_employee.birthDate ? master_employee.birthDate|date('d-m-Y') : ''
                                                            }
                                                        }) }}
                                                    </div>
                                                    {{ form_errors(form.birthDate) }}
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.gender, 'Gender', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-venus-mars"></i></span>
                                                        </div>
                                                        {{ form_widget(form.gender, { 'attr': { 'class': 'form-control' } }) }}
                                                    </div>
                                                    {{ form_errors(form.gender) }}
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.maritalStatus, 'Marital Status', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-life-ring"></i></span>
                                                        </div>
                                                        {{ form_widget(form.maritalStatus, { 'attr': { 'class': 'form-control' } }) }}
                                                    </div>
                                                    {{ form_errors(form.maritalStatus) }}
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Banking Information Section -->
                                    <div class="form-section">
                                        <h5 class="section-header">
                                            <i class="fa fa-university mr-2"></i>Banking Information
                                        </h5>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.bankName, 'Bank Name', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-university"></i></span>
                                                        </div>
                                                        {{ form_widget(form.bankName, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter bank name' } }) }}
                                                    </div>
                                                    {{ form_errors(form.bankName) }}
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.bankAccountNumber, 'Account Number', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-credit-card"></i></span>
                                                        </div>
                                                        {{ form_widget(form.bankAccountNumber, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter bank account number' } }) }}
                                                    </div>
                                                    {{ form_errors(form.bankAccountNumber) }}
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.ifscCode, 'IFSC Code', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-barcode"></i></span>
                                                        </div>
                                                        {{ form_widget(form.ifscCode, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter IFSC code' } }) }}
                                                    </div>
                                                    {{ form_errors(form.ifscCode) }}
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.panNumber, 'PAN Number', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-id-card"></i></span>
                                                        </div>
                                                        {{ form_widget(form.panNumber, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter PAN number' } }) }}
                                                    </div>
                                                    {{ form_errors(form.panNumber) }}
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.aadhaarNumber, 'Aadhaar Number', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-id-card-o"></i></span>
                                                        </div>
                                                        {{ form_widget(form.aadhaarNumber, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter Aadhaar number' } }) }}
                                                    </div>
                                                    {{ form_errors(form.aadhaarNumber) }}
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.uan, 'UAN', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-id-card"></i></span>
                                                        </div>
                                                        {{ form_widget(form.uan, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter UAN number' } }) }}
                                                    </div>
                                                    {{ form_errors(form.uan) }}
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.pfNumber, 'PF Number', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-id-card"></i></span>
                                                        </div>
                                                        {{ form_widget(form.pfNumber, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter PF number' } }) }}
                                                    </div>
                                                    {{ form_errors(form.pfNumber) }}
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.esiNumber, 'ESI Number', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-id-card"></i></span>
                                                        </div>
                                                        {{ form_widget(form.esiNumber, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter ESI number' } }) }}
                                                    </div>
                                                    {{ form_errors(form.esiNumber) }}
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Organizational Structure Section -->
                                    <div class="form-section org-structure-section">
                                        <h5 class="section-header">
                                            <i class="fa fa-sitemap mr-2"></i>Organizational Structure
                                        </h5>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.teamLeader, 'Team Leader', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                    <div class="checkbox-group">
                                                        <div class="input-group mb-2">
                                                            <div class="input-group-prepend">
                                                                <div class="input-group-text">
                                                                    {{ form_widget(form.teamLeader, { 'attr': { 'class': 'custom-checkbox', 'aria-label': 'Checkbox for Team Leader' } }) }}
                                                                </div>
                                                            </div>
                                                            <div class="form-control checkbox-label">
                                                                {{ form_label(form.teamLeader, 'Is Team Leader', { 'label_attr': { 'class': 'ml-2' } }) }}
                                                            </div>
                                                        </div>
                                                        {{ form_errors(form.teamLeader) }}
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.isHrAccount, 'HR Role', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                    <div class="checkbox-group">
                                                        <div class="input-group mb-2">
                                                            <div class="input-group-prepend">
                                                                <div class="input-group-text">
                                                                    {{ form_widget(form.isHrAccount, { 'attr': { 'class': 'custom-checkbox', 'aria-label': 'Checkbox for HR Role' } }) }}
                                                                </div>
                                                            </div>
                                                            <div class="form-control checkbox-label">
                                                                {{ form_label(form.isHrAccount, 'Is HR', { 'label_attr': { 'class': 'ml-2' } }) }}
                                                            </div>
                                                        </div>
                                                        {{ form_errors(form.isHrAccount) }}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.reportsTo, 'Reports To', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                    {% if form.reportsTo.children is not empty %}
                                                    <div class="checkbox-group">
                                                        {% for report in form.reportsTo %}
                                                            <div class="input-group mb-2">
                                                                <div class="input-group-prepend">
                                                                    <div class="input-group-text">
                                                                        {{ form_widget(report, { 'attr': { 'class': 'custom-checkbox', 'aria-label': 'Checkbox for ' ~ report.vars.label|default('report') } }) }}
                                                                    </div>
                                                                </div>
                                                                <div class="form-control checkbox-label">
                                                                    {{ form_label(report, null, { 'label_attr': { 'class': 'ml-2' } }) }}
                                                                </div>
                                                            </div>
                                                        {% endfor %}
                                                        {{ form_errors(form.reportsTo) }}
                                                    </div>
                                                    {% else %}
                                                        <p class="text-muted">No Reports To available.</p>
                                                    {% endif %}
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.departments, 'Departments', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                    {% if form.departments.children is not empty %}
                                                        <div class="checkbox-group">
                                                            {% for department in form.departments %}
                                                                <div class="input-group mb-2">
                                                                    <div class="input-group-prepend">
                                                                        <div class="input-group-text">
                                                                            {{ form_widget(department, { 'attr': { 'class': 'custom-checkbox', 'aria-label': 'Checkbox for ' ~ department.vars.label|default('department') } }) }}
                                                                        </div>
                                                                    </div>
                                                                    <div class="form-control checkbox-label">
                                                                        {{ form_label(department, null, { 'label_attr': { 'class': 'ml-2' } }) }}
                                                                    </div>
                                                                </div>
                                                            {% endfor %}
                                                            {{ form_errors(form.departments) }}
                                                        </div>
                                                    {% else %}
                                                        <p class="text-muted">No Departments available.</p>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="d-flex justify-content-end mt-4">
                                        <a href="{{ path('master_employee') }}" class="btn btn-secondary mr-2">Cancel</a>
                                        <button type="submit" class="btn btn-primary pl-3 pr-3">
                                            {{ employee.id is defined ? 'Update Employee' : 'Create Employee' }}
                                        </button>
                                    </div>

                                    {{ form_end(form) }}
                                {% else %}
                                    <!-- View Mode -->
                                    <div class="form-section employee-details-section">
                                        <!-- Account Information Section -->
                                        <div class="details-section">
                                            <h5 class="section-header">
                                                <i class="fa fa-user mr-2"></i>Account Information
                                            </h5>
                                            <div class="details-container">
                                                <div class="detail-row">
                                                    <div class="detail-item col-md-4"><strong class="detail-label">Email:</strong></div>
                                                    <div class="detail-item col-md-8">{{ employee.email }}</div>
                                                </div>
                                                <div class="detail-row">
                                                    <div class="detail-item col-md-4"><strong class="detail-label">Username:</strong></div>
                                                    <div class="detail-item col-md-8">{{ employee.username }}</div>
                                                </div>
                                                <div class="detail-row">
                                                    <div class="detail-item col-md-4"><strong class="detail-label">Name:</strong></div>
                                                    <div class="detail-item col-md-8">{{ employee.name }}</div>
                                                </div>
                                                <div class="detail-row">
                                                    <div class="detail-item col-md-4"><strong class="detail-label">Employee Code:</strong></div>
                                                    <div class="detail-item col-md-8">{{ employee.employeeCode }}</div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Professional Experience Section -->
                                        <div class="details-section">
                                            <h5 class="section-header">
                                                <i class="fa fa-briefcase mr-2"></i>Professional Experience
                                            </h5>
                                            <div class="details-container">
                                                <div class="detail-row">
                                                    <div class="detail-item col-md-4"><strong class="detail-label">Joining Date:</strong></div>
                                                    <div class="detail-item col-md-8">{{ employee.joiningDate | date('d-m-Y') }}</div>
                                                </div>
                                                <div class="detail-row">
                                                    <div class="detail-item col-md-4"><strong class="detail-label">Total Experience:</strong></div>
                                                    <div class="detail-item col-md-8">{{ employee.totalExperience }}</div>
                                                </div>
                                                <div class="detail-row">
                                                    <div class="detail-item col-md-4"><strong class="detail-label">Confirmation Date:</strong></div>
                                                    <div class="detail-item col-md-8">{{ employee.confirmationDate ? (employee.confirmationDate | date('d-m-Y')) : 'N/A' }}</div>
                                                </div>
                                                <div class="detail-row">
                                                    <div class="detail-item col-md-4"><strong class="detail-label">Previous Company:</strong></div>
                                                    <div class="detail-item col-md-8">{{ employee.previousCompanyName ?: 'N/A' }}</div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Contact Information Section -->
                                        <div class="details-section">
                                            <h5 class="section-header">
                                                <i class="fa fa-address-book mr-2"></i>Contact Information
                                            </h5>
                                            <div class="details-container">
                                                <div class="detail-row">
                                                    <div class="detail-item col-md-4"><strong class="detail-label">Primary Phone:</strong></div>
                                                    <div class="detail-item col-md-8">{{ employee.personalPhoneNumber }}</div>
                                                </div>
                                                <div class="detail-row">
                                                    <div class="detail-item col-md-4"><strong class="detail-label">Alternative Phone:</strong></div>
                                                    <div class="detail-item col-md-8">{{ employee.alternativePhoneNumber ?: 'N/A' }}</div>
                                                </div>
                                                <div class="detail-row">
                                                    <div class="detail-item col-md-4"><strong class="detail-label">Current Address:</strong></div>
                                                    <div class="detail-item col-md-8">{{ employee.currentAddress }}</div>
                                                </div>
                                                <div class="detail-row">
                                                    <div class="detail-item col-md-4"><strong class="detail-label">Permanent Address:</strong></div>
                                                    <div class="detail-item col-md-8">{{ employee.permanentAddress ?: 'N/A' }}</div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Personal Information Section -->
                                        <div class="details-section">
                                            <h5 class="section-header">
                                                <i class="fa fa-info-circle mr-2"></i>Personal Information
                                            </h5>
                                            <div class="details-container">
                                                <div class="detail-row">
                                                    <div class="detail-item col-md-4"><strong class="detail-label">Birth Date:</strong></div>
                                                    <div class="detail-item col-md-8">{{ employee.birthDate | date('d-m-Y') }}</div>
                                                </div>
                                                <div class="detail-row">
                                                    <div class="detail-item col-md-4"><strong class="detail-label">Gender:</strong></div>
                                                    <div class="detail-item col-md-8">{{ employee.gender }}</div>
                                                </div>
                                                <div class="detail-row">
                                                    <div class="detail-item col-md-4"><strong class="detail-label">Marital Status:</strong></div>
                                                    <div class="detail-item col-md-8">{{ employee.maritalStatus }}</div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Banking Information Section -->
                                        <div class="details-section">
                                            <h5 class="section-header">
                                                <i class="fa fa-university mr-2"></i>Banking Information
                                            </h5>
                                            <div class="details-container">
                                                <div class="detail-row">
                                                    <div class="detail-item col-md-4"><strong class="detail-label">Bank Name:</strong></div>
                                                    <div class="detail-item col-md-8">{{ employee.bankName }}</div>
                                                </div>
                                                <div class="detail-row">
                                                    <div class="detail-item col-md-4"><strong class="detail-label">Bank Account:</strong></div>
                                                    <div class="detail-item col-md-8">{{ employee.bankAccountNumber }}</div>
                                                </div>
                                                <div class="detail-row">
                                                    <div class="detail-item col-md-4"><strong class="detail-label">IFSC Code:</strong></div>
                                                    <div class="detail-item col-md-8">{{ employee.ifscCode }}</div>
                                                </div>
                                                <div class="detail-row">
                                                    <div class="detail-item col-md-4"><strong class="detail-label">PAN Number:</strong></div>
                                                    <div class="detail-item col-md-8">{{ employee.panNumber }}</div>
                                                </div>
                                                <div class="detail-row">
                                                    <div class="detail-item col-md-4"><strong class="detail-label">Aadhaar Number:</strong></div>
                                                    <div class="detail-item col-md-8">{{ employee.aadhaarNumber ?: 'N/A' }}</div>
                                                </div>
                                                <div class="detail-row">
                                                    <div class="detail-item col-md-4"><strong class="detail-label">UAN:</strong></div>
                                                    <div class="detail-item col-md-8">{{ employee.uan ?: 'N/A' }}</div>
                                                </div>
                                                <div class="detail-row">
                                                    <div class="detail-item col-md-4"><strong class="detail-label">PF Number:</strong></div>
                                                    <div class="detail-item col-md-8">{{ employee.pfNumber ?: 'N/A' }}</div>
                                                </div>
                                                <div class="detail-row">
                                                    <div class="detail-item col-md-4"><strong class="detail-label">ESI Number:</strong></div>
                                                    <div class="detail-item col-md-8">{{ employee.esiNumber ?: 'N/A' }}</div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Organizational Structure Section -->
                                        <div class="details-section org-structure-section">
                                            <h5 class="section-header">
                                                <i class="fa fa-sitemap mr-2"></i>Organizational Structure
                                            </h5>
                                            <div class="details-container">
                                                <div class="detail-row">
                                                    <div class="detail-item col-md-4"><strong class="detail-label">Reports To:</strong></div>
                                                    <div class="detail-item col-md-8">
                                                        {% for report in employee.reportsTo %}
                                                            {{ report.teamLeader.name }}{% if not loop.last %}, {% endif %}
                                                        {% else %}
                                                            N/A
                                                        {% endfor %}
                                                    </div>
                                                </div>
                                                <div class="detail-row">
                                                    <div class="detail-item col-md-4"><strong class="detail-label">Departments:</strong></div>
                                                    <div class="detail-item col-md-8">
                                                        {% for department in employee.departments %}
                                                            {{ department.depName }}{% if not loop.last %}, {% endif %}
                                                        {% else %}
                                                            N/A
                                                        {% endfor %}
                                                    </div>
                                                </div>
                                                <div class="detail-row">
                                                    <div class="detail-item col-md-4"><strong class="detail-label">Team Leader:</strong></div>
                                                    <div class="detail-item col-md-8">{{ employee.teamLeader ? 'Yes' : 'No' }}</div>
                                                </div>
                                                <div class="detail-row">
                                                    <div class="detail-item col-md-4"><strong class="detail-label">HR Role:</strong></div>
                                                    <div class="detail-item col-md-8">{{ employee.hrAccount ? 'Yes' : 'No' }}</div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Payslip Section -->
                                        <div class="details-section">
                                            <h5 class="section-header">
                                                <i class="fa fa-file-pdf-o mr-2"></i>Employee Payslips
                                            </h5>
                                            <div class="details-container">
                                                {% if payrolls is defined and payrolls|length > 0 %}
                                                    <table class="table table-bordered table-striped">
                                                        <thead>
                                                            <tr>
                                                                <th>Month</th>
                                                                <th>Year</th>
                                                                <th>Actions</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            {% for payroll in payrolls %}
                                                                <tr>
                                                                    {% set monthNames = {
                                                                        1: 'January', 2: 'February', 3: 'March', 4: 'April',
                                                                        5: 'May', 6: 'June', 7: 'July', 8: 'August',
                                                                        9: 'September', 10: 'October', 11: 'November', 12: 'December'
                                                                    } %}
                                                                    <td>{{ monthNames[payroll.month] }}</td>
                                                                    <td>{{ payroll.year }}</td>
                                                                    <td style="display: flex">
                                                                        <a href="{{ path('admin_payroll_payslip_pdf', {month: payroll.month, year: payroll.year, employeeId: employee.id}) }}" target="_blank" class="bs-btn btn btn-sm btn-primary mr-1">
                                                                            <i class="fa fa-eye"></i>
                                                                        </a>
                                                                        <a href="{{ path('admin_payroll_payslip_pdf', {month: payroll.month, year: payroll.year, employeeId: employee.id}) }}" target="_blank" class="bs-btn btn btn-sm btn-primary" download>
                                                                            <i class="bs-fa fa fa-download"></i>
                                                                        </a>
                                                                    </td>
                                                                </tr>
                                                            {% endfor %}
                                                        </tbody>
                                                    </table>
                                                {% else %}
                                                    <p>No payslips available for this employee.</p>
                                                {% endif %}
                                            </div>
                                        </div>

                                        <div class="action-footer mt-4">
                                            <a href="{{ path('master_employee') }}" class="btn btn-secondary mr-2">
                                                Back to List
                                            </a>
                                            <a href="{{ path('master_employee_edit', {'id': employee.id}) }}" class="btn btn-primary pr-3 pl-3" style="padding-top: 10px;">
                                               Edit Employee
                                            </a>
                                        </div>
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="clearfix"></div>
    <link rel="stylesheet" href="{{ asset('assets/css/Error/error.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/AdminForm/Form.css') }}">
    <script>
        const currentEmployeeId = "{{ employee.id is defined ? employee.id : '' }}";
    </script>
    <script src="{{ asset('assets/js/datatables.min.js') }}"></script>
    <script src="{{ asset('bundles/datatables/js/datatables.js') }}"></script>
    <script src="{{ asset('assets/js/moment.min.js') }}"></script>
    <script src="{{ asset('assets/js/daterangepicker.min.js') }}"></script>
    <script src="{{ asset('assets/js/admin/manualemployee.js') }}"></script>
{% endblock %}