security:
    # https://symfony.com/doc/current/security.html#registering-the-user-hashing-passwords
    password_hashers:
        Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface: 'auto'
    # https://symfony.com/doc/current/security.html#loading-the-user-the-user-provider
    providers:
      app_user_provider:
            entity:
                class: App\Entity\User
                property: email

      app_employee_provider:
          entity:
            class: App\Entity\MasterEmployee
            property: email

    firewalls:
        dev:
            pattern: ^/(_(profiler|wdt)|css|images|js)/
            security: false

        api:
            pattern: ^/api
            stateless: true
            security: false

        admin:
           pattern: ^/admin
           provider: app_user_provider
           form_login:
              login_path: app_login_admin
              check_path: app_login_admin
              default_target_path: admin_dashboard
           remember_me:
             secret: '%kernel.secret%' # required
             lifetime: 31536000 # 1 week in seconds
           logout:
            path: app_logout_admin
            target: app_login_admin

        employee:
            pattern: ^/
            provider: app_employee_provider
            form_login:
              login_path: app_login_employee
              check_path: app_login_employee
              default_target_path: dashboard_employee
            remember_me:
              secret: '%kernel.secret%' # required
              lifetime: 31536000 # 1 week in seconds
            logout:
             path: app_logout_employee
             target: app_login_employee



            # activate different ways to authenticate
            # https://symfony.com/doc/current/security.html#the-firewall

            # https://symfony.com/doc/current/security/impersonating_user.html
            # switch_user: true

    # Easy way to control access for large sections of your site
    # Note: Only the *first* access control that matches will be used
    access_control:
         # Employee routes
         - { path: ^/dashboard-employee, roles: [ ROLE_EMPLOYEE ] }
         - { path: ^/feedback-history, roles: [ ROLE_EMPLOYEE ] }
         - { path: ^/forms, roles: [ ROLE_EMPLOYEE ] }
         - { path: ^/employee-profile, roles: [ ROLE_EMPLOYEE ] }
         - { path: ^/employee-feedback-history, roles: [ ROLE_EMPLOYEE ] }
         - { path: ^/self-evaluation-form, roles: [ ROLE_EMPLOYEE ] }
         - { path: ^/self-history, roles: [ ROLE_EMPLOYEE ] }
         - { path: ^/employee/leave-request/list, roles: [ ROLE_EMPLOYEE ] }
         - { path: ^/employee/leave, roles: [ ROLE_EMPLOYEE ] }
         - { path: ^/documents, roles: [ ROLE_EMPLOYEE ] }
         - { path: ^/employee/projects, roles: [ ROLE_EMPLOYEE ] }
         - { path: ^/employee/task, roles: [ ROLE_EMPLOYEE ] }
         - { path: ^/employee-activity, roles: [ ROLE_EMPLOYEE ] }
         - { path: ^/employee/other/activity, roles: [ ROLE_EMPLOYEE ] }
         - { path: ^/bug, roles: [ ROLE_EMPLOYEE ] }
         - { path: ^/my/employee/documents, roles: [ ROLE_EMPLOYEE ] }
         - { path: ^/my-bugs, roles: [ ROLE_EMPLOYEE ] }
         - { path: ^/my-bugs/history, roles: [ ROLE_EMPLOYEE ] }
         - { path: ^/employee/task/history, roles: [ ROLE_EMPLOYEE ] }
         - { path: ^/employee/project, roles: [ ROLE_EMPLOYEE ] }
         - { path: ^/employee/report/360/form, roles: [ ROLE_EMPLOYEE ] }
         # Admin routes
         - { path: ^/admin/dashboard, roles: [ ROLE_ADMIN ] }
         - { path: ^/admin/weekly-hours, roles: [ ROLE_ADMIN ] }
         - { path: ^/admin/master-employee, roles: [ ROLE_ADMIN ] }
         - { path: ^/admin/department, roles: [ ROLE_ADMIN ] }
         - { path: ^/admin/setting, roles: [ ROLE_ADMIN ] }
         - { path: ^/admin/form-template, roles: [ ROLE_ADMIN ] }
         - { path: ^/admin/employee, roles: [ ROLE_ADMIN ] }
         - { path: ^/admin/summary, roles: [ ROLE_ADMIN ] }
         - { path: ^/admin/user/update-password, roles: [ ROLE_ADMIN ] }
         - { path: ^/admin/report, roles: [ ROLE_ADMIN ] }
         - { path: ^/admin/email-config-page, roles: [ ROLE_ADMIN ] }
         - { path: ^/admin/email-template, roles: [ ROLE_ADMIN ] }
         - { path: ^/admin/report-config-page, roles: [ ROLE_ADMIN ] }
         - { path: ^/admin/remaining, roles: [ ROLE_ADMIN ] }
         - { path: ^/admin/api/teamlogger, roles: [ ROLE_ADMIN ] }
         - { path: ^/admin/feedback-history, roles: [ ROLE_ADMIN ] }
         - { path: ^/admin/feedback-history-self-evaluation, roles: [ ROLE_ADMIN ] }
         - { path: ^/admin/holiday/list, roles: [ ROLE_ADMIN ] }
         - { path: ^/admin/leave-type/list, roles: [ ROLE_ADMIN ] }
         - { path: ^/admin/leave-balance/list, roles: [ ROLE_ADMIN ] }
         - { path: ^/admin/leave-requests, roles: [ ROLE_ADMIN ] }
         - { path: ^/admin/leave/list, roles: [ ROLE_ADMIN ] }
         - { path: ^/admin/leave/policy, roles: [ ROLE_ADMIN ] }
         - { path: ^/admin/admin/documents, roles: [ ROLE_ADMIN ] }
         - { path: ^/admin/announcement, roles: [ ROLE_ADMIN ] }
         - { path: ^/admin/project, roles: [ ROLE_ADMIN ] }
         - { path: ^/admin/notification-config-page, roles: [ ROLE_ADMIN ] }
         - { path: ^/admin/employee/activity, roles: [ ROLE_ADMIN ] }
         - { path: ^/admin/bug, roles: [ ROLE_ADMIN ] }
         - { path: ^/admin/employee/report/360/form, roles: [ ROLE_ADMIN ] }
         - { path: ^/admin/attendance-report, roles: [ ROLE_ADMIN ] }
         - { path: ^/admin/payroll, roles: [ ROLE_ADMIN ] }
         - { path: ^/admin/extrapay, roles: [ ROLE_ADMIN ] }
         - { path: ^/admin/employee-arrears, roles: [ ROLE_ADMIN ] }
         - { path: ^/admin/Payroll/PDF, roles: [ ROLE_ADMIN ] }
         - { path: ^/admin/payroll/payslip/pdf, roles: [ ROLE_ADMIN ] }

when@test:
    security:
        password_hashers:
            # By default, password hashers are resource intensive and take time. This is
            # important to generate secure password hashes. In tests however, secure hashes
            # are not important, waste resources and increase test times. The following
            # reduces the work factor to the lowest possible values.
            Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface:
                algorithm: auto
                cost: 4 # Lowest possible value for bcrypt
                time_cost: 3 # Lowest possible value for argon
                memory_cost: 10 # Lowest possible value for argon
