<?php

namespace App\Repository;

use SymfonyCasts\Bundle\ResetPassword\Persistence\ResetPasswordRequestRepositoryInterface;
use SymfonyCasts\Bundle\ResetPassword\Model\ResetPasswordRequestInterface;
use App\Entity\MasterEmployee;
use App\Entity\User;
use App\Entity\ResetPasswordRequest;
use App\Entity\ResetPasswordRequestEmployee;
use DateTimeInterface;

class CustomResetPasswordRequestRepository implements ResetPasswordRequestRepositoryInterface
{

    public function __construct(
        private readonly ResetPasswordRequestRepository         $adminRepository,
        private readonly ResetPasswordRequestEmployeeRepository $employeeRepository,

    )
    {

    }
    public function findResetPasswordRequest(string $selector): ?ResetPasswordRequestInterface
    {
        $request = $this->adminRepository->findResetPasswordRequest($selector);

        if (!$request) {
            $request = $this->employeeRepository->findResetPasswordRequest($selector);
        }

        return $request;
    }

    public function createResetPasswordRequest(
        object            $user,
        DateTimeInterface $expiresAt,
        string            $selector,
        string            $hashedToken
    ): ResetPasswordRequestInterface
    {
        if ($user instanceof MasterEmployee) {
            return new ResetPasswordRequestEmployee($user, $expiresAt, $selector, $hashedToken);
        }

        if ($user instanceof User) {
            return new ResetPasswordRequest($user, $expiresAt, $selector, $hashedToken);
        }

        throw new \InvalidArgumentException('Unsupported user type for password reset.');
    }

    public function removeResetPasswordRequest(object $resetPasswordRequest): void
    {
        if ($resetPasswordRequest instanceof ResetPasswordRequestEmployee) {
            $this->employeeRepository->removeResetPasswordRequest($resetPasswordRequest);
        } elseif ($resetPasswordRequest instanceof ResetPasswordRequest) {
            $this->adminRepository->removeResetPasswordRequest($resetPasswordRequest);
        }
    }

    public function getUserIdentifier(object $user): string
    {
        if ($user instanceof MasterEmployee || $user instanceof User) {
            return $user->getEmail();
        }

        throw new \InvalidArgumentException('Unsupported user type for password reset.');
    }

    public function persistResetPasswordRequest(ResetPasswordRequestInterface $resetPasswordRequest): void
    {
        if ($resetPasswordRequest instanceof ResetPasswordRequestEmployee) {
            $this->employeeRepository->persistResetPasswordRequest($resetPasswordRequest);
        } elseif ($resetPasswordRequest instanceof ResetPasswordRequest) {
            $this->adminRepository->persistResetPasswordRequest($resetPasswordRequest);
        }
    }

    public function getMostRecentNonExpiredRequestDate(object $user): ?\DateTimeInterface
    {
        if ($user instanceof MasterEmployee) {
            return $this->employeeRepository->getMostRecentNonExpiredRequestDate($user);
        }

        if ($user instanceof User) {
            return $this->adminRepository->getMostRecentNonExpiredRequestDate($user);
        }

        return null;
    }

    public function removeRequests(object $user): void
    {
        if ($user instanceof MasterEmployee) {
            $this->employeeRepository->removeRequests($user);
        } elseif ($user instanceof User) {
            $this->adminRepository->removeRequests($user);
        }
    }

    public function removeExpiredResetPasswordRequests(): int
    {
        $adminRemoved = $this->adminRepository->removeExpiredResetPasswordRequests();
        $employeeRemoved = $this->employeeRepository->removeExpiredResetPasswordRequests();

        return $adminRemoved + $employeeRemoved;
    }


}
