<?php

namespace App\Controller\Api;

use App\Entity\ScreenshotLog;
use App\Repository\MasterEmployeeRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\String\Slugger\SluggerInterface;

#[Route('/api/files', name: 'api_files_')]
class FileUploadController extends AbstractController
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private MasterEmployeeRepository $employeeRepository,
        private SluggerInterface $slugger
    ) {}

    #[Route('/upload', name: 'upload', methods: ['POST'])]
    public function uploadFile(Request $request): JsonResponse
    {
        try {
            /** @var UploadedFile $uploadedFile */
            $uploadedFile = $request->files->get('file');
            
            if (!$uploadedFile) {
                return new JsonResponse([
                    'success' => false,
                    'error' => 'No file uploaded'
                ], 400);
            }

            // Validate file
            $maxFileSize = 10 * 1024 * 1024; // 10MB
            if ($uploadedFile->getSize() > $maxFileSize) {
                return new JsonResponse([
                    'success' => false,
                    'error' => 'File too large'
                ], 400);
            }

            $allowedMimeTypes = ['image/jpeg', 'image/png', 'image/webp'];
            if (!in_array($uploadedFile->getMimeType(), $allowedMimeTypes)) {
                return new JsonResponse([
                    'success' => false,
                    'error' => 'Invalid file type'
                ], 400);
            }

            // Get parameters
            $fileType = $request->request->get('file_type');
            $employeeId = $request->request->get('employee_id');
            $metadata = $request->request->get('metadata');

            if (!$fileType || !$employeeId) {
                return new JsonResponse([
                    'success' => false,
                    'error' => 'Missing required parameters'
                ], 400);
            }

            // Find employee
            $employee = $this->employeeRepository->find($employeeId);
            if (!$employee) {
                return new JsonResponse([
                    'success' => false,
                    'error' => 'Employee not found'
                ], 404);
            }

            // Generate unique filename
            $originalFilename = pathinfo($uploadedFile->getClientOriginalName(), PATHINFO_FILENAME);
            $safeFilename = $this->slugger->slug($originalFilename);
            $newFilename = $safeFilename . '-' . uniqid() . '.' . $uploadedFile->guessExtension();

            // Create employee-specific folder structure: /uploads/screenshots/EmployeeName/YYYY-MM/
            $employeeName = $employee->getName();
            // Clean employee name for folder (remove special characters)
            $cleanEmployeeName = preg_replace('/[^a-zA-Z0-9_-]/', '', str_replace(' ', '', $employeeName));

            // Get month from captured date or current month
            $metadataArray = $metadata ? json_decode($metadata, true) : [];
            if (isset($metadataArray['captured_date'])) {
                $capturedDate = new \DateTime($metadataArray['captured_date']);
                $monthFolder = $capturedDate->format('Y-m');
            } else {
                $monthFolder = date('Y-m');
            }

            // Create upload directory structure
            $uploadDir = $this->getParameter('kernel.project_dir') . '/public/uploads/' . $fileType . 's/' . $cleanEmployeeName . '/' . $monthFolder;
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0755, true);
            }

            // Move the file
            $uploadedFile->move($uploadDir, $newFilename);

            $filePath = '/uploads/' . $fileType . 's/' . $cleanEmployeeName . '/' . $monthFolder . '/' . $newFilename;
            $fullPath = $uploadDir . '/' . $newFilename;

            // Save to database based on file type
            $entity = null;
            if ($fileType === 'screenshot') {
                $entity = $this->saveScreenshotRecord($employee, $filePath, $newFilename, $uploadedFile, $metadata);
            }

            if (!$entity) {
                // Clean up uploaded file if database save failed
                if (file_exists($fullPath)) {
                    unlink($fullPath);
                }
                
                return new JsonResponse([
                    'success' => false,
                    'error' => 'Failed to save file record'
                ], 500);
            }

            return new JsonResponse([
                'success' => true,
                'message' => 'File uploaded successfully',
                'file_url' => $filePath,
                'file_name' => $newFilename,
                'file_size' => $uploadedFile->getSize(),
                'entity_id' => $entity->getId()
            ]);

        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => 'Upload failed: ' . $e->getMessage()
            ], 500);
        }
    }

    private function saveScreenshotRecord($employee, $filePath, $fileName, UploadedFile $file, $metadataJson): ?ScreenshotLog
    {
        try {
            $metadata = $metadataJson ? json_decode($metadataJson, true) : [];

            // Try to find existing screenshot record by filename or metadata
            $existingScreenshot = null;
            if (isset($metadata['original_filename'])) {
                $existingScreenshot = $this->entityManager->getRepository(ScreenshotLog::class)
                    ->findOneBy([
                        'employee' => $employee,
                        'fileName' => $metadata['original_filename']
                    ]);
            }

            if ($existingScreenshot) {
                // Update existing record with server file path
                $existingScreenshot->setFilePath($filePath);
                $existingScreenshot->setIsProcessed(true);

                $this->entityManager->flush();
                return $existingScreenshot;
            } else {
                // Create new record if not found
                $screenshot = new ScreenshotLog();
                $screenshot->setEmployee($employee);
                $screenshot->setCapturedAt(new \DateTime($metadata['captured_at'] ?? 'now'));
                $screenshot->setCaptureType($metadata['capture_type'] ?? 'manual');
                $screenshot->setFilePath($filePath);
                $screenshot->setFileName($fileName);
                $screenshot->setFileSize($file->getSize());
                $screenshot->setResolution($metadata['resolution'] ?? '1920x1080');
                $screenshot->setDeviceId($metadata['device_id'] ?? null);
                $screenshot->setActiveApplication($metadata['active_application'] ?? null);
                $screenshot->setActiveWindow($metadata['active_window'] ?? null);
                $screenshot->setMetadata($metadata);
                $screenshot->setIsSynced(true);
                $screenshot->setIsProcessed(true);
                $screenshot->setCreatedAt(new \DateTime());

                $this->entityManager->persist($screenshot);
                $this->entityManager->flush();

                return $screenshot;
            }
        } catch (\Exception $e) {
            error_log('Failed to save screenshot record: ' . $e->getMessage());
            return null;
        }
    }



    #[Route('/cleanup', name: 'cleanup', methods: ['POST'])]
    public function cleanupOldFiles(Request $request): JsonResponse
    {
        try {
            $data = json_decode($request->getContent(), true);
            $daysOld = $data['days_old'] ?? 30;
            $fileType = $data['file_type'] ?? 'all'; // 'screenshot' or 'all'

            $cutoffDate = new \DateTime();
            $cutoffDate->modify("-{$daysOld} days");

            $deletedCount = 0;

            if ($fileType === 'screenshot' || $fileType === 'all') {
                $deletedCount += $this->cleanupScreenshots($cutoffDate);
            }

            return new JsonResponse([
                'success' => true,
                'message' => "Cleaned up {$deletedCount} old files",
                'deleted_count' => $deletedCount,
                'cutoff_date' => $cutoffDate->format('Y-m-d H:i:s')
            ]);

        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => 'Cleanup failed: ' . $e->getMessage()
            ], 500);
        }
    }

    private function cleanupScreenshots(\DateTime $cutoffDate): int
    {
        $screenshots = $this->entityManager->getRepository(ScreenshotLog::class)
            ->createQueryBuilder('s')
            ->where('s.capturedAt < :cutoffDate')
            ->andWhere('s.isSynced = true')
            ->setParameter('cutoffDate', $cutoffDate)
            ->getQuery()
            ->getResult();

        $deletedCount = 0;
        foreach ($screenshots as $screenshot) {
            $fullPath = $this->getParameter('kernel.project_dir') . '/public' . $screenshot->getFilePath();
            if (file_exists($fullPath)) {
                unlink($fullPath);
            }
            $this->entityManager->remove($screenshot);
            $deletedCount++;
        }

        $this->entityManager->flush();
        return $deletedCount;
    }



    #[Route('/stats', name: 'stats', methods: ['GET'])]
    public function getFileStats(Request $request): JsonResponse
    {
        try {
            $employeeId = $request->query->get('employee_id');
            $startDate = $request->query->get('start_date', date('Y-m-d', strtotime('-30 days')));
            $endDate = $request->query->get('end_date', date('Y-m-d'));

            $criteria = [];
            if ($employeeId) {
                $employee = $this->employeeRepository->find($employeeId);
                if ($employee) {
                    $criteria['employee'] = $employee;
                }
            }

            // Get screenshot stats
            $screenshotCount = $this->entityManager->getRepository(ScreenshotLog::class)
                ->createQueryBuilder('s')
                ->select('COUNT(s.id)')
                ->where('DATE(s.capturedAt) BETWEEN :startDate AND :endDate')
                ->setParameter('startDate', $startDate)
                ->setParameter('endDate', $endDate);

            if (isset($criteria['employee'])) {
                $screenshotCount->andWhere('s.employee = :employee')
                    ->setParameter('employee', $criteria['employee']);
            }

            $screenshotCount = $screenshotCount->getQuery()->getSingleScalarResult();

            return new JsonResponse([
                'success' => true,
                'data' => [
                    'screenshots' => $screenshotCount,
                    'period' => [
                        'start_date' => $startDate,
                        'end_date' => $endDate
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => 'Failed to get file stats: ' . $e->getMessage()
            ], 500);
        }
    }
}
