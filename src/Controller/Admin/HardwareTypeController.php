<?php

namespace App\Controller\Admin;

use App\Entity\HardwareType;
use App\Form\HardwareTypeFormType;
use App\Service\Admin\HardwareTypeService;
use App\Traits\ExceptionLoggerTrait;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Csrf\CsrfToken;
use Symfony\Component\Security\Csrf\CsrfTokenManagerInterface;

#[Route('/admin')]
class HardwareTypeController extends AbstractController
{
    use ExceptionLoggerTrait;

    public function __construct(
        ParameterBagInterface                   $parameterBag,
        private readonly EntityManagerInterface $entityManager,
        private readonly HardwareTypeService    $hardwareTypeService,
    )
    {
        $this->initializeLogger($parameterBag, 'hardware_type_controller');
    }

    #[Route(path: '/hardware-type', name: 'hardware_type', methods: ['GET', 'POST'])]
    public function index(Request $request): Response
    {
        $table = $this->hardwareTypeService->createMasterHardwareTypeTable($request);

        if ($table->isCallback()) {
            return $table->getResponse();
        }

        return $this->render('admin/HardwareType/index.html.twig', [
            'datatable' => $table,
        ]);
    }

    #[Route('/hardware-type/create', name: 'hardware_type_create', methods: ['GET', 'POST'])]
    public function create(Request $request): Response
    {
        $hardwareType = new HardwareType();
        $form = $this->createForm(HardwareTypeFormType::class, $hardwareType);

        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $this->entityManager->persist($hardwareType);
            $this->entityManager->flush();

            $this->addFlash('success', 'Hardware type created successfully.');
            return $this->redirectToRoute('hardware_type');
        }

        return $this->render('admin/HardwareType/new.html.twig', [
            'form' => $form->createView(),
            'mode' => 'create',
        ]);
    }

    #[Route('/hardware-type/edit/{id}', name: 'hardware_type_edit', methods: ['GET', 'POST'])]
    public function edit(Request $request, HardwareType $hardwareType): Response
    {
        $form = $this->createForm(HardwareTypeFormType::class, $hardwareType);

        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $this->entityManager->flush();
            $this->addFlash('success', 'Hardware type updated successfully.');
            return $this->redirectToRoute('hardware_type');
        }

        return $this->render('admin/HardwareType/new.html.twig', [
            'form' => $form->createView(),
            'hardwareType' => $hardwareType,
            'mode' => 'edit',
        ]);
    }

    #[Route('/hardware-type/delete/{id}', name: 'hardware_type_delete', methods: ['POST'])]
    public function delete(
        Request                   $request,
        HardwareType              $hardwareType,
        CsrfTokenManagerInterface $csrfTokenManager
    ): Response
    {
        $token = $request->request->get('_token');

        if (!$csrfTokenManager->isTokenValid(new CsrfToken('delete' . $hardwareType->getId(), $token))) {
            return $this->json([
                'success' => false,
                'message' => 'Invalid CSRF token.'
            ], Response::HTTP_FORBIDDEN);
        }
        $this->entityManager->remove($hardwareType);
        $this->entityManager->flush();

        return $this->json([
            'success' => true,
            'message' => 'Hardware type has been deleted.'
        ], Response::HTTP_OK);
    }
}