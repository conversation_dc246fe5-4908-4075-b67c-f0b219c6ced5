<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Forgot Password{% endblock %}</title>
    <link rel="stylesheet" href="{{ asset('assets/css/bootstrap.min.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/forgot/request.css') }}">
</head>
<body>
<div class="auth-container">
    <div class="auth-box">
        <h2 class="mb-4 text-center font-weight-bold">Forgot Password</h2>
        <img src="{{ asset('images/login-image.png') }}" alt="Network illustration" class="auth-image">

        <form id="resetPasswordForm">
            <div class="mb-3">
                <input type="email" id="input-28" class="form-control" placeholder="Enter your email" required>
                <div id="emailError" class="text-danger" style="display: none;"></div>
            </div>
            <button type="button" id="btnSubmit" class="btn btn-orange w-100 mb-3 reset-btn">
                <i class="bi bi-arrow-repeat me-2"></i>
                Reset Password
            </button>
            <div id="formSuccessMsg" class="alert alert-success" style="display: none;"></div>
        </form>
        <div class="text-center">
        <a href="{{ url('app_login_employee') }}" class="login-link">Back to Login</a>
        </div>
    </div>
</div>

<script src="{{ asset('assets/js/jquery.min.js') }}"></script>
<script src="{{ asset('assets/js/bootstrap.bundle.min.js') }}"></script>
<script>
    $(document).ready(function () {
        $("#input-28").on("blur", function () {
            validateAndCheckEmail($(this).val());
        });

        $("#btnSubmit").click(function () {
            $("#emailError").hide();
            var email = $("#input-28").val();
            var $btn = $(this);
            $btn.prop('disabled', true).text('Processing...');

            validateAndCheckEmail(email, function (isValid) {
                if (isValid) {
                    submitResetRequest(email, function () {
                        $btn.prop('disabled', false).text('Reset Password');
                    });
                } else {
                    $btn.prop('disabled', false).text('Reset Password');
                }
            });
        });

        function validateEmail(email) {
            var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!email) {
                $("#emailError").text("Email is required.");
                $("#emailError").show();
                return false;
            } else if (!emailRegex.test(email)) {
                $("#emailError").text("Please enter a valid email address.");
                $("#emailError").show();
                return false;
            }
            return true;
        }

        function validateAndCheckEmail(email, callback) {
            if (!validateEmail(email)) {
                if (callback) callback(false);
                return;
            }

            $.ajax({
                url: "{{ url('api_check_reset_token') }}",
                type: "POST",
                data: JSON.stringify({ email: email }),
                contentType: "application/json",
                dataType: "json",
                success: function (data) {
                    if (data.hasNonExpiredToken) {
                        $("#emailError").text("A reset email has already been sent. Please wait 1 hour or check your inbox.");
                        $("#emailError").show();
                        if (callback) callback(false);
                    } else {
                        $("#emailError").hide();
                        if (callback) callback(true);
                    }
                },
                error: function (xhr) {
                    $("#emailError").text(xhr.responseJSON?.message || "An error occurred while checking the email.");
                    $("#emailError").show();
                    if (callback) callback(false);
                }
            });
        }

        function submitResetRequest(email, callback) {
            $.ajax({
                url: "{{ url('api_user_forgot_password') }}",
                type: "POST",
                data: JSON.stringify({ email: email }),
                contentType: "application/json",
                dataType: "json",
                success: function (data) {
                    $("#formSuccessMsg").text(data.message);
                    $("#formSuccessMsg").show();
                    setTimeout(function () {
                        window.location.href = '{{ url('app_login_employee') }}';
                    }, 2000);
                    if (callback) callback();
                },
                error: function (xhr) {
                    $("#emailError").text(xhr.responseJSON?.message || "Error submitting request.");
                    $("#emailError").show();
                    if (callback) callback();
                }
            });
        }
    });
</script>
</body>
</html>