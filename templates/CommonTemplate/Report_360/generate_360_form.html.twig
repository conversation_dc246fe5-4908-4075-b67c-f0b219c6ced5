 <div class="feedback-history-container">
        <div class="feedback-history-wrapper">
            <div class="period-selector-card">
                <div class="period-selector-header">
                    <div class="selector-icon">
                        <img src="{{ asset('images/calender.svg') }}" alt="Calendar Icon" width="24" height="24">
                    </div>
                    <span>Employee 360° Report</span>
                </div>
                <div class="period-selector-content">
                    {% if app.flashes('error') %}
                        {% for message in app.flashes('error') %}
                            <div class="empty-state-card">
                                <div class="empty-state-icon">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24"
                                         fill="none" stroke="#fd7e14" stroke-width="1.5" stroke-linecap="round"
                                         stroke-linejoin="round">
                                        <circle cx="12" cy="12" r="10"></circle>
                                        <line x1="12" y1="8" x2="12" y2="12"></line>
                                        <line x1="12" y1="16" x2="12.01" y2="16"></line>
                                    </svg>
                                </div>
                                <p>{{ message }}</p>
                            </div>
                        {% endfor %}
                    {% endif %}

                    <form method="post" data-turbo="false" action="{% if is_granted('ROLE_ADMIN') %}{{ path('admin_generate_360_report_form') }}{% elseif is_granted('ROLE_EMPLOYEE') %}{{ path('generate_360_report_form') }}{% endif %}">
                    <div class="selection-fields">
                            {% if is_granted('ROLE_ADMIN') %}
                                <div class="select-wrapper">
                                    <select name="employeeId" id="employeeId" class="form-select" required>
                                        <option value="">Select Employee</option>
                                        {% for employee in employees %}
                                            <option value="{{ employee.id }}"
                                                    {% if selectedEmployeeId is defined and selectedEmployeeId == employee.id %}selected{% endif %}>
                                                {{ employee.name }}
                                            </option>
                                        {% endfor %}
                                    </select>
                                    <div class="select-arrow">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                             fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                             stroke-linejoin="round">
                                            <polyline points="6 9 12 15 18 9"></polyline>
                                        </svg>
                                    </div>
                                </div>
                            {% endif %}
                            <div class="select-wrapper">
                                <select name="periodType" id="periodType" class="form-select" required>
                                    <option value="year"
                                            {% if selectedPeriodType is defined and selectedPeriodType == 'year' %}selected{% endif %}>
                                        Yearly
                                    </option>
                                    <option value="month"
                                            {% if selectedPeriodType is defined and selectedPeriodType == 'month' %}selected{% endif %}>
                                        Monthly
                                    </option>
                                </select>
                                <div class="select-arrow">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                         fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                         stroke-linejoin="round">
                                        <polyline points="6 9 12 15 18 9"></polyline>
                                    </svg>
                                </div>
                            </div>

                            <div class="select-wrapper">
                                <input type="number"
                                       name="year"
                                       id="year"
                                       class="form-select"
                                       value="{{ selectedYear is defined ? selectedYear : currentYear }}"
                                       min="2000"
                                       max="{{ currentYear }}"
                                       required>
                            </div>

                            <div class="select-wrapper" id="month-group"
                                 style="display: {% if selectedPeriodType is defined and selectedPeriodType == 'month' %}block{% else %}none{% endif %};">
                                <select name="month" id="month" class="form-select">
                                    <option value="">Select Month</option>
                                    {% for i in 1..12 %}
                                        <option value="{{ i }}"
                                                {% if selectedMonth is defined and selectedMonth == i %}selected{% endif %}>
                                            {{ ('2025-' ~ i ~ '-01')|date('F') }}
                                        </option>
                                    {% endfor %}
                                </select>
                                <div class="select-arrow">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                         fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                         stroke-linejoin="round">
                                        <polyline points="6 9 12 15 18 9"></polyline>
                                    </svg>
                                </div>
                            </div>
                            <button type="submit" class="btn btn-primary justify-content-end">Generate Report</button>
                        </div>
                    </form>
                </div>
            </div>
    {# Render report below form if reportData is available #}
    {% if reportData %}
            <div class="period-selector-card">
                <div class="period-selector-header">
                    <div class="selector-icon">
                        <img src="{{ asset('images/file.svg') }}" alt="Report Icon" width="24" height="24">
                    </div>
                    <span>Performance Metrics - {{ reportData.employee_name }} ({{ reportData.report_period }})</span>
                </div>
                <div class="period-selector-content">
                    <div class="timeline">
                        <div class="timeline-period">
                            {#                                <div class="timeline-period-header"> #}
                            {#                                    <h3></h3> #}
                            {#                                </div> #}
                            <div class="timeline-events">
                                <div class="feedback-card">
                                    <div class="feedback-card-header">
                                        <div class="feedback-icon">
                                            <img src="{{ asset('images/file.svg') }}" alt="Evaluation Icon" width="24"
                                                 height="24">
                                        </div>
                                        <div class="feedback-title">
                                            <h4>Performance Evaluation</h4>
                                        </div>
                                    </div>

                                    <div class="feedback-stats">
                                        <div class="stat-item">
                                            <div>
                                                <span class="stat-label">Task Manager</span>
                                                <span class="stat-value">{{ reportData.task_score.score|number_format(2) }}%</span>
                                            </div>
                                            <div class="stat-bar">
                                                <div class="stat-progress"
                                                     style="width: {{ reportData.task_score.score }}%"></div>
                                            </div>
                                        </div>
                                        <div class="stat-item">
                                            <div>
                                                <span class="stat-label">Team Logger</span>
                                                <span class="stat-value">{{ reportData.team_logger_score.score|number_format(2) }}%</span>
                                            </div>
                                            <div class="stat-bar">
                                                <div class="stat-progress"
                                                     style="width: {{ reportData.team_logger_score.score }}%"></div>
                                            </div>
                                        </div>
                                        <div class="stat-item">
                                            <div>
                                                <span class="stat-label">Performance Feedback</span>
                                                <span class="stat-value">{{ reportData.performance_feedback_score.score|number_format(2) }}%</span>
                                            </div>
                                            <div class="stat-bar">
                                                <div class="stat-progress"
                                                     style="width: {{ reportData.performance_feedback_score.score }}%"></div>
                                            </div>
                                        </div>
                                        <div class="stat-item">
                                            <div>
                                                <span class="stat-label">Bug Tracker</span>
                                                <span class="stat-value">{{ reportData.bug_tracker_score.score|number_format(2) }}%</span>
                                            </div>
                                            <div class="stat-bar">
                                                <div class="stat-progress"
                                                     style="width: {{ reportData.bug_tracker_score.score }}%"></div>
                                            </div>
                                        </div>
                                        <div class="stat-item">
                                            <div>
                                                <span class="stat-label">Total Score</span>
                                                <span class="stat-value">{{ reportData.final_score|number_format(2) }}%</span>
                                            </div>
                                            <div class="stat-bar">
                                                <div class="stat-progress"
                                                     style="width: {{ reportData.final_score }}%"></div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="feedback-sections">
                                        <div class="feedback-section">
                                            <div class="section-header">
                                                <div class="section-icon">
                                                    <img src="{{ asset('images/file.svg') }}" alt="Module Icon"
                                                         width="18" height="18">
                                                </div>
                                                <h5>Module Breakdown</h5>
                                            </div>
                                            <div class="section-responses">
                                                <div class="response-item">
                                                    <div class="response-question">Task Manager (50%)</div>
                                                    <div class="response-answer">
                                                        <div class="text-response">
                                                            <strong>Criteria:</strong> Percentage of tasks completed on
                                                            time.<br>
                                                            <strong>Performance:</strong> {{ reportData.task_score.details }}
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="response-item">
                                                    <div class="response-question">Team Logger (20%)</div>
                                                    <div class="response-answer">
                                                        <div class="text-response">
                                                            <strong>Criteria:</strong> Active time based on logged
                                                            hours.<br>
                                                            <strong>Performance:</strong> {{ reportData.team_logger_score.details }}
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="response-item">
                                                    <div class="response-question">Performance Feedback (20%)</div>
                                                    <div class="response-answer">
                                                        <div class="text-response">
                                                            <strong>Criteria:</strong> Average rating from performance
                                                            feedback.<br>
                                                            <strong>Performance:</strong> {{ reportData.performance_feedback_score.details }}
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="response-item">
                                                    <div class="response-question">Bug Tracker (10%)</div>
                                                    <div class="response-answer">
                                                        <div class="text-response">
                                                            <strong>Criteria:</strong> Bug reopen rate (lower is
                                                            better).<br>
                                                            <strong>Performance:</strong> {{ reportData.bug_tracker_score.details }}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="feedback-section">
                                            <div class="section-header">
                                                <div class="section-icon">
                                                    <img src="{{ asset('images/clock.svg') }}" alt="Extra Hours Icon" width="18" height="18">
                                                </div>
                                                <h5>Weekend Hours</h5>
                                            </div>
                                            <div class="section-responses">
                                                <div class="response-item">
                                                    <div class="response-answer">
                                                        <div class="text-response">
                                                            {{ reportData.Extra_Hours }} hours
                                                            <small class="text-muted">(Logged on Saturdays/Sundays)</small>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="feedback-section">
                                            <div class="section-header">
                                                <div class="section-icon">
                                                    <img src="{{ asset('images/comment.svg') }}" alt="Comment Icon"
                                                         width="18" height="18">
                                                </div>
                                                <h5>Note</h5>
                                            </div>
                                            <div class="section-responses">
                                                <div class="response-item">
                                                    <div class="response-answer">
                                                        <div class="text-response">
                                                            {{ reportData.comments }}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="feedback-section">
                                            <div class="section-header">
                                                <div class="section-icon">
                                                    <img src="{{ asset('images/star.svg') }}" alt="Calendar Icon"
                                                         width="18" height="18">
                                                </div>
                                                <h5>Overall Performance Rating</h5>
                                            </div>
                                            <div class="section-responses">
                                                <div class="response-item">
                                                    <div class="response-answer">
                                                        <div class="text-response">
                                                                <span id="overall-rating"
                                                                      aria-describedby="rating-note">
                                                                    {% if reportData.final_score >= 90 %}
                                                                        <span class="badge badge-primary">Excellent ({{ reportData.final_score }}%)</span>
                                                                    {% elseif reportData.final_score >= 80 %}
                                                                        <span class="badge badge-primary">Good ({{ reportData.final_score }}%)</span>
                                                                    {% elseif reportData.final_score >= 70 %}
                                                                        <span class="badge badge-primary">Satisfactory ({{ reportData.final_score }}%)</span>
                                                                    {% else %}
                                                                        <span class="badge badge-danger">Needs Improvement ({{ reportData.final_score }}%)</span>
                                                                    {% endif %}
                                                                </span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="rating-note" id="rating-note">
                                                        <span>Rating Scale:
                                                            <span class="badge badge-primary">90–100% → Excellent</span>
                                                            <span class="badge badge-primary">80–89% → Good</span>
                                                            <span class="badge badge-primary">70–79% → Satisfactory</span>
                                                            <span class="badge badge-danger">Below 70% → Needs Improvement</span>
                                                        </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
    <div class="clearfix"></div>
    <link rel="stylesheet" href="{{ asset('assets/css/Form/performance-report.css') }}">
    <script>
        document.getElementById('periodType').addEventListener('change', function () {
            const monthGroup = document.getElementById('month-group');
            const monthSelect = document.getElementById('month');

            if (this.value === 'month') {
                monthGroup.style.display = 'block';
                monthSelect.setAttribute('required', 'required');
            } else {
                monthGroup.style.display = 'none';
                monthSelect.removeAttribute('required');
                monthSelect.value = '';
            }
        });

        document.addEventListener('DOMContentLoaded', function () {
            const periodType = document.getElementById('periodType').value;
            if (periodType === 'month') {
                document.getElementById('month').setAttribute('required', 'required');
            }
        });
    </script>