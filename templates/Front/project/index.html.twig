{% extends 'Front/dashboard/base.html.twig' %}

{% block title %}Documents{% endblock %}

{% block body %}
    {% include 'Front/partials/breadcrumbs.html.twig' with {
        breadcrumbs: [
            { 'label': 'Dashboard', 'url': url('dashboard_employee') },
        ],
        active: 'Projects'
    } %}

    <div class="content">
        <div class="animated fadeIn">
            <div class="row">
                <div id="flash-container"></div>
                <div id="order-loader">
                    <img src="{{ asset('images/loader.svg') }}" alt="loader">
                </div>
                <div class="col-md-12">
                    <div id="flash-container"></div>
                    <div class="card">
                        <div class="card-header d-flex justify-content-between">
                            <strong class="card-title">Projects</strong>
                            <div class="custom-control custom-switch-wrapper d-flex align-items-center">
                                <div class="custom-switch">
                                    <input
                                            type="checkbox"
                                            class="custom-switch-input"
                                            id="allowSelfTaskToggle"
                                            name="allowSelfTaskToggle"
                                            {% if employee.isAllowSelfTask %}checked{% endif %}>
                                    <label class="custom-switch-label" for="allowSelfTaskToggle"></label>
                                </div>
                                <div class="ml-3">
                                    <label class="form-label font-weight-bold" for="allowSelfTaskToggle">Allow Self Task Assignment</label>
                                </div>
                            </div>

                        </div>
                        <div class="card-body">
                            <div class="table-stats order-table ov-h text-center">
                                <div id="project" class="datatable-main-wrapper">Loading...</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="clearfix"></div>
    <link rel="stylesheet" href="{{ asset('assets/css/Form/notification.css') }}">
    <script src="{{ asset('assets/js/datatables.min.js') }}"></script>
    <script src="{{ asset('bundles/datatables/js/datatables.js') }}"></script>
    <script>
        $(function () {
            recordsTable = $('#project').initDataTables({{ datatable_settings(datatable) }});

            $('#allowSelfTaskToggle').on('change', function() {
                const isChecked = $(this).is(':checked');
                $.ajax({
                    url: '{{ path('employee_toggle_self_task') }}',
                    method: 'POST',
                    data: {
                        allowSelfTask: isChecked ? 1 : 0
                    },
                    success: function(response) {
                        if (response.success) {
                            showFlashMessage('success', response.message);
                        } else {
                            $('#allowSelfTaskToggle').prop('checked', !isChecked);
                            showFlashMessage('error', response.message || 'Failed to update setting');
                        }
                    },
                    error: function() {
                        $('#allowSelfTaskToggle').prop('checked', !isChecked);
                        showFlashMessage('error', 'An error occurred while updating the setting');
                    }
                });
            });

            function showFlashMessage(type, message) {
                if (typeof window.FlashMessage === 'undefined') {
                    alert(message);
                    return;
                }
                window.FlashMessage[type](message, {
                    progress: true,
                    timeout: 2000,
                    container: '.flash-container',
                    theme: 'default'
                });
            }
        });
    </script>
    <script src="{{ asset('assets/js/datatableclearsearch.js') }}"></script>
{% endblock %}