<?php
namespace App\MessageHandler;

use App\Entity\LeaveRequest;
use App\Entity\MasterEmployee;
use App\Entity\User;
use App\Message\LeaveRequestStatusNotificationMessage;
use App\Service\Common\EmailService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Mailer\Exception\TransportExceptionInterface;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use App\Traits\ExceptionLoggerTrait;

#[AsMessageHandler]
class LeaveRequestStatusNotificationHandler
{
    use ExceptionLoggerTrait;

    public function __construct(
        private readonly ParameterBagInterface $parameterBag,
        private readonly EntityManagerInterface $entityManager,
        private readonly EmailService $emailService,
    ) {
        $this->initializeLogger($parameterBag, 'leave_status_notifications_handler');
    }

    public function __invoke(LeaveRequestStatusNotificationMessage $message): void
    {
        $leaveRequest = $this->entityManager->getRepository(LeaveRequest::class)
            ->find($message->getLeaveRequestId());
        if (!$leaveRequest) {
            $this->log('Leave request not found', ['leave_request_id' => $message->getLeaveRequestId()]);
            return;
        }

        $employee = $leaveRequest->getEmployee();
        if (!$employee) {
            $this->log('Employee not found for leave request', ['leave_request_id' => $message->getLeaveRequestId()]);
            return;
        }
        $approver = $leaveRequest->getAdminApprovedBy() ??
            $leaveRequest->getHrApprovedBy() ??
            $leaveRequest->getTeamLeadApprovedBy();

        $approverName = 'System';
        if ($approver instanceof User) {
            $approverName = $approver->getUsername();
        } elseif ($approver instanceof MasterEmployee) {
            $approverName = $approver->getName();
        }
        $approvedOn = 'N/A';
        if ($message->getStatus() === LeaveRequest::STATUS_APPROVED || $message->getStatus() === LeaveRequest::STATUS_REJECTED) {
            $approvedOn = ($leaveRequest->getAdminApprovedOn() ??
                $leaveRequest->getHrApprovedOn() ??
                $leaveRequest->getTeamLeadApprovedOn())->format('d-m-Y');
        }

        $placeholders = [
            '{{employee_name}}' => $employee->getName(),
            '{{start_date}}' => $leaveRequest->getStartDate()->format('d-m-Y'),
            '{{end_date}}' => $leaveRequest->getEndDate()->format('d-m-Y'),
            '{{days_requested}}' => $leaveRequest->getDaysRequested(),
            '{{leave_type}}' => $leaveRequest->getLeaveType()->getLeaveType()->getName(),
            '{{status}}' => $message->getStatus(),
            '{{approver_name}}' => $approverName,
            '{{approved_on}}' => $approvedOn,
            '{{app_base_url}}' => rtrim($this->parameterBag->get('app_base_url'), '/'),
        ];

        $templateIdentifier = $message->getStatus() === LeaveRequest::STATUS_APPROVED
            ? 'leave_request_approved'
            : 'leave_request_rejected';
        $emailContent = $this->emailService->generateEmailContent($templateIdentifier, $placeholders);

        if ($employee->getEmail()) {
            try {
                $this->emailService->sendEmail(
                    $employee->getEmail(),
                    "Your Leave Request has been {$message->getStatus()}",
                    $emailContent
                );
            } catch (\Exception $e) {
                $this->log('Failed to send email', [
                    'recipient' => $employee->getEmail(),
                    'error' => $e->getMessage()
                ]);
            } catch (TransportExceptionInterface $e) {
                $this->log('Failed to send email', [
                    'recipient' => $employee->getEmail(),
                    'error' => $e->getMessage()
                ]);
            }
        }
    }
}