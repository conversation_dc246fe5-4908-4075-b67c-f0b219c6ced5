<?php
namespace App\Twig;

use App\Entity\EmployeeDocument;
use App\Entity\MasterEmployee;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\SecurityBundle\Security;
use Twig\Extension\AbstractExtension;
use Twig\Extension\GlobalsInterface;

class GlobalEmployeeExtension extends AbstractExtension implements GlobalsInterface
{
    private EntityManagerInterface $entityManager;
    private Security $security;

    public function __construct(EntityManagerInterface $entityManager, Security $security)
    {
        $this->entityManager = $entityManager;
        $this->security = $security;
    }

    public function getGlobals(): array
    {
        $user = $this->security->getUser();

        $profilePicture = null;
        if ($user instanceof MasterEmployee) {
            $profilePicture = $this->entityManager->getRepository(EmployeeDocument::class)
                ->findOneBy([
                    'masterEmployee' => $user,
                    'documentType' => 'PROFILE_PICTURE',
                ]);
        }

        return [
            'profilePicture' => $profilePicture,
        ];
    }
}
