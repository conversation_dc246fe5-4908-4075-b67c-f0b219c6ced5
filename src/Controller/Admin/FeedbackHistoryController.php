<?php

namespace App\Controller\Admin;

use App\Service\Common\FeedbackHistoryService;
use App\Traits\ExceptionLoggerTrait;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Symfony\Component\Routing\Annotation\Route;

#[Route('/admin')]
class FeedbackHistoryController extends AbstractController
{
    use ExceptionLoggerTrait;

    #[Route('/feedback-history', name: 'admin_employee_history', methods: ['GET', 'POST'])]
    public function showHistory(Request $request, FeedbackHistoryService $feedbackService): Response
    {
        return $this->handleFeedbackHistory(
            $request,
            $feedbackService,
            'getGroupedSubmissions',
            'admin/FeedBackHistory/FeedbackHistory.html.twig'
        );
    }

    #[Route('/feedback-history-self-evaluation', name: 'admin_employee_history_self_evaluation', methods: ['GET', 'POST'])]
    public function showEvaluationFormHistory(Request $request, FeedbackHistoryService $feedbackService): Response
    {
        return $this->handleFeedbackHistory(
            $request,
            $feedbackService,
            'getGroupedSelfEvaluationSubmissions',
            'admin/FeedBackHistory/selfEvaluationHistory.html.twig'
        );
    }

    private function handleFeedbackHistory(
        Request $request,
        FeedbackHistoryService $feedbackService,
        string $submissionMethod,
        string $template
    ): Response {
        $employees = $feedbackService->getAllEmployees();
        $selectedEmployee = null;
        $selectedDate = date('Y-m');
        $groupedSubmissions = [];

        if ($request->isMethod('POST')) {
            $employeeId = $request->request->get('employee');
            $selectedDate = $request->request->get('date') ?: date('Y-m');

            if ($employeeId) {
                $selectedEmployee = $feedbackService->getSelectedEmployee($employeeId);
                if (!in_array($selectedEmployee, $employees, true)) {
                    throw new AccessDeniedHttpException('You are not authorized to view this employee\'s feedback.');
                }
                $groupedSubmissions = $feedbackService->$submissionMethod($selectedEmployee, $selectedDate);
            }else {
                $this->addFlash('warning', 'Please select an employee to view history.');
            }
        }

        $availableDates = $feedbackService->getAvailableDates();

        return $this->render($template, [
            'groupedSubmissions' => $groupedSubmissions,
            'availableDates' => $availableDates,
            'selectedDate' => $selectedDate,
            'employees' => $employees,
            'selectedEmployee' => $selectedEmployee
        ]);
    }
}