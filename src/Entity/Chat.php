<?php

namespace App\Entity;

use DateTimeZone;
use Doctrine\ORM\Mapping as ORM;
use App\Service\TimezoneService;

#[ORM\Entity]
class Chat
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    /** @phpstan-ignore-next-line */
    private ?int $id = null;

    #[ORM\ManyToOne(targetEntity: EmployeeNotification::class, inversedBy: 'chats')]
    #[ORM\JoinColumn(nullable: false)]
    private ?EmployeeNotification $employeeNotification = null;

    #[ORM\Column(type: 'text')]
    private string $message;

    #[ORM\Column(type: 'datetime')]
    private \DateTimeInterface $createdAt;

    #[ORM\Column(type: 'datetime', nullable: true)]
    private ?\DateTimeInterface $readAt = null;

    #[ORM\Column(type: 'boolean')]
    private bool $isFromEmployee;

    #[ORM\Column(type: 'boolean', options: ['default' => false])]
    private bool $isDisapprovalReason = false;

    #[ORM\Column(type: 'boolean', options: ['default' => false])]
    private bool $isSystemMessage = false;

    public function __construct(
        string $message,
        bool $isFromEmployee,
        ?EmployeeNotification $employeeNotification,
        bool $isDisapprovalReason = false,
        bool $isSystemMessage = false
    ) {
        $this->message = $message;
        $this->isFromEmployee = $isFromEmployee;
        $timezone = new DateTimeZone('Asia/Kolkata');
        $this->createdAt = new \DateTime('now', $timezone);
        $this->employeeNotification = $employeeNotification;
        $this->isDisapprovalReason = $isDisapprovalReason;
        $this->isSystemMessage = $isSystemMessage;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getMessage(): string
    {
        return $this->message;
    }

    public function setMessage(string $message): self
    {
        $this->message = $message;
        return $this;
    }

    public function getCreatedAt(): \DateTimeInterface
    {
        return $this->createdAt;
    }

    public function getReadAt(): ?\DateTimeInterface
    {   
        return $this->readAt;
    }

    public function setReadAt(?\DateTimeInterface $readAt): self
    {
        $this->readAt = $readAt;
        return $this;
    }

    public function isFromEmployee(): bool
    {
        return $this->isFromEmployee;
    }

    public function isFromAdmin(): bool
    {
        return !$this->isFromEmployee;
    }

    public function getEmployeeNotification(): ?EmployeeNotification
    {
        return $this->employeeNotification;
    }

    public function setEmployeeNotification(?EmployeeNotification $employeeNotification): self
    {
        $this->employeeNotification = $employeeNotification;
        return $this;
    }

    public function isDisapprovalReason(): bool
    {
        return $this->isDisapprovalReason;
    }

    public function setIsDisapprovalReason(bool $isDisapprovalReason): self
    {
        $this->isDisapprovalReason = $isDisapprovalReason;
        return $this;
    }

    public function isSystemMessage(): bool
    {
        return $this->isSystemMessage;
    }

    public function setIsSystemMessage(bool $isSystemMessage): self
    {
        $this->isSystemMessage = $isSystemMessage;
        return $this;
    }

    public function isUnread(): bool
    {
        return $this->readAt === null;
    }

    public function markAsRead(): self
    {
        if ($this->readAt === null) {
            $timezone = new DateTimeZone('Asia/Kolkata');
            $this->readAt = new \DateTime('now', $timezone);
        }
        return $this;
    }
}