<?php
namespace App\Controller\Admin;

use App\Service\Admin\EmailConfigurationService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Csrf\CsrfTokenManagerInterface;

#[Route('/admin')]
class EmailController extends AbstractController
{
    public function __construct(
        private readonly EmailConfigurationService $emailConfigurationService,
        private readonly CsrfTokenManagerInterface $csrfTokenManager
    ) {
    }

    #[Route('/email-config-page', name: 'email_config_page', methods: ['GET'])]
    public function emailConfigPage(): Response
    {
        $settings = $this->emailConfigurationService->getEmailSettings();

        return $this->render('admin/email_config.html.twig', [
            'settings' => $settings,
        ]);
    }

    /**
     * @throws \JsonException
     */
    #[Route('/email-config/update', name: 'email_config_update', methods: ['POST'])]
    public function update(Request $request): JsonResponse
    {
        $token = $request->headers->get('X-CSRF-Token');
        if (!$this->csrfTokenManager->isTokenValid(new \Symfony\Component\Security\Csrf\CsrfToken('setting_update', $token))) {
            return new JsonResponse(['message' => 'Invalid CSRF token'], Response::HTTP_FORBIDDEN);
        }

        $data = json_decode($request->getContent(), true, 512, JSON_THROW_ON_ERROR);

        if (!$data) {
            return new JsonResponse(['message' => 'Invalid data'], Response::HTTP_BAD_REQUEST);
        }

        try {
            $requiredFields = ['Username', 'Password', 'Host', 'Port'];
            foreach ($requiredFields as $field) {
                if (empty($data[$field])) {
                    return new JsonResponse(['message' => $field . ' is required'], Response::HTTP_BAD_REQUEST);
                }
            }

            $dsn = sprintf(
                'smtp://%s:%s@%s:%s',
                $data['Username'],
                $data['Password'],
                $data['Host'],
                $data['Port']
            );

            $this->emailConfigurationService->updateConfiguration('MAILER_DSN', $dsn);

            if (!empty($data['Sender_Address'])) {
                $this->emailConfigurationService->updateConfiguration('MAILER_FROM_ADDRESS', $data['Sender_Address']);
            }

            return new JsonResponse(['message' => 'Email settings updated successfully']);
        } catch (\Exception $e) {
            return new JsonResponse(['message' => 'Error updating settings: ' . $e->getMessage()], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}