<?php

namespace App\Command;

use App\Service\Admin\EmployeeHoursService;
use App\Traits\ExceptionLoggerTrait;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\DecodingExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;

class SyncEmployeeHoursCommand extends Command
{
    use ExceptionLoggerTrait;

    protected static $defaultName = 'app:fetch-daily-hours';

    public function __construct(private readonly  EmployeeHoursService $employeeHoursService, ParameterBagInterface $parameterBag)
    {
        parent::__construct(self::$defaultName);
        $this->initializeLogger($parameterBag, 'employee_hours_command');
    }

    protected function configure(): void
    {
        $this
            ->setDescription('Fetch daily hours information for employees and save it into the database.')
            ->setHelp('This command retrieves employee daily hours from the Teamlogger API and stores them in the database.');
        $this->addOption(
            'date',
            'd',
            InputOption::VALUE_OPTIONAL,
            'Enter Date'
        );
    }

    /**
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     * @throws ClientExceptionInterface
     * @throws DecodingExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $io->title('Fetching Employee Daily Hours');

        try {
            $dateOption = $input->getOption('date') ?? 'today';
            $today = new \DateTime($dateOption);

            $result = $this->employeeHoursService->fetchAndStoreDailyHours($today);

            $io->success(sprintf(
                'Processing complete. Success: %d, Failures: %d.',
                $result['success'],
                $result['failures']
            ));

            return Command::SUCCESS;

        } catch (\Exception $e) {
            $this->log(
                'Error fetching and syncing employees hours.',
                [
                    'error_message' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                    'trace' => $e->getTraceAsString(),
                ]
            );
            $io->error(sprintf('Failed to fetch or process data. Error: %s', $e->getMessage()));
            return Command::FAILURE;
        }
    }
}