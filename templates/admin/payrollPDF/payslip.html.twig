<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Payslip</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 13px;
            color: #000;
            line-height: 1.4;
        }
        .payslip-container {
            max-width: 800px;
            margin: 0 auto;
            border: 2px solid #f78739;
            padding: 25px;
        }
        .header {
            text-align: center;
            font-weight: bold;
            font-size: 18px;
            color: #f78739;
            margin-bottom: 5px;
        }
        .sub-header {
            text-align: center;
            font-size: 13px;
            margin-bottom: 15px;
        }
        .center {
            text-align: center;
        }
        .section-title {
            font-weight: bold;
            margin-top: 15px;
            margin-bottom: 5px;
            color: #f78739;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
        }
        th, td {
            border: 1px solid #ccc;
            padding: 10px 12px;
            text-align: left;
        }
        th {
            background-color: #f78739;
            color: white;
        }
        .right {
            text-align: right;
        }
        .footer {
            font-size: 10px;
            text-align: center;
            margin-top: 20px;
            color: #888;
        }
    </style>
</head>
<body>
<div class="payslip-container">
    <div class="center" style="margin-bottom: 10px;">
        <img src="{{ absolute_url(asset('images/Brainstream-logo-TM.jpg')) }}" alt="Company Logo" style="height: 60px;">
    </div>
    <div class="sub-header">606, Time Square Arcade, Opp. Bagban party plot, Thaltej Shilaj Road, Ahmedabad 380059, India.</div>
    <div class="center" style="font-weight:bold; margin-bottom:10px;">Salary Slip For The Month of {{ data.monthName }} {{ data.year }}</div>

    <table>
        <tr>
            <td><strong>Empl. No:</strong> {{ data.employeeNumber }}</td>
            <td><strong>P.F. No:</strong> {{ data.pfNumber }}</td>
            <td><strong>Emp. Name:</strong> {{ data.employeeName }}</td>
            <td><strong>ESI No:</strong> {{ data.esiNumber }}</td>
        </tr>
        <tr>
            <td><strong>Bank:</strong> {{ data.bankName }}</td>
            <td><strong>Department:</strong> {{ data.department }}</td>
            <td><strong>A/C No:</strong> {{ data.accountNumber }}</td>
            <td><strong>UAN:</strong> {{ data.uan }}</td>
        </tr>
    </table>

    <table>
        <thead>
        <tr>
            <th colspan="2" class="center">Working Details</th>
            <th colspan="2" class="center">Earnings</th>
            <th colspan="2" class="center">Deductions</th>
        </tr>
        </thead>
        <tbody>
        <tr>
            <td><strong>WD</strong></td><td>{{ data.workingDetails.WD }}</td>
            <td><strong>BASIC</strong></td><td>{{ data.earnings.BASIC }}</td>
            <td><strong>PF</strong></td><td>{{ data.deductions.PF }}</td>
        </tr>
        <tr>
            <td><strong>PD</strong></td><td>{{ data.workingDetails.PD }}</td>
            <td><strong>HRA</strong></td><td>{{ data.earnings.HRA }}</td>
            <td><strong>ESI</strong></td><td>{{ data.deductions.ESI }}</td>
        </tr>
        <tr>
            <td><strong>UL</strong></td><td>{{ data.workingDetails.UL }}</td>
            <td><strong>CONV</strong></td><td>{{ data.earnings.CONV }}</td>
            <td><strong>TDS</strong></td><td>{{ data.deductions.TDS }}</td>
        </tr>
        <tr>
            <td><strong>TOTAL</strong></td><td>{{ data.workingDetails.TOTAL }}</td>
            <td><strong>SPECIAL</strong></td><td>{{ data.earnings.SPECIAL }}</td>
            <td><strong>OTH</strong></td><td>{{ data.deductions.OTH }}</td>
        </tr>
        <tr>
            <td colspan="2"></td>
            <td><strong>BONUS</strong></td><td>{{ data.earnings.BONUS }}</td>
            <td><strong>LEAVE</strong></td><td>{{ data.deductions.LEAVE }}</td>
        </tr>
        <tr>
            <td colspan="2"></td>
            <td><strong>MEDICAL</strong></td><td>{{ data.earnings.Medical }}</td>
            <td colspan="2"></td>
        </tr>
        <tr>
            <td colspan="2"></td>
            <td><strong>TELEPHONE</strong></td><td>{{ data.earnings.Telephone }}</td>
            <td colspan="2"></td>
        </tr>
        </tbody>
        <tfoot>
        <tr class="totals">
            <td colspan="4" class="right">Gross Income</td>
            <td colspan="2">{{ data.grossIncome }}</td>
        </tr>
        <tr class="totals">
            <td colspan="4" class="right">Gross Deduction</td>
            <td colspan="2">{{ data.grossDeduction }}</td>
        </tr>
        <tr class="totals">
            <td colspan="4" class="right">Net Salary</td>
            <td colspan="2">{{ data.salaryAmount }}</td>
        </tr>
        <tr class="totals">
            <td colspan="4" class="right">Payable Amount</td>
            <td colspan="2">{{ data.payableAmount }}</td>
        </tr>
        </tfoot>
    </table>

    <div class="footer">
        This is a computer-generated document and does not require a signature.
    </div>
</div>
</body>
</html>