<?php

namespace App\Service\Admin;

use App\Entity\ExtraPay;
use App\Entity\SalaryStructure;
use App\Helper\ModalboxHelper;
use App\Helper\SettingHelper;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\QueryBuilder;
use Omines\DataTablesBundle\Adapter\Doctrine\ORMAdapter;
use Omines\DataTablesBundle\Column\NumberColumn;
use Omines\DataTablesBundle\Column\TextColumn;
use Omines\DataTablesBundle\DataTable;
use Omines\DataTablesBundle\DataTableFactory;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Request;
use App\Traits\ExceptionLoggerTrait;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Security\Csrf\CsrfTokenManagerInterface;

class ExtraPayService
{
    use ExceptionLoggerTrait;

    public function __construct(
        private readonly DataTableFactory $dataTableFactory,
        private readonly ParameterBagInterface $parameterBag,
        private readonly RouterInterface $router,
        private readonly CsrfTokenManagerInterface $csrfTokenManager,
        private readonly ModalboxHelper $modalboxHelper,
        private readonly Security $security,
        private readonly AttendenceTableService $attendenceTableService,
        private readonly SettingHelper $settingHelper,
        private readonly EntityManagerInterface $entityManager,
    ) {
        $this->initializeLogger($parameterBag, 'extra_pay_service');
    }

    public function createExtraPayTable(Request $request): DataTable
    {
        $dataTable = $this->dataTableFactory->create();

        foreach ($this->getColumns() as $key => $column) {
            $dataTable->add($key, $column['className'], array_filter($column, fn($k) => $k !== 'className', ARRAY_FILTER_USE_KEY));
        }

        return $dataTable->createAdapter(ORMAdapter::class, [
            'entity' => ExtraPay::class,
            'query' => fn(QueryBuilder $qb) => $this->buildQuery($qb),
        ])
            ->addOrderBy('year', 'desc')
            ->addOrderBy('month', 'desc')
            ->handleRequest($request);
    }

    private function getColumns(): array
    {
        return [
            'employee' => [
                'label' => 'Employee',
                'field' => 'employee.name',
                'className' => TextColumn::class,
            ],
            'month' => [
                'label' => 'Month',
                'className' => TextColumn::class,
                'render' => fn($value, $context) => self::MONTH_MAP[$context->getMonth()] ?? '',
            ],
            'year' => [
                'label' => 'Year',
                'field' => 'e.year',
                'className' => NumberColumn::class,
            ],
            'hours' => [
                'label' => 'Hours',
                'field' => 'e.hours',
                'render' => fn($value) => $value ?: '-',
                'className' => NumberColumn::class,
            ],
            'totalPay' => [
                'label' => 'Total Pay',
                'field' => 'e.totalPay',
                'className' => NumberColumn::class,
                'render' => fn($value) => $value ?: '-',
            ],
            'bonus' => [
                'label' => 'Bonus',
                'field' => 'e.bonus',
                'className' => NumberColumn::class,
                'render' => fn($value) => $value ?: '-',
            ],
            'officeExpense' => [
                'label' => 'Office Expense',
                'field' => 'e.officeExpense',
                'className' => NumberColumn::class,
                'render' => fn($value) => $value ?: '-',
            ],
            'id' => [
                'label' => 'Actions',
                'field' => 'e.id',
                'className' => NumberColumn::class,
                'orderable' => false,
                'render' => fn($value, $context) => $this->renderActionColumn($context),
            ],
        ];
    }

    private const MONTH_MAP = [
        1 => 'January',
        2 => 'February',
        3 => 'March',
        4 => 'April',
        5 => 'May',
        6 => 'June',
        7 => 'July',
        8 => 'August',
        9 => 'September',
        10 => 'October',
        11 => 'November',
        12 => 'December',
    ];

    private function renderActionColumn($context): string
    {
        $extraPay = $context;
        $entityId = $extraPay->getId();
        $editUrl = $this->router->generate('admin_extrapay_edit', ['id' => $entityId]);
        $deleteUrl = $this->router->generate('admin_extrapay_delete', ['id' => $entityId]);
        $csrfToken = $this->csrfTokenManager->getToken('delete_extrapay_' . $entityId)->getValue();

        $modalId = 'deleteModal_' . $entityId;
        $modalBody = 'Are you sure you want to delete this extra pay record?<br><br>' .
            '<strong>Warning:</strong> This action cannot be undone.';

        $footerButtons = sprintf(
            '<button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>' .
            '<button type="button" class="btn btn-primary delete-extrapay-btn" data-delete="true" data-url="%s" data-token="%s">Delete</button>',
            $deleteUrl,
            $csrfToken
        );

        $deleteModal = $this->modalboxHelper->renderModal(
            $modalId,
            'Confirm Extra Pay Deletion',
            $modalBody,
            $footerButtons
        );

        return sprintf(
            '<div class="d-inline-flex gap-2">
                <a href="%s" class="bs-btn btn btn-primary btn-sm bs-m" title="Edit">
                    <i class="bs-fa fa fa-edit"></i>
                </a>
                <button type="button" class="bs-btn btn btn-primary btn-sm bs-m open-delete-extrapay-modal" data-toggle="modal" data-target="#%s" title="Delete">
                    <i class="bs-fa fa fa-trash"></i>
                </button>
            </div>%s',
            $editUrl,
            $modalId,
            $deleteModal
        );
    }

    private function buildQuery(QueryBuilder $qb): void
    {
        $qb->select('e')
            ->from(ExtraPay::class, 'e')
            ->leftJoin('e.employee', 'employee');
    }

    /**
     * @throws \DateMalformedStringException
     */
    public function calculateOtAndTotalPay(
        int $employeeId,
        int $year,
        int $month,
        string $hoursInput,
        float $bonus = 0,
        float $officeExpense = 0
    ): array {
        $hours = $this->convertTimeToDecimal($hoursInput);
        $salaryStructure = $this->entityManager->getRepository(SalaryStructure::class)
            ->findValidCtcForEmployee($employeeId, new \DateTime("$year-$month-01"));
        $salary = $salaryStructure ? (float)$salaryStructure->getCtc() : 0;
        $attendenceService = $this->attendenceTableService;
        $totalWorkingDays = $attendenceService->getTotalWorkingDays($year, $month);
        $settings = $this->settingHelper->getSettingConfiguration();
        $totalWorkingHours = isset($settings['hours']) ? (float)$settings['hours'] : 0;
        $salaryPerDay = $totalWorkingDays > 0 ? $salary / $totalWorkingDays : 0;
        $salaryPerHour = $totalWorkingHours > 0 ? $salaryPerDay / $totalWorkingHours : 0;
        $otPay = $salaryPerHour * $hours;
        $totalPay = $bonus + $officeExpense + $otPay;

        return [
            'salary' => $salary,
            'totalWorkingDays' => $totalWorkingDays,
            'totalWorkingHours' => $totalWorkingHours,
            'salaryPerDay' => $salaryPerDay,
            'salaryPerHour' => $salaryPerHour,
            'otPay' => $otPay,
            'totalPay' => $totalPay,
            'hoursDecimal' => $hours,
        ];
    }

    private function convertTimeToDecimal(string $time): float
    {
        $parts = explode('.', str_replace(':', '.', $time));
        $hours = isset($parts[0]) ? (int)$parts[0] : 0;
        $minutes = isset($parts[1]) ? (int)$parts[1] : 0;
        $decimal = $hours + ($minutes / 60);
        return round($decimal, 2);
    }
}