<?php

namespace App\Controller\Front;

use App\Entity\Task;
use App\Entity\TaskTimeLog;
use App\Entity\MasterEmployee;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Csrf\CsrfTokenManagerInterface;
use Symfony\Component\Security\Csrf\CsrfToken;

#[Route('/employee')]
class TaskTimeTrackingController extends AbstractController
{
    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly CsrfTokenManagerInterface $csrfTokenManager,
    ) {
    }

    #[Route('/task/{id}/time-tracking/start', name: 'employee_task_time_tracking_start', methods: ['POST'])]
    public function startTimeTracking(Request $request, Task $task): JsonResponse
    {
        $employee = $this->getUser();
        if (!$employee instanceof MasterEmployee) {
            return new JsonResponse(['success' => false, 'error' => 'Unauthorized'], Response::HTTP_FORBIDDEN);
        }

        if ($task->getAssignedTo() !== $employee) {
            return new JsonResponse(['success' => false, 'error' => 'You are not assigned to this task'], Response::HTTP_FORBIDDEN);
        }

        $token = new CsrfToken('time_tracking_' . $task->getId(), $request->request->get('_csrf_token'));
        if (!$this->csrfTokenManager->isTokenValid($token)) {
            return new JsonResponse(['success' => false, 'error' => 'Invalid CSRF token'], Response::HTTP_BAD_REQUEST);
        }

        $activeTimeLog = $this->entityManager->getRepository(TaskTimeLog::class)->findOneBy([
            'task' => $task,
            'employee' => $employee,
            'active' => true
        ]);

        if ($activeTimeLog) {
            return new JsonResponse(['success' => false, 'error' => 'You already have an active time tracker for this task'], Response::HTTP_BAD_REQUEST);
        }

        $now = new \DateTime('now');

        $timeLog = new TaskTimeLog();
        $timeLog->setTask($task);
        $timeLog->setEmployee($employee);
        $timeLog->setStartTime(clone $now);
        $timeLog->setActive(true);
        $timeLog->setCreatedAt(clone $now);
        $timeLog->setUpdatedAt(clone $now);

        $this->entityManager->persist($timeLog);
        $this->entityManager->flush();

        return new JsonResponse([
            'success' => true,
            'message' => 'Time tracking started successfully.',
            'timeLogId' => $timeLog->getId(),
            'startTime' => $timeLog->getStartTime()->format('Y-m-d H:i:s')
        ], Response::HTTP_OK);
    }

    #[Route('/task/{id}/time-tracking/stop', name: 'employee_task_time_tracking_stop', methods: ['POST'])]
    public function stopTimeTracking(Request $request, Task $task): JsonResponse
    {
        $employee = $this->getUser();
        if (!$employee instanceof MasterEmployee) {
            return new JsonResponse(['success' => false, 'error' => 'Unauthorized'], Response::HTTP_FORBIDDEN);
        }

        if ($task->getAssignedTo() !== $employee) {
            return new JsonResponse(['success' => false, 'error' => 'You are not assigned to this task'], Response::HTTP_FORBIDDEN);
        }

        $token = new CsrfToken('time_tracking_' . $task->getId(), $request->request->get('_csrf_token'));
        if (!$this->csrfTokenManager->isTokenValid($token)) {
            return new JsonResponse(['success' => false, 'error' => 'Invalid CSRF token'], Response::HTTP_BAD_REQUEST);
        }

        $activeTimeLog = $this->entityManager->getRepository(TaskTimeLog::class)->findOneBy([
            'task' => $task,
            'employee' => $employee,
            'active' => true
        ]);

        if (!$activeTimeLog) {
            return new JsonResponse(['success' => false, 'error' => 'No active time tracker found for this task'], Response::HTTP_BAD_REQUEST);
        }

        $now = new \DateTime('now');

        $activeTimeLog->setEndTime(clone $now);
        $activeTimeLog->setActive(false);
        $activeTimeLog->setUpdatedAt(clone $now);

        $startTime = $activeTimeLog->getStartTime();
        $endTime = clone $now;

        $interval = $startTime->diff($endTime);
        $seconds = ($interval->days * 86400) + ($interval->h * 3600) + ($interval->i * 60) + $interval->s;
        $hours = $seconds / 3600.0;

        $activeTimeLog->setDuration($hours);

        $currentActualHours = $task->getActualHours() ?? 0;
        $task->setActualHours($currentActualHours + $hours);
        $task->setUpdatedAt(clone $now);

        $this->entityManager->persist($activeTimeLog);
        $this->entityManager->persist($task);
        $this->entityManager->flush();

        return new JsonResponse([
            'success' => true,
            'message' => 'Time tracking stopped successfully.',
            'duration' => $hours,
            'actualHours' => $task->getActualHours(),
            'endTime' => $endTime->format('Y-m-d H:i:s')
        ], Response::HTTP_OK);
    }

    #[Route('/task/{id}/time-tracking/status', name: 'employee_task_time_tracking_status', methods: ['GET'])]
    public function getTimeTrackingStatus(Task $task): JsonResponse
    {
        $employee = $this->getUser();
        if (!$employee instanceof MasterEmployee) {
            return new JsonResponse(['success' => false, 'error' => 'Unauthorized'], Response::HTTP_FORBIDDEN);
        }

        if ($task->getAssignedTo() !== $employee) {
            return new JsonResponse(['success' => false, 'error' => 'You are not assigned to this task'], Response::HTTP_FORBIDDEN);
        }

        $activeTimeLog = $this->entityManager->getRepository(TaskTimeLog::class)->findOneBy([
            'task' => $task,
            'employee' => $employee,
            'active' => true
        ]);

        if ($activeTimeLog) {
            return new JsonResponse([
                'success' => true,
                'isTracking' => true,
                'timeLogId' => $activeTimeLog->getId(),
                'startTime' => $activeTimeLog->getStartTime()->format('Y-m-d H:i:s')
            ], Response::HTTP_OK);
        } else {
            return new JsonResponse([
                'success' => true,
                'isTracking' => false,
                'actualHours' => $task->getActualHours() ?? 0
            ], Response::HTTP_OK);
        }
    }
}