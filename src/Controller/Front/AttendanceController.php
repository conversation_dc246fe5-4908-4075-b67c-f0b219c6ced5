<?php

namespace App\Controller\Front;

use App\Entity\MasterEmployee;
use App\Service\Front\AttendanceService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class AttendanceController extends AbstractController
{
    public function __construct(
        private readonly AttendanceService $attendanceService
    ) {
    }
    #[Route('/attendance/punch', name: 'attendance_punch', methods: ['POST'])]
    public function punchAttendance(Request $request): JsonResponse
    {
        $user = $this->getUser();
        if (!$user instanceof MasterEmployee) {
            return new JsonResponse(['error' => 'Unauthorized'], 403);
        }
        if ($response = $this->attendanceService->validateUser($user)) {
            return $response;
        }

        $status = $request->request->get('status');
        return $this->attendanceService->punchAttendance($user, $status);
    }

    #[Route('/attendance/today-status', name: 'attendance_today_status', methods: ['GET'])]
    public function getTodayStatus(): JsonResponse
    {
        $user = $this->getUser();
        if (!$user instanceof MasterEmployee) {
            return new JsonResponse(['error' => 'Unauthorized'], 403);
        }
        if ($response = $this->attendanceService->validateUser($user)) {
            return $response;
        }

        return $this->attendanceService->getTodayStatus($user);
    }

    #[Route('/attendance', name: 'attendance_index', methods: ['GET'])]
    public function index(): Response
    {
        $user = $this->getUser();
        $hasPunchedToday = false;

        if ($user instanceof MasterEmployee) {
            $hasPunchedToday = $this->attendanceService->hasPunchedToday($user);
        }

        return $this->render('Front/include/main.html.twig', [
            'has_punched_today' => $hasPunchedToday,
        ]);
    }

    #[Route('/attendance/calendar-data', name: 'attendance_calendar_data', methods: ['GET'])]
    public function getCalendarData(Request $request): JsonResponse
    {
        $user = $this->getUser();
        if (!$user instanceof MasterEmployee) {
            return new JsonResponse(['error' => 'Unauthorized'], 403);
        }

        $startDate = $request->query->get('start') ? new \DateTime($request->query->get('start')) : null;
        $endDate = $request->query->get('end') ? new \DateTime($request->query->get('end')) : null;

        $calendarData = $this->attendanceService->getAttendanceCalendarData($user, $startDate, $endDate);

        return new JsonResponse($calendarData['events']);
    }

    #[Route('/attendance/date-details/{date}', name: 'attendance_date_details', methods: ['GET'])]
    public function getDateDetails(string $date): JsonResponse
    {
        $user = $this->getUser();
        if (!$user instanceof MasterEmployee) {
            return new JsonResponse(['error' => 'Unauthorized'], 403);
        }

        if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $date)) {
            return new JsonResponse(['error' => 'Invalid date format'], 400);
        }

        return $this->attendanceService->getDateAttendanceDetails($user, $date);
    }

    #[Route('/attendance/weekly-stats', name: 'attendance_weekly_stats', methods: ['GET'])]
    public function getWeeklyStats(): JsonResponse
    {
        $user = $this->getUser();
        if (!$user instanceof MasterEmployee) {
            return new JsonResponse(['error' => 'Unauthorized'], 403);
        }
        if ($response = $this->attendanceService->validateUser($user)) {
            return $response;
        }

        return $this->attendanceService->getWeeklyStats($user);
    }
}