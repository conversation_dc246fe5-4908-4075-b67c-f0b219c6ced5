<?php

namespace App\Service\Admin;

use App\Entity\Employee;
use App\Entity\EmployeeHours;
use App\Entity\MasterEmployee;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\QueryBuilder;
use Omines\DataTablesBundle\Adapter\Doctrine\ORMAdapter;
use Omines\DataTablesBundle\Column\TextColumn;
use Omines\DataTablesBundle\DataTable;
use Omines\DataTablesBundle\DataTableFactory;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use App\Traits\ExceptionLoggerTrait;
use App\Helper\TimeHelper;
use function Symfony\Component\Translation\t;

class WeeklyHoursService
{
    use ExceptionLoggerTrait;

    private array $employeeHoursCache = [];

    public function __construct(
        private readonly DataTableFactory       $dataTableFactory,
        private readonly RequestStack           $requestStack,
        private readonly TimeHelper             $timeHelper,
        private readonly EntityManagerInterface $entityManager
    )
    {
    }

    public function createWeeklyReport(Request $request): DataTable
    {
        $dataTable = $this->dataTableFactory->create();
        $session = $this->requestStack->getSession();
        $filters = $session->get('weekly_hours_filters', []);
        if (!$filters) {
            $startDate = new \DateTime('monday this week');
            $endDate = new \DateTime('sunday this week');
            $session->set('weekly_hours_filters', [
                'startDate' => $startDate->format('Y-m-d'),
                'endDate' => $endDate->format('Y-m-d'),
            ]);
        } else {
            $startDate = new \DateTime($filters['startDate']);
            $endDate = new \DateTime($filters['endDate']);
        }
        $dateColumns = $this->generateDateColumns($startDate, $endDate);

        // Add Employee Column
        $dataTable->add('employee', TextColumn::class, [
            'label' => 'Employee',
            'field' => 'me.name',
            'orderable' => true,
            'globalSearchable' => true,
        ]);

        // Add Date Columns for Hours
        foreach ($dateColumns as $key => $column) {
            $dataTable->add($key, TextColumn::class, [
                'label' => $column['label'],
                'field' => '',
                'render' => function ($value, $context) use ($column) {
                    return $this->formatHoursForDate(
                        $context->getId(),
                        $column['date']
                    );
                },
                'raw' => true,
            ]);
        }

        // Build the DataTable with the Adapter
        return $dataTable->createAdapter(ORMAdapter::class, [
            'entity' => Employee::class,
            'query' => function (QueryBuilder $qb) use ($startDate, $endDate) {
                return $this->buildQuery($qb, $startDate, $endDate);
            },
        ])
            ->addOrderBy('employee', 'asc')
            ->handleRequest($request);
    }

    private function generateDateColumns(\DateTime $startDate, \DateTime $endDate): array
    {
        $columns = [];
        $currentDate = clone $startDate;

        while ($currentDate <= $endDate) {
            $key = 'date_' . $currentDate->format('Y_m_d');
            $columns[$key] = [
                'label' => $currentDate->format('d-m-Y'),
                'date' => $currentDate->format('Y-m-d'),
            ];
            $currentDate->modify('+1 day');
        }

        return $columns;
    }

    private function buildQuery(QueryBuilder $qb, \DateTime $startDate, \DateTime $endDate): QueryBuilder
    {
        $session = $this->requestStack->getSession();
        $filters = $session->get('weekly_hours_filters', []);

        $qb->select('e,me')
            ->from(Employee::class, 'e')
            ->leftJoin('e.masterEmployee', 'me')
            ->orderBy('me.name', 'ASC');

        if (!empty($filters['employeeId'])) {
            $qb->andWhere('e.id = :employeeId')
                ->setParameter('employeeId', $filters['employeeId']);
        }

        // Fetch and Cache Results
        $this->cacheResults($startDate, $endDate);

        return $qb;
    }

    private function cacheResults(\DateTime $startDate, \DateTime $endDate): void
    {
        $qb = $this->entityManager->createQueryBuilder();

        $qb->select('eh, e, me')
            ->from(EmployeeHours::class, 'eh')
            ->join('eh.employee', 'e')
            ->join('e.masterEmployee', 'me')
            ->where('eh.reportDate BETWEEN :startDate AND :endDate')
            ->setParameter('startDate', $startDate->format('Y-m-d'))
            ->setParameter('endDate', $endDate->format('Y-m-d'));
        $results = $qb->getQuery()->getResult();

        foreach ($results as $employeeHours) {
            /** @var EmployeeHours $employeeHours */
            $employeeId = $employeeHours->getEmployee()->getId();
            $date = $employeeHours->getReportDate()->format('Y-m-d');
            $hours = $employeeHours->getTotalHours();

            if (!isset($this->employeeHoursCache[$employeeId])) {
                $this->employeeHoursCache[$employeeId] = [];
            }

            $this->employeeHoursCache[$employeeId][$date] = $hours;
        }
    }

    private function formatHoursForDate(int $employeeId, string $date): string
    {
        $hours = $this->employeeHoursCache[$employeeId][$date] ?? 0;
        return $hours === 0 ? '00:00' : $this->timeHelper->convertDecimalToTime($hours);
    }
}