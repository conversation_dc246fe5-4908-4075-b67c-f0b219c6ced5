{% extends 'Front/dashboard/base.html.twig' %}
{% block body %}
    <div class="content">
        <div class="animated fadeIn">
            <div class="row">
                <div class="col-lg-3 col-md-6">
                    <div class="card">
                        <div class="card-body">
                            <div class="stat-widget-five">
                                <div class="stat-icon dib flat-color-1">
                                    <i class="fa fa-clock-o"></i>
                                </div>
                                <div class="stat-content">
                                    <div class="text-left dib">
                                        <div class="stat-text"><span class="count">{{ totalHours|round(0, 'floor') }}</span>
                                            <span class="count-sub-text">Hour(s)</span></div>
                                        <div class="stat-heading">This Month</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6">
                    <div class="card">
                        <div class="card-body">
                            <div class="stat-widget-five">
                                <div class="stat-icon dib flat-color-1">
                                    <i class="fa fa-bell-o"></i>
                                </div>
                                <div class="stat-content">
                                    <div class="text-left dib">
                                        <div class="stat-text"><span class="count">{{ employeeNotified }}</span></div>
                                        <div class="stat-heading">Notified </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6">
                    <div class="card">
                        <div class="card-body">
                            <div class="stat-widget-five">
                                <div class="stat-icon dib flat-color-5">
                                    <i class="fa fa-check-circle"></i>
                                </div>
                                <div class="stat-content">
                                    <div class="text-left dib">
                                        <div class="stat-text"><span class="count">{{ justifiedCount }}</span></div>
                                        <div class="stat-heading">Justified</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6">
                    <div class="card">
                        <div class="card-body">
                            <div class="stat-widget-five">
                                <div class="stat-icon dib flat-color-6">
                                    <i class="fa fa-times-circle"></i>
                                </div>
                                <div class="stat-content">
                                    <div class="text-left dib">
                                        <div class="stat-text"><span class="count">{{ notJustifiedCount }}</span></div>
                                        <div class="stat-heading">Not Justified</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <section class="card-attendence">
            <h2>Attendance Management</h2>

            <div class="stats-container">
                <div class="stat-card">
                    <h4>This Week - My Stats</h4>
                    <div class="stat-value" id="totalHours"></div>
                </div>

                <div class="stat-card">
                    <h4>Weekly Attendance</h4>
                    <div class="week-days" id="weeklyAttendance">
                        <div class="day">
                            <span class="day-label">Mon</span>
                        </div>
                        <div class="day">
                            <span class="day-label">Tue</span>
                        </div>
                        <div class="day">
                            <span class="day-label">Wed</span>
                        </div>
                        <div class="day">
                            <span class="day-label">Thu</span>
                        </div>
                        <div class="day">
                            <span class="day-label">Fri</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="action-buttons">
                <button id="punchInBtn" class="btn" data-status="Office In">
                    <i class="fa fa-sign-in"></i> Office In
                </button>
                <button id="lunchOutBtn" class="btn btn-outline" data-status="Going out for Lunch">
                    <i class="fa fa-cutlery"></i> Going out for Lunch
                </button>
                <button id="lunchInBtn" class="btn btn-outline" data-status="Back from Lunch">
                    <i class="fa fa-sign-in"></i> Back from Lunch
                </button>
                <button id="leaveBtn" class="btn btn-outline" data-status="Leaving for the Day">
                    <i class="fa fa-sign-out"></i> Leaving for the Day
                </button>
            </div>

            <input type="hidden" name="_csrf_token" value="{{ csrf_token('attendance_punch') }}">
        </section>


        <script>
            document.addEventListener('DOMContentLoaded', async () => {
                const buttons = {
                    'Office In': document.getElementById('punchInBtn'),
                    'Going out for Lunch': document.getElementById('lunchOutBtn'),
                    'Back from Lunch': document.getElementById('lunchInBtn'),
                    'Leaving for the Day': document.getElementById('leaveBtn')
                };

                const today = new Date().toDateString();
                const weeklyAttendanceContainer = document.getElementById('weeklyAttendance');
                const totalHoursElement = document.getElementById('totalHours');

                async function updateButtonState() {
                    try {
                        const response = await fetch('/attendance/today-status', {
                            method: 'GET',
                            headers: { 'Accept': 'application/json' }
                        });
                        const data = await response.json();

                        if (response.ok) {
                            const lunchOutDone = localStorage.getItem('lunchOutDone') === today;
                            const lunchInDone = localStorage.getItem('lunchInDone') === today;

                            Object.entries(buttons).forEach(([status, button]) => {
                                if (!button) return;

                                let canPunch = data.canPunch.includes(status);

                                // 🔐 Custom lunch button logic
                                if (status === 'Going out for Lunch' && lunchOutDone) {
                                    canPunch = false;
                                }
                                if (status === 'Back from Lunch' && lunchInDone) {
                                    canPunch = false;
                                }

                                // 🔒 If both lunch punches are done, lock both buttons
                                if ((status === 'Going out for Lunch' || status === 'Back from Lunch') && lunchInDone) {
                                    canPunch = false;
                                }

                                button.disabled = !canPunch;
                                button.classList.toggle('btn-outline', canPunch);

                                if (status === 'Office In' && !canPunch) {
                                    localStorage.setItem('lastPunchDate', today);
                                }
                            });

                            if (data.canPunch.includes('Office In')) {
                                localStorage.removeItem('lastPunchDate');
                            }
                        } else {
                            console.error('Error fetching today status:', data.error);
                        }
                    } catch (error) {
                        console.error('Error fetching today status:', error);
                    }
                }

                async function updateWeeklyStats() {
                    try {
                        const response = await fetch('/attendance/weekly-stats', {
                            method: 'GET',
                            headers: { 'Accept': 'application/json' }
                        });
                        const data = await response.json();

                        if (response.ok) {
                            totalHoursElement.textContent = `${data.totalHoursFormatted} hrs`;
                            weeklyAttendanceContainer.innerHTML = '';
                            data.weeklyData.forEach(day => {
                                const statusClass = day.status === 'P' ? 'present' : (day.status === 'A' ? 'absent' : 'neutral');
                                const dayElement = document.createElement('div');
                                dayElement.className = 'day';
                                dayElement.innerHTML = `
                            <span class="day-label">${day.day}</span>
                            <span class="status ${statusClass}">${day.status}</span>
                        `;
                                weeklyAttendanceContainer.appendChild(dayElement);
                            });
                        } else {
                            console.error('Error fetching weekly stats:', data.error);
                        }
                    } catch (error) {
                        console.error('Error fetching weekly stats:', error);
                    }
                }

                await updateButtonState();
                await updateWeeklyStats();

                function checkNewDay() {
                    const currentDate = new Date().toDateString();
                    const lastPunchDate = localStorage.getItem('lastPunchDate');

                    if (currentDate !== lastPunchDate) {
                        localStorage.removeItem('lunchOutDone');
                        localStorage.removeItem('lunchInDone');
                        updateButtonState();
                        updateWeeklyStats();
                    }
                }

                setInterval(checkNewDay, 60000);

                Object.values(buttons).forEach(button => {
                    if (!button) return;

                    button.addEventListener('click', async () => {
                        const status = button.getAttribute('data-status');

                        try {
                            const response = await fetch('/attendance/punch', {
                                method: 'POST',
                                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                                body: new URLSearchParams({
                                    'status': status,
                                    '_csrf_token': document.querySelector('input[name="_csrf_token"]').value
                                })
                            });

                            const data = await response.json();

                            if (data.success) {
                                button.disabled = true;
                                button.classList.remove('btn-outline');

                                if (status === 'Office In') {
                                    localStorage.setItem('lastPunchDate', today);
                                }

                                if (status === 'Going out for Lunch') {
                                    localStorage.setItem('lunchOutDone', today);
                                }

                                if (status === 'Back from Lunch') {
                                    localStorage.setItem('lunchInDone', today);
                                }

                                alertbox.render({
                                    alertIcon: 'success',
                                    title: `${status}`,
                                    message: data.message || `${status}` + 'recorded successfully!',
                                    btnTitle: 'Ok',
                                    border: true
                                });
                                await updateButtonState();
                                await updateWeeklyStats();
                                setTimeout(() => window.location.reload(), 1000);
                            } else {
                                alertbox.render({
                                    alertIcon: 'error',
                                    title: `${data.error}`,
                                    message: error.message || 'Failed to record attendance',
                                    btnTitle: 'Ok',
                                    border: true
                                });
                            }
                        } catch (error) {
                            alert(`Error recording ${status}`);
                        }
                    });
                });
            });
        </script>

        <div class="orders">
            {% include 'CommonTemplate/dashboard.html.twig'%}
        </div>
    </div>
    <div class="clearfix"></div>
    <link rel="stylesheet" href="{{ asset('assets/css/dashboard.css') }}">
{% endblock %}
