const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const os = require('os');

class DatabaseService {
    constructor() {
        this.db = null;
        this.dbPath = this.getDbPath();
    }

    getDbPath() {
        try {
            // Try to use Electron's app.getPath if available
            const { app } = require('electron');
            return path.join(app.getPath('userData'), 'brainee-hrm.db');
        } catch (error) {
            // Fallback for testing or non-Electron environments
            const userDataPath = process.env.NODE_ENV === 'test'
                ? path.join(os.tmpdir(), 'brainee-hrm-test')
                : path.join(os.homedir(), '.config', 'brainee-hrm-desktop');

            // Create directory if it doesn't exist
            const fs = require('fs');
            if (!fs.existsSync(userDataPath)) {
                fs.mkdirSync(userDataPath, { recursive: true });
            }

            return path.join(userDataPath, 'brainee-hrm.db');
        }
    }

    async initialize() {
        return new Promise((resolve, reject) => {
            this.db = new sqlite3.Database(this.dbPath, (err) => {
                if (err) {
                    console.error('Error opening database:', err);
                    reject(err);
                    return;
                }
                
                console.log('Connected to SQLite database at:', this.dbPath);
                this.createTables().then(resolve).catch(reject);
            });
        });
    }

    async createTables() {
        return new Promise((resolve, reject) => {
            this.db.serialize(() => {
                // Users table
                this.db.run(`CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY,
                    email TEXT UNIQUE NOT NULL,
                    username TEXT UNIQUE,
                    name TEXT,
                    roles TEXT,
                    employee_code TEXT,
                    is_team_leader BOOLEAN DEFAULT 0,
                    is_hr_account BOOLEAN DEFAULT 0,
                    password TEXT,
                    last_sync TEXT,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
                )`);
                
                // Tokens table
                this.db.run(`CREATE TABLE IF NOT EXISTS tokens (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER,
                    token TEXT UNIQUE NOT NULL,
                    device_id TEXT,
                    expires_at TEXT,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )`);
                
                // Activity log table
                this.db.run(`CREATE TABLE IF NOT EXISTS activity_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER,
                    action TEXT NOT NULL,
                    details TEXT,
                    timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )`);
                
                // App settings table
                this.db.run(`CREATE TABLE IF NOT EXISTS app_settings (
                    key TEXT PRIMARY KEY,
                    value TEXT,
                    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
                )`);
                
                // Sync status table
                this.db.run(`CREATE TABLE IF NOT EXISTS sync_status (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    table_name TEXT NOT NULL,
                    last_sync TEXT,
                    status TEXT DEFAULT 'pending'
                )`);

                // Sync queue table for offline operations
                this.db.run(`CREATE TABLE IF NOT EXISTS sync_queue (
                    id TEXT PRIMARY KEY,
                    action TEXT NOT NULL,
                    data TEXT NOT NULL,
                    timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
                    attempts INTEGER DEFAULT 0
                )`);

                // Website usage table
                this.db.run(`CREATE TABLE IF NOT EXISTS website_usage (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    employee_id INTEGER NOT NULL,
                    visit_date TEXT NOT NULL,
                    url TEXT NOT NULL,
                    domain TEXT NOT NULL,
                    page_title TEXT,
                    visit_time TEXT NOT NULL,
                    leave_time TEXT,
                    duration INTEGER,
                    category TEXT,
                    productivity_level TEXT,
                    browser_name TEXT,
                    visit_count INTEGER DEFAULT 1,
                    device_id TEXT,
                    metadata TEXT,
                    is_synced INTEGER DEFAULT 0,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
                )`);

                // Application usage table
                this.db.run(`CREATE TABLE IF NOT EXISTS application_usage (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    employee_id INTEGER NOT NULL,
                    usage_date TEXT NOT NULL,
                    application_name TEXT NOT NULL,
                    application_path TEXT,
                    window_title TEXT,
                    start_time TEXT NOT NULL,
                    end_time TEXT,
                    duration INTEGER,
                    category TEXT,
                    switch_count INTEGER DEFAULT 1,
                    device_id TEXT,
                    metadata TEXT,
                    is_synced INTEGER DEFAULT 0,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
                )`);



                // Time logs table
                this.db.run(`CREATE TABLE IF NOT EXISTS time_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    employee_id INTEGER NOT NULL,
                    log_date TEXT NOT NULL,
                    start_time TEXT NOT NULL,
                    end_time TEXT,
                    log_type TEXT NOT NULL,
                    duration INTEGER,
                    description TEXT,
                    device_id TEXT,
                    metadata TEXT,
                    is_synced INTEGER DEFAULT 0,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (employee_id) REFERENCES users (id)
                )`);

                // Screenshot logs table
                this.db.run(`CREATE TABLE IF NOT EXISTS screenshot_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    employee_id INTEGER NOT NULL,
                    captured_at TEXT NOT NULL,
                    capture_type TEXT NOT NULL,
                    file_path TEXT NOT NULL,
                    file_name TEXT,
                    file_size INTEGER,
                    resolution TEXT,
                    device_id TEXT,
                    active_application TEXT,
                    active_window TEXT,
                    metadata TEXT,
                    is_synced INTEGER DEFAULT 0,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (employee_id) REFERENCES users (id)
                )`);

                // Productivity reports table
                this.db.run(`CREATE TABLE IF NOT EXISTS productivity_reports (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    employee_id INTEGER NOT NULL,
                    report_date TEXT NOT NULL,
                    report_type TEXT NOT NULL,
                    total_work_time INTEGER,
                    productive_time INTEGER,
                    neutral_time INTEGER,
                    distracting_time INTEGER,
                    idle_time INTEGER,
                    applications_used INTEGER,
                    websites_visited INTEGER,
                    screenshots_taken INTEGER,

                    productivity_score REAL,
                    metadata TEXT,
                    is_synced INTEGER DEFAULT 0,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (employee_id) REFERENCES users (id)
                )`);

                console.log('Database tables created/verified');
                resolve();
            });
        });
    }

    async query(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.all(sql, params, (err, rows) => {
                if (err) {
                    console.error('Database query error:', err);
                    reject(err);
                } else {
                    resolve(rows);
                }
            });
        });
    }

    async run(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.run(sql, params, function(err) {
                if (err) {
                    console.error('Database run error:', err);
                    reject(err);
                } else {
                    resolve({ lastID: this.lastID, changes: this.changes });
                }
            });
        });
    }

    async get(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.get(sql, params, (err, row) => {
                if (err) {
                    console.error('Database get error:', err);
                    reject(err);
                } else {
                    resolve(row);
                }
            });
        });
    }

    async all(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.all(sql, params, (err, rows) => {
                if (err) {
                    console.error('Database all error:', err);
                    reject(err);
                } else {
                    resolve(rows);
                }
            });
        });
    }

    // User management methods
    async saveUser(userData) {
        const sql = `INSERT OR REPLACE INTO users 
            (id, email, username, name, roles, employee_code, is_team_leader, is_hr_account, last_sync)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`;
        
        const params = [
            userData.id,
            userData.email,
            userData.username,
            userData.name,
            JSON.stringify(userData.roles),
            userData.employeeCode,
            userData.isTeamLeader ? 1 : 0,
            userData.isHrAccount ? 1 : 0,
            new Date().toISOString()
        ];

        return this.run(sql, params);
    }

    async getUser(identifier) {
        const sql = `SELECT * FROM users WHERE email = ? OR username = ? OR id = ?`;
        return this.get(sql, [identifier, identifier, identifier]);
    }

    async saveToken(userId, token, deviceId, expiresAt) {
        const sql = `INSERT OR REPLACE INTO tokens (user_id, token, device_id, expires_at)
                     VALUES (?, ?, ?, ?)`;
        return this.run(sql, [userId, token, deviceId, expiresAt]);
    }

    async getValidToken(token) {
        const sql = `SELECT * FROM tokens WHERE token = ? AND expires_at > datetime('now')`;
        return this.get(sql, [token]);
    }

    async logActivity(userId, action, details = null) {
        const sql = `INSERT INTO activity_log (user_id, action, details) VALUES (?, ?, ?)`;
        return this.run(sql, [userId, action, details]);
    }

    async getRecentActivity(userId, limit = 10) {
        const sql = `SELECT * FROM activity_log WHERE user_id = ? 
                     ORDER BY timestamp DESC LIMIT ?`;
        return this.all(sql, [userId, limit]);
    }

    async setSetting(key, value) {
        const sql = `INSERT OR REPLACE INTO app_settings (key, value, updated_at) 
                     VALUES (?, ?, datetime('now'))`;
        return this.run(sql, [key, value]);
    }

    async getSetting(key) {
        const sql = `SELECT value FROM app_settings WHERE key = ?`;
        const result = await this.get(sql, [key]);
        return result ? result.value : null;
    }

    async updateSyncStatus(tableName, status = 'completed') {
        const sql = `INSERT OR REPLACE INTO sync_status (table_name, last_sync, status)
                     VALUES (?, datetime('now'), ?)`;
        return this.run(sql, [tableName, status]);
    }

    async getSyncStatus(tableName) {
        const sql = `SELECT * FROM sync_status WHERE table_name = ?`;
        return this.get(sql, [tableName]);
    }

    async clearExpiredTokens() {
        const sql = `DELETE FROM tokens WHERE expires_at <= datetime('now')`;
        return this.run(sql);
    }

    async close() {
        return new Promise((resolve) => {
            if (this.db) {
                this.db.close((err) => {
                    if (err) {
                        console.error('Error closing database:', err);
                    } else {
                        console.log('Database connection closed');
                    }
                    resolve();
                });
            } else {
                resolve();
            }
        });
    }

    // Backup and restore methods
    async backup(backupPath) {
        return new Promise((resolve, reject) => {
            const fs = require('fs');
            const readStream = fs.createReadStream(this.dbPath);
            const writeStream = fs.createWriteStream(backupPath);
            
            readStream.pipe(writeStream);
            
            writeStream.on('finish', () => {
                console.log('Database backup completed:', backupPath);
                resolve(backupPath);
            });
            
            writeStream.on('error', (err) => {
                console.error('Database backup failed:', err);
                reject(err);
            });
        });
    }

    async getStats() {
        const userCount = await this.get('SELECT COUNT(*) as count FROM users');
        const tokenCount = await this.get('SELECT COUNT(*) as count FROM tokens');
        const activityCount = await this.get('SELECT COUNT(*) as count FROM activity_log');
        
        return {
            users: userCount.count,
            tokens: tokenCount.count,
            activities: activityCount.count,
            dbPath: this.dbPath
        };
    }
}

module.exports = DatabaseService;
