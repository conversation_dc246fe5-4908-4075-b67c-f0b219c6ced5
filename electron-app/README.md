# Brainee HRM Desktop Application

A desktop employee tracking application built with Electron.js that connects to the Brainee HRM Symfony backend.

## Features

- **Secure Login**: Connects to MasterEmployee entity in Symfony backend
- **Token-based Authentication**: Uses JWT tokens for secure API communication
- **No Logout**: Once logged in, users cannot logout (as per requirements)
- **Auto-start**: Automatically starts with the operating system
- **SQLite Local Storage**: Offline functionality with local database
- **System Tray**: Runs in background with system tray integration
- **Cross-platform**: Works on Windows, macOS, and Linux

## Prerequisites

- Node.js (v16 or higher)
- npm or yarn
- Running Brainee HRM Symfony backend

## Installation

1. Navigate to the electron-app directory:
```bash
cd electron-app
```

2. Install dependencies:
```bash
npm install
```

3. Configure the backend URL in `renderer/js/login.js` if different from default:
```javascript
this.apiBaseUrl = 'http://your-symfony-backend-url';
```

4. Ensure the Symfony backend is running and accessible:
```bash
# In the main project directory
symfony server:start
# or
php -S localhost:8000 -t public
```

## Development

Run in development mode:
```bash
npm run dev
```

## Building

Build for current platform:
```bash
npm run build
```

Build for specific platforms:
```bash
npm run build-win    # Windows
npm run build-mac    # macOS
npm run build-linux  # Linux
```

## Configuration

The app stores configuration and user data in:
- Windows: `%APPDATA%/brainee-hrm-desktop`
- macOS: `~/Library/Application Support/brainee-hrm-desktop`
- Linux: `~/.config/brainee-hrm-desktop`

## Backend API Requirements

The Symfony backend needs to provide these API endpoints:

- `POST /api/employee/login` - Employee authentication
- `POST /api/validate-token` - Token validation
- `GET /api/health` - Health check

## Security Features

- Secure token storage using electron-store
- Device identification using machine ID
- Local SQLite database for offline functionality
- No logout functionality (as per requirements)

## Auto-start Configuration

The app automatically configures itself to start with the operating system. This can be managed through:
- Windows: Task Manager > Startup tab
- macOS: System Preferences > Users & Groups > Login Items
- Linux: Varies by desktop environment

## Troubleshooting

1. **Connection Issues**: Check if the Symfony backend is running and accessible
2. **Auto-start Not Working**: Check OS-specific startup settings
3. **Database Issues**: Delete the app data folder to reset local database

## License

MIT License
