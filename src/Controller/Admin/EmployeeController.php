<?php

namespace App\Controller\Admin;

use App\Service\Admin\EmployeeHoursService;
use App\Service\Admin\EmployeeService;
use App\Service\Admin\EmployeeSummaryService;
use App\Service\Admin\EmployeeTableService;
use App\Traits\ExceptionLoggerTrait;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\Session\SessionInterface;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\DecodingExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;

#[Route('/admin')]
class EmployeeController extends AbstractController
{
    use ExceptionLoggerTrait;
    public function __construct(
        private readonly EmployeeService        $employeeService,
        private readonly EmployeeTableService   $employeeTableService,
        ParameterBagInterface                   $parameterBag,
        private readonly EmployeeSummaryService $employeeSummaryService,
        private readonly EmployeeHoursService   $employeeHoursService,
    )
    {
        $this->initializeLogger($parameterBag, 'employee_controller');
    }

    /**
     * @param Request $request
     * @return Response
     */
    #[Route(path: '/employee', name: 'employee', methods: ['GET', 'POST'])]
    public function index(Request $request): Response
    {
        $table = $this->employeeTableService->createEmployeeTable($request);

        if ($table->isCallback()) {
            return $table->getResponse();
        }

        return $this->render('admin/employee_list.html.twig', [
            'datatable' => $table,
        ]);
    }

    /**
     * @return JsonResponse
     * @throws ClientExceptionInterface
     * @throws DecodingExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     */
    #[Route(path: '/employee/sync', name: 'employee_sync', methods: ['POST'])]
    public function sync(): JsonResponse
    {
        try {
            $result = $this->employeeService->fetchAndSyncEmployees();

            return new JsonResponse([
                'success' => true,
                'message' => sprintf(
                    'Sync completed successfully. %d new employees added, %d existing employees updated.',
                    $result['newCount'],
                    $result['updatedCount']
                )
            ], Response::HTTP_OK);
        } catch (\Exception $e) {
            $this->log(
                'Error fetching and syncing employees.',
                [
                    'error_message' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                    'trace' => $e->getTraceAsString(),
                ]
            );
            return new JsonResponse([
                'success' => false,
                'message' => 'Synchronization failed: ' . $e->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * @param Request $request
     * @return Response
     */
    #[Route(path: '/summary', name: 'summary', methods: ['GET', 'POST'])]
    public function summary(Request $request , SessionInterface $session): Response
    {
        $table = $this->employeeSummaryService->createEmployeesummary($request);

        if ($table->isCallback()) {
            return $table->getResponse();
        }
        $session->remove('employee_summary_filters');

        return $this->render('admin/employee_summary.html.twig', [
            'datatable' => $table,
            'filters' => [],
        ]);
    }

    /**
     * @return JsonResponse
     * @throws ClientExceptionInterface
     * @throws DecodingExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     */
    #[Route(path: '/summary/sync', name: 'summary_sync', methods: ['POST'])]
    public function summarySync(): JsonResponse
    {
        try {
            $date = new \DateTime();
            $result = $this->employeeHoursService->fetchAndStoreDailyHours($date);

            return new JsonResponse([
                'success' => true,
                'message' => sprintf(
                    'Sync completed successfully. %d success, %d failures.',
                    $result['success'],
                    $result['failures']
                )
            ], Response::HTTP_OK);
        } catch (\Exception $e) {
            $this->log(
                'Error fetching and syncing employees summary.',
                [
                    'error_message' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                    'trace' => $e->getTraceAsString(),
                ]
            );
            return new JsonResponse([
                'success' => false,
                'message' => 'Synchronization failed: Could not establish a connection with the TeamLogger API.'
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * @return JsonResponse
     */
    #[Route(path: '/employee/list', name: 'employee_list', methods: ['GET'])]
    public function list(): JsonResponse
    {
        try {
            $employeeOptions = $this->employeeService->getEmployeeOptions();
            return new JsonResponse([
                'success' => true,
                'employees' => $employeeOptions,
            ], Response::HTTP_OK);
        } catch (\Exception $e) {
            $this->log(
                'Error fetching and syncing employees list.',
                [
                    'error_message' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                    'trace' => $e->getTraceAsString(),
                ]
            );
            return new JsonResponse([
                'success' => false,
                'message' => 'Failed to fetch employees: ' . $e->getMessage(),
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    #[Route('/employee/summary/data', name: 'employee_summary_data', methods: ['POST'])]
    public function fetchEmployeeHours(Request $request): JsonResponse
    {
        try {
            $data = json_decode($request->getContent(), true);
            if (!is_array($data) || !isset($data['startDate'], $data['endDate'])) {
                throw new \InvalidArgumentException('Invalid input data');
            }
            $startDate = new \DateTime($data['startDate']);
            $endDate = new \DateTime($data['endDate']);
            $request->getSession()->set('employee_summary_filters', [
                'startDate' => $startDate->format('Y-m-d'),
                'endDate' => $endDate->format('Y-m-d'),
            ]);
            return new JsonResponse([
                'success' => true,
                'message' => 'Filters updated successfully.',
            ]);
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'message' => 'Error: ' . $e->getMessage(),
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws \DateMalformedStringException
     */
    #[Route('/employee/summary/filters', name: 'employee_summary_filters', methods: ['GET'])]
    public function getFilters(Request $request): JsonResponse
    {
        $filters = $request->getSession()->get('employee_summary_filters', []);

        $startDate = new \DateTime($filters['startDate'] ?? 'now');
        $endDate = new \DateTime($filters['endDate'] ?? 'now');
        $dateRange = $startDate->format('d-m-Y') . ' - ' . $endDate->format('d-m-Y');

        return new JsonResponse([
            'success' => true,
            'filters' => [
                'dateRange' => $dateRange,
            ],
        ]);
    }
}