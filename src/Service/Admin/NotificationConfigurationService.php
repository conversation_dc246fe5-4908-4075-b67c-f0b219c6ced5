<?php
namespace App\Service\Admin;

use App\Entity\Setting;
use App\Repository\SettingRepository;
use Doctrine\ORM\EntityManagerInterface;
use App\Traits\ExceptionLoggerTrait;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;

class NotificationConfigurationService
{
    use ExceptionLoggerTrait;

    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly SettingRepository $settingRepository,
        ParameterBagInterface $parameterBag
    ) {
        $this->initializeLogger($parameterBag, 'NotificationConfigurationService');
    }

    public function getNotificationSettings(array $fields): array
    {
        $settings = [];

        try {
            foreach ($fields as $key => $field) {
                $setting = $this->settingRepository->findOneBy(['fieldName' => $field]);
                $settings[$key] = $setting ? filter_var($setting->getFieldValue(), FILTER_VALIDATE_BOOLEAN) : false;
            }
        } catch (\Exception $e) {
            $this->log('Error fetching notification settings', ['exception' => $e]);
        }

        return $settings;
    }

    public function updateConfiguration(string $fieldName, ?string $fieldValue): void
    {
        try {
            $configuration = $this->settingRepository->findOneBy(['fieldName' => $fieldName]);

            if (!$configuration) {
                $configuration = new Setting();
                $configuration->setFieldName($fieldName);
                $configuration->setDateCreated(new \DateTime());
            }

            $configuration->setFieldValue($fieldValue);
            $configuration->setDateUpdated(new \DateTime());

            $this->entityManager->persist($configuration);
            $this->entityManager->flush();
        } catch (\Exception $e) {
            $this->log('Error updating configuration', ['fieldName' => $fieldName, 'exception' => $e]);
        }
    }
}