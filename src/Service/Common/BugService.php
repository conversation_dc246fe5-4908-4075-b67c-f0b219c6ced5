<?php

namespace App\Service\Common;

use App\Entity\Bug;
use App\Entity\MasterEmployee;
use App\Entity\User;
use App\Helper\ModalboxHelper;
use Doctrine\ORM\QueryBuilder;
use Omines\DataTablesBundle\Adapter\Doctrine\ORMAdapter;
use Omines\DataTablesBundle\Column\DateTimeColumn;
use Omines\DataTablesBundle\Column\TextColumn;
use Omines\DataTablesBundle\DataTable;
use Omines\DataTablesBundle\DataTableFactory;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Request;
use App\Traits\ExceptionLoggerTrait;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Security\Csrf\CsrfTokenManagerInterface;
use App\Service\TimezoneService;

class BugService
{
    use ExceptionLoggerTrait;

    public function __construct(
        private readonly DataTableFactory $dataTableFactory,
        private readonly RouterInterface $router,
        ParameterBagInterface $parameterBag,
        private readonly CsrfTokenManagerInterface $csrfTokenManager,
        private readonly ModalboxHelper $modalboxHelper,
        private readonly TimezoneService $timezoneService,
        private readonly Security $security
    ) {
        $this->initializeLogger($parameterBag, 'bug_table_service');
    }

    private function buildBugTable(
        Request $request,
        MasterEmployee $employee,
        callable $queryCallback
    ): DataTable {
        $dataTable = $this->dataTableFactory->create();

        foreach ($this->getColumns($employee) as $key => $column) {
            $dataTable->add(
                $key,
                $column['className'],
                array_filter($column, fn($k) => $k !== 'className', ARRAY_FILTER_USE_KEY)
            );
        }

        return $dataTable->createAdapter(ORMAdapter::class, [
            'entity' => Bug::class,
            'query' => $queryCallback,
        ])
            ->addOrderBy('createdAt', 'desc')
            ->handleRequest($request);
    }

    public function createMyBugsTable(Request $request, MasterEmployee $employee): DataTable
    {
        return $this->buildBugTable($request, $employee, fn(QueryBuilder $qb) =>
        $this->buildMyBugsQuery($qb, $employee)
        );
    }

    public function createMyBugsHistoryTable(Request $request, MasterEmployee $employee): DataTable
    {
        return $this->buildBugTable($request, $employee, fn(QueryBuilder $qb) =>
        $this->buildMyBugsHistoryQuery($qb, $employee)
        );
    }

    public function createBugTable(Request $request, MasterEmployee $employee): DataTable
    {
        $taskId = $request->query->getInt('task');
        $taskAssignmentId = $request->query->getInt('taskAssignmentId');

        return $this->buildBugTable($request, $employee, fn(QueryBuilder $qb) =>
        $this->buildQuery($qb, $employee, $taskId, $taskAssignmentId)
        );
    }

    public function adminBugTable(Request $request, User $user): DataTable
    {
        $dataTable = $this->dataTableFactory->create();

        foreach ($this->getAdminColumns($user) as $key => $column) {
            $dataTable->add($key, $column['className'], array_filter($column, fn($k) => $k !== 'className', ARRAY_FILTER_USE_KEY));
        }

        return $dataTable->createAdapter(ORMAdapter::class, [
            'entity' => Bug::class,
            'query' => fn(QueryBuilder $qb) => $this->buildQueryAdmin($qb, $user),
        ])
            ->addOrderBy('createdAt', 'desc')
            ->handleRequest($request);
    }

    private function getAdminColumns($user): array
    {
        return $this->getColumns($user);
    }

    private function getColumns($employee): array
    {
        return [
            'project' => [
                'label' => 'Project Name',
                'field' => 'p.name',
                'className' => TextColumn::class,
            ],
            'task' => [
                'label' => 'Task',
                'field' => 't.title',
                'className' => TextColumn::class,
                'render' => function ($value, $context) {
                    $title = $context->getTask()?->getTitle() ?? '-';
                    $maxLength = 20;
                    if (strlen($title) > $maxLength) {
                        $truncated = substr($title, 0, $maxLength) . '...';
                        return sprintf('<span title="%s">%s</span>', htmlspecialchars($title), htmlspecialchars($truncated));
                    }
                    return htmlspecialchars($title);
                },
            ],
            'assignedBy' => [
                'label' => 'Assigned By',
                'field' => 'b.id',
                'className' => TextColumn::class,
                'data' => fn($entity) => $entity->getAssignedBy()?->getName()
                    ?? $entity->getAssignedByAdmin()?->getUsername()
                    ?? 'Unassigned',
            ],
            'assignedTo' => [
                'label' => 'Assigned To',
                'field' => 'a.name',
                'className' => TextColumn::class,
                'render' => fn($value, $context) => $context->getAssignedTo() ? $context->getAssignedTo()->getName() : 'Unassigned',
            ],
            'priority' => [
                'label' => 'Priority',
                'data' => fn($entity) => $entity->getPriority()->value,
                'className' => TextColumn::class,
                'render' => fn($value) => $this->renderPriorityBadge($value),
            ],
            'severity' => [
                'label' => 'Severity',
                'data' => fn($entity) => $entity->getSeverity()->value,
                'className' => TextColumn::class,
                'render' => fn($value) => $this->renderSeverityBadge($value),
            ],
            'category' => [
                'label' => 'Category',
                'data' => fn($entity) => $entity->getCategory()->value,
                'className' => TextColumn::class,
                'render' => fn($value) => $this->renderCategoryBadge($value),
            ],
            'status' => [
                'label' => 'Status',
                'data' => fn($entity) => $entity->getStatus()->value,
                'className' => TextColumn::class,
                'render' => fn($value) => $this->renderStatusBadge($value),
            ],
            'createdAt' => [
                'label' => 'Created Date',
                'field' => 'b.createdAt',
                'format' => 'd-m-Y',
                'className' => DateTimeColumn::class,
                'searchable' => false,
            ],
            'actions' => [
                'label' => 'Actions',
                'field' => 'b.id',
                'className' => TextColumn::class,
                'orderable' => false,
                'render' => fn($value, $context) => $this->renderActionColumn($context, $employee),
            ],
        ];
    }

    private function renderPriorityBadge(string $priority): string
    {
        $colors = [
            'Low' => 'badge btn-primary',
            'Medium' => 'badge btn-primary',
            'High' => 'badge bg-danger',
            'Urgent' => 'badge bg-dark',
        ];

        return sprintf('<span class="%s">%s</span>', $colors[$priority] ?? 'badge bg-secondary', $priority);
    }

    private function renderSeverityBadge(string $severity): string
    {
        $colors = [
            'Minor' => 'badge btn-primary',
            'Moderate' => 'badge btn-primary',
            'Major' => 'badge btn-primary',
            'Critical' => 'badge bg-danger',
        ];

        return sprintf('<span class="%s">%s</span>', $colors[$severity] ?? 'badge bg-secondary', $severity);
    }

    private function renderCategoryBadge(string $category): string
    {
        $colors = [
            'UI' => 'badge btn-primary',
            'Functional' => 'badge btn-primary',
            'Performance' => 'badge btn-primary',
            'Security' => 'badge btn-danger',
            'Other' => 'badge btn-primary',
        ];

        return sprintf('<span class="%s">%s</span>', $colors[$category] ?? 'badge bg-secondary', $category);
    }

    private function renderStatusBadge(string $status): string
    {
        $colors = [
            'Open' => 'badge btn-primary',
            'In Progress' => 'badge btn-primary',
            'Resolved' => 'badge bg-success',
            'Closed' => 'badge btn-primary',
            'Reopened' => 'badge btn-danger',
        ];

        return sprintf('<span class="%s">%s</span>', $colors[$status] ?? 'badge bg-secondary', $status);
    }

    private function renderActionColumn(Bug $context, User|MasterEmployee $employee): string
    {
        $entityId = $context->getId();
        $isAdmin = $employee instanceof User && $this->security->isGranted('ROLE_ADMIN');
        $baseRoute = $isAdmin ? 'admin_bug' : 'bug';
        $editUrl = $this->router->generate("{$baseRoute}_edit", ['id' => $entityId]);
        $updateStatusUrl = $this->router->generate("{$baseRoute}_update_status", ['id' => $entityId]);
        $viewModalId = 'viewModal_' . $entityId;

        $isAssignedByCurrentEmployee = ($employee instanceof MasterEmployee && $context->getAssignedBy() === $employee) ||
            ($isAdmin && $context->getAssignedByAdmin() === $employee);
        $isAssignedToCurrentEmployee = $context->getAssignedTo() === $employee;

        $statusHistories = $context->getStatusHistory()->toArray();
        $statusHistoryTableRows = $this->generateStatusHistoryTableRows($statusHistories);

        $taskDescription = $context->getTaskDescription() ?? 'No description available.';
        $comments = $context->getComments() ?? 'No comments available.';
        $testerComments = $context->getTesterComments() ?? '';
        $statusForm = '';
        $statusFormClass = (!$isAssignedToCurrentEmployee) ? 'd-none' : '';

        if ($isAssignedToCurrentEmployee) {
            $statusForm = sprintf(
                '<form id="statusForm_%s" action="%s" method="POST" class="mt-3">
                <input type="hidden" name="_csrf_token" value="%s">
                <div class="form-group">
                    <label for="status_%s" class="form-label fw-bold"><i class="bs-fa fa fa-tasks mr-1"></i> Update Status</label>
                    <select name="status" id="status_%s" class="form-control">
                        <option value="Open" %s>Open</option>
                        <option value="In Progress" %s>In Progress</option>
                        <option value="Resolved" %s>Resolved</option>
                        <option value="Closed" %s>Closed</option>
                        <option value="Reopened" %s>Reopened</option>
                    </select>
                </div>
                <div class="form-group mt-3">
                    <label for="testerComments_%s" class="form-label fw-bold"><i class="bs-fa fa fa-comment mr-1"></i>Comments</label>
                    <textarea name="testerComments" id="testerComments_%s" class="form-control ckeditor">%s</textarea>
                </div>
                <button type="submit" class="btn btn-primary mt-2">Update</button>
            </form>',
                $entityId,
                $updateStatusUrl,
                $this->csrfTokenManager->getToken('update_status_' . $entityId)->getValue(),
                $entityId,
                $entityId,
                $context->getStatus()->value === 'Open' ? 'selected' : '',
                $context->getStatus()->value === 'In Progress' ? 'selected' : '',
                $context->getStatus()->value === 'Resolved' ? 'selected' : '',
                $context->getStatus()->value === 'Closed' ? 'selected' : '',
                $context->getStatus()->value === 'Reopened' ? 'selected' : '',
                $entityId,
                $entityId,
                htmlspecialchars($testerComments)
            );
        }

        $viewModalBody = sprintf(
            '
            <label for="" class="form-label fw-bold"><i class="bs-fa fa fa-tasks mr-1"></i>Task Description</label>
            <div class="task-description-container mb-3">%s</div>
            <label for="" class="form-label fw-bold"><i class="bs-fa fa fa-tasks mr-1"></i>Bugs Info</label>
            <div class="task-description-container mb-3">%s</div>
            <div class="status-form-container %s">%s</div>
            <div class="tester-comments-container mb-3">%s</div>
            <div class="status-history-container">
                <h5 class="font-weight-bold m-3">Status Change History</h5>
                <table class="table table-bordered table-striped status-history-table">
                    <thead>
                        <tr>
                            <th>Changed By</th>
                            <th>Status</th>
                            <th>Changed At</th>
                        </tr>
                    </thead>
                    <tbody>%s</tbody>
                </table>
            </div>',
            $taskDescription,
            $comments,
            $statusFormClass,
            $statusForm,
            $isAssignedByCurrentEmployee ? sprintf('<h5 class="font-weight-bold m-3"><i class="bs-fa fa fa-comment mr-1"></i>Comments</h5><div class="task-description-container mb-3">%s</div>', $testerComments) : '',
            $statusHistoryTableRows,
        );

        $viewFooterButtons = '<button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>';

        $viewModal = $this->modalboxHelper->renderModal(
            $viewModalId,
            'Bug Details: ' . htmlspecialchars($context->getTask()?->getTitle() ?? 'N/A'),
            $viewModalBody,
            $viewFooterButtons
        );

        $ajaxScript = sprintf(
            '<script>
                document.getElementById("statusForm_%s")?.addEventListener("submit", function(e) {
                    e.preventDefault();
                    const form = this;
                    const formData = new FormData(form);

                    fetch(form.action, {
                        method: "POST",
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            closeModal(document.getElementById("%s"));
                            if (recordsTable) {
                                recordsTable.then(dt => dt.ajax.reload(null, false));
                            }
                        } else {
                            alert("Error: " + (data.error || "Failed to update status."));
                        }
                    })
                    .catch(error => {
                        alert("Error: " + error.message);
                    });
                });

                function closeModal(modal) {
                    if (modal) {
                        if (typeof $ !== "undefined" && $.fn.modal) {
                            $(modal).modal("hide");
                        } else {
                            modal.classList.remove("show");
                            modal.style.display = "none";
                        }

                        document.body.classList.remove("modal-open");
                        const backdrop = document.querySelector(".modal-backdrop");
                        if (backdrop) {
                            backdrop.remove();
                        }
                    }
                }
                if (document.getElementById("testerComments_%s")) {
                    CKEDITOR.replace("testerComments_%s");
                }
            </script>',
            $entityId,
            $viewModalId,
            $entityId,
            $entityId
        );

        $actionButtons = '<div class="d-inline-flex gap-2">';
        $actionButtons .= sprintf(
            '<button type="button" class="bs-btn btn btn-primary btn-sm" style="margin: 2px;" data-toggle="modal" data-target="#%s" title="View Details">
                <i class="bs-fa fa fa-eye"></i>
            </button>',
            $viewModalId
        );

        if ($isAssignedByCurrentEmployee || $isAdmin) {
            $actionButtons .= sprintf(
                '<a href="%s" class="bs-btn btn btn-primary btn-sm" style="margin: 2px;" title="Edit Bug">
                <i class="bs-fa fa fa-edit"></i>
            </a>',
                $editUrl
            );
        }

        $actionButtons .= '</div>';

        return $actionButtons . $viewModal . $ajaxScript;
    }

    private function generateStatusHistoryTableRows(array $statusHistories): string
    {
        if (empty($statusHistories)) {
            return '<tr><td colspan="3">No status history available</td></tr>';
        }

        $rows = array_map(function ($history) {
            $changedBy = $history->getChangedByAdmin()?->getUsername() ?? $history->getChangedBy()?->getName() ?? 'Unknown';
            $changedAt = $this->timezoneService->formatDateTime($history->getChangedAt(), 'd-m-Y H:i:s');
            $status = htmlspecialchars($history->getStatus()->value);

            return "<tr><td>$changedBy</td><td>$status</td><td>$changedAt</td></tr>";
        }, $statusHistories);

        return implode('', $rows);
    }

    private function buildQuery(QueryBuilder $qb, MasterEmployee $employee, ?int $taskId = null, ?int $taskAssignmentId = null): void
    {
        $qb->select('b, p, t, ta, a')
            ->from(Bug::class, 'b')
            ->leftJoin('b.project', 'p')
            ->leftJoin('b.task', 't')
            ->leftJoin('b.taskAssignment', 'ta')
            ->leftJoin('b.assignedTo', 'a')
            ->where('b.assignedTo = :employee OR b.assignedBy = :employee')
            ->setParameter('employee', $employee);

        if ($taskId) {
            $qb->andWhere('b.task = :taskId')
                ->setParameter('taskId', $taskId);
        }

        if ($taskAssignmentId) {
            $qb->andWhere('b.taskAssignment = :taskAssignmentId')
                ->setParameter('taskAssignmentId', $taskAssignmentId);
        }
    }

    private function buildQueryAdmin(QueryBuilder $qb, User $user): void
    {
        $qb->select('b, p, t, a, ab')
            ->from(Bug::class, 'b')
            ->leftJoin('b.project', 'p')
            ->leftJoin('b.task', 't')
            ->leftJoin('b.assignedTo', 'a')
            ->leftJoin('b.assignedBy', 'ab')
            ->where('b.assignedByAdmin = :user')
            ->setParameter('user', $user);
    }

    private function buildBugsQuery(QueryBuilder $qb, MasterEmployee $employee, bool $isHistory): void
    {
        $qb->select('b, p, t, ta, a')
            ->from(Bug::class, 'b')
            ->leftJoin('b.project', 'p')
            ->leftJoin('b.task', 't')
            ->leftJoin('b.taskAssignment', 'ta')
            ->leftJoin('b.assignedTo', 'a')
            ->where('b.assignedTo = :employee')
            ->setParameter('employee', $employee);

        if ($isHistory) {
            $qb->andWhere('b.status = :status');
        } else {
            $qb->andWhere('b.status != :status');
        }

        $qb->setParameter('status', 'closed');
    }

    private function buildMyBugsQuery(QueryBuilder $qb, MasterEmployee $employee): void
    {
        $this->buildBugsQuery($qb, $employee, false);
    }

    private function buildMyBugsHistoryQuery(QueryBuilder $qb, MasterEmployee $employee): void
    {
        $this->buildBugsQuery($qb, $employee, true);
    }
}
?>