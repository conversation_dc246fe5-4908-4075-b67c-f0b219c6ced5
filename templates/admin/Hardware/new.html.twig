{% extends 'admin/dashboard/base.html.twig' %}

{% block body %}
    {% include 'admin/partials/breadcrumbs.html.twig' with {
        title: 'Hardware',
        breadcrumbs: [
            { 'label': 'Dashboard', 'url': url('admin_dashboard') },
            { 'label': 'Hardware', 'url': url('hardware') }
        ],
        active: mode == 'view' ? 'View Hardware' : (mode == 'edit' ? 'Edit Hardware' : 'Create Hardware')
    } %}

    <div class="content">
        <div class="animated fadeIn">
            <div class="row">
                <div class="col-lg-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <strong class="card-title">
                                {{ mode == 'view' ? 'View Hardware' : (mode == 'edit' ? 'Edit Hardware' : 'Create Hardware') }}
                            </strong>
                            <div class="d-flex align-items-center">
                                <a href="{{ path('hardware') }}" class="btn btn-sm btn-outline-secondary mr-2">
                                    <i class="fa fa-list mr-1"></i>Back to List
                                </a>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="hardware-form">
                                {% if form is defined %}
                                    {{ form_start(form, { 'attr': { 'class': 'needs-validation use-loader', 'novalidate': 'novalidate', 'id': 'hardware-form-element' } }) }}

                                    <!-- General Information Section -->
                                    <div class="form-section">
                                        <h5 class="section-header">
                                            <i class="fa fa-laptop mr-2"></i>General Information
                                        </h5>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.hardwareName, 'Hardware Name', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-laptop"></i></span>
                                                        </div>
                                                        {{ form_widget(form.hardwareName, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter hardware name', 'required': 'required', 'id': 'hardware_hardwareName' } }) }}
                                                    </div>
                                                    {{ form_errors(form.hardwareName) }}
                                                    <small class="form-text text-muted">Name for the hardware</small>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.hardwareType, 'Hardware Type', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-cogs"></i></span>
                                                        </div>
                                                        {{ form_widget(form.hardwareType, { 'attr': { 'class': 'form-control', 'required': 'required', 'id': 'hardware_hardwareType' } }) }}
                                                    </div>
                                                    {{ form_errors(form.hardwareType) }}
                                                    <small class="form-text text-muted">Select the type of hardware (e.g., Laptop, Desktop)</small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.employee, 'Assigned Employee', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-user"></i></span>
                                                        </div>
                                                        {{ form_widget(form.employee, { 'attr': { 'class': 'form-control', 'id': 'hardware_employee' } }) }}
                                                    </div>
                                                    {{ form_errors(form.employee) }}
                                                    <small class="form-text text-muted">Select the employee assigned to this hardware</small>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.description, 'Description', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-info-circle"></i></span>
                                                        </div>
                                                        {{ form_widget(form.description, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter description', 'id': 'hardware_description' } }) }}
                                                    </div>
                                                    {{ form_errors(form.description) }}
                                                    <small class="form-text text-muted">Brief description of the hardware</small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.showOnDashboard, 'Show On Dashboard', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-building"></i></span>
                                                        </div>
                                                        {{ form_widget(form.showOnDashboard, { 'attr': { 'class': 'form-control','required': 'required' } }) }}
                                                    </div>
                                                    {{ form_errors(form.showOnDashboard) }}
                                                    <small class="form-text text-muted">You want to show on dashboard this hardware</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Purchase Details Section -->
                                    <div class="form-section">
                                        <h5 class="section-header">
                                            <i class="fa fa-shopping-cart mr-2"></i>Purchase Details
                                        </h5>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.purchaseDate, 'Purchase Date', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-calendar"></i></span>
                                                        </div>
                                                        {{ form_widget(form.purchaseDate, {
                                                            'attr': {
                                                                'class': 'form-control datepicker',
                                                                'required': 'required',
                                                                'data-initial-date': hardware_form.purchaseDate is defined and hardware_form.purchaseDate ? hardware_form.purchaseDate|date('d-m-Y') : ''
                                                            }
                                                        }) }}
                                                    </div>
                                                    {{ form_errors(form.purchaseDate) }}
                                                    <small class="form-text text-muted">Date when the hardware was purchased</small>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.warrantyEndDate, 'Warranty End Date', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-calendar"></i></span>
                                                        </div>
                                                        {{ form_widget(form.warrantyEndDate, {
                                                            'attr': {
                                                                'class': 'form-control datepicker',
                                                                'required': 'required',
                                                                'data-initial-date': hardware_form.warrantyEndDate is defined and hardware_form.warrantyEndDate ? hardware_form.warrantyEndDate|date('d-m-Y') : ''
                                                            }
                                                        }) }}
                                                    </div>
                                                    {{ form_errors(form.warrantyEndDate) }}
                                                    <small class="form-text text-muted">Date when the hardware was purchased</small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.vendorName, 'Vendor Name', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-user"></i></span>
                                                        </div>
                                                        {{ form_widget(form.vendorName, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter vendor name', 'required': 'required', 'id': 'hardware_vendorName' } }) }}
                                                    </div>
                                                    {{ form_errors(form.vendorName) }}
                                                    <small class="form-text text-muted">Name of the vendor who supplied the hardware</small>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.manufacturerName, 'Manufacturer Name', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-industry"></i></span>
                                                        </div>
                                                        {{ form_widget(form.manufacturerName, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter manufacturer name', 'id': 'hardware_manufacturerName' } }) }}
                                                    </div>
                                                    {{ form_errors(form.manufacturerName) }}
                                                    <small class="form-text text-muted">Name of the hardware manufacturer</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Technical Specifications Section -->
                                    <div class="form-section">
                                        <h5 class="section-header">
                                            <i class="fa fa-cog mr-2"></i>Technical Specifications
                                        </h5>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.manufacturerModel, 'Manufacturer Model', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-tag"></i></span>
                                                        </div>
                                                        {{ form_widget(form.manufacturerModel, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter manufacturer model', 'id': 'hardware_manufacturerModel' } }) }}
                                                    </div>
                                                    {{ form_errors(form.manufacturerModel) }}
                                                    <small class="form-text text-muted">Model number of the hardware</small>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.manufacturerSerial, 'Manufacturer Serial', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-barcode"></i></span>
                                                        </div>
                                                        {{ form_widget(form.manufacturerSerial, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter manufacturer serial', 'id': 'hardware_manufacturerSerial' } }) }}
                                                    </div>
                                                    {{ form_errors(form.manufacturerSerial) }}
                                                    <small class="form-text text-muted">Serial number of the hardware</small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.ram, 'RAM', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-random"></i></span>
                                                        </div>
                                                        {{ form_widget(form.ram, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter RAM (e.g., 8GB)', 'id': 'hardware_ram' } }) }}
                                                    </div>
                                                    {{ form_errors(form.ram) }}
                                                    <small class="form-text text-muted">RAM capacity (if applicable)</small>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.rom, 'Storage', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-mercury"></i></span>
                                                        </div>
                                                        {{ form_widget(form.rom, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter storage (e.g., 256GB SSD)', 'id': 'hardware_rom' } }) }}
                                                    </div>
                                                    {{ form_errors(form.rom) }}
                                                    <small class="form-text text-muted">Storage capacity (if applicable)</small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.condition, 'Condition', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-check-circle"></i></span>
                                                        </div>
                                                        {{ form_widget(form.condition, { 'attr': { 'class': 'form-control', 'id': 'hardware_condition' } }) }}
                                                    </div>
                                                    {{ form_errors(form.condition) }}
                                                    <small class="form-text text-muted">Current condition of the hardware</small>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.pcName, 'PC Name', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-desktop"></i></span>
                                                        </div>
                                                        {{ form_widget(form.pcName, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter PC name', 'id': 'hardware_pcName' } }) }}
                                                    </div>
                                                    {{ form_errors(form.pcName) }}
                                                    <small class="form-text text-muted">Network name of the PC (if applicable)</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Network Information Section -->
                                    <div class="form-section">
                                        <h5 class="section-header">
                                            <i class="fa fa-network-wired mr-2"></i>Network Information
                                        </h5>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.ipAddress, 'IP Address', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-neuter"></i></span>
                                                        </div>
                                                        {{ form_widget(form.ipAddress, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter IP address', 'id': 'hardware_ipAddress' } }) }}
                                                    </div>
                                                    {{ form_errors(form.ipAddress) }}
                                                    <small class="form-text text-muted">IP address of the hardware (if applicable)</small>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.macAddress, 'MAC Address', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-address-book"></i></span>
                                                        </div>
                                                        {{ form_widget(form.macAddress, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter MAC address', 'id': 'hardware_macAddress' } }) }}
                                                    </div>
                                                    {{ form_errors(form.macAddress) }}
                                                    <small class="form-text text-muted">MAC address of the hardware (if applicable)</small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.location, 'Location', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-map-marker"></i></span>
                                                        </div>
                                                        {{ form_widget(form.location, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter location', 'id': 'hardware_location' } }) }}
                                                    </div>
                                                    {{ form_errors(form.location) }}
                                                    <small class="form-text text-muted">Physical location of the hardware</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="d-flex justify-content-end mt-4">
                                        <a href="{{ path('hardware') }}" class="btn btn-secondary mr-2">Cancel</a>
                                        <button type="submit" class="btn btn-primary pl-3 pr-3">
                                            {{ hardware.id is defined ? 'Update Hardware' : 'Create Hardware' }}
                                        </button>
                                    </div>

                                    {{ form_end(form) }}
                                {% else %}
                                    <!-- View Mode with Tabs -->
                                    <div class="profile-container">
                                        <div class="tabs">
                                            <div class="tab active" data-target="hardware-details">Hardware Details</div>
                                            <div class="tab" data-target="hardware-history">History</div>
                                        </div>

                                        <div class="tab-content active" id="hardware-details">
                                            <div class="form-section employee-details-section">
                                                <!-- General Information Section -->
                                                <div class="details-section">
                                                    <h5 class="section-header">
                                                        <i class="fa fa-laptop mr-2"></i>General Information
                                                    </h5>
                                                    <div class="details-container">
                                                        <div class="detail-row">
                                                            <div class="detail-item col-md-4"><strong class="detail-label">Hardware Name:</strong></div>
                                                            <div class="detail-item col-md-8">{{ hardware.hardwareName ?: 'N/A' }}</div>
                                                        </div>
                                                        <div class="detail-row">
                                                            <div class="detail-item col-md-4"><strong class="detail-label">Hardware Type:</strong></div>
                                                            <div class="detail-item col-md-8">{{ hardware.hardwareType ? hardware.hardwareType.name : 'N/A' }}</div>
                                                        </div>
                                                        <div class="detail-row">
                                                            <div class="detail-item col-md-4"><strong class="detail-label">Assigned Employee:</strong></div>
                                                            <div class="detail-item col-md-8">{{ hardware.employee ? hardware.employee.name : 'N/A' }}</div>
                                                        </div>
                                                        <div class="detail-row">
                                                            <div class="detail-item col-md-4"><strong class="detail-label">Description:</strong></div>
                                                            <div class="detail-item col-md-8">{{ hardware.description ?: 'N/A' }}</div>
                                                        </div>
                                                        <div class="detail-row">
                                                            <div class="detail-item col-md-4"><strong class="detail-label">Show On Dashboard:</strong></div>
                                                            <div class="detail-item col-md-8">{{ hardware.showOnDashboard ? 'Yes' : 'No' }}</div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Purchase Details Section -->
                                                <div class="details-section">
                                                    <h5 class="section-header">
                                                        <i class="fa fa-shopping-cart mr-2"></i>Purchase Details
                                                    </h5>
                                                    <div class="details-container">
                                                        <div class="detail-row">
                                                            <div class="detail-item col-md-4"><strong class="detail-label">Purchase Date:</strong></div>
                                                            <div class="detail-item col-md-8">{{ hardware.purchaseDate ? hardware.purchaseDate|date('d-m-Y') : 'N/A' }}</div>
                                                        </div>
                                                        <div class="detail-row">
                                                            <div class="detail-item col-md-4"><strong class="detail-label">Warranty End Date:</strong></div>
                                                            <div class="detail-item col-md-8">{{ hardware.warrantyEndDate ? hardware.warrantyEndDate|date('d-m-Y') : 'N/A' }}</div>
                                                        </div>
                                                        <div class="detail-row">
                                                            <div class="detail-item col-md-4"><strong class="detail-label">Vendor Name:</strong></div>
                                                            <div class="detail-item col-md-8">{{ hardware.vendorName ?: 'N/A' }}</div>
                                                        </div>
                                                        <div class="detail-row">
                                                            <div class="detail-item col-md-4"><strong class="detail-label">Manufacturer Name:</strong></div>
                                                            <div class="detail-item col-md-8">{{ hardware.manufacturerName ?: 'N/A' }}</div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Technical Specifications Section -->
                                                <div class="details-section">
                                                    <h5 class="section-header">
                                                        <i class="fa fa-cog mr-2"></i>Technical Specifications
                                                    </h5>
                                                    <div class="details-container">
                                                        <div class="detail-row">
                                                            <div class="detail-item col-md-4"><strong class="detail-label">Manufacturer Model:</strong></div>
                                                            <div class="detail-item col-md-8">{{ hardware.manufacturerModel ?: 'N/A' }}</div>
                                                        </div>
                                                        <div class="detail-row">
                                                            <div class="detail-item col-md-4"><strong class="detail-label">Manufacturer Serial:</strong></div>
                                                            <div class="detail-item col-md-8">{{ hardware.manufacturerSerial ?: 'N/A' }}</div>
                                                        </div>
                                                        <div class="detail-row">
                                                            <div class="detail-item col-md-4"><strong class="detail-label">RAM:</strong></div>
                                                            <div class="detail-item col-md-8">{{ hardware.ram ?: 'N/A' }}</div>
                                                        </div>
                                                        <div class="detail-row">
                                                            <div class="detail-item col-md-4"><strong class="detail-label">Storage:</strong></div>
                                                            <div class="detail-item col-md-8">{{ hardware.rom ?: 'N/A' }}</div>
                                                        </div>
                                                        <div class="detail-row">
                                                            <div class="detail-item col-md-4"><strong class="detail-label">Condition:</strong></div>
                                                            <div class="detail-item col-md-8">{{ hardware.condition ?: 'N/A' }}</div>
                                                        </div>
                                                        <div class="detail-row">
                                                            <div class="detail-item col-md-4"><strong class="detail-label">PC Name:</strong></div>
                                                            <div class="detail-item col-md-8">{{ hardware.pcName ?: 'N/A' }}</div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="details-section">
                                                    <h5 class="section-header">
                                                        <i class="fa fa-wifi mr-2"></i>Network Information
                                                    </h5>
                                                    <div class="details-container">
                                                        <div class="detail-row">
                                                            <div class="detail-item col-md-4"><strong class="detail-label">IP Address:</strong></div>
                                                            <div class="detail-item col-md-8">{{ hardware.ipAddress ?: 'N/A' }}</div>
                                                        </div>
                                                        <div class="detail-row">
                                                            <div class="detail-item col-md-4"><strong class="detail-label">MAC Address:</strong></div>
                                                            <div class="detail-item col-md-8">{{ hardware.macAddress ?: 'N/A' }}</div>
                                                        </div>
                                                        <div class="detail-row">
                                                            <div class="detail-item col-md-4"><strong class="detail-label">Location:</strong></div>
                                                            <div class="detail-item col-md-8">{{ hardware.location ?: 'N/A' }}</div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="tab-content" id="hardware-history">
                                            <div class="form-section employee-details-section">
                                                <h5 class="section-header">
                                                    <i class="fa fa-history mr-2"></i>Hardware History
                                                </h5>
                                                {% if history is defined and history|length > 0 %}
                                                    <div class="history-table">
                                                        <div class="table-responsive-lg">
                                                        <table class="table table-bordered">
                                                            <thead>
                                                            <tr class="text-center">
                                                                <th>Changed By</th>
                                                                <th>Change Type</th>
                                                                <th>Changed At</th>
                                                                <th>Old Data</th>
                                                                <th>New Data</th>
{#                                                                <th>Changes</th>#}
                                                            </tr>
                                                            </thead>
                                                            <tbody>
                                                            {% for entry in history %}
                                                                {% set oldData = entry.oldData is not null ? entry.oldData : {} %}
                                                                {% set newData = entry.newData is not null ? entry.newData : {} %}
                                                                <tr class="text-center">
                                                                    <td>{{ entry.changedBy ?: 'N/A' }}</td>
                                                                    <td>{{ entry.changeType ?: 'N/A' }}</td>
                                                                    <td>{{ entry.changedAt ? entry.changedAt|custom_date : 'N/A' }}</td>
                                                                    <td>
                                                                        {% if oldData is not empty %}
                                                                            <table class="nested-table">
                                                                                {% for key, value in oldData %}
                                                                                    <tr>
                                                                                        <td><strong>{{ key|capitalize }}:</strong></td>
                                                                                        <td>
                                                                                            {% if key == 'description' %}
                                                                                                {% set description = value ? value : '-' %}
                                                                                                {% set maxLength = 25 %}
                                                                                                {% if description|length > maxLength %}
                                                                                                    <span title="{{ description|e('html_attr') }}">{{ (description|slice(0, maxLength) ~ '...')|e }}</span>
                                                                                                {% else %}
                                                                                                    {{ description|e }}
                                                                                                {% endif %}
                                                                                            {% else %}
                                                                                                {{ value == false ? 'N/A' : value }}
                                                                                            {% endif %}
                                                                                        </td>
                                                                                    </tr>
                                                                                {% endfor %}
                                                                            </table>
                                                                        {% else %}
                                                                            N/A
                                                                        {% endif %}
                                                                    </td>
                                                                    <td>
                                                                        {% if newData is not empty %}
                                                                            <table class="nested-table">
                                                                                {% for key, newValue in newData %}
                                                                                    {% set oldValue = oldData[key] is defined ? oldData[key] : null %}
                                                                                    {% if oldValue != newValue %}
                                                                                        <tr>
                                                                                            <td><strong>{{ key|capitalize }}:</strong></td>
                                                                                            <td>
                                                                                                {% if oldValue is not null %}
                                                                                                    {% if key == 'description' %}
                                                                                                        {% set description = oldValue ? oldValue : '-' %}
                                                                                                        {% set maxLength = 25 %}
                                                                                                        {% if description|length > maxLength %}
                                                                                                            <span style="color: red; text-decoration: line-through;" title="{{ description|e('html_attr') }}">{{ (description|slice(0, maxLength) ~ '...')|e }}</span>
                                                                                                        {% else %}
                                                                                                            <span style="color: red; text-decoration: line-through;">{{ description|e }}</span>
                                                                                                        {% endif %}
                                                                                                    {% else %}
                                                                                                        <span style="color: red; text-decoration: line-through;">{{ oldValue == false ? 'N/A' : oldValue }}</span>
                                                                                                    {% endif %}
                                                                                                    →
                                                                                                {% endif %}
                                                                                                {% if key == 'description' %}
                                                                                                    {% set description = newValue ? newValue : '-' %}
                                                                                                    {% set maxLength = 25 %}
                                                                                                    {% if description|length > maxLength %}
                                                                                                        <span style="color: green;" title="{{ description|e('html_attr') }}">{{ (description|slice(0, maxLength) ~ '...')|e }}</span>
                                                                                                    {% else %}
                                                                                                        <span style="color: green;">{{ description|e }}</span>
                                                                                                    {% endif %}
                                                                                                {% else %}
                                                                                                    <span style="color: green;">{{ newValue == false ? 'N/A' : newValue }}</span>
                                                                                                {% endif %}
                                                                                            </td>
                                                                                        </tr>
                                                                                    {% endif %}
                                                                                {% endfor %}
                                                                            </table>
                                                                        {% else %}
                                                                            N/A
                                                                        {% endif %}
                                                                    </td>

                                                                    {#                                                                    <td>#}
{#                                                                        {% if oldData is not empty or newData is not empty %}#}
{#                                                                            <table class="nested-table">#}
{#                                                                                {% for key, newValue in newData %}#}
{#                                                                                    {% set oldValue = oldData[key] is defined ? oldData[key] : null %}#}
{#                                                                                    {% if oldValue != newValue and (oldValue is not null or newValue is not null) %}#}
{#                                                                                        <tr>#}
{#                                                                                            <td><strong>{{ key|capitalize }}:</strong></td>#}
{#                                                                                            <td>#}
{#                                                                                                {% if oldValue is not null %}<span style="color: red; text-decoration: line-through;">{{ oldValue }}</span> → {% endif %}#}
{#                                                                                                <span style="color: green;">{{ newValue == false ? 'N/A' : newValue }}</span>#}
{#                                                                                            </td>#}
{#                                                                                        </tr>#}
{#                                                                                    {% endif %}#}
{#                                                                                {% endfor %}#}
{#                                                                                {% for key, oldValue in oldData %}#}
{#                                                                                    {% if key not in newData %}#}
{#                                                                                        <tr>#}
{#                                                                                            <td><strong>{{ key|capitalize }}:</strong></td>#}
{#                                                                                            <td><span style="color: red; text-decoration: line-through;">{{ oldValue }}</span> → <span style="color: gray;">Removed</span></td>#}
{#                                                                                        </tr>#}
{#                                                                                    {% endif %}#}
{#                                                                                {% endfor %}#}
{#                                                                            </table>#}
{#                                                                        {% else %}#}
{#                                                                            No changes#}
{#                                                                        {% endif %}#}
{#                                                                    </td>#}
                                                                </tr>
                                                            {% endfor %}
                                                            </tbody>
                                                        </table>
                                                        </div>
                                                    </div>
                                                {% else %}
                                                    <p>No history available for this hardware.</p>
                                                {% endif %}
                                            </div>
                                        </div>

                                        <div class="action-footer mt-4">
                                            <a href="{{ path('hardware') }}" class="btn btn-secondary mr-2">
                                                Back to List
                                            </a>
                                            <a href="{{ path('hardware_edit', {'id': hardware.id}) }}" class="btn btn-primary pr-3 pl-3" style="padding-top: 10px;">
                                                Edit Hardware
                                            </a>
                                        </div>
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="clearfix"></div>
    <link rel="stylesheet" href="{{ asset('assets/css/Error/error.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/AdminForm/Form.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/Hardware/hardware.css') }}">
    <script src="{{ asset('assets/js/datatables.min.js') }}"></script>
    <script src="{{ asset('bundles/datatables/js/datatables.js') }}"></script>
    <script src="{{ asset('assets/js/moment.min.js') }}"></script>
    <script src="{{ asset('assets/js/daterangepicker.min.js') }}"></script>
    <script src="{{ asset('assets/js/commonDatepicker.js') }}"></script>
    <script src="{{ asset('assets/js/admin/hardware.js') }}"></script>
    <script src="{{ asset('assets/js/admin/tab.js') }}"></script>
{% endblock %}