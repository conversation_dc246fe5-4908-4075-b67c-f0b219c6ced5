<?php

namespace App\Command;

use App\Service\Admin\EmployeeHoursTrackerService;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'app:track-extra-hours',
    description: 'Track extra hours worked beyond standard hours to fulfill remaining hours'
)]
class TrackEmployeeHoursCommand extends Command
{
    public function __construct(
        private readonly EmployeeHoursTrackerService $employeeHoursTrackerService,
    ) {
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $io->title('Starting extra hours tracking process');

        try {
            $result = $this->employeeHoursTrackerService->processRemainingHours($io);

            if ($result['processedCount'] === 0) {
                $io->info('No pending remaining hours to track');
            } else {
                $io->success(sprintf('Successfully processed %d employee records', $result['processedCount']));
            }

            return Command::SUCCESS;

        } catch (\Exception $e) {
            $io->error('An error occurred: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }
}