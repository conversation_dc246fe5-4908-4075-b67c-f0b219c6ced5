<?php

namespace App\Service\Admin;

use App\Entity\EmployeeArrears;
use App\Entity\SalaryStructure;
use App\Helper\ModalboxHelper;
use App\Helper\SettingHelper;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\QueryBuilder;
use Omines\DataTablesBundle\Adapter\Doctrine\ORMAdapter;
use Omines\DataTablesBundle\Column\NumberColumn;
use Omines\DataTablesBundle\Column\TextColumn;
use Omines\DataTablesBundle\DataTable;
use Omines\DataTablesBundle\DataTableFactory;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Request;
use App\Traits\ExceptionLoggerTrait;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Security\Csrf\CsrfTokenManagerInterface;

class EmployeeArrearsService
{
    use ExceptionLoggerTrait;

    public function __construct(
        private readonly DataTableFactory $dataTableFactory,
        private readonly ParameterBagInterface $parameterBag,
        private readonly RouterInterface $router,
        private readonly CsrfTokenManagerInterface $csrfTokenManager,
        private readonly ModalboxHelper $modalboxHelper,
        private readonly Security $security,
        private readonly AttendenceTableService $attendenceTableService,
        private readonly SettingHelper $settingHelper,
        private readonly EntityManagerInterface $entityManager,
        private readonly AttendenceTableService $attendanceTableService,
    ) {
        $this->initializeLogger($parameterBag, 'employee_arrears_service');
    }

    public function createEmployeeArrearsTable(Request $request): DataTable
    {
        $dataTable = $this->dataTableFactory->create();

        foreach ($this->getColumns() as $key => $column) {
            $dataTable->add($key, $column['className'], array_filter($column, fn($k) => $k !== 'className', ARRAY_FILTER_USE_KEY));
        }

        return $dataTable->createAdapter(ORMAdapter::class, [
            'entity' => EmployeeArrears::class,
            'query' => fn(QueryBuilder $qb) => $this->buildQuery($qb),
        ])
            ->addOrderBy('year', 'desc')
            ->addOrderBy('month', 'desc')
            ->handleRequest($request);
    }

    private function getColumns(): array
    {
        return [
            'employee' => [
                'label' => 'Employee',
                'field' => 'employee.name',
                'className' => TextColumn::class,
            ],
            'month' => [
                'label' => 'Month',
                'className' => TextColumn::class,
                'render' => fn($value, $context) => self::MONTH_MAP[$context->getMonth()] ?? '',
            ],
            'year' => [
                'label' => 'Year',
                'field' => 'e.year',
                'className' => NumberColumn::class,
            ],
            'oldSalary' => [
                'label' => 'Previous Salary',
                'field' => 'e.oldSalary',
                'className' => NumberColumn::class,
            ],
            'newSalary' => [
                'label' => 'New Salary',
                'field' => 'e.newSalary',
                'className' => NumberColumn::class,
            ],
            'arrearsDays' => [
                'label' => 'Arrears Days',
                'field' => 'e.arrearsDays',
                'className' => NumberColumn::class,
            ],
            'totalArrears' => [
                'label' => 'Total Arrears',
                'field' => 'e.totalArrears',
                'className' => NumberColumn::class,
            ],
            'id' => [
                'label' => 'Actions',
                'field' => 'e.id',
                'className' => NumberColumn::class,
                'orderable' => false,
                'render' => fn($value, $context) => $this->renderActionColumn($context),
            ],
        ];
    }

    private const MONTH_MAP = [
        1 => 'January',
        2 => 'February',
        3 => 'March',
        4 => 'April',
        5 => 'May',
        6 => 'June',
        7 => 'July',
        8 => 'August',
        9 => 'September',
        10 => 'October',
        11 => 'November',
        12 => 'December',
    ];

    private function renderActionColumn($context): string
    {
        $arrears = $context;
        $entityId = $arrears->getId();
        $editUrl = $this->router->generate('admin_employee_arrears_edit', ['id' => $entityId]);
        $deleteUrl = $this->router->generate('admin_employee_arrears_delete', ['id' => $entityId]);
        $csrfToken = $this->csrfTokenManager->getToken('delete_employee_arrears_' . $entityId)->getValue();

        $modalId = 'deleteModal_' . $entityId;
        $modalBody = 'Are you sure you want to delete this arrears record?<br><br>' .
            '<strong>Warning:</strong> This action cannot be undone.';

        $footerButtons = sprintf(
            '<button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>' .
            '<button type="button" class="btn btn-primary delete-arrears-btn" data-delete="true" data-url="%s" data-token="%s">Delete</button>',
            $deleteUrl,
            $csrfToken
        );

        $deleteModal = $this->modalboxHelper->renderModal(
            $modalId,
            'Confirm Arrears Deletion',
            $modalBody,
            $footerButtons
        );

        return sprintf(
            '<div class="d-inline-flex gap-2">
                <a href="%s" class="bs-btn btn btn-primary btn-sm bs-m" title="Edit">
                    <i class="bs-fa fa fa-edit"></i>
                </a>
                <button type="button" class="bs-btn btn btn-primary btn-sm bs-m open-delete-arrears-modal" data-toggle="modal" data-target="#%s" title="Delete">
                    <i class="bs-fa fa fa-trash"></i>
                </button>
            </div>%s',
            $editUrl,
            $modalId,
            $deleteModal
        );
    }

    private function buildQuery(QueryBuilder $qb): void
    {
        $qb->select('e')
            ->from(EmployeeArrears::class, 'e')
            ->leftJoin('e.employee', 'employee');
    }

    /**
     * @throws \DateMalformedStringException
     */
    public function fetchSalaryInfo(int $employeeId, int $year, int $month): array
    {
        $salaryStructures = $this->entityManager->getRepository(SalaryStructure::class)
            ->findAllForEmployeeMonth($employeeId, $year, $month);

        $oldSalary = 0.0;
        $newSalary = 0.0;
        $changeDate = null;

        if (count($salaryStructures) > 1) {
            $oldSalary = (float)$salaryStructures[0]->getCtc();
            $newSalary = (float)$salaryStructures[1]->getCtc();
            $changeDate = $salaryStructures[1]->getStartDate();
        } elseif (count($salaryStructures) === 1) {
            $oldSalary = $newSalary = (float)$salaryStructures[0]->getCtc();
            $changeDate = $salaryStructures[0]->getStartDate();
        }

        $totalWorkingDays = $this->attendanceTableService->getTotalWorkingDays($year, $month);
        $arrearsDays = $this->attendanceTableService->getWorkingDaysAfterDate($employeeId, $year, $month, $changeDate);

        return [
            'oldSalary' => $oldSalary,
            'newSalary' => $newSalary,
            'totalWorkingDays' => $totalWorkingDays,
            'arrearsDays' => $arrearsDays,
        ];
    }
} 