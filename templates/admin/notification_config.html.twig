{% extends 'admin/dashboard/base.html.twig' %}

{% block body %}
    {% include 'admin/partials/breadcrumbs.html.twig' with {
        breadcrumbs: [
            { 'label': 'Dashboard', 'url': url('admin_dashboard') },
        ],
        active: 'Task Mailer'
    } %}

    <div class="content">
        <div class="animated fadeIn">
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <strong class="card-title">Task Mailer</strong>
                        </div>
                        <div class="card-body">
                            <form id="update-settings" novalidate>
                                <div class="tabs">
                                    <div class="tab active" data-target="task-notifications">Task Notifications</div>
                                    <div class="tab" data-target="bug-notifications">Bug Notifications</div>
                                    <div class="tab" data-target="project-notifications">Project Notifications</div>
                                </div>
                                <div class="tab-content active" id="task-notifications">
                                    <div class="form-group mb-4 p-2 bg-light rounded">
                                        <div class="custom-control custom-switch-wrapper d-flex align-items-center">
                                            <div class="custom-switch">
                                                <input
                                                        type="checkbox"
                                                        class="custom-switch-input"
                                                        id="TaskNotificationEnabled"
                                                        name="TaskNotificationEnabled"
                                                        {% if settings['TaskNotificationEnabled']|default(false) %}checked{% endif %}>
                                                <label class="custom-switch-label"
                                                       for="TaskNotificationEnabled"></label>
                                            </div>
                                            <div class="ml-3">
                                                <label class="form-label font-weight-bold"
                                                       for="TaskNotificationEnabled">Task Notifications</label>
                                                <p class="text-muted small mb-0">Enable task assignment
                                                    notifications</p>
                                            </div>
                                        </div>
                                        <div class="invalid-feedback mt-2" id="task-notification-error"></div>
                                    </div>

                                    <div class="form-group mb-4 p-2 bg-light rounded">
                                        <div class="custom-control custom-switch-wrapper d-flex align-items-center">
                                            <div class="custom-switch">
                                                <input
                                                        type="checkbox"
                                                        class="custom-switch-input"
                                                        id="TaskAssignmentUpdateEnabled"
                                                        name="TaskAssignmentUpdateEnabled"
                                                        {% if settings['TaskAssignmentUpdateEnabled']|default(false) %}checked{% endif %}>
                                                <label class="custom-switch-label"
                                                       for="TaskAssignmentUpdateEnabled"></label>
                                            </div>
                                            <div class="ml-3">
                                                <label class="form-label font-weight-bold"
                                                       for="TaskAssignmentUpdateEnabled">Task Assignment Update
                                                    Notifications</label>
                                                <p class="text-muted small mb-0">Enable task assignment update
                                                    notifications</p>
                                            </div>
                                        </div>
                                        <div class="invalid-feedback" id="task-assignment-update-error"></div>
                                    </div>

                                    <div class="form-group mb-4 p-2 bg-light rounded">
                                        <div class="custom-control custom-switch-wrapper d-flex align-items-center">
                                            <div class="custom-switch">
                                                <input
                                                        type="checkbox"
                                                        class="custom-switch-input"
                                                        id="TaskStatusUpdateEnabled"
                                                        name="TaskStatusUpdateEnabled"
                                                        {% if settings['TaskStatusUpdateEnabled']|default(false) %}checked{% endif %}>
                                                <label class="custom-switch-label"
                                                       for="TaskStatusUpdateEnabled"></label>
                                            </div>
                                            <div class="ml-3">
                                                <label class="form-label font-weight-bold"
                                                       for="TaskStatusUpdateEnabled">Task Status Update
                                                    Notifications</label>
                                                <p class="text-muted small mb-0">Enable task status update
                                                    notifications</p>
                                            </div>
                                        </div>
                                        <div class="invalid-feedback" id="task-status-update-error"></div>
                                    </div>

                                    <div class="form-group mb-4 p-2 bg-light rounded">
                                        <div class="custom-control custom-switch-wrapper d-flex align-items-center">
                                            <div class="custom-switch">
                                                <input
                                                        type="checkbox"
                                                        class="custom-switch-input"
                                                        id="TaskDescriptionUpdateEnabled"
                                                        name="TaskDescriptionUpdateEnabled"
                                                        {% if settings['TaskDescriptionUpdateEnabled']|default(false) %}checked{% endif %}>
                                                <label class="custom-switch-label"
                                                       for="TaskDescriptionUpdateEnabled"></label>
                                            </div>
                                            <div class="ml-3">
                                                <label class="form-label font-weight-bold"
                                                       for="TaskDescriptionUpdateEnabled">Task Description Update
                                                    Notifications</label>
                                                <p class="text-muted small mb-0">Enable task description update
                                                    notifications</p>
                                            </div>
                                        </div>
                                        <div class="invalid-feedback" id="task-description-update-error"></div>
                                    </div>
                                </div>

                                <div class="tab-content" id="bug-notifications">
                                    <div class="form-group mb-4 p-2 bg-light rounded">
                                        <div class="custom-control custom-switch-wrapper d-flex align-items-center">
                                            <div class="custom-switch">
                                                <input
                                                        type="checkbox"
                                                        class="custom-switch-input"
                                                        id="BugNotificationEnabled"
                                                        name="BugNotificationEnabled"
                                                        {% if settings['BugNotificationEnabled']|default(false) %}checked{% endif %}>
                                                <label class="custom-switch-label" for="BugNotificationEnabled"></label>
                                            </div>
                                            <div class="ml-3">
                                                <label class="form-label font-weight-bold" for="BugNotificationEnabled">Bug
                                                    Notifications</label>
                                                <p class="text-muted small mb-0">Enable bug assignment notifications</p>
                                            </div>
                                        </div>
                                        <div class="invalid-feedback" id="bug-notification-error"></div>
                                    </div>

                                    <div class="form-group mb-4 p-2 bg-light rounded">
                                        <div class="custom-control custom-switch-wrapper d-flex align-items-center">
                                            <div class="custom-switch">
                                                <input
                                                        type="checkbox"
                                                        class="custom-switch-input"
                                                        id="BugStatusUpdateEnabled"
                                                        name="BugStatusUpdateEnabled"
                                                        {% if settings['BugStatusUpdateEnabled']|default(false) %}checked{% endif %}>
                                                <label class="custom-switch-label" for="BugStatusUpdateEnabled"></label>
                                            </div>
                                            <div class="ml-3">
                                                <label class="form-label font-weight-bold" for="BugStatusUpdateEnabled">Bug
                                                    Status Update Notifications</label>
                                                <p class="text-muted small mb-0">Enable bug update notifications</p>
                                            </div>
                                        </div>
                                        <div class="invalid-feedback" id="bug-status-update-error"></div>
                                    </div>

                                    <div class="form-group mb-4 p-2 bg-light rounded">
                                        <div class="custom-control custom-switch-wrapper d-flex align-items-center">
                                            <div class="custom-switch">
                                                <input
                                                        type="checkbox"
                                                        class="custom-switch-input"
                                                        id="BugDescriptionUpdateEnabled"
                                                        name="BugDescriptionUpdateEnabled"
                                                        {% if settings['BugDescriptionUpdateEnabled']|default(false) %}checked{% endif %}>
                                                <label class="custom-switch-label"
                                                       for="BugDescriptionUpdateEnabled"></label>
                                            </div>
                                            <div class="ml-3">
                                                <label class="form-label font-weight-bold"
                                                       for="BugDescriptionUpdateEnabled">Bug Description Update
                                                    Notifications</label>
                                                <p class="text-muted small mb-0">Enable bug description update
                                                    notifications</p>
                                            </div>
                                        </div>
                                        <div class="invalid-feedback" id="bug-description-update-error"></div>
                                    </div>
                                </div>
                                <div class="tab-content" id="project-notifications">
                                    <div class="form-group p-2 bg-light rounded">
                                        <div class="custom-control custom-switch-wrapper d-flex align-items-center">
                                            <div class="custom-switch">
                                                <input
                                                        type="checkbox"
                                                        class="custom-switch-input"
                                                        id="ProjectNotificationEnabled"
                                                        name="ProjectNotificationEnabled"
                                                        {% if settings['ProjectNotificationEnabled']|default(false) %}checked{% endif %}>
                                                <label class="custom-switch-label"
                                                       for="ProjectNotificationEnabled"></label>
                                            </div>
                                            <div class="ml-3">
                                                <label class="form-label font-weight-bold"
                                                       for="ProjectNotificationEnabled">Project Notifications</label>
                                                <p class="text-muted small mb-0">Enable project assignment and update
                                                    notifications</p>
                                            </div>
                                        </div>
                                        <div class="invalid-feedback mt-2" id="project-notification-error"></div>
                                    </div>
                                </div>
                                <div>
                                    <button
                                            id="email-config"
                                            type="submit"
                                            class="btn btn-block btn-primary">
                                        Update Settings
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="clearfix"></div>
    <link rel="stylesheet" href="{{ asset('assets/css/Form/notification.css') }}">
    <script src="{{ asset('assets/js/admin/notificationconfig.js') }}"></script>
    <link rel="stylesheet" href="{{ asset('assets/css/Hardware/hardware.css') }}">
    <script>
        const csrfToken = '{{ csrf_token('notification_update') }}';
    </script>
    <script src="{{ asset('assets/js/admin/tab.js') }}"></script>
{% endblock %}