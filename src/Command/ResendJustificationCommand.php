<?php

namespace App\Command;

use App\Repository\EmployeeNotificationRepository;
use App\Service\Admin\EmployeeNotificationService;
use App\Traits\ExceptionLoggerTrait;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use App\Helper\SettingHelper;

class ResendJustificationCommand extends Command
{
    use ExceptionLoggerTrait;

    protected static $defaultName = 'app:resend-justification-link';

    public function __construct(
        private readonly EmployeeNotificationRepository $notificationRepository,
        private readonly EmployeeNotificationService    $notificationService,
        ParameterBagInterface                           $parameterBag,
        private readonly SettingHelper                  $settingHelper,
    )
    {
        parent::__construct('app:resend-justification-link');
        $this->initializeLogger($parameterBag, 'resend_justification_link');
    }

    /**
     * Configures the command.
     */
    protected function configure(): void
    {
        $this
            ->setDescription('Resend justification links to employees who have not justified their hours within 7 days.');
        $this->addOption(
            'date',
            'd',
            InputOption::VALUE_OPTIONAL,
            'Specify the date to send reminder notifications (default: 7 days ago).'
        );
    }

    /**
     * Executes the command.
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        try {
            $inputDate = $input->getOption('date');
            $reminderDays = $this->settingHelper->getSettingConfiguration()['resend'];
            $offsetDays = (int)$reminderDays;
            $referenceDate = $inputDate
                ? new \DateTimeImmutable($inputDate)
                : (new \DateTimeImmutable())->modify("-{$offsetDays} days");
            $output->writeln(sprintf('Fetching notifications pending since: %s', $referenceDate->format('Y-m-d')));
            $pendingNotifications = $this->notificationRepository->findPendingJustifications($referenceDate);

            if (empty($pendingNotifications)) {
                $output->writeln('No pending notifications found for the given date.');
                return Command::SUCCESS;
            }

            $count = 0;
            foreach ($pendingNotifications as $notification) {
                $this->notificationService->resendJustificationLink($notification);
                $count++;
            }

            $output->writeln(sprintf('Successfully resent %d justification links.', $count));
            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->log(
                'Error in ResendJustificationCommand',
                [
                    'error_message' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                    'trace' => $e->getTraceAsString(),
                ]
            );
            $output->writeln('An error occurred: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }
}
