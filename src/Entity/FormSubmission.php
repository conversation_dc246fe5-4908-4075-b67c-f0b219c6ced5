<?php

namespace App\Entity;

use App\Repository\FormSubmissionRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: FormSubmissionRepository::class)]
class FormSubmission
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private ?int $id = null;

    #[ORM\ManyToOne(targetEntity: FormTemplate::class)]
    #[ORM\JoinColumn(nullable: false)]
    private FormTemplate $template;

    #[ORM\ManyToOne(targetEntity: MasterEmployee::class)]
    #[ORM\JoinColumn(nullable: false)]
    private MasterEmployee $employee;

    #[ORM\ManyToOne(targetEntity: MasterEmployee::class)]
    #[ORM\JoinColumn(nullable: true)]
    private ?MasterEmployee $employeeReviewer = null;

    #[ORM\ManyToOne(targetEntity: User::class)]
    #[ORM\JoinColumn(nullable: true)]
    private ?User $userReviewer = null;

    #[ORM\Column(type: 'boolean')]
    private bool $status = true;

    #[ORM\Column(type: 'string', length: 50)]
    private string $reviewPeriod;

    #[ORM\Column(type: 'datetime', nullable: true)]
    private ?\DateTimeInterface $submissionDate = null;

    #[ORM\Column(type: 'datetime', nullable: true)]
    private ?\DateTimeInterface $dueDate = null;

    #[ORM\Column(type: 'boolean', options: ['default' => false])]
    private bool $reminderSent = false;

    #[ORM\Column(type: 'text', nullable: true)]
    private ?string $comments = null;

    #[ORM\Column(type: 'float', nullable: true)]
    private ?string $rating = null;

    #[ORM\Column(type: 'datetime')]
    private \DateTimeInterface $createdAt;

    #[ORM\Column(type: 'datetime')]
    private \DateTimeInterface $updatedAt;

    #[ORM\OneToMany(targetEntity: FormResponse::class, mappedBy: 'submission', orphanRemoval: true)]
    private Collection $responses;

    #[ORM\Column(type: 'float', nullable: true)]
    private ?float $ActiveMinRating = null;

    #[ORM\Column(type: 'float', nullable: true)]
    private ?float $ActiveSecRating = null;


    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): void
    {
        $this->id = $id;
    }

    public function getTemplate(): FormTemplate
    {
        return $this->template;
    }

    public function setTemplate(FormTemplate $template): void
    {
        $this->template = $template;
    }

    public function getEmployee(): MasterEmployee
    {
        return $this->employee;
    }

    public function setEmployee(MasterEmployee $employee): void
    {
        $this->employee = $employee;
    }



    public function isStatus(): bool
    {
        return $this->status;
    }

    public function setStatus(bool $status): void
    {
        $this->status = $status;
    }


    public function getReviewPeriod(): string
    {
        return $this->reviewPeriod;
    }

    public function setReviewPeriod(string $reviewPeriod): void
    {
        $this->reviewPeriod = $reviewPeriod;
    }

    public function getSubmissionDate(): ?\DateTimeInterface
    {
        return $this->submissionDate;
    }

    public function setSubmissionDate(?\DateTimeInterface $submissionDate): void
    {
        $this->submissionDate = $submissionDate;
    }

    public function getDueDate(): ?\DateTimeInterface
    {
        return $this->dueDate;
    }

    public function setDueDate(?\DateTimeInterface $dueDate): void
    {
        $this->dueDate = $dueDate;
    }

    public function isReminderSent(): bool
    {
        return $this->reminderSent;
    }

    public function setReminderSent(bool $reminderSent): void
    {
        $this->reminderSent = $reminderSent;
    }

    public function getComments(): ?string
    {
        return $this->comments;
    }

    public function setComments(?string $comments): void
    {
        $this->comments = $comments;
    }

    public function getCreatedAt(): \DateTimeInterface
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTimeInterface $createdAt): void
    {
        $this->createdAt = $createdAt;
    }

    public function getUpdatedAt(): \DateTimeInterface
    {
        return $this->updatedAt;
    }

    public function setUpdatedAt(\DateTimeInterface $updatedAt): void
    {
        $this->updatedAt = $updatedAt;
    }

    public function getResponses(): Collection
    {
        return $this->responses;
    }

    public function setResponses(Collection $responses): void
    {
        $this->responses = $responses;
    }

    public function __construct()
    {
        $this->responses = new ArrayCollection();
        $this->createdAt = new \DateTime();
        $this->updatedAt = new \DateTime();
    }

    public function getRating(): ?string
    {
        return $this->rating;
    }

    public function setRating(?string $rating): void
    {
        $this->rating = $rating;
    }

    public function getActiveMinRating(): ?float
    {
        return $this->ActiveMinRating;
    }

    public function setActiveMinRating(?float $ActiveMinRating): void
    {
        $this->ActiveMinRating = $ActiveMinRating;
    }

    public function getActiveSecRating(): ?float
    {
        return $this->ActiveSecRating;
    }

    public function setActiveSecRating(?float $ActiveSecRating): void
    {
        $this->ActiveSecRating = $ActiveSecRating;
    }

    public function getEmployeeReviewer(): ?MasterEmployee
    {
        return $this->employeeReviewer;
    }

    public function setEmployeeReviewer(?MasterEmployee $employeeReviewer): void
    {
        $this->employeeReviewer = $employeeReviewer;
    }

    public function getUserReviewer(): ?User
    {
        return $this->userReviewer;
    }

    public function setUserReviewer(?User $userReviewer): void
    {
        $this->userReviewer = $userReviewer;
    }
}
