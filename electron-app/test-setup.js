#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

console.log('🚀 Brainee HRM Desktop - Setup Test\n');

// Test 1: Check if all required files exist
console.log('📁 Checking file structure...');
const requiredFiles = [
    'package.json',
    'src/main.js',
    'src/preload.js',
    'src/database.js',
    'src/offline-service.js',
    'renderer/login.html',
    'renderer/dashboard.html',
    'renderer/css/login.css',
    'renderer/css/dashboard.css',
    'renderer/js/login.js',
    'renderer/js/dashboard.js'
];

let missingFiles = [];
requiredFiles.forEach(file => {
    if (!fs.existsSync(path.join(__dirname, file))) {
        missingFiles.push(file);
    }
});

if (missingFiles.length > 0) {
    console.log('❌ Missing files:');
    missingFiles.forEach(file => console.log(`   - ${file}`));
    process.exit(1);
} else {
    console.log('✅ All required files present');
}

// Test 2: Check package.json dependencies
console.log('\n📦 Checking dependencies...');
const packageJson = JSON.parse(fs.readFileSync(path.join(__dirname, 'package.json'), 'utf8'));
const requiredDeps = [
    'electron',
    'sqlite3',
    'axios',
    'auto-launch',
    'electron-store',
    'node-machine-id'
];

let missingDeps = [];
requiredDeps.forEach(dep => {
    if (!packageJson.dependencies[dep] && !packageJson.devDependencies[dep]) {
        missingDeps.push(dep);
    }
});

if (missingDeps.length > 0) {
    console.log('❌ Missing dependencies:');
    missingDeps.forEach(dep => console.log(`   - ${dep}`));
    console.log('\nRun: npm install');
    process.exit(1);
} else {
    console.log('✅ All dependencies listed in package.json');
}

// Test 3: Check if node_modules exists
console.log('\n🔍 Checking node_modules...');
if (!fs.existsSync(path.join(__dirname, 'node_modules'))) {
    console.log('❌ node_modules not found. Run: npm install');
    process.exit(1);
} else {
    console.log('✅ node_modules directory exists');
}

// Test 4: Test database initialization
console.log('\n🗄️  Testing database initialization...');
try {
    // Set test environment
    process.env.NODE_ENV = 'test';

    const DatabaseService = require('./src/database');
    const dbService = new DatabaseService();

    console.log(`   Database path: ${dbService.dbPath}`);

    // This will create a test database
    dbService.initialize().then(() => {
        console.log('✅ Database service can be initialized');

        // Wait a moment for tables to be created, then test basic operations
        return new Promise(resolve => setTimeout(resolve, 100));
    }).then(() => {
        // Test basic database operations
        return dbService.run('INSERT INTO app_settings (key, value) VALUES (?, ?)', ['test_key', 'test_value']);
    }).then(() => {
        console.log('✅ Database operations working');

        // Clean up test database
        return dbService.close();
    }).then(() => {
        // Remove test database file
        const testDbPath = dbService.dbPath;
        if (fs.existsSync(testDbPath)) {
            fs.unlinkSync(testDbPath);
            console.log('✅ Test database cleaned up');
        }

        runBackendTests();
    }).catch(error => {
        console.log('❌ Database initialization failed:', error.message);
        console.log('   This might be due to SQLite3 compilation issues');
        console.log('   Try running: npm rebuild sqlite3');
        runBackendTests(); // Continue with other tests
    });
} catch (error) {
    console.log('❌ Database service import failed:', error.message);
    console.log('   This might be due to missing SQLite3 module');
    console.log('   Try running: npm install sqlite3');
    runBackendTests(); // Continue with other tests
}

// Test 5: Check backend connectivity
function runBackendTests() {
    console.log('\n🌐 Testing backend connectivity...');
    
    const axios = require('axios');
    const backendUrl = 'http://localhost:8000';
    
    axios.get(`${backendUrl}/api/health`, { timeout: 5000 })
        .then(response => {
            if (response.data && response.data.status === 'ok') {
                console.log('✅ Backend is accessible and responding');
                runFinalTests();
            } else {
                console.log('⚠️  Backend responded but with unexpected data');
                console.log('   Make sure the Symfony backend is properly configured');
                runFinalTests();
            }
        })
        .catch(error => {
            console.log('⚠️  Backend not accessible:', error.message);
            console.log('   This is normal if the Symfony server is not running');
            console.log('   The app will work in offline mode');
            runFinalTests();
        });
}

// Test 6: Final validation
function runFinalTests() {
    console.log('\n🔧 Running final validation...');
    
    // Check if Electron can be started
    console.log('   Testing Electron startup...');
    
    const electronPath = path.join(__dirname, 'node_modules', '.bin', 'electron');
    const isWindows = process.platform === 'win32';
    const electronCmd = isWindows ? electronPath + '.cmd' : electronPath;
    
    if (!fs.existsSync(electronCmd)) {
        console.log('❌ Electron executable not found');
        console.log('   Run: npm install');
        process.exit(1);
    }
    
    console.log('✅ Electron executable found');
    
    // Test auto-launch capability
    console.log('   Testing auto-launch capability...');
    try {
        const AutoLaunch = require('auto-launch');
        const autoLauncher = new AutoLaunch({
            name: 'Test App',
            path: '/test/path'
        });
        console.log('✅ Auto-launch module working');
    } catch (error) {
        console.log('❌ Auto-launch module failed:', error.message);
    }
    
    // Summary
    console.log('\n📋 Setup Test Summary:');
    console.log('✅ File structure complete');
    console.log('✅ Dependencies installed');
    console.log('✅ Database service functional');
    console.log('✅ Electron ready to run');
    
    console.log('\n🎉 Setup test completed successfully!');
    console.log('\nNext steps:');
    console.log('1. Start the Symfony backend: symfony server:start');
    console.log('2. Run the Electron app: npm start');
    console.log('3. Test login with a valid employee account');
    
    console.log('\n📝 Notes:');
    console.log('- The app will auto-start with your OS after first login');
    console.log('- Users cannot logout once logged in (as per requirements)');
    console.log('- Offline mode is available when backend is not accessible');
    console.log('- Data is stored locally in SQLite database');
}

// Handle errors
process.on('uncaughtException', (error) => {
    console.log('\n❌ Unexpected error:', error.message);
    process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
    console.log('\n❌ Unhandled rejection:', reason);
    process.exit(1);
});
