{% extends 'admin/dashboard/base.html.twig' %}

{% block body %}
    {% include 'admin/partials/breadcrumbs.html.twig' with {
        breadcrumbs: [
            { 'label': 'Dashboard', 'url': url('admin_dashboard') },
        ],
        active: 'Notification'
    } %}

    <div class="content">
        <div class="animated fadeIn">
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <strong class="card-title">Notification</strong>
                        </div>
                        <div class="card-body">
                            <form id="update-settings" novalidate>
                                <div class="form-group">
                                    <label for="Remaining" class="control-label required">Justification Limit:</label>
                                    <input
                                            id="Remaining"
                                            name="Remaining"
                                            type="number"
                                            class="form-control"
                                            value="{{ settings['Remaining']|default('') }}"
                                            min="0"
                                            step="1"
                                            required>
                                    <small class="form-text text-muted">Number of remaining items before notification</small>
                                    <div class="invalid-feedback" id="remaining-error"></div>
                                </div>
                                <div class="form-group">
                                    <label for="Disapprove" class="control-label required">Disapproval Reason:</label>
                                    <input
                                            id="Disapprove"
                                            name="Disapprove"
                                            type="text"
                                            class="form-control"
                                            value="{{ settings['Disapprove']|default('') }}"
                                            required>
                                    <small class="form-text text-muted">Default reason for disapproval notifications</small>
                                    <div class="invalid-feedback" id="disapprove-error"></div>
                                </div>
                                <div class="form-group">
                                    <label for="ReminderDays" class="control-label required">Justification Reminder In Days:</label>
                                    <input
                                            id="ReminderDays"
                                            name="ReminderDays"
                                            type="number"
                                            class="form-control"
                                            value="{{ settings['ReminderDays']|default('') }}"
                                            min="1"
                                            step="1"
                                            required>
                                    <small class="form-text text-muted">Days before sending reminder notifications</small>
                                    <div class="invalid-feedback" id="reminder-days-error"></div>
                                </div>
                                <div class="form-group">
                                    <label for="WorkingHours" class="control-label required">Total Working Hours:</label>
                                    <input
                                            id="WorkingHours"
                                            name="WorkingHours"
                                            type="number"
                                            class="form-control"
                                            value="{{ settings['WorkingHours']|default('') }}"
                                            min="1"
                                            step="0.5"
                                            required>
                                    <small class="form-text text-muted">Total working hours per day</small>
                                    <div class="invalid-feedback" id="working-hours-error"></div>
                                </div>
                                <div class="form-group">
                                    <label for="MinimumHours" class="control-label required">Minimum Hours For Notification:</label>
                                    <input
                                            id="MinimumHours"
                                            name="MinimumHours"
                                            type="number"
                                            class="form-control"
                                            value="{{ settings['MinimumHours']|default('') }}"
                                            min="0"
                                            step="0.5"
                                            required>
                                    <small class="form-text text-muted">Minimum hours triggering notifications</small>
                                    <div class="invalid-feedback" id="minimum-hours-error"></div>
                                </div>
                                <div>
                                    <button
                                            id="email-config"
                                            type="submit"
                                            class="btn btn-block btn-primary"
                                            disabled>
                                        Update Settings
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="clearfix"></div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script src="{{ asset('assets/js/admin/reportconfig.js') }}"></script>
    <script>
        const csrfToken = '{{ csrf_token('setting_update') }}';
    </script>

{% endblock %}