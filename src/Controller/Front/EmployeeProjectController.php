<?php

namespace App\Controller\Front;

use App\Entity\MasterEmployee;
use App\Service\Common\EmployeeActivityService;
use App\Service\Common\ProjectService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/employee')]
class EmployeeProjectController extends AbstractController
{
    public function __construct(
        private readonly ProjectService $projectService,
        private readonly EmployeeActivityService $employeeActivityService,
        private readonly EntityManagerInterface $entityManager,
    ) {
    }

    /**
     * @throws \Exception
     */
    #[Route(path:'/projects', name: 'employee_projects', methods: ['GET', 'POST'])]
    public function index(Request $request): Response
    {
        $employee = $this->getUser();
        if (!$employee instanceof MasterEmployee || !$employee->isTeamLeader()) {
            throw $this->createAccessDeniedException('This can only be accessed by a team leader.');
        }
        $table = $this->projectService->createEmployeeProjectTable($request, $employee);

        if ($table->isCallback()) {
            return $table->getResponse();
        }

        return $this->render('Front/project/index.html.twig', [
            'datatable' => $table,
            'employee' => $employee,
        ]);
    }

    /**
     * @throws \Exception
     */
    #[Route(path:'/other/activity', name: 'employee_other_activity', methods: ['GET', 'POST'])]
    public function employeeActivity(Request $request): Response
    {
        $employee = $this->getUser();
        if (!$employee instanceof MasterEmployee || !$employee->isTeamLeader()) {
            throw $this->createAccessDeniedException('This can only be accessed by a team leader.');
        }

        $table = $this->employeeActivityService->createEmployeeAssinedTeamleder($request, $employee);

        if ($table->isCallback()) {
            return $table->getResponse();
        }

        return $this->render('Front/employeeActivity/employeeActivity.html.twig', [
            'datatable' => $table,
        ]);
    }

    #[Route(path: '/toggle-self-task', name: 'employee_toggle_self_task', methods: ['POST'])]
    public function toggleSelfTask(Request $request): JsonResponse
    {
        $employee = $this->getUser();
        if (!$employee instanceof MasterEmployee || !$employee->isTeamLeader()) {
            return new JsonResponse(['success' => false, 'message' => 'Access denied'], 403);
        }

        $allowSelfTask = $request->request->get('allowSelfTask', false);
        $employee->setAllowSelfTask((bool) $allowSelfTask);

        $this->entityManager->flush();

        return new JsonResponse([
            'success' => true,
            'message' => 'Self task assignment setting updated successfully',
            'allowSelfTask' => $employee->isAllowSelfTask()
        ]);
    }
}