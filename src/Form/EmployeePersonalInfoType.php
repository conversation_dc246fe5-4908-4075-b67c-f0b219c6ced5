<?php
namespace App\Form;

use App\Entity\MasterEmployee;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\CallbackTransformer;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\TelType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\NotBlank;

class EmployeePersonalInfoType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('personalPhoneNumber', TelType::class, ['label' => 'Personal Phone Number', 'required' => true])
            ->add('alternativePhoneNumber', TelType::class, ['label' => 'Alternative Phone Number', 'required' => false])
            ->add('currentAddress', TextType::class, ['label' => 'Current Address', 'required' => true])
            ->add('permanentAddress', TextType::class, ['label' => 'Permanent Address', 'required' => false])
            ->add('birthDate', TextType::class, [
                'required' => true,
                'label' => 'Birth Date',
                'attr' => [
                    'class' => 'form-control datepicker',
                    'placeholder' => 'Select date',
                    'required' => 'required',
                ],
                'constraints' => [new NotBlank()],
            ])
            ->add('gender', ChoiceType::class, [
                'label' => 'Gender',
                'choices' => ['Male' => 'male', 'Female' => 'female', 'Other' => 'other'],
                'placeholder' => 'Select gender',
                'required' => true,
                'autocomplete'=>true,
            ])
            ->add('maritalStatus', ChoiceType::class, [
                'label' => 'Marital Status',
                'choices' => ['Single' => 'single', 'Married' => 'married', 'Divorced' => 'divorced', 'Widowed' => 'widowed'],
                'placeholder' => 'Select Marital Status',
                'required' => false,
                'autocomplete'=>true,
            ]);

        $builder->get('birthDate')
            ->addModelTransformer(new CallbackTransformer(
                fn($dateAsObject) => $dateAsObject instanceof \DateTimeInterface ? $dateAsObject->format('d-m-Y') : '',
                fn($dateAsString) => $dateAsString ? \DateTime::createFromFormat('d-m-Y', $dateAsString) : null
            ));
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults(['data_class' => MasterEmployee::class]);
    }
}
