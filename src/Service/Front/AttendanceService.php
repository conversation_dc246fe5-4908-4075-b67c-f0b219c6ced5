<?php

namespace App\Service\Front;

use App\Entity\Attendance;
use App\Entity\MasterEmployee;
use App\Helper\TimeHelper;
use App\Repository\AttendanceRepository;
use App\Repository\EmployeeHoursRepository;
use App\Repository\HolidayRepository;
use App\Service\TimezoneService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\JsonResponse;

readonly class AttendanceService
{
    public function __construct(
        private EntityManagerInterface  $entityManager,
        private AttendanceRepository    $attendanceRepository,
        private EmployeeHoursRepository $employeeHoursRepository,
        private TimeHelper              $timeHelper,
        private TimezoneService         $timezoneService,
        private HolidayRepository       $holidayRepository,
    ) {
    }

    public function validateUser($user): ?JsonResponse
    {
        if (!$user instanceof MasterEmployee) {
            return new JsonResponse(['error' => 'Unauthorized'], 403);
        }
        return null;
    }

    public function punchAttendance(MasterEmployee $employee, string $status): JsonResponse
    {
        $validStatuses = ['Office In', 'Going out for Lunch', 'Back from Lunch', 'Leaving for the Day'];
        if (!in_array($status, $validStatuses, true)) {
            return new JsonResponse(['error' => 'Invalid status'], 400);
        }

        $existingPunch = $this->attendanceRepository->findTodayPunch($employee, $status);
        if ($existingPunch) {
            return new JsonResponse(['error' => "You've already punched for '$status' today"], 400);
        }

        try {
            $attendance = new Attendance();
            $attendance->setEmployee($employee);
            $attendance->setTimestamp(new \DateTimeImmutable());
            $attendance->setStatus($status);

            if ($status === 'Leaving for the Day') {
                $officeIn = $this->attendanceRepository->findTodayOfficeInPunch($employee);
                if ($officeIn) {
                    $attendance->setExitTimestamp(new \DateTimeImmutable());
                }
            }

            $this->entityManager->persist($attendance);
            $this->entityManager->flush();

            return new JsonResponse([
                'success' => true,
                'message' => 'Attendance recorded successfully',
                'timestamp' => $attendance->getTimestamp()->format('Y-m-d H:i:s'),
            ]);
        } catch (\Exception $e) {
            return new JsonResponse([
                'error' => 'Failed to record attendance: ' . $e->getMessage()
            ], 500);
        }
    }

    public function getTodayStatus(MasterEmployee $employee): JsonResponse
    {
        $todayAttendance = $this->attendanceRepository->findTodayAttendance($employee);
        $lastStatus = null;
        $canPunch = [];

        if (empty($todayAttendance)) {
            $canPunch = ['Office In'];
        } else {
            $lastRecord = $todayAttendance[0];
            $lastStatus = $lastRecord->getStatus();

            switch ($lastStatus) {
                case 'Office In':
                    $canPunch = ['Going out for Lunch', 'Leaving for the Day'];
                    break;
                case 'Going out for Lunch':
                    $canPunch = ['Back from Lunch'];
                    break;
                case 'Back from Lunch':
                    $canPunch = ['Going out for Lunch', 'Leaving for the Day'];
                    break;
                case 'Leaving for the Day':
                    $canPunch = [];
                    break;
            }
        }

        return new JsonResponse([
            'lastStatus' => $lastStatus,
            'canPunch' => $canPunch,
            'todayRecords' => array_map(static function($record) {
                return [
                    'status' => $record->getStatus(),
                    'timestamp' => $record->getTimestamp()->format('H:i:s'),
                ];
            }, $todayAttendance)
        ]);
    }

    public function hasPunchedToday(MasterEmployee $employee): bool
    {
        return $this->attendanceRepository->hasPunchedOfficeInToday($employee);
    }


    public function getWeeklyStats(MasterEmployee $employee): JsonResponse
    {
        try {
            $weeklyStats = $this->calculateWeeklyStats($employee);
            return new JsonResponse($weeklyStats);
        } catch (\Exception $e) {
            return new JsonResponse(['error' => 'Failed to fetch weekly stats: ' . $e->getMessage()], 500);
        }
    }

    public function getAttendanceCalendarData(MasterEmployee $employee, ?\DateTime $startDate = null, ?\DateTime $endDate = null): array
    {
        if (!$startDate) {
            $startDate = new \DateTime('first day of this month 00:00:00');
        }
        if (!$endDate) {
            $endDate = new \DateTime('last day of this month 23:59:59');
        }

        $attendanceRecords = $this->attendanceRepository->findByEmployeeAndDateRange(
            $employee,
            $startDate,
            $endDate
        );

        $events = [];

        $holidays = $this->holidayRepository->findByDateRange($startDate, $endDate);
        foreach ($holidays as $holiday) {
            $dateStr = $holiday->getDate()->format('Y-m-d');
            $events[] = [
                'id' => 'holiday_' . $holiday->getId(),
                'title' => $holiday->getName(),
                'start' => $dateStr,
                'color' => '#f78739',
                'extendedProps' => [
                    'isHoliday' => true,
                    'description' => $holiday->getDescription(),
                    'attendanceCount' => 0,
                    'status' => 'Holiday',
                    'hoursWorked' => 0,
                    'hasBreaks' => false
                ]
            ];
        }
        $attendanceByDate = [];
        foreach ($attendanceRecords as $record) {
            $date = $record->getTimestamp()->format('Y-m-d');
            if (!isset($attendanceByDate[$date])) {
                $attendanceByDate[$date] = [];
            }
            $attendanceByDate[$date][] = $record;
        }

        foreach ($attendanceByDate as $date => $dayAttendance) {
            $hasOfficeIn = false;
            $hasLeavingDay = false;
            $totalBreaks = 0;

            foreach ($dayAttendance as $record) {
                if ($record->getStatus() === 'Office In') {
                    $hasOfficeIn = true;
                }
                if ($record->getStatus() === 'Leaving for the Day') {
                    $hasLeavingDay = true;
                }
                if (in_array($record->getStatus(), ['Going out for Lunch', 'Back from Lunch'])) {
                    $totalBreaks++;
                }
            }

            $status = 'Present';
            $color = '#28a745';

            if (!$hasOfficeIn) {
                $status = 'Incomplete';
                $color = '#ffc107';
            } elseif (!$hasLeavingDay) {
                $status = 'In Progress';
                $color = '#17a2b8';
            }

            $hoursWorked = 0;
            if (isset($hoursByDate[$date])) {
                $hoursWorked = $hoursByDate[$date]->getTotalHours() ?? 0;
            }

            $events[] = [
                'id' => $date,
                'title' => $status,
                'start' => $date,
                'color' => $color,
                'extendedProps' => [
                    'isHoliday' => false,
                    'attendanceCount' => count($dayAttendance),
                    'status' => $status,
                    'hoursWorked' => $hoursWorked,
                    'hasBreaks' => $totalBreaks > 0
                ]
            ];
        }

        $currentDate = clone $startDate;
        while ($currentDate <= $endDate && $currentDate < new \DateTime('today')) {
            $dateStr = $currentDate->format('Y-m-d');

            if (!isset($attendanceByDate[$dateStr]) && $currentDate->format('N') <= 5) {
                $hasHoliday = array_filter($holidays, fn($h) => $h->getDate()->format('Y-m-d') === $dateStr);
                if (empty($hasHoliday)) {
                    $events[] = [
                        'id' => $dateStr,
                        'title' => 'Absent',
                        'start' => $dateStr,
                        'color' => '#dc3545',
                        'extendedProps' => [
                            'isHoliday' => false,
                            'attendanceCount' => 0,
                            'status' => 'Absent',
                            'hoursWorked' => 0,
                            'hasBreaks' => false
                        ]
                    ];
                }
            }
            $currentDate->add(new \DateInterval('P1D'));
        }

        return [
            'events' => $events,
            'attendanceByDate' => $attendanceByDate,
        ];
    }

    public function getDateAttendanceDetails(MasterEmployee $employee, string $date): JsonResponse
    {
        try {
            $dateStart = new \DateTime($date . ' 00:00:00');
            $dateEnd = new \DateTime($date . ' 23:59:59');

            $attendanceRecords = $this->attendanceRepository->createQueryBuilder('a')
                ->where('a.employee = :employee')
                ->andWhere('a.timestamp BETWEEN :start AND :end')
                ->setParameter('employee', $employee)
                ->setParameter('start', $dateStart)
                ->setParameter('end', $dateEnd)
                ->orderBy('a.timestamp', 'ASC')
                ->getQuery()
                ->getResult();

            $employeeHours = $this->employeeHoursRepository->findByMasterEmployeeAndDateRange(
                $employee,
                $dateStart,
                $dateEnd
            );

            $details = [];
            $status = 'Absent';
            $officeInTime = null;
            $leavingTime = null;
            $breaks = [];

            if (!empty($attendanceRecords)) {
                $status = 'Present';

                foreach ($attendanceRecords as $record) {
                    $localTime = $this->timezoneService->convertToLocalTime($record->getTimestamp());

                    $details[] = [
                        'status' => $record->getStatus(),
                        'time' => $localTime->format('H:i:s'),
                        'timestamp' => $localTime->format('Y-m-d H:i:s')
                    ];

                    if ($record->getStatus() === 'Office In') {
                        $officeInTime = $localTime;
                    } elseif ($record->getStatus() === 'Leaving for the Day') {
                        $leavingTime = $localTime;
                    } elseif (in_array($record->getStatus(), ['Going out for Lunch', 'Back from Lunch'])) {
                        $breaks[] = [
                            'status' => $record->getStatus(),
                            'time' => $localTime->format('H:i:s')
                        ];
                    }
                }

                if (!$leavingTime) {
                    $status = 'In Progress';
                }
            }

            $totalHours = 0;
            if (!empty($employeeHours)) {
                $hourRecord = reset($employeeHours);
                $hoursWorked = $hourRecord->getTotalHours() ?? 0.0;
                $totalHours += $hoursWorked;
            }

            $workDuration = null;
            if ($officeInTime && $leavingTime) {
                $interval = $officeInTime->diff($leavingTime);
                $workDuration = $interval->format('%H:%I:%S');
            }

            return new JsonResponse([
                'date' => $date,
                'status' => $status,
                'totalHours' => $this->timeHelper->convertDecimalToTime($totalHours),
                'workDuration' => $workDuration,
                'officeInTime' => $officeInTime?->format('H:i:s'),
                'leavingTime' => $leavingTime?->format('H:i:s'),
                'breaks' => $breaks,
                'details' => $details,
                'attendanceCount' => count($attendanceRecords)
            ]);
        } catch (\Exception $e) {
            return new JsonResponse([
                'error' => 'Failed to fetch attendance details: ' . $e->getMessage()
            ], 500);
        }
    }


    private function calculateWeeklyStats(MasterEmployee $employee): array
    {
        $startOfWeek = new \DateTime('monday this week');
        $endOfWeek = new \DateTime('friday this week 23:59:59');

        $weeklyData = [];
        $totalHours = 0;

        $attendanceRecords = $this->attendanceRepository->findByEmployeeAndDateRange(
            $employee,
            $startOfWeek,
            $endOfWeek
        );

        $employeeHours = $this->employeeHoursRepository->findByMasterEmployeeAndDateRange(
            $employee,
            $startOfWeek,
            $endOfWeek
        );

        for ($i = 0; $i < 5; $i++) {
            $currentDate = clone $startOfWeek;
            $currentDate->add(new \DateInterval("P{$i}D"));

            $dayName = $currentDate->format('D');
            $dateString = $currentDate->format('Y-m-d');

            $dayAttendance = array_filter($attendanceRecords, static function($record) use ($currentDate) {
                return $record->getTimestamp()->format('Y-m-d') === $currentDate->format('Y-m-d');
            });

            $dayHours = array_filter($employeeHours, static function($hours) use ($currentDate) {
                return $hours->getReportDate() &&
                    $hours->getReportDate()->format('Y-m-d') === $currentDate->format('Y-m-d');
            });

            $hoursWorked = 0.0;
            if (!empty($dayHours)) {
                $hourRecord = reset($dayHours);
                $hoursWorked = $hourRecord->getTotalHours() ?? 0.0;
                $totalHours += $hoursWorked;
            }
            $isPresent = !empty($dayAttendance);
            $isPast = $currentDate < (new \DateTime())->setTime(0, 0, 0);
            $status = $isPresent ? 'P' : ($isPast ? 'A' : '-');
            $weeklyData[] = [
                'day' => $dayName,
                'date' => $dateString,
                'status' => $status,
                'hours' => $this->timeHelper->convertDecimalToTimeFormat($hoursWorked),
                'present' => $isPresent,
                'attendance_count' => count($dayAttendance)
            ];
        }

        return [
            'weeklyData' => $weeklyData,
            'totalHoursFormatted' => $this->timeHelper->convertDecimalToTime($totalHours),
        ];
    }
}