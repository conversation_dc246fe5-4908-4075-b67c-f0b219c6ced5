<?php

namespace App\Controller\Api;

use App\Entity\ScreenshotLog;
use App\Repository\ScreenshotLogRepository;
use App\Repository\MasterEmployeeRepository;
use App\Service\ScreenshotFileOrganizer;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

#[Route('/api/screenshots', name: 'api_screenshots_')]
class ScreenshotController extends AbstractController
{
    public function __construct(
        private ScreenshotLogRepository $screenshotLogRepository,
        private MasterEmployeeRepository $employeeRepository,
        private EntityManagerInterface $entityManager,
        private ScreenshotFileOrganizer $screenshotOrganizer
    ) {}

    #[Route('/daily-count', name: 'daily_count', methods: ['GET'])]
    public function getDailyCount(Request $request): JsonResponse
    {
        try {
            $employeeId = $request->query->get('employee_id');
            $date = $request->query->get('date', date('Y-m-d'));
            
            if (!$employeeId) {
                return new JsonResponse([
                    'success' => false,
                    'error' => 'Employee ID is required'
                ], 400);
            }

            $startDate = new \DateTime($date . ' 00:00:00');
            $endDate = new \DateTime($date . ' 23:59:59');
            
            $screenshots = $this->screenshotLogRepository->findByEmployeeAndDateRange(
                $employeeId, 
                $startDate, 
                $endDate
            );
            
            return new JsonResponse([
                'success' => true,
                'data' => [
                    'count' => count($screenshots),
                    'date' => $date
                ]
            ]);
            
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => 'Failed to get daily count',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    #[Route('/recent', name: 'recent', methods: ['GET'])]
    public function getRecentScreenshots(Request $request): JsonResponse
    {
        try {
            $employeeId = $request->query->get('employee_id');
            $limit = (int) $request->query->get('limit', 10);
            
            if (!$employeeId) {
                return new JsonResponse([
                    'success' => false,
                    'error' => 'Employee ID is required'
                ], 400);
            }

            $endDate = new \DateTime();
            $startDate = new \DateTime('-7 days'); // Last 7 days
            
            $screenshots = $this->screenshotLogRepository->findByEmployeeAndDateRange(
                $employeeId, 
                $startDate, 
                $endDate
            );
            
            // Limit results
            $screenshots = array_slice($screenshots, 0, $limit);
            
            $formattedScreenshots = [];
            foreach ($screenshots as $screenshot) {
                $formattedScreenshots[] = [
                    'id' => $screenshot->getId(),
                    'captured_at' => $screenshot->getCapturedAt()->format('c'),
                    'capture_type' => $screenshot->getCaptureType(),
                    'file_name' => $screenshot->getFileName(),
                    'file_size' => $screenshot->getFileSize(),
                    'resolution' => $screenshot->getResolution(),
                    'active_application' => $screenshot->getActiveApplication(),
                    'active_window' => $screenshot->getActiveWindow()
                ];
            }
            
            return new JsonResponse([
                'success' => true,
                'data' => $formattedScreenshots
            ]);
            
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => 'Failed to get recent screenshots',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    #[Route('/sync', name: 'sync', methods: ['POST'])]
    public function syncScreenshots(Request $request): JsonResponse
    {
        try {
            $data = json_decode($request->getContent(), true);

            if (!$data || !isset($data['screenshots'])) {
                return new JsonResponse([
                    'success' => false,
                    'error' => 'Invalid data format'
                ], 400);
            }

            $syncedCount = 0;
            $errors = [];

            foreach ($data['screenshots'] as $screenshotData) {
                try {
                    // Validate required fields
                    if (!isset($screenshotData['employee_id'], $screenshotData['captured_at'], $screenshotData['capture_type'])) {
                        $errors[] = 'Missing required fields in screenshot data';
                        continue;
                    }

                    // Find the employee
                    $employee = $this->employeeRepository->find($screenshotData['employee_id']);
                    if (!$employee) {
                        $errors[] = "Employee not found: {$screenshotData['employee_id']}";
                        continue;
                    }

                    // Create new ScreenshotLog entity
                    $screenshotLog = new ScreenshotLog();
                    $screenshotLog->setEmployee($employee);
                    $screenshotLog->setCapturedAt(new \DateTime($screenshotData['captured_at']));
                    $screenshotLog->setCaptureType($screenshotData['capture_type']);

                    if (isset($screenshotData['file_path'])) {
                        $screenshotLog->setFilePath($screenshotData['file_path']);
                    }

                    if (isset($screenshotData['file_name'])) {
                        $screenshotLog->setFileName($screenshotData['file_name']);
                    }

                    if (isset($screenshotData['file_size'])) {
                        $screenshotLog->setFileSize($screenshotData['file_size']);
                    }

                    if (isset($screenshotData['resolution'])) {
                        $screenshotLog->setResolution($screenshotData['resolution']);
                    }

                    if (isset($screenshotData['device_id'])) {
                        $screenshotLog->setDeviceId($screenshotData['device_id']);
                    }

                    if (isset($screenshotData['active_application'])) {
                        $screenshotLog->setActiveApplication($screenshotData['active_application']);
                    }

                    if (isset($screenshotData['active_window'])) {
                        $screenshotLog->setActiveWindow($screenshotData['active_window']);
                    }

                    if (isset($screenshotData['metadata'])) {
                        $screenshotLog->setMetadata($screenshotData['metadata']);
                    }

                    $screenshotLog->setIsSynced(true);
                    $screenshotLog->setIsProcessed(false);
                    $screenshotLog->setCreatedAt(new \DateTime());

                    // Persist to database
                    $this->entityManager->persist($screenshotLog);
                    $syncedCount++;

                } catch (\Exception $e) {
                    $errors[] = $e->getMessage();
                }
            }

            // Flush all changes to database
            if ($syncedCount > 0) {
                $this->entityManager->flush();

                // Automatically organize screenshots after sync
                try {
                    $this->screenshotOrganizer->organizeScreenshots();
                } catch (\Exception $e) {
                    // Log error but don't fail the sync
                    error_log('Screenshot organizer error: ' . $e->getMessage());
                }
            }

            return new JsonResponse([
                'success' => true,
                'data' => [
                    'synced_count' => $syncedCount,
                    'errors' => $errors
                ]
            ]);
            
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => 'Failed to sync screenshots',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    #[Route('/upload', name: 'upload', methods: ['POST'])]
    public function uploadScreenshot(Request $request): JsonResponse
    {
        try {
            // Handle file upload
            $uploadedFile = $request->files->get('screenshot');
            $metadata = $request->request->all();
            
            if (!$uploadedFile) {
                return new JsonResponse([
                    'success' => false,
                    'error' => 'No screenshot file provided'
                ], 400);
            }

            // Here you would save the file and create a ScreenshotLog entity
            // This is a simplified response
            
            return new JsonResponse([
                'success' => true,
                'data' => [
                    'message' => 'Screenshot uploaded successfully',
                    'file_name' => $uploadedFile->getClientOriginalName(),
                    'file_size' => $uploadedFile->getSize()
                ]
            ]);
            
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => 'Failed to upload screenshot',
                'message' => $e->getMessage()
            ], 500);
        }
    }
}
