<!DOCTYPE html>
<html>
<head>
    <title>Justify Your Hours</title>
    <link rel="icon" type="image/x-icon" href="{{ asset('images/Brainstream-favicon.png') }}">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-orange: #fd7e14;
            --dark-orange: #e96c0d;
            --light-orange: #fff0e6;
        }
        body {
            background-color: #f8f9fa;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .form-container {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 2rem;
            width: 100%;
            max-width: 500px;
        }

        .form-container h1 {
            margin-bottom: 1.5rem;
        }

        .hours-summary {
            margin-bottom: 1.5rem;
        }

        .form-select:focus,
        .form-control:focus {
            border-color: var(--primary-orange)!important;
            box-shadow: 0 0 0 0.25rem rgba(253, 126, 20, 0.15);
        }

        .btn-submit {
            width: 100%;
            transition: background-color 0.2s;
        }

        .btn-submit {
            background-color: var(--primary-orange)!important;
            border: none;
            padding: 12px;
            font-weight: 500;
            width: 100%;
            margin-top: 1rem;
        }

        .btn-submit:hover {
            background-color:var(--primary-orange)!important;
        }

        .alert {
            margin-bottom: 1rem;
        }
        .btn:focus-visible {
            color: var(--primary-orange)!important;
            background-color:var(--primary-orange)!important;
            border-color:var(--primary-orange)!important;
            outline: 0;
            box-shadow: var(--primary-orange)!important;
        }
    </style>
</head>
<body>
<div class="form-container">
    <h1 class="fw-bold">Justify Your Hours</h1>

    {% for label, messages in app.flashes %}
        {% for message in messages %}
            <div class="alert {{ label == 'error' ? 'alert-danger' : 'alert-success' }}">
                {{ message }}
            </div>
        {% endfor %}
    {% endfor %}

    <div class="hours-summary">
        <h2 class="h5 fw-bold mb-2">Hours Summary</h2>
        <p class="text-secondary">Date: {{ notification.employeeHours.reportDate|date('d-M-Y') }}</p>
    </div>

    <form method="post" id="justificationForm" onsubmit="return validateForm(event)">
        <div class="mb-3">
            <label for="justification_type" class="form-label fw-medium">
                Select Justification Type:
            </label>
            <select
                    name="justification_type"
                    id="justification_type"
                    class="form-select"
                    onchange="toggleJustification()"
                    required
            >
                <option value="">Select a reason</option>
                <option value="full_day_leave">Full Day Leave</option>
                <option value="half_day_leave">Half Day Leave</option>
                <option value="other">Other</option>
            </select>
        </div>

        <div id="justification_text_container" class="mb-3 d-none">
            <label for="justification_text" class="form-label fw-medium">
                Please provide additional details:
            </label>
            <textarea
                    name="justification_text"
                    id="justification_text"
                    rows="5"
                    class="form-control"
            ></textarea>
            <div id="justification_error" class="text-danger mt-1 d-none">
                Please provide additional details when selecting "Other"
            </div>
        </div>

        <button
                type="submit"
                class="btn btn-primary btn-submit"
        >
            Submit Justification
        </button>
    </form>
</div>

<!-- Bootstrap JS Bundle with Popper -->
{#<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>#}
<script src="{{ asset('assets/js/bootstrap.bundle.min.js') }}"></script>
<script src="{{ asset('assets/js/justification/justifyissue.js') }}"></script>
</body>
</html>