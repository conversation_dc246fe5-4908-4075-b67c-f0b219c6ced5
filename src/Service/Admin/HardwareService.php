<?php

namespace App\Service\Admin;

use App\Entity\Hardware;
use App\Entity\MasterEmployee;
use App\Helper\ModalboxHelper;
use Doctrine\ORM\QueryBuilder;
use Omines\DataTablesBundle\Adapter\Doctrine\ORMAdapter;
use Omines\DataTablesBundle\Column\DateTimeColumn;
use Omines\DataTablesBundle\Column\TextColumn;
use Omines\DataTablesBundle\DataTable;
use Omines\DataTablesBundle\DataTableFactory;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Request;
use App\Traits\ExceptionLoggerTrait;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Security\Csrf\CsrfTokenManagerInterface;

class HardwareService
{
    use ExceptionLoggerTrait;

    public function __construct(
        private readonly DataTableFactory $dataTableFactory,
        private readonly RouterInterface $router,
        ParameterBagInterface $parameterBag,
        private readonly CsrfTokenManagerInterface $csrfTokenManager,
        private readonly ModalboxHelper $modalboxHelper
    ) {
        $this->initializeLogger($parameterBag, 'hardware_table_service');
    }

    public function createMasterHardwareTable(Request $request): DataTable
    {
        $dataTable = $this->dataTableFactory->create();

        foreach ($this->getColumns() as $key => $column) {
            $dataTable->add($key, $column['className'], array_filter($column, fn($k) => $k !== 'className', ARRAY_FILTER_USE_KEY));
        }

        return $dataTable->createAdapter(ORMAdapter::class, [
            'entity' => Hardware::class,
            'query' => fn(QueryBuilder $qb) => $this->buildQuery($qb),
        ])
            ->addOrderBy('hardwareName', 'asc')
            ->handleRequest($request);
    }

    private function getColumns(): array
    {
        return [
            'hardwareName' => [
                'label' => 'Hardware Name',
                'field' => 'h.hardwareName',
                'className' => TextColumn::class,
            ],
            'employee' => [
                'label' => 'Assigned to',
                'field' => 'e.name',
                'className' => TextColumn::class,
                'render' => function ($value, $context) {
                    $employee = $context->getEmployee();
                    return htmlspecialchars($employee ? $employee->getName() : 'Warehouse');
                },
            ],
            'hardwareType' => [
                'label' => 'Hardware Type',
                'field' => 'ht.name',
                'className' => TextColumn::class,
            ],
            'condition' => [
                'label' => 'Condition',
                'field' => 'h.condition',
                'className' => TextColumn::class,
            ],
            'vendorName' => [
                'label' => 'Vendor Name',
                'field' => 'h.vendorName',
                'className' => TextColumn::class,
            ],
            'purchaseDate' => [
                'label' => 'Purchase Date',
                'field' => 'h.purchaseDate',
                'format' => 'd-m-Y',
                'className' => DateTimeColumn::class,
                'searchable' => false,
            ],
            'actions' => [
                'label' => 'Actions',
                'field' => 'h.id',
                'className' => TextColumn::class,
                'orderable' => false,
                'render' => fn($value, $context) => $this->renderActionColumn($context),
            ],
        ];
    }

    private function renderActionColumn($context): string
    {
        $entityId = $context->getId();
        $editUrl = $this->router->generate('hardware_edit', ['id' => $entityId]);
        $viewUrl = $this->router->generate('hardware_view', ['id' => $entityId]);
        $deleteUrl = $this->router->generate('hardware_delete', ['id' => $entityId]);
        $csrfToken = $this->csrfTokenManager->getToken('delete' . $entityId)->getValue();

        $modalId = 'deleteModal_' . $entityId;
        $modalBody = 'Are you sure you want to delete this hardware?<br><br><strong>Warning:</strong> This action cannot be undone.';
        $footerButtons = sprintf(
            '<button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>' .
            '<button type="button" class="btn btn-primary delete-hardware-btn" data-delete="true" data-url="%s" data-token="%s">Delete</button>',
            $deleteUrl,
            $csrfToken
        );

        $deleteModal = $this->modalboxHelper->renderModal(
            $modalId,
            'Confirm Hardware Deletion',
            $modalBody,
            $footerButtons
        );

        return sprintf(
            '<div class="d-inline-flex gap-2">
        <a href="%s" class="bs-btn btn btn-primary btn-sm" style="margin: 2px;"><i class="bs-fa fa fa-eye"></i></a>
        <a href="%s" class="bs-btn btn btn-primary btn-sm" style="margin: 2px;"><i class="bs-fa fa fa-edit"></i></a>
        <button type="button" class="bs-btn btn btn-primary btn-sm" style="margin: 2px;" data-toggle="modal" data-target="#%s">
            <i class="bs-fa fa fa-trash"></i>
        </button>
    </div>%s',
            $viewUrl,
            $editUrl,
            $modalId,
            $deleteModal
        );
    }

    private function buildQuery(QueryBuilder $qb): void
    {
        $qb->select('h','e')
            ->addSelect('ht')
            ->from(Hardware::class, 'h')
            ->leftJoin('h.employee', 'e')
            ->leftJoin('h.hardwareType', 'ht');
    }
}