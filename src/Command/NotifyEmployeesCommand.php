<?php

namespace App\Command;

use App\Service\Admin\EmployeeNotificationService;
use App\Traits\ExceptionLoggerTrait;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use App\Repository\SettingRepository;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\Messenger\Exception\ExceptionInterface;

class NotifyEmployeesCommand extends Command
{
    use ExceptionLoggerTrait;

    protected static $defaultName = 'app:notify-employees';

    public function __construct(
        private readonly EmployeeNotificationService $employeeNotificationService,
        private readonly SettingRepository           $configurationRepository,
        ParameterBagInterface                        $parameterBag
    )
    {
        parent::__construct();
        $this->initializeLogger($parameterBag, 'notify_employees');
    }

    /**
     * @return void
     */
    protected function configure(): void
    {
        $this->setName('app:notify-employees')
            ->setDescription('Notify employees whose total working hours are below the threshold.');
        $this->addOption(
            'day',
            'day',
            InputOption::VALUE_OPTIONAL,
            'Enter Day'
        );
    }

    /**
     * @param string $fieldName
     * @param $defaultValue
     * @return string|null
     */
    private function getConfigurationValue(string $fieldName, $defaultValue): ?string
    {
        $configuration = $this->configurationRepository->findOneBy(['fieldName' => $fieldName]);
        return $configuration ? $configuration->getFieldValue() : $defaultValue;
    }

    /**
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     * @throws ExceptionInterface
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $threshold = $this->getConfigurationValue('THRESHOLD', $_ENV['THRESHOLD']);
        $threshold = is_numeric($threshold) ? (float) $threshold : 0.0;
        $dateOption = $input->getOption('day') ?? 'yesterday';
        $today = new \DateTimeImmutable($dateOption);

        //Saturday or Sunday
        if (in_array($today->format('N'), [6, 7])) {
            $output->writeln('Notifications are not sent on weekends.');
            return Command::SUCCESS;
        }
        $this->employeeNotificationService->sendNotification( $threshold, $today );

        $output->writeln('Notifications sent.');

        return Command::SUCCESS;
    }
}
