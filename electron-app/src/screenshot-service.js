const { screen, desktopCapturer } = require('electron');
const fs = require('fs').promises;
const path = require('path');

class ScreenshotService {
    constructor(dbService, store) {
        this.dbService = dbService;
        this.store = store;
        
        // Screenshot state
        this.isCapturing = false;
        this.captureInterval = null;
        this.burstInterval = null;
        this.lastCaptureTime = null;
        
        // Configuration (loaded from server)
        this.config = {
            enabled: true,
            intervalSeconds: 300, // 5 minutes default
            quality: 80, // 80% quality
            burstMode: false,
            burstIntervalSeconds: 30, // 30 seconds in burst mode
            randomInterval: true, // Randomize intervals
            randomVariation: 0.3, // ±30% variation
            maxDailyScreenshots: 999999, // Unlimited screenshots (admin controls)
            compressionEnabled: true,
            captureActiveWindow: true,
            captureMetadata: true
        };
        
        // Storage paths
        this.screenshotDir = null;
        this.tempDir = null;
        
        // Statistics
        this.dailyCount = 0;
        this.totalCaptured = 0;
        
        this.init();
    }

    async init() {
        console.log('📸 Initializing Screenshot Service...');
        
        // Setup storage directories
        await this.setupStorageDirectories();
        
        // Load configuration
        await this.loadConfiguration();
        
        // Setup database
        await this.setupDatabase();
        
        // Load daily statistics
        await this.loadDailyStats();

        // Don't auto-start here - wait for server configuration
        console.log('📸 Screenshot service initialized, waiting for server configuration');

        console.log('✅ Screenshot Service initialized');
    }

    async setupStorageDirectories() {
        try {
            const { app } = require('electron');
            const userDataPath = app.getPath('userData');
            
            this.screenshotDir = path.join(userDataPath, 'screenshots');
            this.tempDir = path.join(userDataPath, 'temp', 'screenshots');
            
            // Create directories if they don't exist
            await fs.mkdir(this.screenshotDir, { recursive: true });
            await fs.mkdir(this.tempDir, { recursive: true });
            
            console.log('📁 Screenshot directories created:', this.screenshotDir);
        } catch (error) {
            console.error('Failed to setup screenshot directories:', error);
        }
    }

    async loadConfiguration() {
        try {
            // Clear old configuration to ensure fresh start
            await this.store.delete('screenshotConfig');
            console.log('📸 Cleared old screenshot configuration');

            // Use default configuration (will be updated from server)
            console.log('⚙️ Screenshot configuration loaded (defaults):', this.config);
        } catch (error) {
            console.error('Failed to load screenshot configuration:', error);
        }
    }

    async setupDatabase() {
        try {
            await this.dbService.run(`
                CREATE TABLE IF NOT EXISTS screenshot_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    employee_id INTEGER NOT NULL,
                    captured_at TEXT NOT NULL,
                    capture_type TEXT NOT NULL,
                    file_path TEXT NOT NULL,
                    file_name TEXT,
                    file_size INTEGER,
                    resolution TEXT,
                    device_id TEXT,
                    active_application TEXT,
                    active_window TEXT,
                    metadata TEXT,
                    is_synced INTEGER DEFAULT 0,
                    is_processed INTEGER DEFAULT 0,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            `);

            await this.dbService.run(`
                CREATE INDEX IF NOT EXISTS idx_screenshot_employee_captured 
                ON screenshot_logs(employee_id, captured_at)
            `);

            console.log('📊 Screenshot database setup complete');
        } catch (error) {
            console.error('Failed to setup screenshot database:', error);
        }
    }

    async loadDailyStats() {
        try {
            const currentUser = await this.store.get('currentUser');
            if (!currentUser) return;

            const today = new Date().toISOString().split('T')[0];

            // Get local count
            const localResult = await this.dbService.get(`
                SELECT COUNT(*) as count
                FROM screenshot_logs
                WHERE employee_id = ? AND DATE(captured_at) = ?
            `, [currentUser.id, today]);

            const localCount = localResult ? localResult.count : 0;

            // Also count actual files in directory for today
            const fileCount = await this.countTodayScreenshots();

            // Get server count for synced screenshots
            const serverCount = await this.getServerDailyCount(currentUser.id, today);

            // Use the maximum of database count and file count
            this.dailyCount = Math.max(localCount, fileCount) + serverCount;

            // Update last capture time from most recent file
            await this.updateLastCaptureTime();

            console.log(`📊 Daily screenshot count: ${this.dailyCount} (Local DB: ${localCount}, Files: ${fileCount}, Server: ${serverCount})`);
        } catch (error) {
            console.error('Failed to load daily stats:', error);
            this.dailyCount = 0;
        }
    }

    async countTodayScreenshots() {
        try {
            const fs = require('fs').promises;
            const files = await fs.readdir(this.screenshotDir);
            const today = new Date().toISOString().split('T')[0];

            const todayFiles = files.filter(file => {
                return file.includes(today) && file.endsWith('.png');
            });

            return todayFiles.length;
        } catch (error) {
            console.error('Failed to count screenshot files:', error);
            return 0;
        }
    }

    async updateLastCaptureTime() {
        try {
            const fs = require('fs').promises;
            const files = await fs.readdir(this.screenshotDir);

            if (files.length === 0) return;

            // Get the most recent file
            const screenshotFiles = files.filter(file => file.endsWith('.png'));
            if (screenshotFiles.length === 0) return;

            // Sort by filename (which includes timestamp)
            screenshotFiles.sort().reverse();
            const latestFile = screenshotFiles[0];

            // Extract timestamp from filename
            const timestampMatch = latestFile.match(/screenshot_(.+)_/);
            if (timestampMatch) {
                const timestamp = timestampMatch[1].replace(/-/g, ':').replace(/Z$/, 'Z');
                this.lastCaptureTime = new Date(timestamp);
                console.log('📸 Last capture time updated:', this.lastCaptureTime);
            }
        } catch (error) {
            console.error('Failed to update last capture time:', error);
        }
    }

    async getServerDailyCount(employeeId, date) {
        try {
            const authToken = await this.store.get('authToken');
            const response = await fetch(`http://127.0.0.1:8000/api/screenshots/daily-count?employee_id=${employeeId}&date=${date}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    ...(authToken && { 'Authorization': `Bearer ${authToken}` })
                }
            });

            if (response.ok) {
                const result = await response.json();
                return result.success ? (result.data.count || 0) : 0;
            }
        } catch (error) {
            console.log('📡 Server screenshot count not available, using local only');
        }
        return 0;
    }

    async startCapturing() {
        console.log('📸 Screenshot service startCapturing called');
        console.log('📸 Current config:', this.config);

        if (!this.config.enabled) {
            console.log('📸 Screenshot capture is disabled in config');
            return;
        }

        if (this.isCapturing) {
            console.log('⚠️ Screenshot capture already running');
            return;
        }

        console.log('▶️ Starting screenshot capture...');
        this.isCapturing = true;

        // Start appropriate capture mode
        if (this.config.burstMode) {
            console.log('📸 Starting burst mode');
            await this.startBurstMode();
        } else {
            console.log('📸 Starting periodic mode');
            await this.startPeriodicCapture();
        }

        console.log('✅ Screenshot capture started successfully');
    }

    async stopCapturing() {
        if (!this.isCapturing) {
            console.log('⚠️ Screenshot capture not running');
            return;
        }

        console.log('⏹️ Stopping screenshot capture...');
        this.isCapturing = false;

        // Clear intervals
        if (this.captureInterval) {
            clearInterval(this.captureInterval);
            this.captureInterval = null;
        }

        if (this.burstInterval) {
            clearInterval(this.burstInterval);
            this.burstInterval = null;
        }

        console.log('✅ Screenshot capture stopped');
    }

    async startPeriodicCapture() {
        console.log(`📸 Starting periodic capture with ${this.config.intervalSeconds}s interval`);

        const captureScreenshot = async () => {
            if (!this.isCapturing) {
                console.log('📸 Capture stopped, not taking screenshot');
                return;
            }

            try {
                console.log('📸 Taking periodic screenshot...');
                await this.captureScreenshot('periodic');

                // Schedule next capture with optional randomization
                const nextInterval = this.calculateNextInterval();

                this.captureInterval = setTimeout(captureScreenshot, nextInterval);

                console.log(`📸 Next screenshot scheduled in ${Math.round(nextInterval / 1000)}s`);
            } catch (error) {
                console.error('Periodic capture error:', error);
                // Retry after a shorter interval on error
                this.captureInterval = setTimeout(captureScreenshot, 30000);
                console.log('📸 Retrying in 30 seconds due to error');
            }
        };

        // Start first capture immediately
        console.log('📸 Taking initial screenshot...');
        await captureScreenshot();
    }

    async startBurstMode() {
        console.log('💥 Starting burst mode capture...');
        
        this.burstInterval = setInterval(async () => {
            if (!this.isCapturing) return;
            
            try {
                await this.captureScreenshot('burst');
            } catch (error) {
                console.error('Burst capture error:', error);
            }
        }, this.config.burstIntervalSeconds * 1000);
    }

    calculateNextInterval() {
        let interval = this.config.intervalSeconds * 1000;
        console.log(`📸 Base interval: ${this.config.intervalSeconds}s (${interval}ms)`);

        if (this.config.randomInterval) {
            const variation = interval * this.config.randomVariation;
            const randomOffset = (Math.random() - 0.5) * 2 * variation;
            interval += randomOffset;
            console.log(`📸 Random variation applied: ${Math.round(randomOffset)}ms, new interval: ${Math.round(interval)}ms`);
        }

        // Ensure minimum interval of 30 seconds
        const finalInterval = Math.max(interval, 30000);
        console.log(`📸 Final interval: ${Math.round(finalInterval)}ms (${Math.round(finalInterval/1000)}s)`);
        return finalInterval;
    }

    async captureScreenshot(captureType = 'manual') {
        try {
            // No daily limit - admin controls when screenshots are enabled/disabled
            console.log(`📸 Taking screenshot #${this.dailyCount + 1} (${captureType})`);

            const currentUser = await this.store.get('currentUser');
            if (!currentUser) {
                console.log('⚠️ No current user for screenshot');
                return null;
            }

            console.log(`📸 Capturing screenshot (${captureType})...`);

            // Get screen sources
            const sources = await desktopCapturer.getSources({
                types: ['screen'],
                thumbnailSize: { width: 1920, height: 1080 }
            });

            if (sources.length === 0) {
                throw new Error('No screen sources available');
            }

            // Use primary screen
            const primarySource = sources[0];
            const screenshot = primarySource.thumbnail;

            // Get screen resolution
            const primaryDisplay = screen.getPrimaryDisplay();
            const { width, height } = primaryDisplay.bounds;
            const resolution = `${width}x${height}`;

            // Generate filename
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const fileName = `screenshot_${timestamp}_${captureType}.png`;
            const filePath = path.join(this.screenshotDir, fileName);

            // Save screenshot
            const buffer = screenshot.toPNG();
            await fs.writeFile(filePath, buffer);

            // Get file size
            const stats = await fs.stat(filePath);
            const fileSize = stats.size;

            // Get active application info
            const appInfo = await this.getActiveApplicationInfo();

            // Get device ID
            const deviceId = await this.getDeviceId();

            // Create metadata
            const metadata = {
                captureType,
                resolution,
                quality: this.config.quality,
                fileSize,
                timestamp: new Date().toISOString(),
                ...appInfo
            };

            // Save to database
            const result = await this.dbService.run(`
                INSERT INTO screenshot_logs (
                    employee_id, captured_at, capture_type, file_path, file_name,
                    file_size, resolution, device_id, active_application, 
                    active_window, metadata, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `, [
                currentUser.id,
                new Date().toISOString(),
                captureType,
                filePath,
                fileName,
                fileSize,
                resolution,
                deviceId,
                appInfo.activeApplication,
                appInfo.activeWindow,
                JSON.stringify(metadata),
                new Date().toISOString()
            ]);

            // Update statistics
            this.dailyCount++;
            this.totalCaptured++;
            this.lastCaptureTime = new Date();

            console.log(`✅ Screenshot captured: ${fileName} (${this.formatFileSize(fileSize)})`);

            return {
                id: result.lastID,
                fileName,
                filePath,
                fileSize,
                resolution,
                metadata
            };

        } catch (error) {
            console.error('Failed to capture screenshot:', error);
            throw error;
        }
    }

    async getActiveApplicationInfo() {
        try {
            // This is a simplified version - in a real implementation,
            // you'd use native modules to get actual active window info
            return {
                activeApplication: 'Unknown Application',
                activeWindow: 'Unknown Window'
            };
        } catch (error) {
            console.error('Failed to get active application info:', error);
            return {
                activeApplication: null,
                activeWindow: null
            };
        }
    }

    async getDeviceId() {
        try {
            const { machineId } = require('node-machine-id');
            return await machineId();
        } catch (error) {
            return 'unknown-device';
        }
    }

    formatFileSize(bytes) {
        const units = ['B', 'KB', 'MB', 'GB'];
        let size = bytes;
        let unitIndex = 0;
        
        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }
        
        return `${size.toFixed(1)} ${units[unitIndex]}`;
    }

    // Public methods
    async updateConfiguration(newConfig) {
        const wasCapturing = this.isCapturing;

        console.log('📸 Updating screenshot configuration:', newConfig);

        if (wasCapturing) {
            console.log('📸 Stopping capture to update config');
            await this.stopCapturing();
        }

        this.config = { ...this.config, ...newConfig };
        await this.store.set('screenshotConfig', this.config);

        console.log('📸 New config:', this.config);

        // Start capture if enabled (regardless of previous state)
        console.log('📸 Checking if capture should start after config update. Enabled:', this.config.enabled);
        if (this.config.enabled) {
            console.log('📸 Starting capture with new config (enabled)');
            await this.startCapturing();
        } else {
            console.log('📸 Not starting capture (disabled in new config)');
        }

        console.log('⚙️ Screenshot configuration updated successfully');
    }

    // Test method with shorter interval
    async startTestCapture() {
        console.log('📸 Starting test capture with 30 second interval');
        this.config.intervalSeconds = 30; // 30 seconds for testing
        this.config.enabled = true;
        this.config.randomInterval = false; // Disable randomization for testing
        await this.startCapturing();
    }

    // Debug method
    getDebugInfo() {
        return {
            isCapturing: this.isCapturing,
            config: this.config,
            dailyCount: this.dailyCount,
            lastCaptureTime: this.lastCaptureTime,
            captureInterval: !!this.captureInterval,
            burstInterval: !!this.burstInterval
        };
    }

    getConfiguration() {
        return { ...this.config };
    }

    getStatistics() {
        return {
            isCapturing: this.isCapturing,
            dailyCount: this.dailyCount,
            totalCaptured: this.totalCaptured,
            lastCaptureTime: this.lastCaptureTime,
            config: this.config
        };
    }

    async getRecentScreenshots(limit = 10) {
        try {
            const currentUser = await this.store.get('currentUser');
            if (!currentUser) return [];

            return await this.dbService.all(`
                SELECT * FROM screenshot_logs 
                WHERE employee_id = ? 
                ORDER BY captured_at DESC 
                LIMIT ?
            `, [currentUser.id, limit]);
        } catch (error) {
            console.error('Failed to get recent screenshots:', error);
            return [];
        }
    }

    async getUnsyncedScreenshots() {
        try {
            return await this.dbService.all(`
                SELECT * FROM screenshot_logs 
                WHERE is_synced = 0 
                ORDER BY captured_at ASC
            `);
        } catch (error) {
            console.error('Failed to get unsynced screenshots:', error);
            return [];
        }
    }

    async markAsSynced(screenshotIds) {
        try {
            const placeholders = screenshotIds.map(() => '?').join(',');
            await this.dbService.run(`
                UPDATE screenshot_logs 
                SET is_synced = 1 
                WHERE id IN (${placeholders})
            `, screenshotIds);
            
            console.log(`📤 Marked ${screenshotIds.length} screenshots as synced`);
        } catch (error) {
            console.error('Failed to mark screenshots as synced:', error);
        }
    }

    async cleanup() {
        try {
            // Clean up old screenshots (older than 30 days)
            const thirtyDaysAgo = new Date();
            thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

            const oldScreenshots = await this.dbService.all(`
                SELECT file_path FROM screenshot_logs 
                WHERE captured_at < ? AND is_synced = 1
            `, [thirtyDaysAgo.toISOString()]);

            for (const screenshot of oldScreenshots) {
                try {
                    await fs.unlink(screenshot.file_path);
                } catch (error) {
                    // File might already be deleted
                }
            }

            // Remove from database
            await this.dbService.run(`
                DELETE FROM screenshot_logs 
                WHERE captured_at < ? AND is_synced = 1
            `, [thirtyDaysAgo.toISOString()]);

            console.log(`🧹 Cleaned up ${oldScreenshots.length} old screenshots`);
        } catch (error) {
            console.error('Failed to cleanup screenshots:', error);
        }
    }

    async destroy() {
        console.log('🔄 Destroying screenshot service...');
        
        await this.stopCapturing();
        
        // Cleanup any remaining intervals
        if (this.captureInterval) {
            clearInterval(this.captureInterval);
        }
        if (this.burstInterval) {
            clearInterval(this.burstInterval);
        }
        
        console.log('✅ Screenshot service destroyed');
    }
}

module.exports = ScreenshotService;
