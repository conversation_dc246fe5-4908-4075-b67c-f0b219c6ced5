<?php

namespace App\Entity;

use App\Repository\EmployeeRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: EmployeeRepository::class)]
class Employee
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    /** @phpstan-ignore-next-line */
    private ?int $id = null;

    #[ORM\Column(length: 255)]
    private ?string $guid = null;

    #[ORM\ManyToOne(targetEntity: MasterEmployee::class, inversedBy: "employees")]
    #[ORM\JoinColumn(nullable: true, onDelete: "SET NULL")] // Allow NULL values
    private ?MasterEmployee $masterEmployee = null;

    public function getId(): ?int
    {
        return $this->id;
    }
    public function getGuid(): ?string
    {
        return $this->guid;
    }

    public function setGuid(?string $guid): void
    {
        $this->guid = $guid;
    }

    public function getMasterEmployee(): ?MasterEmployee
    {
        return $this->masterEmployee;
    }

    public function setMasterEmployee(?MasterEmployee $masterEmployee): static
    {
        $this->masterEmployee = $masterEmployee;

        return $this;
    }
}