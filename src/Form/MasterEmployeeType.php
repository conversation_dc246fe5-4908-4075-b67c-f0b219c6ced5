<?php

namespace App\Form;

use App\Entity\Department;
use App\Entity\MasterEmployee;
use Doctrine\ORM\EntityRepository;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\CallbackTransformer;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\DateType;
use Symfony\Component\Form\Extension\Core\Type\EmailType;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\Form\Extension\Core\Type\TelType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\NotBlank;

class MasterEmployeeType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('id', HiddenType::class, [
                'required' => false,
            ])
            ->add('email', EmailType::class, ['label' => 'Email', 'required' => true])
            ->add('username', TextType::class, ['label' => 'Username', 'required' => true])
            ->add('name', TextType::class, ['label' => 'Name', 'required' => true])
            ->add('employeeCode', TextType::class, ['label' => 'Employee Code', 'required' => false])
            ->add('joiningDate', TextType::class, [
                'required' => true,
                'label' => 'Joining Date',
                'attr' => [
                    'class' => 'form-control datepicker',
                    'placeholder' => 'Select date',
                    'required' => 'required',
                ],
                'constraints' => [new NotBlank()],
            ])
            ->add('totalExperience', TextType::class, ['label' => 'Total Experience', 'required' => false])
            ->add('confirmationDate', TextType::class, [
                'required' => false,
                'label' => 'Confirmation Date',
                'attr' => [
                    'class' => 'form-control datepicker',
                    'placeholder' => 'Select date',
                    'required' => 'required',
                ],
            ])
            ->add('previousCompanyName', TextType::class, ['label' => 'Previous Company Name', 'required' => false])
            ->add('personalPhoneNumber', TelType::class, ['label' => 'Personal Phone Number', 'required' => true])
            ->add('alternativePhoneNumber', TelType::class, ['label' => 'Alternative Phone Number', 'required' => false])
            ->add('currentAddress', TextType::class, ['label' => 'Current Address', 'required' => true])
            ->add('permanentAddress', TextType::class, ['label' => 'Permanent Address', 'required' => false])
            ->add('birthDate', TextType::class, [
                'required' => true,
                'label' => 'Birth Date',
                'attr' => [
                    'class' => 'form-control datepicker',
                    'placeholder' => 'Select date',
                    'required' => 'required',
                ],
                'constraints' => [new NotBlank()],
            ])
            ->add('gender', ChoiceType::class, [
                'label' => 'Gender',
                'choices' => ['Male' => 'male', 'Female' => 'female', 'Other' => 'other'],
                'placeholder' => 'Select gender',
                'required' => true,
                'autocomplete'=>true,
            ])
            ->add('maritalStatus', ChoiceType::class, [
                'label' => 'Marital Status',
                'choices' => ['Single' => 'single', 'Married' => 'married', 'Divorced' => 'divorced', 'Widowed' => 'widowed'],
                'placeholder' => 'Select Marital Status',
                'required' => false,
                'autocomplete'=>true,
            ])
            ->add('bankName', TextType::class, ['label' => 'Bank Name', 'required' => false])
            ->add('bankAccountNumber', NumberType::class, ['label' => 'Bank Account Number', 'required' => false])
            ->add('panNumber', TextType::class, ['label' => 'PAN Number', 'required' => true])
            ->add('aadhaarNumber', NumberType::class, ['label' => 'Aadhaar Number', 'required' => true])
            ->add('esiNumber', TextType::class, ['label' => 'ESI Number', 'required' => false])
            ->add('pfNumber', TextType::class, ['label' => 'PF Number', 'required' => false])
            ->add('uan', TextType::class, ['label' => 'UAN', 'required' => false])
            ->add('ifscCode', TextType::class, ['label' => 'IFSC Code', 'required' => false])
            ->add('departments', EntityType::class, [
                'label' => false,
                'class' => Department::class,
                'choice_label' => 'depName',
                'multiple' => true,
                'expanded' => true,
                'required' => false,
                'query_builder' => fn(EntityRepository $er) => $er->createQueryBuilder('d')
                    ->where('d.depStatus = :active')
                    ->andWhere('d.isDelete = 0')
                    ->setParameter('active', MasterEmployee::ACTIVE)
            ])
            ->add('teamLeader', CheckboxType::class, ['label' => 'Team Leader', 'required' => false])
            ->add('isHrAccount', CheckboxType::class, ['label' => false, 'required' => false])
            ->add('reportsTo', EntityType::class, [
                'label' => false,
                'class' => MasterEmployee::class,
                'choice_label' => 'name',
                'multiple' => true,
                'expanded' => true,
                'required' => false,
                'mapped' => false,
                'query_builder' => function (EntityRepository $er) use ($options) {
                    $qb = $er->createQueryBuilder('e')
                        ->where('e.teamLeader = true')
                        ->andWhere('e.isDelete = false');
                    if ($options['data'] && $options['data']->getId()) {
                        $qb->andWhere('e.id != :currentId')
                            ->setParameter('currentId', $options['data']->getId());
                    }
                    return $qb;
                }
            ]);
        foreach (['joiningDate', 'confirmationDate','birthDate'] as $field) {
            $builder->get($field)
                ->addModelTransformer(new CallbackTransformer(
                    function ($dateAsObject) {
                        return $dateAsObject instanceof \DateTimeInterface ? $dateAsObject->format('d-m-Y') : '';
                    },
                    function ($dateAsString) {
                        return $dateAsString ? \DateTime::createFromFormat('d-m-Y', $dateAsString) : null;
                    }
                ));
        }
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults(['data_class' => MasterEmployee::class]);
    }
}