<?php

namespace App\Helper;

class TimeHelper
{
    /**
     * Converts decimal hours to time format (HH:mm).
     *
     * @param float $decimalHours
     * @return string
     */
    public function convertDecimalToTime(float $decimalHours): string
    {
        $hours = floor($decimalHours);
        $minutes = ($decimalHours - $hours) * 60;
        return sprintf('%02d:%02d', $hours, floor($minutes));
    }

    public function convertDecimalToTimeFormat(float $decimalHours): float
    {
        $hours = floor($decimalHours);
        $minutes = ($decimalHours - $hours) * 60;
        return (float)sprintf('%02d.%02d', $hours, floor($minutes));
    }

    // Used for estimatedHours: 1.30 => 1 hr 30 min = 90
    public function clockStyleToMinutes(float $hours): int
    {
        $hrs = floor($hours);
        $mins = round(($hours - $hrs) * 100); // 0.30 = 30 mins
        return (int) ($hrs * 60 + $mins);
    }

    // Used for actualHours: 1.3349 => 1.3349 * 60
    public function decimalHoursToMinutes(float $hours): int
    {
        return (int)round($hours * 60);
    }
}
