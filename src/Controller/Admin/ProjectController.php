<?php

namespace App\Controller\Admin;

use App\Entity\Project;
use App\Entity\User;
use App\Form\ProjectFormType;
use App\Message\TaskAndProjectNotificationMessage;
use App\Service\Common\ProjectService;
use App\Traits\ExceptionLoggerTrait;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Messenger\Exception\ExceptionInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Csrf\CsrfToken;
use Symfony\Component\Security\Csrf\CsrfTokenManagerInterface;

#[Route('/admin')]
class ProjectController extends AbstractController
{
    use ExceptionLoggerTrait;

    public function __construct(
        ParameterBagInterface $parameterBag,
        private readonly EntityManagerInterface $entityManager,
        private readonly ProjectService $projectService,
        private readonly MessageBusInterface $messageBus,

    ) {
        $this->initializeLogger($parameterBag, 'project_controller');
    }

    #[Route(path: '/project', name: 'project', methods: ['GET', 'POST'])]
    public function index(Request $request): Response
    {
        $table = $this->projectService->createMasterProjectTable($request);

        if ($table->isCallback()) {
            return $table->getResponse();
        }

        return $this->render('admin/project/index.html.twig', [
            'datatable' => $table,
        ]);
    }

    /**
     * @throws ExceptionInterface
     */
    #[Route('/project/create', name: 'project_create', methods: ['GET', 'POST'])]
    public function createProject(Request $request): Response
    {
        $admin = $this->getUser();
        if (!$admin instanceof User) {
            throw $this->createAccessDeniedException('This can only be accessed by an admin.');
        }
        $project = new Project();
        $project->setCreatedBy($admin);
        $form = $this->createForm(ProjectFormType::class, $project);

        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $this->entityManager->persist($project);
            $this->entityManager->flush();
            try {
                $message = new TaskAndProjectNotificationMessage('project', $project->getId(), 'create', (array)null);
                $this->messageBus->dispatch($message);
            }catch (\Throwable $e) {
                $this->log('Notification dispatch failed: ' . $e->getMessage());
            }
            $this->addFlash('success', 'Project created successfully.');
            return $this->redirectToRoute('project');
        }

        return $this->render('admin/project/new.html.twig', [
            'form' => $form->createView(),
            'mode' => 'create',
        ]);
    }

    /**
     * @throws ExceptionInterface
     */
    #[Route('/project/edit/{id}', name: 'project_edit', methods: ['GET', 'POST'])]
    public function editProject(Request $request, Project $project): Response
    {
        $form = $this->createForm(ProjectFormType::class, $project);
        $originalManager = $project->getProjectManager();
        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $newManager = $project->getProjectManager();
            $project->setUpdatedAt(new \DateTime());
            $this->entityManager->flush();
            $action = ($originalManager !== $newManager) ? 'update' : 'no-change';
            try {
                $message = new TaskAndProjectNotificationMessage('project', $project->getId(), $action, (array)null);
                $this->messageBus->dispatch($message);
            }catch (\Throwable $e) {
                $this->log('Notification dispatch failed: ' . $e->getMessage());
            }
            $this->addFlash('success', 'Project updated successfully.');
            return $this->redirectToRoute('project');
        }

        return $this->render('admin/project/new.html.twig', [
            'form' => $form->createView(),
            'project' => $project,
            'mode' => 'edit',
        ]);
    }

    #[Route('/project/delete/{id}', name: 'project_delete', methods: ['POST'])]
    public function deleteProject(
        Request $request,
        Project $project,
        CsrfTokenManagerInterface $csrfTokenManager
    ): Response {
        $token = $request->request->get('_token');

        if (!$csrfTokenManager->isTokenValid(new CsrfToken('delete' . $project->getId(), $token))) {
            return $this->json([
                'success' => false,
                'message' => 'Invalid CSRF token.'
            ], Response::HTTP_FORBIDDEN);
        }

        $this->entityManager->remove($project);
        $this->entityManager->flush();

        return $this->json([
            'success' => true,
            'message' => 'Project has been deleted.'
        ], Response::HTTP_OK);
    }

    #[Route('/project/view/{id}', name: 'project_view', methods: ['GET'])]
    public function show(Project $project): Response
    {
        return $this->render('admin/project/new.html.twig', [
            'project' => $project,
            'mode' => 'view',
        ]);
    }

    #[Route('/project/check-field', name: 'project_check_field', methods: ['POST'])]
    public function checkField(Request $request): JsonResponse
    {
        $field = $request->request->get('field');
        $value = $request->request->get('value');
        $currentId = $request->request->get('currentId');
        if (!$field || !$value) {
            return new JsonResponse(['isUnique' => false, 'error' => 'Missing field or value']);
        }
        $criteria = [$field => $value];
        $project = $this->entityManager->getRepository(Project::class)->findOneBy($criteria);
        $isUnique = !$project || ($currentId && (string)$project->getId() === (string)$currentId);
        return new JsonResponse(['isUnique' => $isUnique]);
    }
}