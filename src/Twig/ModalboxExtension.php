<?php

namespace App\Twig;

use App\Helper\ModalboxHelper;
use Twig\Extension\AbstractExtension;
use Twig\TwigFunction;

class ModalboxExtension extends AbstractExtension
{
    public function getFunctions(): array
    {
        return [
            new TwigFunction('render_modal', [$this, 'renderModal'], ['is_safe' => ['html']]),
        ];
    }

    /**
     * @param string $modalId
     * @param string $modalTitle
     * @param string $modalBody
     * @param string $footerButtons
     * @return string
     */
    public function renderModal(string $modalId, string $modalTitle, string $modalBody, string $footerButtons): string
    {
        return ModalboxHelper::renderModal($modalId, $modalTitle, $modalBody, $footerButtons);
    }
}