<?php

namespace App\MessageHandler;

use App\Message\PayslipEmailMessage;
use App\Repository\PayrollRepository;
use App\Service\Admin\PayrollPDFService;
use App\Service\Common\EmailService;
use setasign\Fpdi\PdfParser\PdfParserException;
use setasign\Fpdi\PdfReader\PdfReaderException;
use Symfony\Component\Mailer\Exception\TransportExceptionInterface;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;
use setasign\Fpdi\Tcpdf\Fpdi;

#[AsMessageHandler]
class PayslipEmailHandler
{
    public function __construct(
        private readonly PayrollRepository $payrollRepository,
        private readonly PayrollPDFService $payrollPDFService,
        private readonly EmailService $emailService,
    ) {}

    /**
     * @throws TransportExceptionInterface
     * @throws PdfParserException
     * @throws PdfReaderException
     */
    public function __invoke(PayslipEmailMessage $message): void
    {
        $payroll = $this->payrollRepository->findOneBy([
            'employee' => $message->getEmployeeId(),
            'month' => $message->getMonth(),
            'year' => $message->getYear(),
        ]);
        if (!$payroll) return;

        $employee = $payroll->getEmployee();
        if (!$employee || !$employee->getEmail()) return;

        $payslipData = $this->payrollPDFService->preparePayslipData($payroll);
        $pdfContent = $this->payrollPDFService->generatePayslipPdf($payslipData);
        $month = $message->getMonth();
        $year = $message->getYear();
        $aadhaar = $employee->getAadhaarNumber();
        $aadhaarLast4 = $aadhaar ? substr($aadhaar, -4) : null;
        $protectedPdfContent = $pdfContent;
        if ($aadhaarLast4 && strlen($aadhaarLast4) === 4) {
            $tmpFile = tempnam(sys_get_temp_dir(), 'payslip_') . '.pdf';
            file_put_contents($tmpFile, $pdfContent);

            $fpdi = new Fpdi();
            $fpdi->setPrintHeader(false);
            $fpdi->setPrintFooter(false);

            $pageCount = $fpdi->setSourceFile($tmpFile);
            for ($pageNo = 1; $pageNo <= $pageCount; $pageNo++) {
                $tplIdx = $fpdi->importPage($pageNo);
                $fpdi->AddPage();
                $fpdi->useTemplate($tplIdx);
            }

            $fpdi->SetProtection(['print', 'copy'], $aadhaarLast4, null, 0, null);
            $protectedPdfContent = $fpdi->Output('', 'S');

            unlink($tmpFile);
        }

        $this->emailService->sendEmailPDF(
            $employee->getEmail(),
            'Your Payslip for ' . $payslipData['monthName'] . ' ' . $payslipData['year'],
            'Please find your payslip attached.',
            [
                [
                    'content' => $protectedPdfContent,
                    'filename' => sprintf('%s_%s_%s_payslip.pdf', $employee->getEmployeeCode(), $month, $year),
                    'mime' => 'application/pdf',
                ]
            ]
        );
    }
}