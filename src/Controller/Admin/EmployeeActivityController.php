<?php

namespace App\Controller\Admin;

use App\Entity\User;
use App\Service\Common\EmployeeActivityService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/admin')]
class EmployeeActivityController extends AbstractController
{
    public function __construct(
        private readonly EmployeeActivityService $employeeActivityService,
    ) {
    }

    /**
     * @throws \Exception
     */
    #[Route(path:'/employee/activity', name: 'admin_employee_activity', methods: ['GET', 'POST'])]
    public function employeeActivity(Request $request): Response
    {
        $user = $this->getUser();
        if (!$user instanceof User) {
            throw $this->createAccessDeniedException('This can only be accessed by a admin.');
        }

        $table = $this->employeeActivityService->createForAdmin($request);

        if ($table->isCallback()) {
            return $table->getResponse();
        }

        return $this->render('admin/employeeActivity/activity.html.twig', [
            'datatable' => $table,
        ]);
    }
}