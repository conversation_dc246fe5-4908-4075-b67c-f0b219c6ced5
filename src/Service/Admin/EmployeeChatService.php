<?php

namespace App\Service\Admin;

use App\Entity\Chat;
use App\Repository\EmployeeNotificationRepository;
use App\Repository\UserRepository;
use Doctrine\ORM\EntityManagerInterface;

readonly class EmployeeChatService
{
    public function __construct(
        private EntityManagerInterface         $entityManager,
        private EmployeeNotificationRepository $notificationRepository,
        private UserRepository                 $userRepository,
    ) {}

    public function getNotificationByUuid(string $uuid)
    {
        return $this->notificationRepository->findOneBy(['uuid' => $uuid]);
    }

    public function getAllUsers()
    {
        return $this->userRepository->findAll();
    }

    public function getMessagesByNotification($notification): array
    {
        return $notification->getChats()->map(function (Chat $chat) {
            return [
                'id' => $chat->getId(),
                'message' => $chat->getMessage(),
                'isAdmin' => $chat->isFromAdmin(),
                'timestamp' => $chat->getCreatedAt()->format('d-m-Y H:i:s'),
                'isDisapprovalReason' => $chat->isDisapprovalReason(),
            ];
        })->toArray();
    }

    public function createMessage($notification, string $message, bool $isAdmin): Chat
    {
        $chat = new Chat(
            message: $message,
            isFromEmployee: !$isAdmin,
            employeeNotification: $notification,
            isDisapprovalReason: false
        );

        $this->entityManager->persist($chat);
        $this->entityManager->flush();

        return $chat;
    }
}
