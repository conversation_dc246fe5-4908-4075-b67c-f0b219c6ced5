<?php

namespace App\Controller\Admin;

use App\Entity\MasterEmployee;
use App\Entity\SalaryStructure;
use App\Entity\User;
use App\Form\SalaryStructureFormType;
use App\Service\Admin\SalaryStructureService;
use App\Traits\ExceptionLoggerTrait;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Csrf\CsrfTokenManagerInterface;

#[Route('/admin')]
class SalaryStructureController extends AbstractController
{
    use ExceptionLoggerTrait;

    public function __construct(
        private readonly EntityManagerInterface    $entityManager,
        private readonly CsrfTokenManagerInterface $csrfTokenManager,
        private readonly SalaryStructureService $salaryStructureService,
    )
    {
    }

    #[Route('/employee/{employeeId}/salary', name: 'admin_employee_salary', methods: ['GET', 'POST'])]
    public function index(Request $request, int $employeeId): Response
    {
        $user = $this->getUser();
        if (!$user instanceof User) {
            throw $this->createAccessDeniedException('This can only be accessed by an admin.');
        }
        $employee = $this->entityManager->getRepository(MasterEmployee::class)->find($employeeId);
        if (!$employee) {
            throw $this->createNotFoundException('Employee not found');
        }

        $table = $this->salaryStructureService->createSalaryStructureTable($request, $employee);

        if ($table->isCallback()) {
            return $table->getResponse();
        }

        return $this->render('admin/SalaryStructure/index.html.twig', [
            'datatable' => $table,
            'employee' => $employee,
        ]);
    }
    #[Route('/employee/{employeeId}/salary/new', name: 'admin_employee_salary_new', methods: ['GET', 'POST'])]
    public function new(Request $request, int $employeeId): Response
    {
        $user = $this->getUser();
        if (!$user instanceof User) {
            throw $this->createAccessDeniedException('This can only be accessed by an admin.');
        }
        $employee = $this->entityManager->getRepository(MasterEmployee::class)->find($employeeId);
        if (!$employee) {
            throw $this->createNotFoundException('Employee not found');
        }
        $salaryStructure = new SalaryStructure();
        $salaryStructure->setEmployee($employee);
        $form = $this->createForm(SalaryStructureFormType::class, $salaryStructure);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            try {
                $this->entityManager->persist($salaryStructure);
                $this->entityManager->flush();

                $this->addFlash('success', 'Salary structure created successfully.');
                return $this->redirectToRoute('admin_employee_salary', ['employeeId' => $employeeId]);
            } catch (\Exception $e) {
                $this->addFlash('error', 'Error creating salary structure: ' . $e->getMessage());
                return $this->redirectToRoute('admin_employee_salary_new', ['employeeId' => $employeeId]);
            }
        }

        return $this->render('admin/SalaryStructure/new.html.twig', [
            'form' => $form->createView(),
            'employee' => $employee,
            'mode' => 'create',
        ]);
    }

    #[Route('/employee/{employeeId}/salary/{id}/edit', name: 'admin_employee_salary_edit', methods: ['GET', 'POST'])]
    public function edit(Request $request, int $employeeId, SalaryStructure $salaryStructure): Response
    {
        $employee = $this->entityManager->getRepository(MasterEmployee::class)->find($employeeId);
        if (!$employee) {
            throw $this->createNotFoundException('Employee not found');
        }

        $form = $this->createForm(SalaryStructureFormType::class, $salaryStructure);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $this->entityManager->flush();

            $this->addFlash('success', 'Salary structure updated successfully.');
            return $this->redirectToRoute('admin_employee_salary', ['employeeId' => $employeeId]);
        }

        return $this->render('admin/SalaryStructure/new.html.twig', [
            'form' => $form->createView(),
            'employee' => $employee,
            'mode' => 'edit',
        ]);
    }

    #[Route('/employee/{employeeId}/salary/{id}/delete', name: 'admin_employee_salary_delete', methods: ['POST'])]
    public function delete(Request $request, SalaryStructure $salaryStructure): Response
    {
        $token = $request->request->get('_token');

        if (!$this->isCsrfTokenValid('delete_salary_structure_' . $salaryStructure->getId(), $token)) {
            return $this->json([
                'success' => false,
                'message' => 'Invalid CSRF token.'
            ], Response::HTTP_FORBIDDEN);
        }

        try {
            $this->entityManager->remove($salaryStructure);
            $this->entityManager->flush();

            return $this->json([
                'success' => true,
                'message' => 'Salary structure deleted successfully.'
            ], Response::HTTP_OK);
        } catch (\Exception $e) {
            return $this->json([
                'success' => false,
                'message' => 'Error deleting salary structure: ' . $e->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}