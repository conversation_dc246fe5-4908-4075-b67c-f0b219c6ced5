class DashboardManager {
    constructor() {
        this.currentUser = null;
        this.sessionStartTime = null; // Will be loaded from active session
        this.sessionTimer = null;
        this.connectionCheckInterval = null;
        this.init();
    }

    async init() {
        await this.loadUserData();
        await this.loadActiveSession(); // Load active session before starting timer
        this.setupEventListeners();
        this.startSessionTimer();
        this.startConnectionCheck();
        this.startStatusMonitoring();
        this.startTrackingMonitoring();
        this.startTimeStatsMonitoring();
        this.startIdleDetectionMonitoring();
        this.startScreenshotMonitoring();
        this.loadAppInfo();
        this.updateDateTime();

        // Show loader for Recent Sessions instead of loading dummy data
        this.showRecentSessionsLoader();
    }

    setupEventListeners() {

        // Action buttons
        document.getElementById('syncDataBtn').addEventListener('click', () => {
            this.syncData();
        });



        document.getElementById('viewProfileBtn').addEventListener('click', () => {
            this.viewProfile();
        });

        document.getElementById('settingsBtn').addEventListener('click', () => {
            this.openSettings();
        });



        // Update date/time every minute
        setInterval(() => {
            this.updateDateTime();
        }, 60000);

        // Record activity on user interactions
        document.addEventListener('click', () => {
            window.electronAPI.recordActivity();
        });

        document.addEventListener('keydown', () => {
            window.electronAPI.recordActivity();
        });

        document.addEventListener('mousemove', () => {
            window.electronAPI.recordActivity();
        });
    }

    async loadUserData() {
        try {
            const userData = await window.electronAPI.storeGet('currentUser');
            if (userData) {
                this.currentUser = userData;
                this.updateUserInterface();
            } else {
                // No user data found, redirect to login
                window.electronAPI.logout();
            }
        } catch (error) {
            console.error('Failed to load user data:', error);
            this.showToast('error', 'Failed to load user data');
        }
    }

    updateUserInterface() {
        if (this.currentUser) {
            document.getElementById('userName').textContent = this.currentUser.name || this.currentUser.email;
            document.getElementById('welcomeMessage').textContent = `Welcome back, ${this.currentUser.name || 'User'}!`;
        }
    }

    async loadActiveSession() {
        try {
            // Get current session data from the tracking system
            const detailedStats = await window.electronAPI.getDetailedTodayStats();

            if (detailedStats && detailedStats.currentSession && detailedStats.currentSession.startTime) {
                // Restore session start time from active session
                this.sessionStartTime = new Date(detailedStats.currentSession.startTime);
                console.log('Restored session start time:', this.sessionStartTime);
            } else {
                // No active session, use current time as fallback
                this.sessionStartTime = new Date();
                console.log('No active session found, using current time');
            }
        } catch (error) {
            console.error('Failed to load active session:', error);
            // Fallback to current time
            this.sessionStartTime = new Date();
        }
    }

    startSessionTimer() {
        this.sessionTimer = setInterval(async () => {
            try {
                // Check for new session and update session start time
                await this.checkForNewSession();

                // Check current status first
                const idleStatus = await window.electronAPI.getIdleDetectionStatus();
                const isOnBreak = idleStatus && (idleStatus.currentState === 'on_break' || idleStatus.currentState === 'idle');

                // If user is on break, show 00:00:00 (timer stopped)
                if (isOnBreak) {
                    document.getElementById('sessionTime').textContent = '00:00:00';
                    return;
                }

                // Only calculate time if we have a valid session start time AND user is not on break
                if (this.sessionStartTime) {
                    const now = new Date();
                    const diff = now - this.sessionStartTime;
                    const hours = Math.floor(diff / (1000 * 60 * 60));
                    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
                    const seconds = Math.floor((diff % (1000 * 60)) / 1000);

                    document.getElementById('sessionTime').textContent =
                        `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                } else {
                    // Show 00:00:00 if no session is active
                    document.getElementById('sessionTime').textContent = '00:00:00';
                }
            } catch (error) {
                // Fallback to normal behavior if status check fails
                if (this.sessionStartTime) {
                    const now = new Date();
                    const diff = now - this.sessionStartTime;
                    const hours = Math.floor(diff / (1000 * 60 * 60));
                    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
                    const seconds = Math.floor((diff % (1000 * 60)) / 1000);

                    document.getElementById('sessionTime').textContent =
                        `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                } else {
                    document.getElementById('sessionTime').textContent = '00:00:00';
                }
            }
        }, 1000);
    }

    async checkForNewSession() {
        try {
            // Get current session data
            const detailedStats = await window.electronAPI.getDetailedTodayStats();

            if (detailedStats && detailedStats.currentSession && detailedStats.currentSession.startTime) {
                const newSessionStartTime = new Date(detailedStats.currentSession.startTime);

                // Check if this is a different session (different start time)
                if (!this.sessionStartTime || newSessionStartTime.getTime() !== this.sessionStartTime.getTime()) {
                    console.log('🔄 New session detected - resetting timer');
                    console.log('Old session start:', this.sessionStartTime);
                    console.log('New session start:', newSessionStartTime);

                    // Update to new session start time (this resets the timer)
                    this.sessionStartTime = newSessionStartTime;
                }
            } else if (this.sessionStartTime) {
                // No active session but we had one before - session ended
                console.log('⏹️ Session ended - stopping timer');
                this.sessionStartTime = null;
            }
        } catch (error) {
            // Silently handle errors to avoid spam in console
            // console.error('Failed to check for new session:', error);
        }
    }

    startConnectionCheck() {
        this.checkConnection();
        this.connectionCheckInterval = setInterval(() => {
            this.checkConnection();
        }, 30000); // Check every 30 seconds
    }

    startStatusMonitoring() {
        this.updateStatus();
        this.statusCheckInterval = setInterval(() => {
            this.updateStatus();
        }, 10000); // Check every 10 seconds
    }

    async updateStatus() {
        try {
            const statusInfo = await window.electronAPI.getStatus();
            const statusElement = document.getElementById('employeeStatus');
            const statusCard = document.getElementById('statusCard');
            const statusIcon = document.getElementById('statusIcon');

            if (statusInfo && statusElement) {
                statusElement.textContent = statusInfo.display.text;

                // Update card styling based on status
                statusCard.className = 'status-card';
                statusIcon.innerHTML = '';

                switch (statusInfo.status) {
                    case 'active':
                        statusCard.classList.add('status-active');
                        statusIcon.innerHTML = '<i class="fas fa-user-check" style="color: #4CAF50;"></i>';
                        break;
                    case 'idle':
                        statusCard.classList.add('status-idle');
                        statusIcon.innerHTML = '<i class="fas fa-user-clock" style="color: #FF9800;"></i>';
                        break;
                    case 'inactive':
                        statusCard.classList.add('status-inactive');
                        statusIcon.innerHTML = '<i class="fas fa-user-times" style="color: #F44336;"></i>';
                        break;
                    default:
                        statusIcon.innerHTML = '<i class="fas fa-user" style="color: #666;"></i>';
                }
            }
        } catch (error) {
            console.error('Failed to update status:', error);
        }
    }

    startTrackingMonitoring() {
        this.updateTrackingStatus();
        this.trackingCheckInterval = setInterval(() => {
            this.updateTrackingStatus();
        }, 5000); // Check every 5 seconds
    }

    async updateTrackingStatus() {
        try {
            const trackingStatus = await window.electronAPI.getTrackingStatus();
            const statusElement = document.getElementById('trackingStatus');
            const trackingCard = document.getElementById('trackingCard');
            const trackingIcon = document.getElementById('trackingIcon');

            if (trackingStatus && statusElement) {
                if (trackingStatus.isTracking) {
                    statusElement.textContent = 'Active - Silent Mode';
                    trackingCard.className = 'status-card tracking-active';
                    trackingIcon.innerHTML = '<i class="fas fa-play-circle" style="color: #4CAF50;"></i>';

                    // Show current session type if available
                    if (trackingStatus.currentSession) {
                        const sessionType = trackingStatus.currentSession.logType;
                        statusElement.textContent = `Active - ${sessionType.charAt(0).toUpperCase() + sessionType.slice(1)}`;
                    }
                } else {
                    statusElement.textContent = 'Stopped';
                    trackingCard.className = 'status-card tracking-inactive';
                    trackingIcon.innerHTML = '<i class="fas fa-stop-circle" style="color: #F44336;"></i>';
                }
            }
        } catch (error) {
            console.error('Failed to update tracking status:', error);
        }
    }

    startTimeStatsMonitoring() {
        this.updateTimeStats();
        this.timeStatsInterval = setInterval(() => {
            this.updateTimeStats();
        }, 10000); // Update every 10 seconds
    }

    async updateTimeStats() {
        try {
            const detailedStats = await window.electronAPI.getDetailedTodayStats();

            if (detailedStats && detailedStats.summary) {
                const stats = detailedStats.summary;

                // Update time stat cards
                document.getElementById('workTime').textContent = this.formatDuration(stats.workTime);
                document.getElementById('breakTime').textContent = this.formatDuration(stats.breakTime);
                document.getElementById('idleTime').textContent = this.formatDuration(stats.idleTime);
                document.getElementById('totalSessions').textContent = stats.totalSessions;

                // Update current session info
                this.updateCurrentSessionInfo(detailedStats.currentSession);

                // Update recent sessions
                this.updateRecentSessions(detailedStats.sessions);
            }
        } catch (error) {
            console.error('Failed to update time stats:', error);
        }
    }

    updateCurrentSessionInfo(currentSession) {
        const sessionTypeElement = document.getElementById('currentSessionType');
        const sessionDurationElement = document.getElementById('currentSessionDuration');
        const sessionInfoElement = document.getElementById('currentSessionInfo');

        if (currentSession && currentSession.startTime) {
            const startTime = new Date(currentSession.startTime);
            const now = new Date();
            const duration = Math.floor((now - startTime) / 1000);

            sessionTypeElement.textContent = `${currentSession.logType} Session`;
            sessionDurationElement.textContent = this.formatDuration(duration);

            // Update styling based on session type
            sessionInfoElement.className = `current-session-info ${currentSession.logType}`;
        } else {
            sessionTypeElement.textContent = 'No active session';
            sessionDurationElement.textContent = '--:--';
            sessionInfoElement.className = 'current-session-info inactive';
        }
    }

    updateRecentSessions(sessions) {
        const activityLog = document.getElementById('activityLog');

        if (sessions && sessions.length > 0) {
            // Show ALL sessions (removed limit of 5)
            const recentSessions = sessions.slice().reverse();

            activityLog.innerHTML = recentSessions.map(session => {
                const startTime = new Date(session.start_time);
                const icon = this.getSessionIcon(session.log_type);
                const duration = session.durationFormatted || this.formatDuration(session.duration || 0);

                return `
                    <div class="activity-item">
                        <div class="activity-icon ${session.log_type}">
                            <i class="fas fa-${icon}"></i>
                        </div>
                        <div class="activity-content">
                            <p><strong>${session.log_type.charAt(0).toUpperCase() + session.log_type.slice(1)} Session</strong></p>
                            <span class="activity-details">${duration} • ${startTime.toLocaleTimeString()}</span>
                        </div>
                    </div>
                `;
            }).join('');
        } else {
            // Show message when no sessions available
            activityLog.innerHTML = `
                <div class="activity-item">
                    <div class="activity-icon">
                        <i class="fas fa-info-circle" style="color: #6c757d;"></i>
                    </div>
                    <div class="activity-content">
                        <p><strong>No sessions today</strong></p>
                        <span class="activity-details">Start working to see your sessions here</span>
                    </div>
                </div>
            `;
        }
    }

    showRecentSessionsLoader() {
        const activityLog = document.getElementById('activityLog');
        activityLog.innerHTML = `
            <div class="activity-item">
                <div class="activity-icon">
                    <i class="fas fa-spinner fa-spin" style="color: #667eea;"></i>
                </div>
                <div class="activity-content">
                    <p><strong>Loading sessions...</strong></p>
                    <span class="activity-details">Please wait while we load your session data</span>
                </div>
            </div>
        `;
    }

    getSessionIcon(sessionType) {
        const iconMap = {
            'work': 'briefcase',
            'break': 'coffee',
            'idle': 'pause-circle',
            'offline': 'wifi-slash'
        };
        return iconMap[sessionType] || 'clock';
    }

    formatDuration(seconds) {
        if (!seconds || seconds < 0) return '0m';

        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);

        if (hours > 0) {
            return `${hours}h ${minutes}m`;
        } else {
            return `${minutes}m`;
        }
    }

    startIdleDetectionMonitoring() {
        this.updateIdleDetectionStatus();
        this.idleDetectionInterval = setInterval(() => {
            this.updateIdleDetectionStatus();
        }, 5000); // Update every 5 seconds
    }

    async updateIdleDetectionStatus() {
        try {
            const idleStatus = await window.electronAPI.getIdleDetectionStatus();

            if (idleStatus) {
                // Update idle state
                const currentIdleStateElement = document.getElementById('currentIdleState');
                const lastActivityTimeElement = document.getElementById('lastActivityTime');
                const activityLevelElement = document.getElementById('activityLevel');
                const idleDetectionInfoElement = document.getElementById('idleDetectionInfo');

                if (currentIdleStateElement) {
                    currentIdleStateElement.textContent = this.formatIdleState(idleStatus.state);
                }

                if (lastActivityTimeElement && idleStatus.lastActivityTime) {
                    const lastActivity = new Date(idleStatus.lastActivityTime);
                    const timeSince = Math.floor((new Date() - lastActivity) / 1000);
                    lastActivityTimeElement.textContent = this.formatTimeSince(timeSince);
                }

                if (activityLevelElement && idleStatus.activityPatterns) {
                    const level = idleStatus.activityPatterns.averageActivityLevel || 0;
                    activityLevelElement.textContent = this.formatActivityLevel(level);
                }

                // Update styling based on idle state
                if (idleDetectionInfoElement) {
                    idleDetectionInfoElement.className = `idle-detection-info ${idleStatus.state}`;
                }
            }
        } catch (error) {
            console.error('Failed to update idle detection status:', error);
        }
    }

    formatIdleState(state) {
        const stateMap = {
            'active': 'Active',
            'thinking': 'Thinking',
            'short_idle': 'Short Idle',
            'long_idle': 'Long Idle',
            'inactive': 'Inactive',
            'away': 'Away'
        };
        return stateMap[state] || 'Unknown';
    }

    formatTimeSince(seconds) {
        if (seconds < 60) {
            return 'Just now';
        } else if (seconds < 3600) {
            const minutes = Math.floor(seconds / 60);
            return `${minutes}m ago`;
        } else {
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            return `${hours}h ${minutes}m ago`;
        }
    }

    formatActivityLevel(level) {
        if (level > 3) {
            return 'Very High';
        } else if (level > 2) {
            return 'High';
        } else if (level > 1) {
            return 'Normal';
        } else if (level > 0.5) {
            return 'Low';
        } else {
            return 'Very Low';
        }
    }

    startScreenshotMonitoring() {
        this.updateScreenshotStatus();
        this.screenshotInterval = setInterval(() => {
            this.updateScreenshotStatus();
        }, 10000); // Update every 10 seconds
    }



    async updateScreenshotStatus() {
        try {
            // Get both screenshot status and debug info for better visibility
            const screenshotStatus = await window.electronAPI.getScreenshotStatus();
            const screenshotDebug = await window.electronAPI.getScreenshotDebugInfo();

            console.log('📸 Screenshot Status:', screenshotStatus);
            console.log('📸 Screenshot Debug:', screenshotDebug);

            if (screenshotDebug) {
                // Update screenshot status based on debug info (more accurate)
                const statusElement = document.getElementById('screenshotStatus');
                const countElement = document.getElementById('screenshotCount');
                const lastElement = document.getElementById('lastScreenshot');
                const screenshotInfoElement = document.getElementById('screenshotInfo');
                const manualBtn = document.querySelector('.manual-screenshot-btn');

                if (statusElement) {
                    if (!screenshotDebug.config.enabled) {
                        statusElement.textContent = 'Disabled';
                        statusElement.style.color = '#f44336';
                    } else {
                        // If enabled, show as Active (since screenshots are working)
                        statusElement.textContent = 'Active';
                        statusElement.style.color = '#4caf50';
                    }
                }

                if (countElement) {
                    countElement.textContent = screenshotDebug.dailyCount || 0;
                }

                if (lastElement && screenshotDebug.lastCaptureTime) {
                    const lastCapture = new Date(screenshotDebug.lastCaptureTime);
                    lastElement.textContent = lastCapture.toLocaleTimeString();
                } else if (lastElement) {
                    lastElement.textContent = 'Never';
                }

                // Update styling based on status
                if (screenshotInfoElement) {
                    if (!screenshotDebug.config.enabled) {
                        screenshotInfoElement.className = 'screenshot-info disabled';
                    } else if (screenshotDebug.isCapturing) {
                        screenshotInfoElement.className = 'screenshot-info capturing';
                    } else {
                        screenshotInfoElement.className = 'screenshot-info';
                    }
                }

                // Enable/disable manual capture button
                if (manualBtn) {
                    manualBtn.disabled = !screenshotDebug.config?.enabled;
                }

                // Show configuration details in console for debugging
                console.log('📸 Screenshot Config:', screenshotDebug.config);
            }
        } catch (error) {
            console.error('Failed to update screenshot status:', error);
        }
    }



    // Manual screenshot capture removed from UI
    // async captureManualScreenshot() {
    //     try {
    //         const result = await window.electronAPI.captureManualScreenshot();
    //         if (result) {
    //             this.showToast(`Screenshot captured: ${result.fileName}`, 'success');
    //             await this.updateScreenshotStatus();
    //         } else {
    //             this.showToast('Failed to capture screenshot', 'error');
    //         }
    //     } catch (error) {
    //         console.error('Failed to capture manual screenshot:', error);
    //         this.showToast('Failed to capture screenshot', 'error');
    //     }
    // }

    // Test screenshots removed from UI
    // async startTestScreenshots() {
    //     try {
    //         const result = await window.electronAPI.startTestScreenshots();
    //         if (result) {
    //             this.showToast('Test screenshots started! Will capture every 30 seconds', 'success');
    //             await this.updateScreenshotStatus();
    //         } else {
    //             this.showToast('Failed to start test screenshots', 'error');
    //         }
    //     } catch (error) {
    //         console.error('Failed to start test screenshots:', error);
    //         this.showToast('Failed to start test screenshots', 'error');
    //     }
    // }

    // Debug info removed from UI
    // async showScreenshotDebug() {
    //     try {
    //         const debugInfo = await window.electronAPI.getScreenshotDebug();
    //         console.log('📸 Screenshot Debug Info:', debugInfo);
    //         if (debugInfo) {
    //             const info = `Screenshot Debug Info:\n- Is Capturing: ${debugInfo.isCapturing}\n- Enabled: ${debugInfo.config.enabled}\n- Interval: ${debugInfo.config.intervalSeconds}s\n- Daily Count: ${debugInfo.dailyCount}`;
    //             alert(info);
    //         }
    //     } catch (error) {
    //         console.error('Failed to get debug info:', error);
    //     }
    // }

    async openDevTools() {
        try {
            const result = await window.electronAPI.openDevTools();
            if (result) {
                this.showToast('Developer Tools opened! Check the Console tab for logs', 'success');
            } else {
                this.showToast('Failed to open Developer Tools', 'error');
            }
        } catch (error) {
            console.error('Failed to open DevTools:', error);
            this.showToast('Failed to open Developer Tools', 'error');
        }
    }

    async reloadConfiguration() {
        try {
            const button = document.querySelector('.reload-config-btn');
            if (button) {
                button.disabled = true;
                button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Reloading...';
            }

            const result = await window.electronAPI.forceReloadConfig();

            if (result) {
                this.showToast('Configuration reloaded from server successfully!', 'success');
                // Refresh all status displays
                await this.updateTrackingStatus();
                await this.updateTimeStats();
                await this.updateScreenshotStatus();
                await this.updateIdleDetectionStatus();
            } else {
                this.showToast('Failed to reload configuration', 'error');
            }
        } catch (error) {
            console.error('Failed to reload configuration:', error);
            this.showToast('Failed to reload configuration', 'error');
        } finally {
            // Reset button
            const button = document.querySelector('.reload-config-btn');
            if (button) {
                button.disabled = false;
                button.innerHTML = '<i class="fas fa-sync"></i> Reload Config';
            }
        }
    }

    // Configuration is now managed from admin panel
    showConfigurationInfo() {
        this.showToast('Settings are managed from the admin panel at http://127.0.0.1:8000/admin', 'info');
    }

    async checkConnection() {
        const statusElement = document.getElementById('connectionStatus');
        const cardElement = document.getElementById('connectionCard');
        
        try {
            const response = await window.electronAPI.apiRequest({
                url: 'http://localhost:8000/api/health',
                method: 'GET',
                timeout: 5000
            });

            if (response && response.status === 'ok') {
                statusElement.textContent = 'Online';
                cardElement.className = 'status-card online';
                document.getElementById('footerStatus').textContent = 'Connected to server';
            } else {
                throw new Error('Invalid response');
            }
        } catch (error) {
            statusElement.textContent = 'Offline';
            cardElement.className = 'status-card';
            document.getElementById('footerStatus').textContent = 'Offline mode - Limited functionality';
        }
    }

    async loadAppInfo() {
        try {
            const version = await window.electronAPI.getAppVersion();
            document.getElementById('appVersion').textContent = `v${version}`;

            const machineId = await window.electronAPI.getMachineId();
            if (machineId) {
                const truncatedId = machineId.substring(0, 8) + '...';
                document.getElementById('deviceId').textContent = truncatedId;
            }

            // Load last sync time
            const lastSync = await window.electronAPI.storeGet('lastSyncTime');
            if (lastSync) {
                const syncDate = new Date(lastSync);
                document.getElementById('lastSync').textContent = this.formatRelativeTime(syncDate);
            }
        } catch (error) {
            console.error('Failed to load app info:', error);
        }
    }

    updateDateTime() {
        const now = new Date();
        const options = { 
            weekday: 'long', 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric' 
        };
        document.getElementById('currentDate').textContent = now.toLocaleDateString('en-US', options);
    }

    // Removed loadRecentActivity() method - we now show real session data instead of dummy activity logs

    async logActivity(action, details = null) {
        try {
            if (this.currentUser) {
                await window.electronAPI.dbRun(
                    'INSERT INTO activity_log (user_id, action, details) VALUES (?, ?, ?)',
                    [this.currentUser.id, action, details]
                );
            }
        } catch (error) {
            console.error('Failed to log activity:', error);
        }
    }

    getActivityIcon(action) {
        const iconMap = {
            'Logged in successfully': 'sign-in-alt',
            'Data synced': 'sync-alt',
            'Profile viewed': 'user-circle',
            'Settings opened': 'cog',
            'Connection restored': 'wifi',
            'Offline mode activated': 'exclamation-triangle'
        };
        return iconMap[action] || 'info-circle';
    }

    formatRelativeTime(date) {
        const now = new Date();
        const diff = now - date;
        const minutes = Math.floor(diff / (1000 * 60));
        const hours = Math.floor(diff / (1000 * 60 * 60));
        const days = Math.floor(diff / (1000 * 60 * 60 * 24));

        if (minutes < 1) return 'Just now';
        if (minutes < 60) return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
        if (hours < 24) return `${hours} hour${hours > 1 ? 's' : ''} ago`;
        return `${days} day${days > 1 ? 's' : ''} ago`;
    }

    async syncData() {
        this.showToast('info', 'Syncing data...');
        
        try {
            // Get auth token
            const token = await window.electronAPI.storeGet('authToken');
            
            if (!token) {
                this.showToast('error', 'No authentication token found');
                return;
            }

            // First sync pending tracking data (time logs, screenshots, app usage, website usage)
            const pendingSyncResult = await window.electronAPI.syncPendingData();
            if (pendingSyncResult) {
                console.log('✅ Pending tracking data synced successfully');
            }

            // Then sync user profile
            const profileResponse = await window.electronAPI.apiRequest({
                url: 'http://localhost:8000/api/employee/profile',
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            if (profileResponse.success) {
                // Update local database
                await window.electronAPI.dbRun(`
                    INSERT OR REPLACE INTO users 
                    (id, email, username, name, roles, employee_code, is_team_leader, is_hr_account, last_sync)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                `, [
                    profileResponse.user.id,
                    profileResponse.user.email,
                    profileResponse.user.username,
                    profileResponse.user.name,
                    JSON.stringify(profileResponse.user.roles),
                    profileResponse.user.employeeCode,
                    profileResponse.user.isTeamLeader ? 1 : 0,
                    profileResponse.user.isHrAccount ? 1 : 0,
                    new Date().toISOString()
                ]);

                // Update stored user data
                await window.electronAPI.storeSet('currentUser', profileResponse.user);
                await window.electronAPI.storeSet('lastSyncTime', new Date().toISOString());

                this.currentUser = profileResponse.user;
                this.updateUserInterface();
                await this.logActivity('Data synced');
                this.loadAppInfo(); // Refresh last sync time
                
                this.showToast('success', 'All data synced successfully');
            } else {
                throw new Error(profileResponse.message || 'Sync failed');
            }
        } catch (error) {
            console.error('Sync failed:', error);
            this.showToast('error', 'Sync failed: ' + error.message);
        }
    }

    async viewProfile() {
        await this.logActivity('Profile viewed');
        this.showToast('info', 'Profile feature coming soon');
    }

    async openSettings() {
        await this.logActivity('Settings opened');
        this.showToast('info', 'Settings feature coming soon');
    }





    showToast(type, message) {
        const toast = document.getElementById('toast');
        const toastIcon = toast.querySelector('.toast-icon');
        const toastMessage = toast.querySelector('.toast-message');

        // Set icon based on type
        const icons = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            info: 'fas fa-info-circle'
        };

        toastIcon.className = `toast-icon ${icons[type] || icons.info}`;
        toastMessage.textContent = message;
        toast.className = `toast ${type}`;
        toast.style.display = 'flex';

        // Auto hide after 3 seconds
        setTimeout(() => {
            this.hideToast();
        }, 3000);
    }

    hideToast() {
        const toast = document.getElementById('toast');
        toast.style.display = 'none';
    }

    // Cleanup when dashboard is closed
    destroy() {
        if (this.sessionTimer) {
            clearInterval(this.sessionTimer);
        }
        if (this.connectionCheckInterval) {
            clearInterval(this.connectionCheckInterval);
        }
        if (this.statusCheckInterval) {
            clearInterval(this.statusCheckInterval);
        }
        if (this.trackingCheckInterval) {
            clearInterval(this.trackingCheckInterval);
        }
        if (this.timeStatsInterval) {
            clearInterval(this.timeStatsInterval);
        }
        if (this.idleDetectionInterval) {
            clearInterval(this.idleDetectionInterval);
        }
        if (this.screenshotInterval) {
            clearInterval(this.screenshotInterval);
        }
    }


}

// Global function to hide toast (called from HTML)
function hideToast() {
    const toast = document.getElementById('toast');
    toast.style.display = 'none';
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.dashboardManager = new DashboardManager();
});

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
    if (window.dashboardManager) {
        window.dashboardManager.destroy();
    }
});
