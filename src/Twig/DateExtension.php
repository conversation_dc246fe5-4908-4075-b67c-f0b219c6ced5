<?php
namespace App\Twig;

use App\Service\TimezoneService;
use DateTimeInterface;
use Twig\Extension\AbstractExtension;
use Twig\TwigFilter;

class DateExtension extends AbstractExtension
{
    public function __construct(private readonly TimezoneService $timezoneService)
    {
    }
    public function getFilters(): array
    {
        return [
            new TwigFilter('custom_date', [$this, 'formatDate']),
            new TwigFilter('custom_dateTime', [$this, 'formatDateTime']),
        ];
    }

    public function formatDate(?\DateTimeInterface $date, string $format = 'd-m-Y'): string
    {
        return $date ? $date->format($format) : 'N/A';
    }
    public function formatDateTime(?DateTimeInterface $date, string $format = 'd-m-Y H:i:s'): string
    {
        if ($date === null) {
            return 'N/A';
        }
        return $this->timezoneService->convertToLocalTime($date)->format($format);
    }
}
?>