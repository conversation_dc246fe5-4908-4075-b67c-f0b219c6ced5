<?php
namespace App\Repository;

use App\Entity\MasterEmployee;
use App\Entity\ResetPasswordRequestEmployee;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use SymfonyCasts\Bundle\ResetPassword\Persistence\ResetPasswordRequestRepositoryInterface;
use SymfonyCasts\Bundle\ResetPassword\Persistence\Repository\ResetPasswordRequestRepositoryTrait;
use SymfonyCasts\Bundle\ResetPassword\Model\ResetPasswordRequestInterface;

/**
 * @extends ServiceEntityRepository<ResetPasswordRequestEmployee>
 */
class ResetPasswordRequestEmployeeRepository extends ServiceEntityRepository implements ResetPasswordRequestRepositoryInterface
{
    use ResetPasswordRequestRepositoryTrait;

    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, ResetPasswordRequestEmployee::class);
    }

    /**
     * Creates a ResetPasswordRequestEmployee entity.
     */
    public function createResetPasswordRequest(object $user, \DateTimeInterface $expiresAt, string $selector, string $hashedToken): ResetPasswordRequestInterface
    {
        if (!$user instanceof MasterEmployee) {
            throw new \InvalidArgumentException('User must be an instance of MasterEmployee.');
        }

        return new ResetPasswordRequestEmployee($user, $expiresAt, $selector, $hashedToken);
    }
}
