<?php

namespace App\Controller\Admin;

use App\Entity\Task;
use App\Entity\TaskStatusHistory;
use App\Entity\TaskTimeLog;
use App\Entity\User;
use App\Entity\MasterEmployee;
use App\Form\TaskType;
use App\Message\TaskAndProjectNotificationMessage;
use App\Repository\ProjectRepository;
use App\Repository\TaskRepository;
use App\Service\Admin\TaskService;
use App\Service\Common\FilterService;
use App\Service\Front\TaskProgressService;
use App\Traits\ExceptionLoggerTrait;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Messenger\Exception\ExceptionInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Csrf\CsrfToken;
use Symfony\Component\Security\Csrf\CsrfTokenManagerInterface;

#[Route('/admin/project')]
class TaskController extends AbstractController
{
    use ExceptionLoggerTrait;
    public function __construct(
        private readonly ProjectRepository $projectRepository,
        private readonly EntityManagerInterface $entityManager,
        private readonly TaskService $taskService,
        private readonly CsrfTokenManagerInterface $csrfTokenManager,
        private readonly TaskProgressService    $taskProgressService,
        private readonly MessageBusInterface $messageBus,
        private readonly FilterService $filterService,
    ) {
    }

    #[Route('/{project_id}/task', name: 'project_task', methods: ['GET', 'POST'])]
    public function index(Request $request, int $project_id): Response
    {
        $project = $this->projectRepository->find($project_id);

        if (!$project) {
            throw new NotFoundHttpException('Project not found');
        }

        $table = $this->taskService->createTaskTable($request, $project_id,true);

        if ($table->isCallback()) {
            return $table->getResponse();
        }

        return $this->render('admin/task/index.html.twig', [
            'datatable' => $table,
            'project' => $project,
        ]);
    }

    /**
     * @throws ExceptionInterface
     */
    #[Route('/{project_id}/task/create', name: 'task_create', methods: ['GET', 'POST'])]
    public function create(Request $request, int $project_id): Response
    {
        $project = $this->projectRepository->find($project_id);

        if (!$project) {
            throw new NotFoundHttpException('Project not found');
        }

        $task = new Task();
        $task->setProject($project);
        $form = $this->createForm(TaskType::class, $task);

        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $user = $this->getUser();

            if ($user instanceof User) {
                $task->setAssignedByAdmin($user);
            } elseif ($user instanceof MasterEmployee) {
                $task->setAssignedBy($user);
                $task->setAssignedByAdmin(null);
            } else {
                throw new \LogicException('Logged-in user must be either a User or MasterEmployee.');
            }

            if ($task->getAssignedTo()) {
                $task->setAssignedAt(new \DateTime());
            } else {
                $task->setAssignedAt(null);
            }
            $task->setCreatedAt(new \DateTime());
            $task->setUpdatedAt(new \DateTime());

            $this->entityManager->persist($task);
            $this->entityManager->flush();
            $statusHistory = new TaskStatusHistory();
            $statusHistory->setTask($task);
            $statusHistory->setChangedBy($user instanceof MasterEmployee ? $user : null);
            $statusHistory->setChangedByAdmin($user instanceof User ? $user : null);
            $statusHistory->setOldStatus('N/A');
            $statusHistory->setNewStatus($task->getStatus());
            $statusHistory->setChangedAt(new \DateTime());
            $this->entityManager->persist($statusHistory);
            $this->entityManager->flush();
            if ($task->getAssignedTo()) {
                try {
                    $message = new TaskAndProjectNotificationMessage('task', $task->getId(), 'create', (array)null);
                    $this->messageBus->dispatch($message);
                }catch (\Throwable $e) {
                    $this->log('Notification dispatch failed: ' . $e->getMessage());
                }
            }

            $this->addFlash('success', 'Task created successfully.');
            return $this->redirectToRoute('project_task', ['project_id' => $project_id]);
        }

        return $this->render('admin/task/new.html.twig', [
            'form' => $form->createView(),
            'project' => $project,
            'mode' => 'create',
        ]);
    }

    /**
     * @throws ExceptionInterface
     */
    #[Route('/{project_id}/task/edit/{id}', name: 'task_edit', methods: ['GET', 'POST'])]
    public function edit(Request $request, int $project_id, Task $task): Response
    {
        $project = $this->projectRepository->find($project_id);

        if (!$project) {
            throw new NotFoundHttpException('Project not found');
        }

        if ($task->getProject()->getId() !== $project_id) {
            throw new NotFoundHttpException('Task does not belong to this project');
        }

        $form = $this->createForm(TaskType::class, $task);
        $originalAssignedTo = $task->getAssignedTo();
        $originalDescription = $task->getDescription();
        $originalStatus = $task->getStatus();

        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $user = $this->getUser();
            $newAssignedTo = $task->getAssignedTo();
            $newDescription = $task->getDescription();
            $newStatus = $task->getStatus();

            if ($user instanceof User) {
                $task->setAssignedByAdmin($user);
                $task->setAssignedBy(null);
            } elseif ($user instanceof MasterEmployee) {
                $task->setAssignedBy($user);
                $task->setAssignedByAdmin(null);
            }

            if ($task->getAssignedTo() && !$task->getAssignedAt()) {
                $task->setAssignedAt(new \DateTime());
            } elseif (!$task->getAssignedTo()) {
                $task->setAssignedAt(null);
            }

            if ($originalStatus !== $newStatus) {
                $statusHistory = new TaskStatusHistory();
                $statusHistory->setTask($task);
                $statusHistory->setChangedBy($user instanceof MasterEmployee ? $user : null);
                $statusHistory->setChangedByAdmin($user instanceof User ? $user : null);
                $statusHistory->setOldStatus($originalStatus);
                $statusHistory->setNewStatus($newStatus);
                $statusHistory->setChangedAt(new \DateTime());
                $this->entityManager->persist($statusHistory);
            }
            $task->setUpdatedAt(new \DateTime());
            $this->entityManager->flush();

            if ($task->getAssignedTo()) {
                $changes = [];
                if ($originalAssignedTo !== $newAssignedTo) {
                    $changes[] = 'assignment';
                }
                if ($originalDescription !== $newDescription) {
                    $changes[] = 'description';
                }
                if ($originalStatus !== $newStatus) {
                    $changes[] = 'status';
                }
                $action = !empty($changes) ? 'update' : 'no-change';
                try {
                    $message = new TaskAndProjectNotificationMessage('task', $task->getId(), $action, $changes);
                    $this->messageBus->dispatch($message);
                }catch (\Throwable $e) {
                    $this->log('Notification dispatch failed: ' . $e->getMessage());
                }
            }

            $this->addFlash('success', 'Task updated successfully.');
            return $this->redirectToRoute('project_task', ['project_id' => $project_id]);
        }

        return $this->render('admin/task/new.html.twig', [
            'form' => $form->createView(),
            'task' => $task,
            'project' => $project,
            'mode' => 'edit',
        ]);
    }

    #[Route('/{project_id}/task/delete/{id}', name: 'task_delete', methods: ['POST'])]
    public function delete(Request $request, int $project_id, Task $task): JsonResponse
    {
        $project = $this->projectRepository->find($project_id);

        if (!$project) {
            return $this->json([
                'success' => false,
                'message' => 'Project not found.'
            ], Response::HTTP_NOT_FOUND);
        }

        if ($task->getProject()->getId() !== $project_id) {
            return $this->json([
                'success' => false,
                'message' => 'Task does not belong to this project.'
            ], Response::HTTP_NOT_FOUND);
        }

        $token = $request->request->get('_token');
        if (!$this->csrfTokenManager->isTokenValid(new CsrfToken('delete' . $task->getId(), $token))) {
            return $this->json([
                'success' => false,
                'message' => 'Invalid CSRF token.'
            ], Response::HTTP_FORBIDDEN);
        }

        $this->entityManager->remove($task);
        $this->entityManager->flush();

        return $this->json([
            'success' => true,
            'message' => 'Task has been deleted.'
        ], Response::HTTP_OK);
    }

    #[Route('/{project_id}/task/view/{id}', name: 'task_view', methods: ['GET'])]
    public function view(int $project_id, Task $task): Response
    {
        $project = $this->projectRepository->find($project_id);

        if (!$project) {
            throw new NotFoundHttpException('Project not found');
        }

        if ($task->getProject()->getId() !== $project_id) {
            throw new NotFoundHttpException('Task does not belong to this project');
        }

        $timeLogs = $this->entityManager->getRepository(TaskTimeLog::class)->findBy(
            ['task' => $task],
            ['createdAt' => 'DESC']
        );

        $statusHistories = $task->getStatusHistories()->toArray();

        return $this->render('admin/task/new.html.twig', [
            'task' => $task,
            'project' => $project,
            'mode' => 'view',
            'timeLogs' => $timeLogs,
            'statusHistories' => $statusHistories,
        ]);
    }

    #[Route('/{project_id}/task/{task_id}/employeeProgress', name: 'task_progress_report', methods: ['GET', 'POST'])]
    public function viewProgress(Request $request,int $project_id,  int $task_id): Response
    {
        $task = $this->entityManager->getRepository(Task::class)->find($task_id);
        if (!$task) {
            throw new NotFoundHttpException('Task not found');
        }
        $table = $this->taskProgressService->createTaskProgress($request, $task_id);
        if ($table->isCallback()) {
            return $table->getResponse();
        }
        $project = $this->projectRepository->find($project_id);
        return $this->render('admin/task/taskProgress.html.twig', [
            'datatable' => $table,
            'task' => $task,
            'project' => $project,
        ]);
    }

    #[Route('/{project_id}/task/filter', name: 'admin_task_filter', methods: ['POST'])]
    public function updateTaskFilter(Request $request, int $project_id): JsonResponse
    {
        try {
            $data = json_decode($request->getContent(), true, 512, JSON_THROW_ON_ERROR);
            if (!is_array($data) || !array_key_exists('startDate', $data) || !array_key_exists('endDate', $data)) {
                throw new \InvalidArgumentException('Invalid input data');
            }
            if (empty($data['startDate']) || empty($data['endDate'])) {
                $this->filterService->clearFilters($project_id);
            } else {
                $this->filterService->saveFilters($project_id, $data['startDate'], $data['endDate']);
            }
            return new JsonResponse([
                'success' => true,
                'message' => 'Filters updated successfully.',
            ]);
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'message' => 'Error: ' . $e->getMessage(),
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    #[Route('/{project_id}/task/filter', name: 'admin_task_filter_get', methods: ['GET'])]
    public function getTaskFilters( int $project_id,TaskRepository $taskRepository): JsonResponse
    {
        $filters = $this->filterService->getFilters($project_id, $taskRepository);

        return new JsonResponse([
            'success' => true,
            'filters' => [
                'dateRange' => $filters['dateRange'],
                'minDate' =>  $filters['minDate'],
                'maxDate' => $filters['maxDate'],
            ],
        ]);
    }
}