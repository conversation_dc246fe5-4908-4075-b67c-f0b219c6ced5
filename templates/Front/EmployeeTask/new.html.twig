{% extends 'Front/dashboard/base.html.twig' %}

{% block body %}
    {% include 'Front/partials/breadcrumbs.html.twig' with {
        breadcrumbs: [
            { 'label': 'Dashboard', 'url': url('dashboard_employee') },
            {
                'label': isHistory ? 'Task History' : 'My Tasks',
                'url': isHistory ? path('employee_task_history') : path('employee_task')
            },
            { 'label': task.title , 'url': path('employee_task_progress', { 'task_id': task.id }) },
        ],
        active: mode == 'edit' ? 'Edit Progress' : 'Add Progress'
    } %}
    <div class="content">
        <div class="animated fadeIn">
            <div class="row">
                <div class="col-lg-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <strong class="card-title">{{ mode == 'edit' ? 'Edit Task Progress' : 'Add Task Progress' }}</strong>
                            <div class="d-flex align-items-center">
                                <a href="{{ path('employee_task_progress', { 'task_id': task.id }) }}" class="btn btn-sm btn-outline-secondary mr-2">
                                    <i class="fa fa-list mr-1"></i>Back to List
                                </a>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="progress-form">
                                {{ form_start(form, { 'attr': { 'class': 'needs-validation ', 'novalidate': 'novalidate', 'data-turbo': 'false'} }) }}
                                <div class="form-section">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                {{ form_label(form.progressPercent, 'Progress (%)', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text"><i class="fa fa-percent"></i></span>
                                                    </div>
                                                    {{ form_widget(form.progressPercent, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter progress percentage', 'required': 'required' } }) }}
                                                </div>
                                                {{ form_errors(form.progressPercent) }}
                                                <small class="form-text text-muted">Enter progress percentage (0-100)</small>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                {{ form_label(form.progressDate, 'Progress Date', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text"><i class="fa fa-calendar-check-o"></i></span>
                                                    </div>
                                                    {{ form_widget(form.progressDate, {
                                                        'attr': {
                                                            'class': 'form-control datepicker',
                                                            'data-initial-date': task_progress.progressDate is defined and task_progress.progressDate ? task_progress.progressDate|date('d-m-Y') : ''
                                                        }
                                                    }) }}
                                                </div>
                                                {{ form_errors(form.progressDate) }}
                                                <small class="form-text text-muted">Select the date of this progress</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                {{ form_label(form.hoursWorked, 'Hours Worked', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text"><i class="fa fa-clock-o"></i></span>
                                                    </div>
                                                    {{ form_widget(form.hoursWorked, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter hours worked' } }) }}
                                                </div>
                                                {{ form_errors(form.hoursWorked) }}
                                                <small class="form-text text-muted">Enter hours worked on this task</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="mb-3">
                                                {{ form_label(form.comments, 'Comment', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                                {{ form_widget(form.comments, { 'attr': { 'class': 'form-control', 'required': 'required' } }) }}
                                                {{ form_errors(form.comments) }}
                                                <small class="form-text text-muted">Provide any additional comment</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="action-footer mt-4">
                                    <a href="{{ path('employee_task_progress', { 'task_id': task.id }) }}" class="btn btn-secondary mr-2">
                                        Cancel
                                    </a>
                                    <button type="submit" class="btn btn-primary pr-3 pl-3" id="submitButton" data-update="{{  mode == 'edit' ? 'true' : 'false' }}">
                                        {{ mode == 'edit' ? 'Update Progress' : 'Save Progress' }}
                                    </button>
                                </div>
                                {{ form_end(form) }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="clearfix"></div>
    <link rel="stylesheet" href="{{ asset('assets/css/Error/error.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/AdminForm/Form.css') }}">
    <script src="{{ asset('assets/js/datatables.min.js') }}"></script>
    <script src="{{ asset('bundles/datatables/js/datatables.js') }}"></script>
    <script src="{{ asset('assets/js/moment.min.js') }}"></script>
    <script src="{{ asset('assets/js/daterangepicker.min.js') }}"></script>
    <script src="{{ asset('assets/js/datepickerUtility.js') }}"></script>
    <script src="{{ asset('assets/js/admin/progressTask.js') }}"></script>
{% endblock %}