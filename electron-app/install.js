#!/usr/bin/env node

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Brainee HRM Desktop - Installation Script\n');

// Check if Node.js version is compatible
const nodeVersion = process.version;
const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);

if (majorVersion < 16) {
    console.log('❌ Node.js version 16 or higher is required');
    console.log(`   Current version: ${nodeVersion}`);
    console.log('   Please update Node.js and try again');
    process.exit(1);
}

console.log(`✅ Node.js version ${nodeVersion} is compatible`);

// Check if npm is available
function checkNpm() {
    return new Promise((resolve, reject) => {
        const npm = spawn('npm', ['--version'], { stdio: 'pipe' });
        npm.on('close', (code) => {
            if (code === 0) {
                resolve();
            } else {
                reject(new Error('npm not found'));
            }
        });
    });
}

// Install dependencies
function installDependencies() {
    return new Promise((resolve, reject) => {
        console.log('📦 Installing dependencies...');
        console.log('   This may take a few minutes...\n');
        
        const npm = spawn('npm', ['install'], { 
            stdio: 'inherit',
            cwd: __dirname
        });
        
        npm.on('close', (code) => {
            if (code === 0) {
                console.log('\n✅ Dependencies installed successfully');
                resolve();
            } else {
                reject(new Error('npm install failed'));
            }
        });
    });
}

// Run setup test
function runSetupTest() {
    return new Promise((resolve, reject) => {
        console.log('\n🔍 Running setup test...\n');
        
        const test = spawn('node', ['test-setup.js'], { 
            stdio: 'inherit',
            cwd: __dirname
        });
        
        test.on('close', (code) => {
            if (code === 0) {
                resolve();
            } else {
                reject(new Error('Setup test failed'));
            }
        });
    });
}

// Create desktop shortcut (Windows)
function createWindowsShortcut() {
    if (process.platform !== 'win32') return;
    
    try {
        const desktopPath = path.join(require('os').homedir(), 'Desktop');
        const shortcutPath = path.join(desktopPath, 'Brainee HRM Desktop.lnk');
        
        // This would require additional Windows-specific modules
        // For now, just log the instruction
        console.log('\n📌 To create a desktop shortcut on Windows:');
        console.log('   1. Right-click on Desktop');
        console.log('   2. Select "New" > "Shortcut"');
        console.log(`   3. Enter path: ${path.join(__dirname, 'node_modules', '.bin', 'electron.cmd')} ${__dirname}`);
        console.log('   4. Name it "Brainee HRM Desktop"');
    } catch (error) {
        console.log('⚠️  Could not create desktop shortcut');
    }
}

// Main installation process
async function install() {
    try {
        // Check npm
        await checkNpm();
        console.log('✅ npm is available');
        
        // Install dependencies
        await installDependencies();
        
        // Run setup test
        await runSetupTest();
        
        // Create shortcuts
        createWindowsShortcut();
        
        console.log('\n🎉 Installation completed successfully!');
        console.log('\n📋 What was installed:');
        console.log('   ✅ Electron framework');
        console.log('   ✅ SQLite database support');
        console.log('   ✅ Auto-launch functionality');
        console.log('   ✅ Offline mode support');
        console.log('   ✅ API communication tools');
        
        console.log('\n🚀 Next steps:');
        console.log('   1. Start your Symfony backend server');
        console.log('   2. Run: npm start');
        console.log('   3. Login with your employee credentials');
        
        console.log('\n📝 Important notes:');
        console.log('   • The app will auto-start with your computer');
        console.log('   • Once logged in, you cannot logout (security feature)');
        console.log('   • Works offline when server is not available');
        console.log('   • Data is stored securely in local SQLite database');
        
    } catch (error) {
        console.log('\n❌ Installation failed:', error.message);
        console.log('\n🔧 Troubleshooting:');
        console.log('   • Make sure you have Node.js 16+ installed');
        console.log('   • Check your internet connection');
        console.log('   • Try running: npm cache clean --force');
        console.log('   • Run the installation again');
        process.exit(1);
    }
}

// Handle interruption
process.on('SIGINT', () => {
    console.log('\n\n⚠️  Installation interrupted');
    console.log('   You can run this script again to continue');
    process.exit(1);
});

// Start installation
install();
