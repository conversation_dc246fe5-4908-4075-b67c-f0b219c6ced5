<?php

namespace App\Service\Admin;

use App\Entity\Employee;
use App\Entity\MasterEmployee;
use App\Traits\ExceptionLoggerTrait;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\DecodingExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use App\Repository\SettingRepository;

class EmployeeService
{
    use ExceptionLoggerTrait;

    private const BATCH_SIZE = 20;

    public function __construct(
        private readonly HttpClientInterface    $httpClient,
        ParameterBagInterface                   $parameterBag,
        private readonly EntityManagerInterface $entityManager,
        private readonly SettingRepository      $configurationRepository
    )
    {
        $this->initializeLogger($parameterBag, 'employee_service');
    }

    /**
     * @param string $fieldName
     * @param $defaultValue
     * @return string|null
     */
    private function getConfigurationValue(string $fieldName, $defaultValue): ?string
    {
        $configuration = $this->configurationRepository->findOneBy(['fieldName' => $fieldName]);
        return $configuration ? $configuration->getFieldValue() : $defaultValue;
    }

    /**
     * @throws RedirectionExceptionInterface
     * @throws DecodingExceptionInterface
     * @throws ClientExceptionInterface
     * @throws TransportExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Exception
     */
    public function fetchAndSyncEmployees(): array
    {
        // Dynamically fetch the Base URL and API key
        $apiUrl = $this->getConfigurationValue('Base_URL', $_ENV['BASE_URL']) . 'integration/list_users';
        $apiKey = $this->getConfigurationValue('TEAMLOGGER_API_KEY', $_ENV['TEAMLOGGER_API_KEY']);

        $response = $this->httpClient->request('GET', $apiUrl, [
            'headers' => [
                'Authorization' => "Bearer $apiKey",
                'Accept' => 'application/json',
            ],
        ]);

        if ($response->getStatusCode() !== 200) {
            $this->logger->error('Failed to fetch data from API.', [
                'status_code' => $response->getStatusCode(),
                'response_body' => $response->getContent(false),
            ]);
            throw new Exception('Failed to fetch data. Status code: ' . $response->getStatusCode());
        }

        $usersData = $response->toArray();
        return $this->processBatchUpdates($usersData);
    }

    /**
     * @param array $usersData
     * @return int[]
     */
    private function processBatchUpdates(array $usersData): array
    {
        $newCount = 0;
        $updatedCount = 0;
        $batchCounter = 0;
        $skippedCount = 0;

        $existingEmployees = $this->entityManager->getRepository(Employee::class)
            ->createQueryBuilder('e')
            ->select('e.guid')
            ->getQuery()
            ->getArrayResult();

        $existingGuids = array_column($existingEmployees, 'guid');

        foreach ($usersData as $userData) {
            $masterEmployee = $this->entityManager->getRepository(MasterEmployee::class)
                ->findOneBy(['email' => $userData['email']]);

            if (!$masterEmployee) {
                $this->logger->warning('Skipping employee sync due to missing MasterEmployee.', [
                    'email' => $userData['email'],
                    'guid' => $userData['guid'],
                ]);
                $skippedCount++;
                continue;
            }
            $isExisting = in_array($userData['guid'], $existingGuids);

            if (!$isExisting) {
                $user = new Employee();
                $user->setGuid($userData['guid']);
                $newCount++;
            } else {
                $user = $this->entityManager->getRepository(Employee::class)
                    ->findOneBy(['guid' => $userData['guid']]);
                $updatedCount++;
            }

            $this->updateEmployeeData($user, $userData);

            $masterEmployee = $this->entityManager->getRepository(MasterEmployee::class)
                ->findOneBy(['email' => $userData['email']]);
            $user->setMasterEmployee($masterEmployee);

            $this->entityManager->persist($user);
            $batchCounter++;

            if ($batchCounter % self::BATCH_SIZE === 0) {
                $this->entityManager->flush();
                $this->entityManager->clear();
                $this->logger->info('Processed batch', [
                    'batch_number' => floor($batchCounter / self::BATCH_SIZE),
                    'processed_records' => $batchCounter
                ]);
            }
        }

        // Flush remaining records
        if ($batchCounter % self::BATCH_SIZE !== 0) {
            $this->entityManager->flush();
            $this->entityManager->clear();
        }

        return [
            'newCount' => $newCount,
            'updatedCount' => $updatedCount,
            'totalProcessed' => $batchCounter,
            'skippedCount' => $skippedCount,
        ];
    }


    /**
     * @param Employee $employee
     * @param array $userData
     * @return void
     */
    private function updateEmployeeData(Employee $employee, array $userData): void
    {
        $employee->setGuid($userData['guid']);
    }

    /**
     * @return array
     */
    public function getAllEmployees(): array
    {
        return $this->entityManager->getRepository(Employee::class)->findAll();
    }

    public function getEmployeeOptions(): array
    {
        $employees = $this->getAllEmployees();

        return array_map(fn($employee) => [
            'id' => $employee->getId(),
            'name' => $employee->getMasterEmployee()?->getName() ?? '',
        ], $employees);
    }
}