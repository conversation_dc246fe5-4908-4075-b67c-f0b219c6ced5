const { powerMonitor } = require('electron');
const { machineId } = require('node-machine-id');

class ApplicationTracker {
    constructor(dbService, store) {
        this.dbService = dbService;
        this.store = store;
        
        // Configuration
        this.config = {
            enabled: true,
            trackWindowTitles: true,
            productivityCategorization: true,
            minDuration: 5, // Minimum seconds to track
            updateInterval: 5000 // 5 seconds
        };
        
        // State
        this.isTracking = false;
        this.currentApp = null;
        this.appStartTime = null;
        this.trackingInterval = null;
        this.lastActiveApp = null;
        
        // Application categories for productivity analysis
        this.productivityCategories = {
            productive: [
                'code', 'vscode', 'visual studio', 'intellij', 'eclipse', 'atom', 'sublime',
                'terminal', 'cmd', 'powershell', 'bash', 'git', 'docker',
                'excel', 'word', 'powerpoint', 'outlook', 'teams', 'slack',
                'photoshop', 'illustrator', 'figma', 'sketch', 'blender',
                'mysql', 'postgres', 'mongodb', 'redis'
            ],
            neutral: [
                'explorer', 'finder', 'file manager', 'calculator', 'notepad',
                'settings', 'control panel', 'system preferences', 'task manager'
            ],
            distracting: [
                'facebook', 'twitter', 'instagram', 'tiktok', 'youtube', 'netflix',
                'spotify', 'steam', 'game', 'gaming', 'discord', 'whatsapp',
                'telegram', 'snapchat', 'reddit'
            ]
        };
        
        this.init();
    }

    async init() {
        console.log('🖥️ Initializing Application Tracker...');
        
        try {
            // Load configuration
            await this.loadConfiguration();
            
            // Setup system listeners
            this.setupSystemListeners();
            
            console.log('✅ Application Tracker initialized');
        } catch (error) {
            console.error('❌ Failed to initialize Application Tracker:', error);
        }
    }

    async loadConfiguration() {
        try {
            const storedConfig = await this.store.get('applicationTrackerConfig');
            if (storedConfig) {
                this.config = { ...this.config, ...storedConfig };
            }
            
            console.log('⚙️ Application tracker configuration loaded:', this.config);
        } catch (error) {
            console.error('Failed to load application tracker configuration:', error);
        }
    }

    async updateConfiguration(newConfig) {
        try {
            this.config = { ...this.config, ...newConfig };
            await this.store.set('applicationTrackerConfig', this.config);
            
            console.log('⚙️ Application tracker configuration updated:', this.config);
            
            // Restart tracking if needed
            if (this.isTracking) {
                await this.stopTracking();
                if (this.config.enabled) {
                    await this.startTracking();
                }
            }
            
            return true;
        } catch (error) {
            console.error('Failed to update application tracker configuration:', error);
            return false;
        }
    }

    setupSystemListeners() {
        // Listen for system events
        powerMonitor.on('suspend', () => {
            this.handleSystemEvent('suspend');
        });

        powerMonitor.on('resume', () => {
            this.handleSystemEvent('resume');
        });

        powerMonitor.on('lock-screen', () => {
            this.handleSystemEvent('lock');
        });

        powerMonitor.on('unlock-screen', () => {
            this.handleSystemEvent('unlock');
        });
    }

    async handleSystemEvent(eventType) {
        console.log(`🖥️ System event: ${eventType}`);
        
        if (eventType === 'suspend' || eventType === 'lock') {
            await this.endCurrentAppSession('system_' + eventType);
        } else if (eventType === 'resume' || eventType === 'unlock') {
            // Resume tracking will happen naturally when user becomes active
            console.log('🖥️ System resumed, waiting for application activity...');
        }
    }

    async startTracking() {
        if (!this.config.enabled) {
            console.log('🖥️ Application tracking is disabled');
            return false;
        }

        if (this.isTracking) {
            console.log('⚠️ Application tracking already running');
            return false;
        }

        console.log('▶️ Starting application tracking...');
        this.isTracking = true;

        // Start tracking interval
        this.trackingInterval = setInterval(async () => {
            await this.trackCurrentApplication();
        }, this.config.updateInterval);

        // Initial track
        await this.trackCurrentApplication();

        console.log('✅ Application tracking started');
        return true;
    }

    async stopTracking() {
        if (!this.isTracking) {
            console.log('⚠️ Application tracking not running');
            return false;
        }

        console.log('⏹️ Stopping application tracking...');
        this.isTracking = false;

        if (this.trackingInterval) {
            clearInterval(this.trackingInterval);
            this.trackingInterval = null;
        }

        // End current session
        await this.endCurrentAppSession('tracking_stopped');

        console.log('✅ Application tracking stopped');
        return true;
    }

    async trackCurrentApplication() {
        try {
            const activeApp = await this.getActiveApplication();
            
            if (!activeApp) {
                return;
            }

            // Check if application changed
            if (this.hasApplicationChanged(activeApp)) {
                // End previous session
                if (this.currentApp) {
                    await this.endCurrentAppSession('app_switch');
                }
                
                // Start new session
                await this.startNewAppSession(activeApp);
            } else {
                // Update current session
                await this.updateCurrentSession();
            }

        } catch (error) {
            console.error('Error tracking application:', error);
        }
    }

    async getActiveApplication() {
        try {
            // Use platform-specific methods to get the active application
            const platform = process.platform;

            if (platform === 'win32') {
                return await this.getActiveApplicationWindows();
            } else if (platform === 'darwin') {
                return await this.getActiveApplicationMacOS();
            } else if (platform === 'linux') {
                return await this.getActiveApplicationLinux();
            }

            // Fallback to Electron window detection
            return await this.getActiveApplicationFallback();

        } catch (error) {
            console.error('Failed to get active application:', error);
            return await this.getActiveApplicationFallback();
        }
    }

    async getActiveApplicationWindows() {
        try {
            // For Windows, we'll use a simple approach with child_process
            const { exec } = require('child_process');

            return new Promise((resolve) => {
                exec('powershell "Get-Process | Where-Object {$_.MainWindowTitle -ne \\"\\"} | Select-Object ProcessName, MainWindowTitle, Id | ConvertTo-Json"',
                    (error, stdout, stderr) => {
                        if (error) {
                            console.error('Windows app detection error:', error);
                            resolve(this.getActiveApplicationFallback());
                            return;
                        }

                        try {
                            const processes = JSON.parse(stdout);
                            const activeProcesses = Array.isArray(processes) ? processes : [processes];

                            // Get the first process with a window title (simplified)
                            const activeApp = activeProcesses.find(p => p.MainWindowTitle && p.MainWindowTitle.trim() !== '');

                            if (activeApp) {
                                resolve({
                                    name: activeApp.ProcessName,
                                    path: `C:\\Windows\\System32\\${activeApp.ProcessName}.exe`, // Simplified
                                    windowTitle: activeApp.MainWindowTitle,
                                    processId: activeApp.Id
                                });
                            } else {
                                resolve(this.getActiveApplicationFallback());
                            }
                        } catch (parseError) {
                            console.error('Failed to parse Windows process data:', parseError);
                            resolve(this.getActiveApplicationFallback());
                        }
                    }
                );
            });
        } catch (error) {
            console.error('Windows application detection failed:', error);
            return this.getActiveApplicationFallback();
        }
    }

    async getActiveApplicationMacOS() {
        try {
            const { exec } = require('child_process');

            return new Promise((resolve) => {
                exec('osascript -e "tell application \\"System Events\\" to get name of first application process whose frontmost is true"',
                    (error, stdout, stderr) => {
                        if (error) {
                            console.error('macOS app detection error:', error);
                            resolve(this.getActiveApplicationFallback());
                            return;
                        }

                        const appName = stdout.trim();
                        if (appName) {
                            resolve({
                                name: appName,
                                path: `/Applications/${appName}.app`, // Simplified
                                windowTitle: appName,
                                processId: null
                            });
                        } else {
                            resolve(this.getActiveApplicationFallback());
                        }
                    }
                );
            });
        } catch (error) {
            console.error('macOS application detection failed:', error);
            return this.getActiveApplicationFallback();
        }
    }

    async getActiveApplicationLinux() {
        try {
            const { exec } = require('child_process');

            return new Promise((resolve) => {
                exec('xdotool getwindowfocus getwindowname 2>/dev/null || echo "Unknown"',
                    (error, stdout, stderr) => {
                        if (error) {
                            console.error('Linux app detection error:', error);
                            resolve(this.getActiveApplicationFallback());
                            return;
                        }

                        const windowTitle = stdout.trim();
                        if (windowTitle && windowTitle !== 'Unknown') {
                            // Extract application name from window title (simplified)
                            const appName = windowTitle.split(' - ').pop() || windowTitle;

                            resolve({
                                name: appName,
                                path: `/usr/bin/${appName.toLowerCase()}`, // Simplified
                                windowTitle: windowTitle,
                                processId: null
                            });
                        } else {
                            resolve(this.getActiveApplicationFallback());
                        }
                    }
                );
            });
        } catch (error) {
            console.error('Linux application detection failed:', error);
            return this.getActiveApplicationFallback();
        }
    }

    async getActiveApplicationFallback() {
        try {
            const { BrowserWindow } = require('electron');
            const focusedWindow = BrowserWindow.getFocusedWindow();

            if (!focusedWindow) {
                return null;
            }

            return {
                name: 'Brainee HRM Desktop',
                path: process.execPath,
                windowTitle: focusedWindow.getTitle() || 'Brainee HRM Desktop',
                processId: process.pid
            };

        } catch (error) {
            console.error('Fallback application detection failed:', error);
            return null;
        }
    }

    hasApplicationChanged(newApp) {
        if (!this.currentApp) {
            return true;
        }

        return (
            this.currentApp.name !== newApp.name ||
            this.currentApp.path !== newApp.path ||
            (this.config.trackWindowTitles && this.currentApp.windowTitle !== newApp.windowTitle)
        );
    }

    async startNewAppSession(appInfo) {
        try {
            const currentUser = await this.store.get('currentUser');
            if (!currentUser) {
                console.log('⚠️ No current user for application tracking');
                return;
            }

            const now = new Date();
            const today = now.toISOString().split('T')[0];

            this.currentApp = appInfo;
            this.appStartTime = now;

            // Categorize application
            const category = this.categorizeApplication(appInfo.name);

            // Get device ID
            const deviceId = await this.getDeviceId();

            // Save to database
            const result = await this.dbService.run(`
                INSERT INTO application_usage (
                    employee_id, usage_date, application_name, application_path,
                    window_title, start_time, category, device_id, metadata
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            `, [
                currentUser.id,
                today,
                appInfo.name,
                appInfo.path,
                this.config.trackWindowTitles ? appInfo.windowTitle : null,
                now.toISOString(),
                category,
                deviceId,
                JSON.stringify({
                    processId: appInfo.processId,
                    startTime: now.toISOString(),
                    trackingEnabled: this.config.enabled
                })
            ]);

            this.currentApp.sessionId = result.lastID;

            console.log(`🖥️ Started tracking: ${appInfo.name} (${category})`);

        } catch (error) {
            console.error('Failed to start new app session:', error);
        }
    }

    async endCurrentAppSession(reason = 'normal') {
        if (!this.currentApp || !this.appStartTime) {
            return;
        }

        try {
            const endTime = new Date();
            const duration = Math.floor((endTime - this.appStartTime) / 1000);

            // Only save if duration meets minimum threshold
            if (duration >= this.config.minDuration) {
                await this.dbService.run(`
                    UPDATE application_usage 
                    SET end_time = ?, duration = ?, updated_at = ?
                    WHERE id = ?
                `, [
                    endTime.toISOString(),
                    duration,
                    endTime.toISOString(),
                    this.currentApp.sessionId
                ]);

                console.log(`🖥️ Ended tracking: ${this.currentApp.name} (${this.formatDuration(duration)}) - ${reason}`);
            } else {
                // Remove session if too short
                await this.dbService.run(`
                    DELETE FROM application_usage WHERE id = ?
                `, [this.currentApp.sessionId]);

                console.log(`🖥️ Removed short session: ${this.currentApp.name} (${duration}s < ${this.config.minDuration}s)`);
            }

        } catch (error) {
            console.error('Failed to end app session:', error);
        }

        this.lastActiveApp = this.currentApp;
        this.currentApp = null;
        this.appStartTime = null;
    }

    async updateCurrentSession() {
        if (!this.currentApp || !this.appStartTime) {
            return;
        }

        try {
            const now = new Date();
            const duration = Math.floor((now - this.appStartTime) / 1000);

            // Update metadata with current duration
            const metadata = {
                processId: this.currentApp.processId,
                startTime: this.appStartTime.toISOString(),
                currentDuration: duration,
                lastUpdate: now.toISOString(),
                trackingEnabled: this.config.enabled
            };

            await this.dbService.run(`
                UPDATE application_usage 
                SET metadata = ?, updated_at = ?
                WHERE id = ?
            `, [
                JSON.stringify(metadata),
                now.toISOString(),
                this.currentApp.sessionId
            ]);

        } catch (error) {
            console.error('Failed to update current session:', error);
        }
    }

    categorizeApplication(appName) {
        if (!this.config.productivityCategorization) {
            return 'neutral';
        }

        const name = appName.toLowerCase();

        for (const [category, keywords] of Object.entries(this.productivityCategories)) {
            if (keywords.some(keyword => name.includes(keyword))) {
                return category;
            }
        }

        return 'neutral';
    }

    async getDeviceId() {
        try {
            return await machineId();
        } catch (error) {
            return 'unknown-device';
        }
    }

    formatDuration(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;

        if (hours > 0) {
            return `${hours}h ${minutes}m ${secs}s`;
        } else if (minutes > 0) {
            return `${minutes}m ${secs}s`;
        } else {
            return `${secs}s`;
        }
    }

    // Public methods for external access
    getConfiguration() {
        return { ...this.config };
    }

    getStatus() {
        return {
            isTracking: this.isTracking,
            currentApp: this.currentApp ? {
                name: this.currentApp.name,
                windowTitle: this.currentApp.windowTitle,
                startTime: this.appStartTime,
                duration: this.appStartTime ? Math.floor((new Date() - this.appStartTime) / 1000) : 0
            } : null,
            lastActiveApp: this.lastActiveApp,
            config: this.config
        };
    }

    async getTodayStats() {
        try {
            const currentUser = await this.store.get('currentUser');
            if (!currentUser) return null;

            const today = new Date().toISOString().split('T')[0];

            const stats = await this.dbService.all(`
                SELECT 
                    application_name,
                    category,
                    COUNT(*) as session_count,
                    SUM(duration) as total_duration,
                    AVG(duration) as avg_duration,
                    MAX(duration) as max_duration
                FROM application_usage 
                WHERE employee_id = ? AND usage_date = ? AND duration IS NOT NULL
                GROUP BY application_name, category
                ORDER BY total_duration DESC
            `, [currentUser.id, today]);

            return stats;
        } catch (error) {
            console.error('Failed to get today stats:', error);
            return [];
        }
    }

    async getRecentUsage(limit = 20) {
        try {
            const currentUser = await this.store.get('currentUser');
            if (!currentUser) return [];

            return await this.dbService.all(`
                SELECT * FROM application_usage 
                WHERE employee_id = ? 
                ORDER BY start_time DESC 
                LIMIT ?
            `, [currentUser.id, limit]);
        } catch (error) {
            console.error('Failed to get recent usage:', error);
            return [];
        }
    }

    async destroy() {
        console.log('🔄 Destroying application tracker...');
        await this.stopTracking();
        console.log('✅ Application tracker destroyed');
    }
}

module.exports = ApplicationTracker;
