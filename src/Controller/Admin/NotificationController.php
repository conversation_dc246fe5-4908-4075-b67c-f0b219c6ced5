<?php
namespace App\Controller\Admin;

use App\Service\Admin\NotificationConfigurationService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Csrf\CsrfToken;
use Symfony\Component\Security\Csrf\CsrfTokenManagerInterface;
use App\Traits\ExceptionLoggerTrait;

#[Route('/admin')]
class NotificationController extends AbstractController
{
    use ExceptionLoggerTrait;
    private const FIELD_TASK_NOTIFICATION_ENABLED = 'TASK_NOTIFICATION_ENABLED';
    private const FIELD_PROJECT_NOTIFICATION_ENABLED = 'PROJECT_NOTIFICATION_ENABLED';
    private const FIELD_TASK_ASSIGNMENT_UPDATE_ENABLED = 'TASK_ASSIGNMENT_UPDATE_ENABLED';
    private const FIELD_TASK_STATUS_UPDATE_ENABLED = 'TASK_STATUS_UPDATE_ENABLED';
    private const FIELD_TASK_DESCRIPTION_UPDATE_ENABLED = 'TASK_DESCRIPTION_UPDATE_ENABLED';
    private const FIELD_BUG_NOTIFICATION_ENABLED = 'BUG_NOTIFICATION_ENABLED';
    private const  FIELD_BUG_STATUS_UPDATE_ENABLED = 'BUG_STATUS_UPDATE_ENABLED';
    private const FIELD_BUG_DESCRIPTION_UPDATE_ENABLED = 'BUG_DESCRIPTION_UPDATE_ENABLED';

    public function __construct(
        private readonly NotificationConfigurationService $notificationConfigService,
        private readonly CsrfTokenManagerInterface $csrfTokenManager,
        ParameterBagInterface $parameterBag
    ) {
        $this->initializeLogger($parameterBag, 'notification_controller');
    }

    #[Route('/notification-config-page', name: 'notification_config_page', methods: ['GET'])]
    public function notificationConfigPage(): Response
    {
        $fields = [
            'TaskNotificationEnabled' => self::FIELD_TASK_NOTIFICATION_ENABLED,
            'ProjectNotificationEnabled' => self::FIELD_PROJECT_NOTIFICATION_ENABLED,
            'TaskAssignmentUpdateEnabled' => self::FIELD_TASK_ASSIGNMENT_UPDATE_ENABLED,
            'TaskStatusUpdateEnabled' => self::FIELD_TASK_STATUS_UPDATE_ENABLED,
            'TaskDescriptionUpdateEnabled' => self::FIELD_TASK_DESCRIPTION_UPDATE_ENABLED,
            'BugNotificationEnabled' => self::FIELD_BUG_NOTIFICATION_ENABLED,
            'BugStatusUpdateEnabled' => self::FIELD_BUG_STATUS_UPDATE_ENABLED,
            'BugDescriptionUpdateEnabled' => self::FIELD_BUG_DESCRIPTION_UPDATE_ENABLED,
        ];

        $settings = $this->notificationConfigService->getNotificationSettings($fields);

        return $this->render('admin/notification_config.html.twig', [
            'settings' => $settings,
        ]);
    }

    #[Route('/notification-config/update', name: 'notification_config_update', methods: ['POST'])]
    public function update(Request $request): JsonResponse
    {
        $token = $request->headers->get('X-CSRF-Token');
        if (!$this->csrfTokenManager->isTokenValid(new CsrfToken('notification_update', $token))) {
            return new JsonResponse(['message' => 'Invalid CSRF token'], Response::HTTP_FORBIDDEN);
        }

        try {
            $data = json_decode($request->getContent(), true, 512, JSON_THROW_ON_ERROR);

            $fields = [
                'TaskNotificationEnabled' => self::FIELD_TASK_NOTIFICATION_ENABLED,
                'ProjectNotificationEnabled' => self::FIELD_PROJECT_NOTIFICATION_ENABLED,
                'TaskAssignmentUpdateEnabled' => self::FIELD_TASK_ASSIGNMENT_UPDATE_ENABLED,
                'TaskStatusUpdateEnabled' => self::FIELD_TASK_STATUS_UPDATE_ENABLED,
                'TaskDescriptionUpdateEnabled' => self::FIELD_TASK_DESCRIPTION_UPDATE_ENABLED,
                'BugNotificationEnabled' => self::FIELD_BUG_NOTIFICATION_ENABLED,
                'BugStatusUpdateEnabled' => self::FIELD_BUG_STATUS_UPDATE_ENABLED,
                'BugDescriptionUpdateEnabled' => self::FIELD_BUG_DESCRIPTION_UPDATE_ENABLED,
            ];

            foreach ($fields as $key => $fieldName) {
                if (isset($data[$key])) {
                    $this->notificationConfigService->updateConfiguration($fieldName, $data[$key]);
                }
            }

            return new JsonResponse(['message' => 'Notification settings updated successfully']);
        }catch (\Exception $e) {
            $this->notificationConfigService->log('Error updating settings', ['exception' => $e]);
            return new JsonResponse(['message' => 'Error updating settings: ' . $e->getMessage()], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}