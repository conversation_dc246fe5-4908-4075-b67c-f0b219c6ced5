<?php

namespace App\Service\Common;

use App\Repository\FormSubmissionRepository;
use App\Repository\MasterEmployeeRepository;

readonly class FeedbackHistoryService
{
    public function __construct(
        private FormSubmissionRepository $submissionRepository,
        private MasterEmployeeRepository $masterEmployeeRepository,
    ) {}

    public function getEmployeesReportingTo($teamLeader): array
    {
        return $this->masterEmployeeRepository->findEmployeesReportingTo($teamLeader);
    }

    public function getAllEmployees(): array
    {
        return $this->masterEmployeeRepository->findBy(['isDelete' => false]);
    }

    public function getSelectedEmployee(?string $employeeId)
    {
        return $employeeId ? $this->masterEmployeeRepository->find($employeeId) : null;
    }

    public function getAvailableDates(): array
    {
        return $this->submissionRepository->findUniqueSubmissionAdminDates();
    }

    public function getGroupedSubmissions($selectedEmployee, string $selectedDate): array
    {
        $submissions = $this->submissionRepository->findSubmissionsWithDetailsByFilters(
            $selectedEmployee,
            $selectedDate
        );

        $groupedSubmissions = [];
        foreach ($submissions as $submission) {
            $period = $submission->getReviewPeriod();
            $groupedSubmissions[$period][] = $submission;
        }

        krsort($groupedSubmissions);
        return $groupedSubmissions;
    }

    public function getGroupedSelfEvaluationSubmissions($selectedEmployee, string $selectedDate): array
    {
        $submissions = $this->submissionRepository->findSelfEvaluationsByFilters(
            $selectedEmployee,
            $selectedDate
        );

        $groupedSubmissions = [];
        foreach ($submissions as $submission) {
            $period = $submission->getReviewPeriod();
            $groupedSubmissions[$period][] = $submission;
        }

        krsort($groupedSubmissions);
        return $groupedSubmissions;
    }
}
