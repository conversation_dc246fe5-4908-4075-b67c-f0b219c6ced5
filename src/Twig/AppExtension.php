<?php
namespace App\Twig;

use Twig\Extension\AbstractExtension;
use Twig\TwigFilter;

class AppExtension extends AbstractExtension
{
    public function getFilters(): array
    {
        return [
            new TwigFilter('json_decode', [$this, 'jsonDecode']),
        ];
    }

    /**
     * @throws \JsonException
     */
    public function jsonDecode($string)
    {
        return json_decode($string, true, 512, JSON_THROW_ON_ERROR);
    }
}
?>