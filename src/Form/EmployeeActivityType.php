<?php

namespace App\Form;

use App\Entity\EmployeeActivity;
use App\Entity\Task;
use App\Entity\MasterEmployee;
use Doctrine\ORM\EntityRepository;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\CallbackTransformer;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\GreaterThan;
use Symfony\Component\Validator\Constraints\Length;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Context\ExecutionContextInterface;
use Symfony\Component\Validator\Constraints as Assert;

class EmployeeActivityType extends AbstractType
{
    private Security $security;

    public function __construct(Security $security)
    {
        $this->security = $security;
    }

    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $employee = $this->security->getUser();

        if (!$employee instanceof MasterEmployee) {
            throw new \LogicException('Logged-in user must be an instance of MasterEmployee.');
        }

        $builder
            ->add('activityType', ChoiceType::class, [
                'choices' => [
                    'Meeting' => 'meeting',
                    'Help' => 'help',
                    'Training' => 'training',
                    'Interview' => 'interview',
                    'Others' => 'others',
                ],
                'autocomplete' => true,
                'placeholder' => 'Select activity type',
                'required' => true,
                'attr' => ['class' => 'form-control'],
                'constraints' => [
                    new NotBlank(['message' => 'Activity type cannot be blank']),
                ],
            ])
            ->add('description', TextareaType::class, [
                'required' => true,
                'attr' => ['rows' => 5, 'class' => 'form-control'],
                'constraints' => [
                    new NotBlank(['message' => 'Description cannot be blank']),
                    new Length([
                        'max' => 10000,
                        'maxMessage' => 'Description cannot exceed 10000 characters',
                    ]),
                ],
            ])
            ->add('date', TextType::class, [
                'required' => true,
                'label' => 'Date',
                'attr' => [
                    'class' => 'form-control datepicker',
                    'placeholder' => 'Select date',
                    'required' => 'required',
                ],
                'constraints' => [new NotBlank()],
            ])
            ->add('hours', NumberType::class, [
                'scale' => 2,
                'required' => true,
                'attr' => ['step' => '0.01', 'class' => 'form-control'],
                'constraints' => [
                    new NotBlank(['message' => 'Hours cannot be blank']),
                    new GreaterThan([
                        'value' => 0,
                        'message' => 'Hours must be greater than 0',
                    ]),
                ],
            ])
            ->add('relatedTask', EntityType::class, [
                'class' => Task::class,
                'choice_label' => 'title',
                'autocomplete' => true,
                'placeholder' => 'Select a task (optional)',
                'required' => false,
                'attr' => ['class' => 'form-control'],
                'query_builder' => function (EntityRepository $er) use ($employee) {
                    return $er->createQueryBuilder('t')
                        ->where('t.assignedTo = :employeeId')
                        ->andWhere('t.status IN (:statuses)')
                        ->setParameter('employeeId', $employee->getId())
                        ->setParameter('statuses', ['IN_PROGRESS', 'NEW'])
                        ->orderBy('t.title', 'ASC');
                },
            ]);
        $dateTransformer = new CallbackTransformer(
            function ($dateAsObject) {
                return $dateAsObject instanceof \DateTimeInterface ? $dateAsObject->format('d-m-Y') : '';
            },
            function ($dateAsString) {
                return $dateAsString ? \DateTime::createFromFormat('d-m-Y', $dateAsString) : null;
            }
        );

        $builder->get('date')->addModelTransformer($dateTransformer);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => EmployeeActivity::class,
            'constraints' => [
                new Assert\Callback(function ($data, ExecutionContextInterface $context) {
                    if ($data instanceof EmployeeActivity && $data->getDate()) {
                        $today = new \DateTime();
                        if ($data->getDate() > $today) {
                            $context->buildViolation('Activity date cannot be in the future.')
                                ->atPath('date')
                                ->addViolation();
                        }
                    }
                })
            ],
        ]);
    }
}