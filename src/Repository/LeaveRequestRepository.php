<?php

namespace App\Repository;

use App\Entity\LeaveRequest;
use App\Entity\MasterEmployee;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;

class LeaveRequestRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, LeaveRequest::class);
    }

    /**
     * Find leave requests that overlap with the given date range for a specific employee
     */
    public function findOverlappingLeaves(MasterEmployee $employee, \DateTimeInterface $startDate, \DateTimeInterface $endDate): array
    {
        return $this->createQueryBuilder('lr')
            ->where('lr.employee = :employee')
            ->andWhere('lr.status != :rejected_status')
            ->andWhere(
                '(lr.startDate <= :end_date AND lr.endDate >= :start_date)'
            )
            ->setParameter('employee', $employee)
            ->setParameter('rejected_status', LeaveRequest::STATUS_REJECTED)
            ->setParameter('start_date', $startDate)
            ->setParameter('end_date', $endDate)
            ->getQuery()
            ->getResult();
    }

    /**
     *
     * @param \DateTimeInterface $startDate
     * @param \DateTimeInterface $endDate
     * @return LeaveRequest[]
     */
    public function findByDateRange(\DateTimeInterface $startDate, \DateTimeInterface $endDate): array
    {
        return $this->createQueryBuilder('lr')
            ->where('lr.startDate <= :endDate')
            ->andWhere('lr.endDate >= :startDate')
            ->andWhere('lr.status IN (:statuses)')
            ->setParameter('startDate', $startDate)
            ->setParameter('endDate', $endDate)
            ->setParameter('statuses', [LeaveRequest::STATUS_PENDING, LeaveRequest::STATUS_APPROVED])
            ->orderBy('lr.startDate', 'ASC')
            ->getQuery()
            ->getResult();
    }

    public function findApprovedLeavesForDate(\DateTimeInterface $date): array
    {
        $qb = $this->createQueryBuilder('lr')
            ->join('lr.employee', 'e')
            ->where('lr.status = :status')
            ->andWhere('lr.startDate <= :date')
            ->andWhere('lr.endDate >= :date')
            ->setParameter('status', LeaveRequest::STATUS_APPROVED)
            ->setParameter('date', $date->format('Y-m-d'));

        $qb->andWhere(
            $qb->expr()->orX(
                $qb->expr()->isNull('lr.startHalfDay'),
                $qb->expr()->andX(
                    $qb->expr()->eq('lr.startDate', ':date'),
                    $qb->expr()->in('lr.startHalfDay', ':halfDayValues')
                ),
                $qb->expr()->andX(
                    $qb->expr()->eq('lr.endDate', ':date'),
                    $qb->expr()->in('lr.endHalfDay', ':halfDayValues')
                )
            )
        )
            ->setParameter('halfDayValues', [LeaveRequest::HALF_DAY_MORNING, LeaveRequest::HALF_DAY_AFTERNOON]);

        return $qb->getQuery()->getResult();
    }

    /**
     * Get total LWP (Leave Without Pay) days for an employee for a given month/year.
     * @throws \DateMalformedStringException
     */
    public function getLwpDaysForEmployee(MasterEmployee $employee, int $year, int $month): float
    {
        $startDate = new \DateTimeImmutable(sprintf('%04d-%02d-01', $year, $month));
        $endDate = $startDate->modify('last day of this month');
        $qb = $this->createQueryBuilder('lr')
            ->join('lr.leaveType', 'lb')
            ->join('lb.leaveType', 'lt')
            ->where('lr.employee = :employee')
            ->andWhere('lr.status = :approved')
            ->andWhere('lt.isPaid = false')
            ->andWhere('lr.startDate <= :endDate')
            ->andWhere('lr.endDate >= :startDate')
            ->setParameter('employee', $employee)
            ->setParameter('approved', LeaveRequest::STATUS_APPROVED)
            ->setParameter('startDate', $startDate)
            ->setParameter('endDate', $endDate);
        $leaves = $qb->getQuery()->getResult();
        $totalLwp = 0.0;
        foreach ($leaves as $leave) {
            $leaveStart = $leave->getStartDate() < $startDate ? $startDate : $leave->getStartDate();
            $leaveEnd = $leave->getEndDate() > $endDate ? $endDate : $leave->getEndDate();

            $current = clone $leaveStart;
            while ($current <= $leaveEnd) {
                $isStart = $current == $leave->getStartDate();
                $isEnd = $current == $leave->getEndDate();

                if ($leaveStart == $leaveEnd) {
                    if ($leave->getStartHalfDay() || $leave->getEndHalfDay()) {
                        $totalLwp += 0.5;
                    } else {
                        $totalLwp += 1.0;
                    }
                } else {
                    if ($isStart && $leave->getStartHalfDay()) {
                        $totalLwp += 0.5;
                    } elseif ($isEnd && $leave->getEndHalfDay()) {
                        $totalLwp += 0.5;
                    } elseif (!$isStart && !$isEnd) {
                        $totalLwp += 1.0;
                    } elseif ($isStart && !$leave->getStartHalfDay()) {
                        $totalLwp += 1.0;
                    } elseif ($isEnd && !$leave->getEndHalfDay()) {
                        $totalLwp += 1.0;
                    }
                }
                $current = $current->modify('+1 day');
            }
        }
        return $totalLwp;
    }

    /**
     * @throws \DateMalformedStringException
     */
    public function getMinLeaveRequestDate(): ?\DateTime
    {
        $result = $this->createQueryBuilder('lr')
            ->select('MIN(lr.startDate) as minDate')
            ->getQuery()
            ->getOneOrNullResult();

        return isset($result['minDate']) ? new \DateTime($result['minDate']) : null;
    }

    /**
     * @throws \DateMalformedStringException
     */
    public function getMaxLeaveRequestDate(): ?\DateTime
    {
        $result = $this->createQueryBuilder('lr')
            ->select('MAX(lr.endDate) as maxDate')
            ->getQuery()
            ->getOneOrNullResult();

        return isset($result['maxDate']) ? new \DateTime($result['maxDate']) : null;
    }

}
