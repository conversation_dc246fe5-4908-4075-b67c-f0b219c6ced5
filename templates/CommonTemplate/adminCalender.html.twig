<div class="card-header btn-primary text-white border-0">
    <h5 class="card-title mb-0">
        <i class="fa fa-calendar me-2"></i>Calendar
    </h5>
</div>
<div id="calendar" class="p-3"></div>

{{ render_modal('attendanceModal', 'Attendance Details', '', '<button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>') }}

{% block stylesheets %}
    <style>
        .fc {
            --fc-border-color: #e6e6e6;
            --fc-day-border-color: #f0f0f0;
            --fc-event-bg-color: #f78739;
            --fc-event-border-color: #e06c1f;
            --fc-today-bg-color: #fff9f5;
            max-height: 500px;
        }
        .fc-theme-standard .fc-scrollgrid {
            border-radius: 4px;
            overflow: hidden;
        }
        .fc-theme-standard td, .fc-theme-standard th {
            border: 1px solid var(--fc-border-color);
        }
        .fc-daygrid-body {
            width: 373px !important;
        }
        .fc-col-header {
            width: 373px !important;
        }
        .fc-scrollgrid-sync-table {
            width: 373px !important;
        }
        .fc-daygrid-day-number {
            font-size: 14px;
            font-weight: 500;
            color: #444;
            padding: 8px;
        }
        .fc-day-today {
            background-color: #fff9f5 !important;
        }
        .fc-day-today .fc-daygrid-day-number {
            background-color: #f78739;
            color: white;
            border-radius: 50%;
            width: 28px;
            height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 2px;
        }
        .fc-col-header-cell {
            background-color: #fcf2eb;
            padding: 8px 0;
        }
        .fc-col-header-cell-cushion {
            font-weight: 600;
            color: #f78739;
            padding: 6px;
            text-decoration: none !important;
        }
        .fc-day-other .fc-daygrid-day-number {
            color: #aaa;
        }
        .fc-toolbar-title {
            font-size: 18px !important;
            font-weight: 600;
            color: #444;
        }
        .fc-toolbar-chunk .fc-button-group {
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            border-radius: 6px;
            overflow: hidden;
        }
        .fc-button-primary {
            background-color: white !important;
            color: #666 !important;
            border: 1px solid #e0e0e0 !important;
            transition: all 0.2s ease;
        }
        .fc-button-primary:hover {
            background-color: #f6f6f6 !important;
        }
        .fc-button-primary:not(:disabled).fc-button-active,
        .fc-button-primary:not(:disabled):active {
            background-color: #f78739 !important;
            color: white !important;
            border-color: #f78739 !important;
        }
        .fc-button-primary:disabled {
            background-color: #f1f1f1 !important;
            color: #aaa !important;
        }
        .fc-daygrid-day:hover {
            background-color: #fcf8f5;
        }
        .fc-daygrid-day-events {
            padding: 0 2px;
        }
        .fc-event {
            border-radius: 4px;
            padding: 2px 4px;
            font-size: 12px;
            cursor: pointer;
        }
        .fc-event:hover {
            filter: brightness(95%);
        }
        .fc-daygrid-event-dot {
            border-color: #f78739 !important;
        }
        .fc-list-event-dot {
            border-color: #f78739;
        }
        .fc-list-event:hover td {
            background-color: #fcf8f5;
        }
        .fc-list-day-cushion {
            background-color: #fcf2eb !important;
        }
        .fc-today-button {
            text-transform: uppercase;
            font-size: 13px !important;
            font-weight: 600 !important;
        }
        .fc-today-button:not(:disabled) {
            background-color: #f78739 !important;
            border-color: #f78739 !important;
        }
        .fc-today-button:not(:disabled):hover {
            background-color: #e06c1f !important;
        }
        .fc-view-harness {
            min-height: 350px;
        }
        @media (max-width: 768px) {
            .fc-toolbar {
                flex-direction: column;
                gap: 10px;
            }
            .fc-toolbar-chunk {
                display: flex;
                justify-content: center;
            }
        }
        .attendance-modal {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .attendance-header {
            background-color: #f78739;
            color: white;
            padding: 15px 20px;
            border-radius: 5px 5px 0 0;
        }
        .attendance-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin: 0;
        }
        .attendance-date {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        .attendance-summary {
            display: flex;
            justify-content: space-between;
            background-color: #f8f9fa;
            padding: 10px 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .summary-item {
            text-align: center;
            flex: 1;
        }
        .summary-count {
            font-size: 1.5rem;
            font-weight: 700;
        }
        .present-count {
            color: #28a745;
        }
        .absent-count {
            color: #dc3545;
        }
        .attendance-list {
            max-height: 400px;
            overflow-y: auto;
        }
        .list-title {
            font-size: 1rem;
            font-weight: 600;
            color: #495057;
            margin-top: 15px;
            padding-bottom: 5px;
            border-bottom: 2px solid #f78739;
        }
        .employee-card {
            display: flex;
            align-items: center;
            padding: 10px;
            margin: 8px 0;
            border-radius: 5px;
            background-color: #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            transition: all 0.2s ease;
        }
        .employee-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .employee-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            color: #6c757d;
            font-weight: 600;
        }
        .employee-name {
            flex: 1;
            font-weight: 500;
        }
        .status-badge {
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        .status-present {
            background-color: #e6f7eb;
            color: #28a745;
        }
        .status-absent {
            background-color: #fce8e8;
            color: #dc3545;
        }
        .no-data {
            text-align: center;
            padding: 20px;
            color: #6c757d;
            font-style: italic;
        }
    </style>
{% endblock %}

{% block javascripts %}
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            var calendarEl = document.getElementById('calendar');

            function shouldSkipPopup(dateStrOrDateObj, attendanceCount = 0, isHoliday = false) {
                if (isHoliday) return true;
                const dateObj = typeof dateStrOrDateObj === 'string' ? new Date(dateStrOrDateObj) : dateStrOrDateObj;
                const today = new Date();
                today.setHours(0, 0, 0, 0);
                const dayOfWeek = dateObj.getDay();
                const isWeekend = (dayOfWeek === 0 || dayOfWeek === 6);
                const isFuture = dateObj > today;
                return (isFuture || isWeekend) && attendanceCount === 0;
            }

            var calendar = new FullCalendar.Calendar(calendarEl, {
                initialView: 'dayGridMonth',
                initialDate: '{{ "now"|date("Y-m-d") }}',
                headerToolbar: {
                    left: 'prev,next today',
                    center: 'title',
                    right: 'dayGridMonth,listWeek'
                },
                buttonText: {
                    today: 'Today',
                    month: 'Month',
                    list: 'List'
                },
                events: function(fetchInfo, successCallback, failureCallback) {
                    fetch(`{{ path('admin_attendance_calendar_data') }}?start=${fetchInfo.startStr}&end=${fetchInfo.endStr}`)
                        .then(response => response.json())
                        .then(events => successCallback(events))
                        .catch(error => failureCallback(error));
                },
                dayMaxEvents: 2,
                eventTimeFormat: {
                    hour: '2-digit',
                    minute: '2-digit',
                    meridiem: false
                },
                firstDay: 1,
                fixedWeekCount: false,
                showNonCurrentDates: true,
                height: 'auto',
                dateClick: function(info) {
                    const dateStr = info.dateStr;
                    const eventsForDate = calendar.getEvents().filter(event =>
                        event.startStr === dateStr
                    );

                    let isHoliday = false;
                    let totalAttendance = 0;

                    eventsForDate.forEach(event => {
                        const props = event.extendedProps || {};
                        if (props.isHoliday) isHoliday = true;
                        totalAttendance += props.attendanceCount || 0;
                    });

                    if (shouldSkipPopup(dateStr, totalAttendance, isHoliday)) {
                        return;
                    }

                    loadAttendanceDetails(dateStr);
                }
            });

            calendar.render();

            function loadAttendanceDetails(date) {
                const modal = new bootstrap.Modal(document.getElementById('attendanceModal'));
                const modalBody = document.querySelector('#attendanceModal .modal-body');
                const modalTitle = document.querySelector('#attendanceModal .modal-title');

                modalBody.innerHTML = `
                    <div class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Loading attendance details...</p>
                    </div>
                `;

                modal.show();

                fetch(`{{ path('admin_attendance_date_details', {'date': 'DATE_PLACEHOLDER'}) }}`.replace('DATE_PLACEHOLDER', date))
                    .then(response => response.json())
                    .then(data => {
                        if (data.error) {
                            modalBody.innerHTML = `
                                <div class="alert alert-danger">
                                    <i class="fas fa-exclamation-circle me-2"></i>
                                    ${data.error}
                                </div>
                            `;
                            return;
                        }

                        const formattedDate = formatDate(date);
                        const presentCount = data.presentEmployees.length;
                        const absentCount = data.absentEmployees.length;

                        let html = `
                            <div class="attendance-header">
                                <h5 class="attendance-title">Attendance Details</h5>
                                <div class="attendance-date">${formattedDate}</div>
                            </div>

                            <div class="attendance-summary">
                                <div class="summary-item">
                                    <div class="summary-count present-count">${presentCount}</div>
                                    <div>Present</div>
                                </div>
                                <div class="summary-item">
                                    <div class="summary-count absent-count">${absentCount}</div>
                                    <div>Absent</div>
                                </div>
                                <div class="summary-item">
                                    <div class="summary-count">${presentCount + absentCount}</div>
                                    <div>Total</div>
                                </div>
                            </div>

                            <div class="attendance-list">
                        `;

                        if (presentCount > 0) {
                            html += `<div class="list-title">Present Employees (${presentCount})</div>`;
                            data.presentEmployees.forEach(emp => {
                                const initials = getInitials(emp.name);
                                html += `
                                    <div class="employee-card">
                                        <div class="employee-avatar">${initials}</div>
                                        <div class="employee-name">${emp.name}</div>
                                        <div class="status-badge status-present">Present</div>
                                    </div>
                                `;
                            });
                        } else {
                            html += `<div class="no-data">No employees were present on this day</div>`;
                        }

                        if (absentCount > 0) {
                            html += `<div class="list-title">Absent Employees (${absentCount})</div>`;
                            data.absentEmployees.forEach(emp => {
                                const initials = getInitials(emp.name);
                                html += `
                                    <div class="employee-card">
                                        <div class="employee-avatar">${initials}</div>
                                        <div class="employee-name">${emp.name}</div>
                                        <div class="status-badge status-absent">Absent</div>
                                    </div>
                                `;
                            });
                        } else if (presentCount > 0) {
                            html += `<div class="no-data">All employees were present</div>`;
                        }

                        html += `</div>`;
                        modalBody.innerHTML = html;
                        modalTitle.textContent = 'Attendance Details (HRM)';
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        modalBody.innerHTML = `
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-circle me-2"></i>
                                Failed to load attendance details
                            </div>
                        `;
                    });
            }

            function formatDate(dateStr) {
                const date = new Date(dateStr);
                return date.toLocaleDateString('en-IN', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                });
            }
            function getInitials(name) {
                return name.split(' ')
                    .map(part => part[0])
                    .join('')
                    .toUpperCase();
            }
        });
    </script>
{% endblock %}