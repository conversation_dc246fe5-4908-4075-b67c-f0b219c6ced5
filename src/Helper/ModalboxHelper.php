<?php

namespace App\Helper;

class ModalboxHelper
{
    /**
     * @param string $modalId
     * @param string $modalTitle
     * @param string $modalBody
     * @param string $footerButtons
     * @return string
     */
    public static function renderModal(string $modalId, string $modalTitle, string $modalBody, string $footerButtons): string
    {
        $escapedModalId = htmlspecialchars($modalId, ENT_QUOTES, 'UTF-8');
        $escapedModalTitle = htmlspecialchars($modalTitle, ENT_QUOTES, 'UTF-8');
//        $modalHeaderCloseButton = '<button type="button" class="close" data-dismiss="modal" aria-label="Close">
//                                    <span aria-hidden="true">&times;</span>
//                                 </button>';

        return sprintf(
            '
            <div class="modal fade" id="%s" tabindex="-1" role="dialog" aria-labelledby="%sLabel" aria-hidden="true">
                <div class="modal-dialog modal-lg" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title font-weight-bold" id="%sLabel">%s</h5>
                        </div>
                        <div class="modal-body">%s</div>
                        <div class="modal-footer">%s</div>
                    </div>
                </div>
            </div>',
            $escapedModalId,
            $escapedModalId,
            $escapedModalId,
            $escapedModalTitle,
//            $modalHeaderCloseButton,
            $modalBody,
            $footerButtons
        );
    }

}
