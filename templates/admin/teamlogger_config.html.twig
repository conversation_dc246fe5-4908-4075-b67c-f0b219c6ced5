{% extends 'admin/dashboard/base.html.twig' %}

{% block body %}
    {% include 'admin/partials/breadcrumbs.html.twig' with {
        breadcrumbs: [
            { 'label': 'Dashboard', 'url': url('admin_dashboard') },
        ],
        active: 'Teamlogger'
    } %}

    <div class="content">
        <div class="animated fadeIn">
            <div class="row">
                <div id="order-loader">
                    <img src="{{ asset('images/loader.svg') }}" alt="loader">
                </div>
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between ">
                        <strong class="card-title">Teamlogger</strong>
                            <button id="sync-employees" class="btn btn-primary pr-3 pl-3">Sync Teamlogger Employees</button>
                        </div>
                        <div id="pay-invoice">
                            <div class="card-body">
                                <form id="update-settings-form" method="post" novalidate="novalidate">
                                    <div class="form-group">
                                        <label for="Base_URL" class="control-label mb-1 required">Base URL:</label>
                                        <input
                                                id="Base_URL"
                                                name="Base_URL"
                                                type="url"
                                                class="form-control"
                                                aria-required="true"
                                                aria-invalid="false"
                                                value="{{ Base_URL }}"
                                                required>
                                        <small class="form-text text-muted">Enter a valid URL (e.g., https://example.com)</small>
                                        <div class="invalid-feedback" id="base-url-error"></div>
                                    </div>
                                    <div class="form-group">
                                        <label for="TEAMLOGGER_API_KEY" class="control-label mb-1 required">API Key:</label>
                                        <input
                                                id="TEAMLOGGER_API_KEY"
                                                name="TEAMLOGGER_API_KEY"
                                                type="text"
                                                class="form-control"
                                                aria-required="true"
                                                aria-invalid="false"
                                                value="{{ TEAMLOGGER_API_KEY }}"
                                                required>
                                        <small class="form-text text-muted">Enter a valid JWT API key (format: header.payload.signature)</small>
                                        <div class="invalid-feedback" id="api-key-error"></div>
                                    </div>
                                    <div class="form-group">
                                        <label for="THRESHOLD" class="control-label mb-1 required">Threshold Value:</label>
                                        <input
                                                id="THRESHOLD"
                                                name="THRESHOLD"
                                                type="number"
                                                class="form-control"
                                                aria-required="true"
                                                aria-invalid="false"
                                                value="{{ THRESHOLD }}"
                                                min="0"
                                                max="24"
                                                step="1"
                                                required>
                                        <small class="form-text text-muted">Enter a positive integer value</small>
                                        <div class="invalid-feedback" id="threshold-error"></div>
                                    </div>
                                    <div>
                                        <button
                                                id="payment-button"
                                                type="submit"
                                                class="btn btn-primary btn-block"
                                                disabled>
                                            Update Settings
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="clearfix"></div>
    <script src="{{ asset('assets/js/admin/teamlogger.js') }}"></script>
    <script>
        const csrfToken = '{{ csrf_token('setting_update') }}';
    </script>
    <script src="{{ asset('assets/js/datatables.min.js') }}"></script>
    <script src="{{ asset('bundles/datatables/js/datatables.js') }}"></script>
    <script src="{{ asset('assets/js/admin/employee_list.js') }}"></script>
{% endblock %}
