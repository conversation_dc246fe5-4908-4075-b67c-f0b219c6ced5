const { app, <PERSON>rowserWindow } = require('electron');
const IdleWarningPopup = require('./src/idle-warning-popup');

// Test popup window closing behavior
async function testPopupClose() {
    console.log('🧪 Testing Popup Window Closing...');
    
    const popup = new IdleWarningPopup();
    popup.setupIpcHandlers();
    
    // Show popup
    console.log('📱 Showing popup...');
    popup.show(
        1, // 1 minute idle
        () => console.log('⏰ Timeout callback'),
        () => console.log('✅ Resume callback')
    );
    
    // Test automatic close after 3 seconds
    setTimeout(() => {
        console.log('🔄 Testing popup close...');
        popup.hide();
        
        // Check if window is actually closed
        setTimeout(() => {
            if (popup.isVisible()) {
                console.log('❌ FAILED: Popup window is still visible!');
            } else {
                console.log('✅ SUCCESS: Popup window closed properly!');
            }
            app.quit();
        }, 1000);
    }, 3000);
}

app.whenReady().then(() => {
    testPopupClose();
});

app.on('window-all-closed', () => {
    app.quit();
});
