<?php

namespace App\Form;

use App\Entity\LeaveBalance;
use App\Entity\LeaveRequest;
use App\Entity\MasterEmployee;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\CallbackTransformer;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\Length;

class LeaveRequestFormType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $employee = $options['employee'] ?? null;

        if (!$employee instanceof MasterEmployee) {
            throw new \LogicException('No valid employee found to fetch leave requests.');
        }

        $builder
            ->add('leaveType', EntityType::class, [
                'class' => LeaveBalance::class,
                'choice_label' => function (LeaveBalance $leaveBalance) {
                    $remainingDays = $leaveBalance->getRemainingDays();
                    $remainingDaysText = '';
                    if ($remainingDays < 100) {
                        $remainingDaysText = '('.number_format($leaveBalance->getRemainingDays(), 1).' days remaining)';
                    }
                    return sprintf(
                        '%s %s',
                        $leaveBalance->getLeaveType()->getName(),
                        $remainingDaysText
                    );
                },
                'query_builder' => function (\Doctrine\ORM\EntityRepository $er) use ($employee) {
                    return $er->createQueryBuilder('lb')
                        ->leftJoin('lb.leaveType', 'lt')
                        ->where('lb.employee = :employee')
                        ->andWhere('lb.remainingDays > 0')
                        ->andWhere('lt.isDelete = :isDelete')
                        ->andWhere('lb.year = (SELECT MAX(lb2.year) FROM App\Entity\LeaveBalance lb2 WHERE lb2.employee = :employee)')
                        ->setParameter('employee', $employee)
                        ->setParameter('isDelete', false);
                },
                'placeholder' => 'Select a leave type',
                'required' => true,
            ])
            ->add('startDate', TextType::class, [
                'required' => true,
                'label' => 'Date',
                'attr' => [
                    'class' => 'form-control datepicker',
                    'placeholder' => 'Select date',
                    'required' => 'required',
                ],
            ])
            ->add('startHalfDay', ChoiceType::class, [
                'choices' => [
                    'Full Day' => null,
                    'First Half' => LeaveRequest::HALF_DAY_MORNING,
                    'Second Half' => LeaveRequest::HALF_DAY_AFTERNOON,
                ],
                'required' => true,
            ])
            ->add('endDate', TextType::class, [
                'required' => true,
                'label' => 'Date',
                'attr' => [
                    'class' => 'form-control datepicker',
                    'placeholder' => 'Select date',
                    'required' => 'required',
                ],
            ])
            ->add('endHalfDay', ChoiceType::class, [
                'choices' => [
                    'Full Day' => null,
                    'First Half' => LeaveRequest::HALF_DAY_MORNING,
                    'Second Half' => LeaveRequest::HALF_DAY_AFTERNOON,
                ],
                'required' => true,
            ])
            ->add('reason', TextareaType::class, [
                'required' => true,
                'attr' => ['class' => 'form-control', 'rows' => 1,'maxlength' => 255,'data-validation' => 'max:255'],
                'constraints' => [
                    new Length([
                        'max' => 255,
                        'maxMessage' => 'Reason name cannot be longer than {{ limit }} characters',
                    ]),
                ],
            ]);


        $dateTransformer = new CallbackTransformer(
            function ($dateAsObject) {
                return $dateAsObject instanceof \DateTimeInterface ? $dateAsObject->format('d-m-Y') : '';
            },
            function ($dateAsString) {
                return $dateAsString ? \DateTime::createFromFormat('d-m-Y', $dateAsString) : null;
            }
        );

        $builder->get('startDate')->addModelTransformer($dateTransformer);
        $builder->get('endDate')->addModelTransformer($dateTransformer);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => LeaveRequest::class,
            'employee' => null,
        ]);
    }

}