<?php

namespace App\Entity;

use App\Repository\DepartmentRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: DepartmentRepository::class)]
#[ORM\HasLifecycleCallbacks]
class Department
{
    public const ACTIVE = true;
    public const INACTIVE = false;
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    /** @phpstan-ignore-next-line */
    private ?int $id = null;

    #[ORM\Column(length: 255)]
    private ?string $depName = null;

    #[ORM\Column(length: 255 , nullable: true)]
    private ?string $depDescription = null;

    #[ORM\Column(type: 'boolean')]
    private bool $depStatus = true;
    #[ORM\Column(type: 'datetime', options: ['default' => 'CURRENT_TIMESTAMP'])]
    private ?\DateTimeInterface $dateCreated = null;

    #[ORM\Column(type: 'datetime', options: ['default' => 'CURRENT_TIMESTAMP'])]
    private ?\DateTimeInterface $dateUpdated = null;

    #[ORM\ManyToMany(targetEntity: MasterEmployee::class, mappedBy: 'departments')]
    private Collection $employees;

    #[ORM\Column(type: Types::BOOLEAN, options: ["default" => false])]
    private ?bool $isDelete = self::INACTIVE;

    public function __construct()
    {
        $this->employees = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getDepName(): ?string
    {
        return $this->depName;
    }

    public function setDepName(?string $depName): self
    {
        $this->depName = $depName;
        return $this;
    }

    public function getDepDescription(): ?string
    {
        return $this->depDescription;
    }

    public function setDepDescription(?string $depDescription): self
    {
        $this->depDescription = $depDescription;
        return $this;
    }


    public function getDateCreated(): ?\DateTimeInterface
    {
        return $this->dateCreated;
    }

    public function setDateCreated(?\DateTimeInterface $dateCreated): static
    {
        $this->dateCreated = $dateCreated;
        return $this;
    }

    public function getDateUpdated(): ?\DateTimeInterface
    {
        return $this->dateUpdated;
    }

    public function setDateUpdated(?\DateTimeInterface $dateUpdated): static
    {
        $this->dateUpdated = $dateUpdated;
        return $this;
    }

    #[ORM\PrePersist]
    public function onPrePersist(): void
    {
        $this->dateCreated = new \DateTime();
        $this->dateUpdated = new \DateTime();
    }

    #[ORM\PreUpdate]
    public function onPreUpdate(): void
    {
        $this->dateUpdated = new \DateTime();
    }

    /**
     * @return Collection
     */
    public function getEmployees(): Collection
    {
        return $this->employees;
    }

    public function addEmployee(MasterEmployee $employee): static
    {
        if (!$this->employees->contains($employee)) {
            $this->employees->add($employee);
            $employee->addDepartment($this); // Sync the other side
        }
        return $this;
    }

    public function removeEmployee(MasterEmployee $employee): static
    {
        $this->employees->removeElement($employee);
        return $this;
    }

    public function getIsDelete(): ?bool
    {
        return $this->isDelete;
    }

    public function setIsDelete(?bool $isDelete): void
    {
        $this->isDelete = $isDelete;
    }
    public function isDepStatus(): bool
    {
        return $this->depStatus;
    }

    public function setDepStatus(bool $depStatus): void
    {
        $this->depStatus = $depStatus;
    }
    public function hasEmployees(): bool
    {
        return !$this->employees->isEmpty();
    }

}