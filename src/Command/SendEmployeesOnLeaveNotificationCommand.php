<?php

namespace App\Command;

use App\Message\EmployeesOnLeaveNotificationMessage;
use App\Repository\LeaveRequestRepository;
use DateTimeImmutable;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Messenger\Exception\ExceptionInterface;
use Symfony\Component\Messenger\MessageBusInterface;

#[AsCommand(
    name: 'app:send-employees-on-leave-notification',
    description: 'Sends email notifications to admins about employees on leave for the current or specified day.'
)]
class SendEmployeesOnLeaveNotificationCommand extends Command
{
    public function __construct(
        private readonly LeaveRequestRepository $leaveRequestRepository,
        private readonly MessageBusInterface $messageBus
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->addOption('date', null, InputOption::VALUE_OPTIONAL, 'Date to check for employees on leave (format: Y-m-d)', null);
    }

    /**
     * @throws ExceptionInterface
     * @throws \DateMalformedStringException
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $dateStr = $input->getOption('date');
        $date = $dateStr ? new DateTimeImmutable($dateStr) : new DateTimeImmutable();
        $leaveRequests = $this->leaveRequestRepository->findApprovedLeavesForDate($date);

        if (empty($leaveRequests)) {
            $output->writeln('No employees are on leave for ' . $date->format('Y-m-d'));
            return Command::SUCCESS;
        }
        $employeeIds = array_unique(array_map(fn($leave) => $leave->getEmployee()->getId(), $leaveRequests));
        $leaveRequestIds = array_map(fn($leave) => $leave->getId(), $leaveRequests);
        $this->messageBus->dispatch(new EmployeesOnLeaveNotificationMessage($employeeIds, $leaveRequestIds, $date));

        $output->writeln(sprintf('Dispatched notification for %d employees on leave for %s', count($employeeIds), $date->format('Y-m-d')));

        return Command::SUCCESS;
    }
}