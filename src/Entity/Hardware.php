<?php

namespace App\Entity;

use App\Repository\HardwareRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: HardwareRepository::class)]
class Hardware
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    /** @phpstan-ignore-next-line */
    private ?int $id = null;

    #[ORM\Column(length: 255)]
    private ?string $hardwareName = null;

    #[ORM\ManyToOne(targetEntity: HardwareType::class)]
    #[ORM\JoinColumn(name: "hardware_type_id", referencedColumnName: "id", nullable: false, onDelete:'cascade')]
    private ?HardwareType $hardwareType = null;

    #[ORM\ManyToOne(targetEntity: MasterEmployee::class)]
    #[ORM\JoinColumn(name: "master_employee_id", referencedColumnName: "id", nullable: true)]
    private ?MasterEmployee $employee = null;

    #[ORM\Column(type: "text", nullable: true)]
    private ?string $description = null;

    #[ORM\Column(type: "date")]
    private ?\DateTimeInterface $purchaseDate = null;

    #[ORM\Column(type: "date")]
    private ?\DateTimeInterface $warrantyEndDate = null;

    #[ORM\Column(length: 255)]
    private ?string $vendorName = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $manufacturerName = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $manufacturerModel = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $manufacturerSerial = null;

    #[ORM\Column(name: '`condition`', length: 50)]
    private ?string $condition = null;

    #[ORM\Column(length: 15, nullable: true)]
    private ?string $ipAddress = null;

    #[ORM\Column(length: 17, nullable: true)]
    private ?string $macAddress = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $location = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $pcName = null;

    #[ORM\Column(length: 50, nullable: true)]
    private ?string $ram = null;

    #[ORM\Column(length: 50, nullable: true)]
    private ?string $rom = null;

    #[ORM\Column(type: 'boolean')]
    private bool $showOnDashboard = false;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getHardwareName(): ?string
    {
        return $this->hardwareName;
    }

    public function setHardwareName(?string $hardwareName): void
    {
        $this->hardwareName = $hardwareName;
    }

    public function getHardwareType(): ?HardwareType
    {
        return $this->hardwareType;
    }

    public function setHardwareType(?HardwareType $hardwareType): void
    {
        $this->hardwareType = $hardwareType;
    }

    public function getEmployee(): ?MasterEmployee
    {
        return $this->employee;
    }

    public function setEmployee(?MasterEmployee $employee): void
    {
        $this->employee = $employee;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): void
    {
        $this->description = $description;
    }

    public function getPurchaseDate(): ?\DateTimeInterface
    {
        return $this->purchaseDate;
    }

    public function setPurchaseDate(?\DateTimeInterface $purchaseDate): void
    {
        $this->purchaseDate = $purchaseDate;
    }

    public function getWarrantyEndDate(): ?\DateTimeInterface
    {
        return $this->warrantyEndDate;
    }

    public function setWarrantyEndDate(?\DateTimeInterface $warrantyEndDate): void
    {
        $this->warrantyEndDate = $warrantyEndDate;
    }

    public function getVendorName(): ?string
    {
        return $this->vendorName;
    }

    public function setVendorName(?string $vendorName): void
    {
        $this->vendorName = $vendorName;
    }

    public function getManufacturerName(): ?string
    {
        return $this->manufacturerName;
    }

    public function setManufacturerName(?string $manufacturerName): void
    {
        $this->manufacturerName = $manufacturerName;
    }

    public function getManufacturerModel(): ?string
    {
        return $this->manufacturerModel;
    }

    public function setManufacturerModel(?string $manufacturerModel): void
    {
        $this->manufacturerModel = $manufacturerModel;
    }

    public function getManufacturerSerial(): ?string
    {
        return $this->manufacturerSerial;
    }

    public function setManufacturerSerial(?string $manufacturerSerial): void
    {
        $this->manufacturerSerial = $manufacturerSerial;
    }

    public function getCondition(): ?string
    {
        return $this->condition;
    }

    public function setCondition(?string $condition): void
    {
        $this->condition = $condition;
    }

    public function getIpAddress(): ?string
    {
        return $this->ipAddress;
    }

    public function setIpAddress(?string $ipAddress): void
    {
        $this->ipAddress = $ipAddress;
    }

    public function getMacAddress(): ?string
    {
        return $this->macAddress;
    }

    public function setMacAddress(?string $macAddress): void
    {
        $this->macAddress = $macAddress;
    }

    public function getLocation(): ?string
    {
        return $this->location;
    }

    public function setLocation(?string $location): void
    {
        $this->location = $location;
    }

    public function getPcName(): ?string
    {
        return $this->pcName;
    }

    public function setPcName(?string $pcName): void
    {
        $this->pcName = $pcName;
    }

    public function getRam(): ?string
    {
        return $this->ram;
    }

    public function setRam(?string $ram): void
    {
        $this->ram = $ram;
    }

    public function getRom(): ?string
    {
        return $this->rom;
    }

    public function setRom(?string $rom): void
    {
        $this->rom = $rom;
    }

    public function isShowOnDashboard(): bool
    {
        return $this->showOnDashboard;
    }

    public function setShowOnDashboard(bool $showOnDashboard): void
    {
        $this->showOnDashboard = $showOnDashboard;
    }
}