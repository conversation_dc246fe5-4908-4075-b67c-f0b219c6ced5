<?php
namespace App\Twig;

use App\Helper\TimeHelper;
use Twig\Extension\AbstractExtension;
use Twig\TwigFilter;

class TimeExtension extends AbstractExtension
{
    public function __construct( private readonly  TimeHelper $timeHelper)
    {
    }

    public function getFilters(): array
    {
        return [
            new TwigFilter('decimal_to_time', [$this->timeHelper, 'convertDecimalToTime']),
        ];
    }
}
