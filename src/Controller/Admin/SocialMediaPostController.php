<?php

namespace App\Controller\Admin;

use App\Entity\SocialMediaPost;
use App\Form\SocialMediaPostType;
use App\Service\Admin\SocialMediaPostService;
use App\Traits\ExceptionLoggerTrait;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Csrf\CsrfToken;
use Symfony\Component\Security\Csrf\CsrfTokenManagerInterface;

#[Route('/admin')]
class SocialMediaPostController extends AbstractController
{
    use ExceptionLoggerTrait;

    public function __construct(
        ParameterBagInterface                       $parameterBag,
        private readonly EntityManagerInterface     $entityManager,
        private readonly SocialMediaPostService     $socialMediaPostService,
    )
    {
        $this->initializeLogger($parameterBag, 'SocialMediaPost_list_controller');
    }

    #[Route(path: '/social-media-post/list', name: 'social_media_post_list', methods: ['GET', 'POST'])]
    public function index(Request $request): Response
    {
        $table = $this->socialMediaPostService->createSocialMediaPostTable($request);

        if ($table->isCallback()) {
            return $table->getResponse();
        }

        return $this->render('admin/social_media_post/index.html.twig', [
            'datatable' => $table,
        ]);
    }

    #[Route('/social-media-post/create', name: 'social_media_post_create', methods: ['GET', 'POST'])]
    public function createSocialMediaPost(Request $request): Response
    {
        $socialMediaPost = new SocialMediaPost();
        $form = $this->createForm(SocialMediaPostType::class, $socialMediaPost);

        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $this->entityManager->persist($socialMediaPost);
            $this->entityManager->flush();

            $this->addFlash('success', 'Social Media Post created successfully.');
            return $this->redirectToRoute('social_media_post_list');
        }

        return $this->render('admin/social_media_post/new.html.twig', [
            'form' => $form->createView(),
            'mode' => 'create',
        ]);
    }

    #[Route('/social-media-post/edit', name: 'social_media_post_edit', methods: ['POST', 'GET'])]
    public function editSocialMediaPost(Request $request, CsrfTokenManagerInterface $csrfTokenManager): Response
    {
        $id = $request->request->get('id');

        if (!$id) {
            $this->addFlash('error', 'No social media post ID provided.');
            return $this->redirectToRoute('social_media_post_list');
        }

        $socialMediaPost = $this->entityManager->getRepository(SocialMediaPost::class)->find($id);

        if (!$socialMediaPost) {
            throw $this->createNotFoundException('Social Media Post not found');
        }

        $form = $this->createForm(SocialMediaPostType::class, $socialMediaPost);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $socialMediaPost->setUpdatedAt(new \DateTime());
            $this->entityManager->flush();

            $this->addFlash('success', 'Social Media Post updated successfully.');
            return $this->redirectToRoute('social_media_post_list');
        }

        return $this->render('admin/social_media_post/new.html.twig', [
            'form' => $form->createView(),
            'socialMediaPost' => $socialMediaPost,
            'mode' => 'edit',
        ]);
    }

    #[Route('/social-media-post/delete/{id}', name: 'social_media_post_delete', methods: ['POST'])]
    public function deleteSocialMediaPost(
        Request                   $request,
        CsrfTokenManagerInterface $csrfTokenManager,
        SocialMediaPost           $socialMediaPost
    ): JsonResponse
    {
        $token = $request->request->get('_token');

        if (!$csrfTokenManager->isTokenValid(new CsrfToken('delete' . $socialMediaPost->getId(), $token))) {
            return new JsonResponse([
                'success' => false,
                'message' => 'Invalid CSRF token.'
            ], Response::HTTP_FORBIDDEN);
        }

        $this->entityManager->remove($socialMediaPost);
        $this->entityManager->flush();

        return new JsonResponse([
            'success' => true,
            'message' => 'Social Media Post has been deleted.'
        ], Response::HTTP_OK);
    }
}