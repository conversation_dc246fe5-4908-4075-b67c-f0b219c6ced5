<?php

namespace App\Service\Admin;

use App\Entity\FormField;
use App\Helper\ModalboxHelper;
use Doctrine\ORM\QueryBuilder;
use Omines\DataTablesBundle\Adapter\Doctrine\ORMAdapter;
use Omines\DataTablesBundle\Column\TextColumn;
use Omines\DataTablesBundle\DataTable;
use Omines\DataTablesBundle\DataTableFactory;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Security\Csrf\CsrfTokenManagerInterface;
use App\Traits\ExceptionLoggerTrait;

class FormFieldService
{
    use ExceptionLoggerTrait;

    public function __construct(
        private readonly DataTableFactory $dataTableFactory,
        private readonly RouterInterface $router,
        ParameterBagInterface $parameterBag,
        private readonly CsrfTokenManagerInterface $csrfTokenManager,
        private readonly ModalboxHelper            $modalboxHelper
    ) {
        $this->initializeLogger($parameterBag, 'FormFieldService');
    }

    public function createFormFieldTable(Request $request, int $sectionId): DataTable
    {
        $dataTable = $this->dataTableFactory->create();

        foreach ($this->getColumns() as $key => $column) {
            $dataTable->add($key, $column['className'], array_filter($column, fn($k) => $k !== 'className', ARRAY_FILTER_USE_KEY));
        }

        return $dataTable->createAdapter(ORMAdapter::class, [
            'entity' => FormField::class,
            'query' => fn(QueryBuilder $qb) => $this->buildQuery($qb, $sectionId),
        ])->handleRequest($request);
    }

    private function getColumns(): array
    {
        return [
            'fieldLabel' => [
                'label' => 'Field Label',
                'field' => 'f.fieldLabel',
                'className' => TextColumn::class,
                'orderable' => false,
            ],
            'fieldType' => [
                'label' => 'Field Type',
                'field' => 'f.fieldType',
                'className' => TextColumn::class,
                'orderable' => false,
            ],
            'fieldOrder' => [
                'label' => 'Order',
                'field' => 'f.fieldOrder',
                'orderable' => false,
                'className' => TextColumn::class,
            ],
            'status' => [
                'label' => 'Status',
                'field' => 'f.isVisible',
                'className' => TextColumn::class,
                'render' => fn($value) => $this->renderStatusBadge($value),
                'orderable' => false,
            ],
            'actions' => [
                'label' => 'Actions',
                'field' => 'f.id',
                'className' => TextColumn::class,
                'orderable' => false,
                'render' => fn($value, $context) => $this->renderActionColumn($context),
            ],
        ];
    }

    private function renderStatusBadge(bool $isVisible): string
    {
        return $isVisible
            ? '<span class="badge bg-success">Active</span>'
            : '<span class="badge bg-danger">Inactive</span>';
    }

    private function renderActionColumn(FormField $formField): string
    {
        $entityId = $formField->getId();
        $section = $formField->getSection();
        $sectionId = $section->getId();
        $templateId = $section->getTemplate()->getId();

        $toggleStatusUrl = $this->router->generate('form_field_toggle_status', [
            'template_id' => $templateId,
            'sections_id' => $sectionId,
            'id' => $entityId
        ]);
        $csrfToken = $this->csrfTokenManager->getToken('toggle-status' . $entityId)->getValue(); // Adjusted to get value

        $editUrl = $this->router->generate('form_field_edit', [
            'template_id' => $templateId,
            'sections_id' => $sectionId,
            'id' => $entityId
        ]);

        $reorderUrl = $this->router->generate('form_field_reorder', [
            'template_id' => $templateId,
            'sections_id' => $sectionId,
            'id' => $entityId
        ]);

        $deleteUrl = $this->router->generate('form_field_delete', [
            'template_id' => $templateId,
            'sections_id' => $sectionId,
            'id' => $entityId
        ]);
        $deleteToken = $this->csrfTokenManager->getToken('delete' . $entityId)->getValue();

        $modalId = 'deleteModal_' . $entityId;
        $modalBody = sprintf(
            'Are you sure you want to delete the field with label "%s"?<br><br>' .
            '<strong>Warning:</strong> This action cannot be undone.',
            htmlspecialchars($formField->getFieldLabel())
        );

        $footerButtons = sprintf(
            '<button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>' .
            '<button type="button" class="btn btn-primary delete-form-field-btn"  data-delete="true"  data-url="%s" data-token="%s">Delete</button>',
            $deleteUrl,
            $deleteToken
        );

        $deleteModal = $this->modalboxHelper->renderModal(
            $modalId,
            'Confirm Field Deletion',
            $modalBody,
            $footerButtons
        );

        return sprintf(
            '<div class="d-inline-flex gap-2">
            <a href="%s" class="bs-btn btn btn-primary btn-sm bs-m" title="Edit"><i class="bs-fa fa fa-edit"></i></a>
            <button class="bs-btn btn btn-primary btn-sm bs-m toggle-status-btn" 
                    data-id="%s" 
                    data-token="%s" 
                    data-url="%s" 
                    title="Toggle Status">
                <i class="bs-fa fa fa-toggle-on"></i>
            </button>
            <a href="%s?direction=up" class="bs-btn btn btn-primary btn-sm bs-m" title="Move Up"><i class="bs-fa fa fa-arrow-up"></i></a>
            <a href="%s?direction=down" class="bs-btn btn btn-primary btn-sm bs-m" title="Move Down"><i class="bs-fa fa fa-arrow-down"></i></a>
            <button type="button" class="bs-btn btn btn-primary btn-sm bs-m" data-toggle="modal" data-target="#%s" title="Deactivate">
                <i class="bs-fa fa fa-trash"></i>
            </button>
        </div>%s',
            $editUrl,
            $entityId,
            $csrfToken,
            $toggleStatusUrl,
            $reorderUrl,
            $reorderUrl,
            $modalId,
            $deleteModal
        );
    }

    public function buildQuery(QueryBuilder $qb, int $sectionId): QueryBuilder
    {
        return $qb->select('f')
            ->from(FormField::class, 'f')
            ->where('f.section = :sectionId')
            ->andWhere('f.isDelete = :isDeleted')
            ->setParameter('sectionId', $sectionId)
            ->setParameter('isDeleted', false)
            ->orderBy('f.fieldOrder', 'ASC');
    }

    public function setDefaultRatingOption(FormField $field):void
    {
        if ($field->getFieldType() === 'rating') {
            $fixedOptions = [
                "options" => [
                    ["label" => "1", "value" => "1"],
                    ["label" => "2", "value" => "2"],
                    ["label" => "3", "value" => "3"],
                    ["label" => "4", "value" => "4"],
                    ["label" => "5", "value" => "5"]
                ]
            ];
            $field->setFieldOptions($fixedOptions);
        }
    }
}