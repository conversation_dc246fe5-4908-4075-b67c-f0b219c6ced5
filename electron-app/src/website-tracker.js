const { machineId } = require('node-machine-id');

class WebsiteTracker {
    constructor(dbService, store) {
        this.dbService = dbService;
        this.store = store;
        
        // Configuration
        this.config = {
            enabled: true,
            trackBrowserTabs: true,
            websiteCategorization: true,
            minDuration: 3, // Minimum seconds to track
            updateInterval: 3000 // 3 seconds
        };
        
        // State
        this.isTracking = false;
        this.currentWebsite = null;
        this.visitStartTime = null;
        this.trackingInterval = null;
        this.lastVisitedSite = null;
        
        // Website categories for productivity analysis
        this.websiteCategories = {
            work: [
                'github.com', 'stackoverflow.com', 'docs.google.com', 'office.com',
                'atlassian.com', 'jira', 'confluence', 'slack.com', 'teams.microsoft.com',
                'aws.amazon.com', 'azure.microsoft.com', 'cloud.google.com',
                'developer.mozilla.org', 'w3schools.com', 'codepen.io'
            ],
            social: [
                'facebook.com', 'twitter.com', 'instagram.com', 'linkedin.com',
                'tiktok.com', 'snapchat.com', 'discord.com', 'reddit.com',
                'whatsapp.com', 'telegram.org', 'pinterest.com'
            ],
            news: [
                'bbc.com', 'cnn.com', 'reuters.com', 'bloomberg.com',
                'techcrunch.com', 'theverge.com', 'ars-technica.com',
                'hacker-news.firebaseapp.com', 'news.ycombinator.com'
            ],
            entertainment: [
                'youtube.com', 'netflix.com', 'spotify.com', 'twitch.tv',
                'hulu.com', 'disney.com', 'amazon.com/prime', 'soundcloud.com',
                'imgur.com', '9gag.com', 'memes.com'
            ],
            shopping: [
                'amazon.com', 'ebay.com', 'alibaba.com', 'etsy.com',
                'walmart.com', 'target.com', 'bestbuy.com', 'flipkart.com'
            ],
            education: [
                'coursera.org', 'udemy.com', 'edx.org', 'khanacademy.org',
                'pluralsight.com', 'lynda.com', 'codecademy.com', 'freecodecamp.org'
            ]
        };
        
        // Productivity levels
        this.productivityLevels = {
            work: 'productive',
            education: 'productive',
            news: 'neutral',
            social: 'distracting',
            entertainment: 'distracting',
            shopping: 'distracting'
        };
        
        this.init();
    }

    async init() {
        console.log('🌐 Initializing Website Tracker...');
        
        try {
            // Load configuration
            await this.loadConfiguration();
            
            console.log('✅ Website Tracker initialized');
        } catch (error) {
            console.error('❌ Failed to initialize Website Tracker:', error);
        }
    }

    async loadConfiguration() {
        try {
            const storedConfig = await this.store.get('websiteTrackerConfig');
            if (storedConfig) {
                this.config = { ...this.config, ...storedConfig };
            }
            
            console.log('⚙️ Website tracker configuration loaded:', this.config);
        } catch (error) {
            console.error('Failed to load website tracker configuration:', error);
        }
    }

    async updateConfiguration(newConfig) {
        try {
            this.config = { ...this.config, ...newConfig };
            await this.store.set('websiteTrackerConfig', this.config);
            
            console.log('⚙️ Website tracker configuration updated:', this.config);
            
            // Restart tracking if needed
            if (this.isTracking) {
                await this.stopTracking();
                if (this.config.enabled) {
                    await this.startTracking();
                }
            }
            
            return true;
        } catch (error) {
            console.error('Failed to update website tracker configuration:', error);
            return false;
        }
    }

    async startTracking() {
        if (!this.config.enabled) {
            console.log('🌐 Website tracking is disabled');
            return false;
        }

        if (this.isTracking) {
            console.log('⚠️ Website tracking already running');
            return false;
        }

        console.log('▶️ Starting website tracking...');
        this.isTracking = true;

        // Start tracking interval
        this.trackingInterval = setInterval(async () => {
            await this.trackCurrentWebsite();
        }, this.config.updateInterval);

        // Initial track
        await this.trackCurrentWebsite();

        console.log('✅ Website tracking started');
        return true;
    }

    async stopTracking() {
        if (!this.isTracking) {
            console.log('⚠️ Website tracking not running');
            return false;
        }

        console.log('⏹️ Stopping website tracking...');
        this.isTracking = false;

        if (this.trackingInterval) {
            clearInterval(this.trackingInterval);
            this.trackingInterval = null;
        }

        // End current session
        await this.endCurrentWebsiteSession('tracking_stopped');

        console.log('✅ Website tracking stopped');
        return true;
    }

    async trackCurrentWebsite() {
        try {
            const activeWebsite = await this.getActiveWebsite();
            
            if (!activeWebsite) {
                return;
            }

            // Check if website changed
            if (this.hasWebsiteChanged(activeWebsite)) {
                // End previous session
                if (this.currentWebsite) {
                    await this.endCurrentWebsiteSession('site_change');
                }
                
                // Start new session
                await this.startNewWebsiteSession(activeWebsite);
            } else {
                // Update current session
                await this.updateCurrentSession();
            }

        } catch (error) {
            console.error('Error tracking website:', error);
        }
    }

    async getActiveWebsite() {
        try {
            // Get the active window information using xdotool
            const activeWindow = await this.getActiveWindowInfo();

            if (!activeWindow || !this.isBrowserWindow(activeWindow.windowTitle, activeWindow.processName)) {
                return null;
            }

            // Extract URL from browser window title
            const websiteInfo = this.extractWebsiteFromTitle(activeWindow.windowTitle);

            if (!websiteInfo) {
                return null;
            }

            return {
                url: websiteInfo.url,
                domain: websiteInfo.domain,
                pageTitle: websiteInfo.pageTitle,
                browserName: this.detectBrowser(activeWindow.windowTitle, activeWindow.processName)
            };

        } catch (error) {
            console.error('Failed to get active website:', error);
            return null;
        }
    }

    async getActiveWindowInfo() {
        try {
            const { exec } = require('child_process');
            const { promisify } = require('util');
            const execAsync = promisify(exec);

            // Get active window ID
            const { stdout: windowId } = await execAsync('xdotool getwindowfocus');

            if (!windowId.trim()) {
                return null;
            }

            // Get window title
            const { stdout: windowTitle } = await execAsync(`xdotool getwindowname ${windowId.trim()}`);

            // Get window PID to identify the application
            const { stdout: windowPid } = await execAsync(`xdotool getwindowpid ${windowId.trim()}`);

            // Get process name from PID
            let processName = '';
            try {
                const { stdout: processInfo } = await execAsync(`ps -p ${windowPid.trim()} -o comm=`);
                processName = processInfo.trim();
            } catch (e) {
                // Fallback: try to get process name differently
                try {
                    const { stdout: processInfo } = await execAsync(`ps -p ${windowPid.trim()} -o args=`);
                    processName = processInfo.trim().split(' ')[0].split('/').pop();
                } catch (e2) {
                    processName = 'unknown';
                }
            }

            return {
                windowId: windowId.trim(),
                windowTitle: windowTitle.trim(),
                windowPid: windowPid.trim(),
                processName: processName
            };

        } catch (error) {
            console.error('Failed to get active window info:', error);
            return null;
        }
    }

    extractWebsiteFromTitle(windowTitle) {
        try {
            // Common browser title patterns:
            // Chrome: "Page Title - Google Chrome"
            // Firefox: "Page Title - Mozilla Firefox"
            // Edge: "Page Title - Microsoft Edge"

            const title = windowTitle.trim();

            // Extract URL patterns from common browser titles
            let url = null;
            let domain = null;
            let pageTitle = null;

            // Pattern 1: Look for URLs in title (some browsers show URLs)
            const urlMatch = title.match(/(https?:\/\/[^\s]+)/);
            if (urlMatch) {
                url = urlMatch[1];
                try {
                    const urlObj = new URL(url);
                    domain = urlObj.hostname;
                } catch (e) {
                    // Invalid URL, continue with other methods
                }
            }

            // Pattern 2: Extract from common browser title formats
            if (!url) {
                // Chrome pattern: "Title - Google Chrome"
                if (title.includes(' - Google Chrome')) {
                    pageTitle = title.replace(' - Google Chrome', '');
                }
                // Firefox pattern: "Title - Mozilla Firefox"
                else if (title.includes(' - Mozilla Firefox')) {
                    pageTitle = title.replace(' - Mozilla Firefox', '');
                }
                // Edge pattern: "Title - Microsoft Edge"
                else if (title.includes(' - Microsoft Edge')) {
                    pageTitle = title.replace(' - Microsoft Edge', '');
                }
                // Generic pattern: "Title - Browser"
                else {
                    const parts = title.split(' - ');
                    if (parts.length >= 2) {
                        pageTitle = parts.slice(0, -1).join(' - ');
                    } else {
                        pageTitle = title;
                    }
                }

                // Try to extract domain from page title if it contains domain-like patterns
                if (pageTitle && !domain) {
                    const domainMatch = pageTitle.match(/([a-zA-Z0-9-]+\.[a-zA-Z]{2,})/);
                    if (domainMatch) {
                        domain = domainMatch[1];
                        url = `https://${domain}`;
                    }
                }
            }

            // If we still don't have a domain, try to infer from title
            if (!domain && pageTitle) {
                // Check for known website patterns in title
                const knownSites = {
                    'GitHub': 'github.com',
                    'Stack Overflow': 'stackoverflow.com',
                    'Google': 'google.com',
                    'YouTube': 'youtube.com',
                    'Facebook': 'facebook.com',
                    'Twitter': 'twitter.com',
                    'LinkedIn': 'linkedin.com',
                    'Reddit': 'reddit.com',
                    'Wikipedia': 'wikipedia.org',
                    'Amazon': 'amazon.com',
                    'Netflix': 'netflix.com'
                };

                for (const [siteName, siteDomain] of Object.entries(knownSites)) {
                    if (pageTitle.toLowerCase().includes(siteName.toLowerCase())) {
                        domain = siteDomain;
                        url = url || `https://${domain}`;
                        break;
                    }
                }
            }

            // Return null if we couldn't extract meaningful website info
            if (!domain && !pageTitle) {
                return null;
            }

            return {
                url: url || `https://${domain || 'unknown.com'}`,
                domain: domain || 'unknown.com',
                pageTitle: pageTitle || title
            };

        } catch (error) {
            console.error('Failed to extract website from title:', error);
            return null;
        }
    }

    isBrowserWindow(windowTitle, processName = '') {
        const browserKeywords = [
            'chrome', 'firefox', 'safari', 'edge', 'opera', 'brave',
            'mozilla', 'webkit', 'chromium'
        ];

        const title = windowTitle.toLowerCase();
        const process = processName.toLowerCase();

        // Check both window title and process name
        return browserKeywords.some(keyword =>
            title.includes(keyword) || process.includes(keyword)
        );
    }

    detectBrowser(windowTitle, processName = '') {
        const title = windowTitle.toLowerCase();
        const process = processName.toLowerCase();

        if (title.includes('google chrome') || title.includes('chrome') || process.includes('chrome')) return 'Chrome';
        if (title.includes('mozilla firefox') || title.includes('firefox') || process.includes('firefox')) return 'Firefox';
        if (title.includes('safari') || process.includes('safari')) return 'Safari';
        if (title.includes('microsoft edge') || title.includes('edge') || process.includes('edge')) return 'Edge';
        if (title.includes('opera') || process.includes('opera')) return 'Opera';
        if (title.includes('brave') || process.includes('brave')) return 'Brave';
        if (title.includes('chromium') || process.includes('chromium')) return 'Chromium';
        
        return 'Unknown Browser';
    }

    hasWebsiteChanged(newWebsite) {
        if (!this.currentWebsite) {
            return true;
        }

        return (
            this.currentWebsite.url !== newWebsite.url ||
            this.currentWebsite.domain !== newWebsite.domain ||
            (this.config.trackBrowserTabs && this.currentWebsite.pageTitle !== newWebsite.pageTitle)
        );
    }

    async startNewWebsiteSession(websiteInfo) {
        try {
            const currentUser = await this.store.get('currentUser');
            if (!currentUser) {
                console.log('⚠️ No current user for website tracking');
                return;
            }

            const now = new Date();
            const today = now.toISOString().split('T')[0];

            this.currentWebsite = websiteInfo;
            this.visitStartTime = now;

            // Categorize website
            const category = this.categorizeWebsite(websiteInfo.domain);
            const productivityLevel = this.getProductivityLevel(category);

            // Get device ID
            const deviceId = await this.getDeviceId();

            // Save to database
            const result = await this.dbService.run(`
                INSERT INTO website_usage (
                    employee_id, visit_date, url, domain, page_title,
                    visit_time, category, productivity_level, browser_name,
                    device_id, metadata
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `, [
                currentUser.id,
                today,
                websiteInfo.url,
                websiteInfo.domain,
                websiteInfo.pageTitle,
                now.toISOString(),
                category,
                productivityLevel,
                websiteInfo.browserName,
                deviceId,
                JSON.stringify({
                    startTime: now.toISOString(),
                    trackingEnabled: this.config.enabled,
                    userAgent: 'Brainee HRM Desktop Tracker'
                })
            ]);

            this.currentWebsite.sessionId = result.lastID;

            console.log(`🌐 Started tracking: ${websiteInfo.domain} (${category}/${productivityLevel})`);

        } catch (error) {
            console.error('Failed to start new website session:', error);
        }
    }

    async endCurrentWebsiteSession(reason = 'normal') {
        if (!this.currentWebsite || !this.visitStartTime) {
            return;
        }

        try {
            const leaveTime = new Date();
            const duration = Math.floor((leaveTime - this.visitStartTime) / 1000);

            // Only save if duration meets minimum threshold
            if (duration >= this.config.minDuration) {
                await this.dbService.run(`
                    UPDATE website_usage 
                    SET leave_time = ?, duration = ?, updated_at = ?
                    WHERE id = ?
                `, [
                    leaveTime.toISOString(),
                    duration,
                    leaveTime.toISOString(),
                    this.currentWebsite.sessionId
                ]);

                console.log(`🌐 Ended tracking: ${this.currentWebsite.domain} (${this.formatDuration(duration)}) - ${reason}`);
            } else {
                // Remove session if too short
                await this.dbService.run(`
                    DELETE FROM website_usage WHERE id = ?
                `, [this.currentWebsite.sessionId]);

                console.log(`🌐 Removed short session: ${this.currentWebsite.domain} (${duration}s < ${this.config.minDuration}s)`);
            }

        } catch (error) {
            console.error('Failed to end website session:', error);
        }

        this.lastVisitedSite = this.currentWebsite;
        this.currentWebsite = null;
        this.visitStartTime = null;
    }

    async updateCurrentSession() {
        if (!this.currentWebsite || !this.visitStartTime) {
            return;
        }

        try {
            const now = new Date();
            const duration = Math.floor((now - this.visitStartTime) / 1000);

            // Update metadata with current duration
            const metadata = {
                startTime: this.visitStartTime.toISOString(),
                currentDuration: duration,
                lastUpdate: now.toISOString(),
                trackingEnabled: this.config.enabled,
                userAgent: 'Brainee HRM Desktop Tracker'
            };

            await this.dbService.run(`
                UPDATE website_usage 
                SET metadata = ?, updated_at = ?
                WHERE id = ?
            `, [
                JSON.stringify(metadata),
                now.toISOString(),
                this.currentWebsite.sessionId
            ]);

        } catch (error) {
            console.error('Failed to update current session:', error);
        }
    }

    categorizeWebsite(domain) {
        if (!this.config.websiteCategorization) {
            return 'general';
        }

        const cleanDomain = domain.replace('www.', '').toLowerCase();

        for (const [category, domains] of Object.entries(this.websiteCategories)) {
            if (domains.some(d => cleanDomain.includes(d) || d.includes(cleanDomain))) {
                return category;
            }
        }

        return 'general';
    }

    getProductivityLevel(category) {
        return this.productivityLevels[category] || 'neutral';
    }

    async getDeviceId() {
        try {
            return await machineId();
        } catch (error) {
            return 'unknown-device';
        }
    }

    formatDuration(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;

        if (hours > 0) {
            return `${hours}h ${minutes}m ${secs}s`;
        } else if (minutes > 0) {
            return `${minutes}m ${secs}s`;
        } else {
            return `${secs}s`;
        }
    }

    // Public methods for external access
    getConfiguration() {
        return { ...this.config };
    }

    getStatus() {
        return {
            isTracking: this.isTracking,
            currentWebsite: this.currentWebsite ? {
                domain: this.currentWebsite.domain,
                url: this.currentWebsite.url,
                pageTitle: this.currentWebsite.pageTitle,
                startTime: this.visitStartTime,
                duration: this.visitStartTime ? Math.floor((new Date() - this.visitStartTime) / 1000) : 0
            } : null,
            lastVisitedSite: this.lastVisitedSite,
            config: this.config
        };
    }

    async getTodayStats() {
        try {
            const currentUser = await this.store.get('currentUser');
            if (!currentUser) return null;

            const today = new Date().toISOString().split('T')[0];

            const stats = await this.dbService.all(`
                SELECT 
                    domain,
                    category,
                    productivity_level,
                    COUNT(*) as visit_count,
                    SUM(duration) as total_duration,
                    AVG(duration) as avg_duration,
                    MAX(duration) as max_duration
                FROM website_usage 
                WHERE employee_id = ? AND visit_date = ? AND duration IS NOT NULL
                GROUP BY domain, category, productivity_level
                ORDER BY total_duration DESC
            `, [currentUser.id, today]);

            return stats;
        } catch (error) {
            console.error('Failed to get today stats:', error);
            return [];
        }
    }

    async getRecentVisits(limit = 20) {
        try {
            const currentUser = await this.store.get('currentUser');
            if (!currentUser) return [];

            return await this.dbService.all(`
                SELECT * FROM website_usage 
                WHERE employee_id = ? 
                ORDER BY visit_time DESC 
                LIMIT ?
            `, [currentUser.id, limit]);
        } catch (error) {
            console.error('Failed to get recent visits:', error);
            return [];
        }
    }

    async destroy() {
        console.log('🔄 Destroying website tracker...');
        await this.stopTracking();
        console.log('✅ Website tracker destroyed');
    }
}

module.exports = WebsiteTracker;
