<?php
namespace App\Service\Common;

use App\Entity\Holiday;
use App\Entity\LeaveRequest;
use App\Entity\LeaveType;
use App\Entity\MasterEmployee;
use App\Entity\User;
use App\Helper\ModalboxHelper;
use App\Repository\LeaveTypeRepository;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\QueryBuilder;
use Omines\DataTablesBundle\Adapter\Doctrine\ORMAdapter;
use Omines\DataTablesBundle\Column\DateTimeColumn;
use Omines\DataTablesBundle\Column\TextColumn;
use Omines\DataTablesBundle\DataTable;
use Omines\DataTablesBundle\DataTableFactory;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Request;
use App\Traits\ExceptionLoggerTrait;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Security\Core\User\UserInterface;
use Symfony\Component\Security\Csrf\CsrfTokenManagerInterface;

class LeaveRequestService
{
    use ExceptionLoggerTrait;
    private null|UserInterface $currentUser = null;

    public function __construct(
        private readonly DataTableFactory $dataTableFactory,
        private readonly EntityManagerInterface $entityManager,
        private readonly RouterInterface $router,
        private readonly CsrfTokenManagerInterface $csrfTokenManager,
        ParameterBagInterface $parameterBag,
        private readonly ModalboxHelper $modalboxHelper,
        private readonly RequestStack $requestStack,
    ) {
        $this->initializeLogger($parameterBag, 'LeaveRequest_Table_service');
    }

    public function createLeaveRequestTable(Request $request, ?MasterEmployee $employee = null): DataTable
    {
        $dataTable = $this->dataTableFactory->create();

        foreach ($this->getColumns() as $key => $column) {
            $dataTable->add($key, $column['className'], array_filter($column, fn($k) => $k !== 'className', ARRAY_FILTER_USE_KEY));
        }
        return $dataTable->createAdapter(ORMAdapter::class, [
            'entity' => LeaveRequest::class,
            'query' => fn(QueryBuilder $qb) => $this->buildQuery($qb, $employee),
        ])
            ->addOrderBy('appliedOn', 'desc')
            ->handleRequest($request);
    }

    private function getColumns(): array
    {
        return [
            'leaveType' => [
                'label' => 'Leave Type',
                'field' => 'lt.name',
                'className' => TextColumn::class,
                'orderable' => true,
                'searchable' => true,
            ],
            'startDate' => [
                'label' => 'Start Date',
                'field' => 'lr.startDate',
                'className' => DateTimeColumn::class,
                'format' => 'd-m-Y',
                'nullValue' => '-',
                'searchable' => false,
                'render' => function ($value, $context) {
                    $date = $context->getStartDate() ? $context->getStartDate()->format('d-m-Y') : '-';
                    $halfDay = $context->getStartHalfDay();
                    $indicator = '';
                    if ($halfDay === LeaveRequest::HALF_DAY_MORNING) {
                        $indicator = '<span class="text-black" title="Morning Half-Day">*</span>';
                    } elseif ($halfDay === LeaveRequest::HALF_DAY_AFTERNOON) {
                        $indicator = '<span class="text-black" title="Afternoon Half-Day">*</span>';
                    } elseif (!$halfDay) {
                        $indicator = '<span class="text-info" title="Full Day"></span>';
                    }
                    return $date . $indicator;
                },
            ],
            'endDate' => [
                'label' => 'End Date',
                'field' => 'lr.endDate',
                'className' => DateTimeColumn::class,
                'format' => 'd-m-Y',
                'nullValue' => '-',
                'searchable' => false,
                'render' => function ($value, $context) {
                    $date = $context->getEndDate() ? $context->getEndDate()->format('d-m-Y') : '-';
                    $startDate = $context->getStartDate();
                    $endDate = $context->getEndDate();
                    $isSingleDay = $startDate && $endDate && $startDate->format('Y-m-d') === $endDate->format('Y-m-d');

                    $halfDay = $isSingleDay ? $context->getStartHalfDay() : $context->getEndHalfDay();
                    $indicator = '';
                    if ($halfDay === LeaveRequest::HALF_DAY_MORNING) {
                        $indicator = '<span class="text-black" title="Morning Half-Day">*</span>';
                    } elseif ($halfDay === LeaveRequest::HALF_DAY_AFTERNOON) {
                        $indicator = '<span class="text-black" title="Afternoon Half-Day">*</span>';
                    } elseif (!$halfDay) {
                        $indicator = '<span class="text-info" title="Full Day"></span>';
                    }
                    return $date . $indicator;
                },
            ],
            'daysRequested' => [
                'label' => 'Days',
                'className' => TextColumn::class,
                'render' => function ($value, $context) {
                    $baseDays = $context->getDaysRequested();
                    $penaltyDays = $context->getPenaltyDays();
                    $penaltyApplied = $context->isPenaltyApplied();

                    if ($penaltyApplied && $penaltyDays > 0) {
                        $totalDays = $baseDays + $penaltyDays;
                        return sprintf(
                            '%s + %s penalty = %s',
                            number_format($baseDays, 1),
                            number_format($penaltyDays, 1),
                            number_format($totalDays, 1)
                        );
                    }
                    return number_format($baseDays, 1);
                },
                'orderable' => false,
            ],
            'status' => [
                'label' => 'Status',
                'field' => 'lr.status',
                'className' => TextColumn::class,
                'render' => fn($value) => $this->renderStatusBadge($value),
            ],
            'appliedOn' => [
                'label' => 'Applied On',
                'field' => 'lr.appliedOn',
                'className' => DateTimeColumn::class,
                'format' => 'd-m-Y',
                'nullValue' => '-',
                'searchable' => false,
            ],
            'reason' => [
                'label' => 'Reason',
                'field' => 'lr.reason',
                'searchable' => false,
                'orderable' => false,
                'className' => TextColumn::class,
                'render' => function ($value, $context) {
                    $reason = $context->getReason() ?? '-';
                    $maxLength = 25;
                    if (strlen($reason) > $maxLength) {
                        $truncated = substr($reason, 0, $maxLength) . '...';
                        return sprintf('<span title="%s">%s</span>', htmlspecialchars($reason), htmlspecialchars($truncated));
                    }
                    return htmlspecialchars($reason);
                },
            ],
            'approvals' => [
                'label' => 'Manager/Hr Approved',
                'orderable' => false,
                'searchable' => false,
                'className' => TextColumn::class,
                'raw' => true,
                'render' => function ($value, $context) {
                    $manager = $context->getTeamLeadApprovedBy()?->getName() ?? '-';
                    $hr = $context->getHrApprovedBy()?->getName() ?? '';
                    return "{$manager} {$hr}";
                },
            ],
            'adminApprovedBy' => [
                'label' => 'Admin Approved',
                'field' => 'lr.adminApprovedBy',
                'searchable' => false,
                'className' => TextColumn::class,
                'render' => fn($value) => $value ?: '-',
            ],
        ];
    }

    private function renderStatusBadge(string $status): string
    {
        return match ($status) {
            LeaveRequest::STATUS_PENDING => '<span class="badge btn-primary">Pending</span>',
            LeaveRequest::STATUS_APPROVED => '<span class="badge badge-success">Approved</span>',
            LeaveRequest::STATUS_REJECTED => '<span class="badge badge-danger">Rejected</span>',
            default => '<span class="badge badge-secondary">Unknown</span>',
        };
    }

    private function buildQuery(QueryBuilder $qb, $employee): QueryBuilder
    {
        return $qb->select('lr', 'lb', 'lt')
            ->from(LeaveRequest::class, 'lr')
            ->leftJoin('lr.leaveType', 'lb')
            ->leftJoin('lb.leaveType', 'lt')
            ->where('lr.employee = :employee')
            ->setParameter('employee', $employee);
    }

    public function createAdminLeaveRequestTable(Request $request): DataTable
    {
        $dataTable = $this->dataTableFactory->create();

        foreach ($this->getAdminColumns() as $key => $column) {
            $dataTable->add($key, $column['className'], array_filter($column, fn($k) => $k !== 'className', ARRAY_FILTER_USE_KEY));
        }

        return $dataTable->createAdapter(ORMAdapter::class, [
            'entity' => LeaveRequest::class,
            'query' => fn(QueryBuilder $qb) => $this->buildAdminQuery($qb),
        ])
            ->addOrderBy('appliedOn', 'desc')
            ->handleRequest($request);
    }

    private function getAdminColumns(): array
    {
        $baseColumns = $this->getColumns();
        $employeeColumn = [
            'employee' => [
                'label' => 'Employee',
                'field' => 'e.name',
                'className' => TextColumn::class,
                'searchable' => true,
            ]
        ];
        $actionsColumn = [
            'actions' => [
                'label' => 'Actions',
                'className' => TextColumn::class,
                'orderable' => false,
                'searchable' => false,
                'render' => fn($value, $context) => $this->renderActions($context),
            ]
        ];
        return array_merge($employeeColumn, $baseColumns, $actionsColumn);
    }

    private function renderActions(LeaveRequest $leaveRequest): string
    {
        $currentDate = new \DateTime();
        $startDate = $leaveRequest->getStartDate();

        $urls = $this->getActionUrls($leaveRequest);
        $approveUrl = $urls['approve'];
        $rejectUrl = $urls['reject'];
        $csrfTokenApprove = $this->csrfTokenManager->getToken('approve' . $leaveRequest->getId())->getValue();
        $csrfTokenReject = $this->csrfTokenManager->getToken('reject' . $leaveRequest->getId())->getValue();

        $approveModalId = 'approveModal_' . $leaveRequest->getId();
        $rejectModalId = 'rejectModal_' . $leaveRequest->getId();

        $approveModalContent = $this->renderPenaltyReviewModalContent($leaveRequest);

        $approveFooter = sprintf(
            '<button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>' .
            '<button type="button" class="btn btn-primary confirm-action-btn" ' .
            'data-url="%s" data-token="%s" data-action="approve" data-has-penalty="%d">Confirm</button>',
            $approveUrl,
            $csrfTokenApprove,
            $leaveRequest->getPenaltyDays() > 0 ? 1 : 0
        );

        $rejectFooter = sprintf(
            '<button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>' .
            '<button type="button" class="btn btn-primary confirm-action-btn" data-url="%s" data-token="%s" data-action="reject">Confirm</button>',
            $rejectUrl,
            $csrfTokenReject
        );

        $approveModal = $this->modalboxHelper->renderModal(
            $approveModalId,
            'Review Leave Request',
            $approveModalContent,
            $approveFooter
        );

        $rejectModal = $this->modalboxHelper->renderModal(
            $rejectModalId,
            'Confirm Rejection',
            sprintf(
                'Are you sure you want to reject this leave request?<br><br>' .
                '<strong>Warning:</strong> This action cannot be undone.'
            ),
            $rejectFooter
        );

        if ($leaveRequest->getStatus() === LeaveRequest::STATUS_PENDING) {
            return sprintf(
                '<div class="d-inline-flex gap-2">' .
                '<button class="bs-btn btn btn-success btn-sm bs-m approve-btn" data-toggle="modal" data-target="#%s" title="Approve">' .
                '<i class="bs-fa fa fa-check"></i>' .
                '</button>' .
                '<button class="bs-btn btn btn-danger btn-sm bs-m reject-btn" data-toggle="modal" data-target="#%s" title="Reject">' .
                '<i class="bs-fa fa fa-times"></i>' .
                '</button>' .
                '</div>%s%s',
                $approveModalId,
                $rejectModalId,
                $approveModal,
                $rejectModal
            );
        }

        if ($leaveRequest->getStatus() === LeaveRequest::STATUS_APPROVED && $startDate >= $currentDate) {
            return sprintf(
                '<div class="d-inline-flex gap-2">' .
                '<button class="bs-btn btn btn-danger btn-sm bs-m reject-btn" data-toggle="modal" data-target="#%s" title="Reject">' .
                '<i class="bs-fa fa fa-times"></i>' .
                '</button>' .
                '</div>%s',
                $rejectModalId,
                $rejectModal
            );
        }
        return '-';
    }

    private function renderPenaltyReviewModalContent(LeaveRequest $leaveRequest): string
    {
        $content = '<div class="container-fluid">';
        $content .= '<div class="row mb-4">';
        $content .= '<div class="col-md-6">';
        $content .= '<div class="card border-0 shadow-sm">';
        $content .= '<div class="card-body">';
        $content .= '<h5 class="card-title text-muted mb-3">Leave Information</h5>';

        $content .= '<div class="mb-3">';
        $content .= '<div class="d-flex justify-content-between">';
        $content .= '<span class="text-muted">Employee:</span>';
        $content .= '<span class="font-weight-bold">'.htmlspecialchars($leaveRequest->getEmployee()->getName()).'</span>';
        $content .= '</div>';
        $content .= '</div>';

        $content .= '<div class="mb-3">';
        $content .= '<div class="d-flex justify-content-between">';
        $content .= '<span class="text-muted">Leave Type:</span>';
        $content .= '<span class="font-weight-bold">'.htmlspecialchars($leaveRequest->getLeaveType()->getLeaveType()->getName()).'</span>';
        $content .= '</div>';
        $content .= '</div>';

        $content .= '<div class="mb-3">';
        $content .= '<div class="d-flex justify-content-between">';
        $content .= '<span class="text-muted">Dates:</span>';
        $content .= '<span class="font-weight-bold">'.
            $leaveRequest->getStartDate()->format('d-m-Y').' to '.
            $leaveRequest->getEndDate()->format('d-m-Y').'</span>';
        $content .= '</div>';
        $content .= '</div>';

        $content .= '<div class="mb-3">';
        $content .= '<div class="d-flex justify-content-between">';
        $content .= '<span class="text-muted">Days Requested:</span>';
        $content .= '<span class="font-weight-bold">'.number_format($leaveRequest->getDaysRequested(), 1).'</span>';
        $content .= '</div>';
        $content .= '</div>';

        $content .= '</div>';
        $content .= '</div>';
        $content .= '</div>';

        $content .= '<div class="col-md-6">';

        if ($leaveRequest->getPenaltyDays() > 0) {
            $content .= '<div class="card border-warning shadow-sm">';
            $content .= '<div class="card-header bg-warning-light border-warning">';
            $content .= '<h5 class="mb-0 text-warning-dark">Penalty Calculation</h5>';
            $content .= '</div>';
            $content .= '<div class="card-body">';

            $content .= '<div class="alert alert-warning mb-4">';
            $content .= '<i class="fas fa-exclamation-triangle mr-2"></i>';
            $content .= 'This request triggers a penalty of <strong>'.number_format($leaveRequest->getPenaltyDays(), 1).' days</strong>';
            $content .= '</div>';

            $content .= '<div class="d-flex justify-content-between mb-2">';
            $content .= '<span>Base Days:</span>';
            $content .= '<span class="fw-bold">'.number_format($leaveRequest->getDaysRequested(), 1).' days</span>';
            $content .= '</div>';

            $content .= '<div class="d-flex justify-content-between mb-2">';
            $content .= '<span>Penalty:</span>';
            $content .= '<span class="fw-bold text-danger">+ '.number_format($leaveRequest->getPenaltyDays(), 1).' days</span>';
            $content .= '</div>';

            $content .= '<div class="form-group mb-4">';
            $content .= '<label for="penaltyDays" class="font-weight-bold text-muted">Adjust Penalty Days</label>';
            $content .= '<div class="input-group">';
            $content .= sprintf(
                '<input type="number" class="form-control" id="penaltyDays" name="penaltyDays" value="%.1f" step="0.5" min="0">',
                $leaveRequest->getPenaltyDays()
            );
            $content .= '<div class="input-group-append">';
            $content .= '<span class="input-group-text">days</span>';
            $content .= '</div>';
            $content .= '</div>';
            $content .= '<small class="form-text text-muted">You can adjust the penalty days if needed</small>';
            $content .= '</div>';

//            $content .= '<div class="form-check mb-2">';
//            $content .= '<input class="form-check-input" type="checkbox" id="applyPenalty" name="applyPenalty" checked>';
//            $content .= '<label class="form-check-label" for="applyPenalty">Apply penalty to leave balance</label>';
//            $content .= '</div>';

            $content .= '</div>';
            $content .= '</div>';
        } else {
            $content .= '<div class="card border-success shadow-sm">';
            $content .= '<div class="card-body text-center py-4">';
            $content .= '<i class="fas fa-check-circle text-success fa-3x mb-3"></i>';
            $content .= '<h5 class="text-primary">No Penalty Applied</h5>';
            $content .= '<p class="text-muted mb-0">This leave request doesn\'t trigger any penalty</p>';
            $content .= '</div>';
            $content .= '</div>';
        }

        $content .= '</div>';
        $content .= '</div>';

        $content .= '</div>';

        return $content;
    }

    private function buildAdminQuery(QueryBuilder $qb): QueryBuilder
    {
        $session = $this->requestStack->getSession();
        $filters = $session->get('employee_task_filters_');
        $qb->select('lr', 'lb', 'lt', 'e', 'hr', 'tln')
            ->from(LeaveRequest::class, 'lr')
            ->leftJoin('lr.leaveType', 'lb')
            ->leftJoin('lb.leaveType', 'lt')
            ->leftJoin('lr.employee', 'e')
            ->leftJoin('lr.hrApprovedBy', 'hr')
            ->leftJoin('lr.teamLeadApprovedBy', 'tln');
        if (!empty($filters['startDate']) && !empty($filters['endDate'])) {
            try {
                $startDate = new \DateTime($filters['startDate']);
                $endDate = new \DateTime($filters['endDate']);
                $qb->andWhere('lr.startDate BETWEEN :startDate AND :endDate')
                    ->setParameter('startDate', $startDate->setTime(0, 0))
                    ->setParameter('endDate', $endDate->setTime(23, 59, 59));
            } catch (\Exception $e) {
                $this->logger->warning('Invalid date filter: ' . $e->getMessage());
            }
        }

        return $qb;
    }

    public function createTeamleaderLeaveRequestTable(Request $request, MasterEmployee $currentUser): DataTable
    {
        $dataTable = $this->dataTableFactory->create();
        $this->currentUser = $currentUser;

        foreach ($this->getAdminColumns() as $key => $column) {
            $dataTable->add($key, $column['className'], array_filter($column, fn($k) => $k !== 'className', ARRAY_FILTER_USE_KEY));
        }

        return $dataTable->createAdapter(ORMAdapter::class, [
            'entity' => LeaveRequest::class,
            'query' => fn(QueryBuilder $qb) => $this->buildTeamleaderQuery($qb, $currentUser),
        ])
            ->addOrderBy('appliedOn', 'desc')
            ->handleRequest($request);
    }

    public function createHrLeaveRequestTable(Request $request, MasterEmployee $currentUser): DataTable
    {
        $dataTable = $this->dataTableFactory->create();
        $this->currentUser = $currentUser;
        foreach ($this->getAdminColumns() as $key => $column) {
            $dataTable->add($key, $column['className'], array_filter($column, fn($k) => $k !== 'className', ARRAY_FILTER_USE_KEY));
        }

        return $dataTable->createAdapter(ORMAdapter::class, [
            'entity' => LeaveRequest::class,
            'query' => fn(QueryBuilder $qb) => $this->buildAdminQuery($qb),
        ])
            ->addOrderBy('appliedOn', 'desc')
            ->handleRequest($request);
    }

    private function buildTeamleaderQuery(QueryBuilder $qb, MasterEmployee $teamLeader): QueryBuilder
    {
        return $qb
            ->select('lr', 'lb', 'lt', 'e', 'hr', 'tln')
            ->from(LeaveRequest::class, 'lr')
            ->leftJoin('lr.leaveType', 'lb')
            ->leftJoin('lb.leaveType', 'lt')
            ->leftJoin('lr.employee', 'e')
            ->leftJoin('lr.hrApprovedBy', 'hr')
            ->leftJoin('lr.teamLeadApprovedBy', 'tln')
            ->where('e.id IN (
                SELECT IDENTITY(er.employee)
                FROM App\Entity\EmployeeReportsTo er
                WHERE er.teamLeader = :teamLeader
            )')
            ->setParameter('teamLeader', $teamLeader->getId());
    }

    private function getActionUrls(LeaveRequest $leaveRequest): array
    {
        $approveRoute = 'admin_leave_request_approve';
        $rejectRoute = 'admin_leave_request_reject';

        if ($this->currentUser instanceof MasterEmployee) {
            if (in_array('ROLE_EMPLOYEE', $this->currentUser->getRoles(), true)) {
                $approveRoute = 'employee_leave_request_approve';
                $rejectRoute = 'employee_leave_request_reject';
            }
        }

        return [
            'approve' => $this->router->generate($approveRoute, ['id' => $leaveRequest->getId()]),
            'reject' => $this->router->generate($rejectRoute, ['id' => $leaveRequest->getId()]),
        ];
    }

    /**
     * Calculate the number of leave days, excluding weekends and holidays, with sandwich rule penalties.
     *
     * @param LeaveRequest $leaveRequest
     * @param array<string> $holidayDates
     * @return array{base: float, penalty: float, total: float}
     * @throws \DateMalformedStringException
     */
    public function calculateDaysRequested(LeaveRequest $leaveRequest, array $holidayDates = []): array
    {
        $startDate = $leaveRequest->getStartDate();
        $endDate = $leaveRequest->getEndDate();
        $appliedOn = $leaveRequest->getAppliedOn();
        $leaveType = $leaveRequest->getLeaveType()?->getLeaveType();

        if (!$startDate || !$endDate || !$appliedOn || !$leaveType) {
            return ['base' => 0.0, 'penalty' => 0.0, 'total' => 0.0];
        }
        if ($startDate == $endDate &&
            (($leaveRequest->getStartHalfDay() && !$leaveRequest->getEndHalfDay()) ||
                (!$leaveRequest->getStartHalfDay() && $leaveRequest->getEndHalfDay()))) {
            $daysNoticed = (int) $appliedOn->diff($startDate)->format('%a');
            $noticeDaysSingle = $leaveType->getNoticeDaysSingle() ?? 0;
            $insufficientNotice = $daysNoticed < $noticeDaysSingle;
            $penalty = $insufficientNotice ? 0.5 : 0.0;

            return ['base' => 0.5, 'penalty' => $penalty, 'total' => 0.5];
        }
        /** @var \DateTime $endDate */
        $period = new \DatePeriod(
            clone $startDate,
            new \DateInterval('P1D'),
            (clone $endDate)->modify('+1 day')
        );

        $workingDays = 0.0;
        $leaveDates = [];
        $nonWorkingDatesInPeriod = [];
        $containsFriday = false;
        $containsMonday = false;
        $holidaysInPeriod = [];
        $halfDaysInPeriod = [];

        foreach ($period as $date) {
            $dateStr = $date->format('Y-m-d');
            $leaveDates[] = $dateStr;
            $day = (int) $date->format('N');
            $isHoliday = in_array($dateStr, $holidayDates, true);
            if ($isHoliday) {
                $holidaysInPeriod[] = $dateStr;
            }
            if ($day >= 6 || $isHoliday) {
                $nonWorkingDatesInPeriod[] = $dateStr;
                continue;
            }

            $workingDays += 1.0;

            if ($day === 1) {
                $containsMonday = true;
            } elseif ($day === 5) {
                $containsFriday = true;
            }
            if (($date->format('Y-m-d') === $startDate->format('Y-m-d') && $leaveRequest->getStartHalfDay()) ||
                ($date->format('Y-m-d') === $endDate->format('Y-m-d') && $leaveRequest->getEndHalfDay())) {
                $halfDaysInPeriod[] = $dateStr;
            }
        }
        $adjustment = 0.0;
        if ($startDate == $endDate) {
            if ($leaveRequest->getStartHalfDay() && $leaveRequest->getEndHalfDay()) {
                $adjustment = 0.5;
            }
        } else {
            if ($leaveRequest->getStartHalfDay()) $adjustment += 0.5;
            if ($leaveRequest->getEndHalfDay()) $adjustment += 0.5;
        }

        $baseDays = max(0.5, $workingDays - $adjustment);
        $isFirstHalfOnly = $startDate == $endDate &&
            $leaveRequest->getStartHalfDay() === LeaveRequest::HALF_DAY_MORNING &&
            !$leaveRequest->getEndHalfDay();
        $isSecondHalfOnly = $startDate == $endDate &&
            $leaveRequest->getEndHalfDay() === LeaveRequest::HALF_DAY_AFTERNOON &&
            !$leaveRequest->getStartHalfDay();
        $daysNoticed = (int) $appliedOn->diff($startDate)->format('%a');
        $noticeDaysSingle = $leaveType->getNoticeDaysSingle() ?? 0;
        $noticeDaysLongWeekend = $leaveType->getNoticeDaysLongWeekend() ?? 0;
        $hasWeekendOrMultipleNonWorkingDays = count($nonWorkingDatesInPeriod) > 0;
        $leaveDuration = (int) $startDate->diff($endDate)->format('%a') + 1;
        $isExtendedLeave = $leaveDuration >= 7;
        $isMultiDayLeave = $leaveDuration > 1;
        $requiredNotice = ($isExtendedLeave || $hasWeekendOrMultipleNonWorkingDays || $isMultiDayLeave) ?
            $noticeDaysLongWeekend : $noticeDaysSingle;
        $insufficientNotice = $daysNoticed < $requiredNotice;
        $penaltyDays = 0.0;
        $consecutiveNonWorkingDaysBefore = $this->getConsecutiveNonWorkingDaysBefore($startDate, $holidayDates);

        if ($isMultiDayLeave && $insufficientNotice) {
            $penaltyDays = count($halfDaysInPeriod) * 0.5;
            $fullDaysCount = count($leaveDates) - count($nonWorkingDatesInPeriod) - count($halfDaysInPeriod);
            $penaltyDays += $fullDaysCount * 0.5;
        }
        if ($leaveDuration > 1 && count($nonWorkingDatesInPeriod) > 0 && $insufficientNotice) {
            if ($isSecondHalfOnly) {
                $penaltyDays = 0.0;
            } elseif ($isFirstHalfOnly) {
                $penaltyDays = 0.5;
            } else {
                $penaltyDays = max($penaltyDays, count($nonWorkingDatesInPeriod));
            }
        } else if (!$isExtendedLeave) {
            if (($containsMonday || $containsFriday) && $insufficientNotice) {
                if ($isSecondHalfOnly) {
                    $penaltyDays += 0.0;
                } elseif ($isFirstHalfOnly) {
                    $penaltyDays += 0.5;
                } else {
                    $penaltyDays += 1.0;
                }
            }
            foreach ($holidayDates as $holidayStr) {
                $holiday = new \DateTime($holidayStr);
                $dayBeforeHoliday = (clone $holiday)->modify('-1 day')->format('Y-m-d');
                $dayAfterHoliday = (clone $holiday)->modify('+1 day')->format('Y-m-d');

                $leaveBeforeHoliday = in_array($dayBeforeHoliday, $leaveDates, true);
                $leaveAfterHoliday = in_array($dayAfterHoliday, $leaveDates, true);

                if (($leaveBeforeHoliday || $leaveAfterHoliday) && $insufficientNotice) {
                    if ($isSecondHalfOnly) {
                        $penaltyDays += 0.0;
                    } elseif ($isFirstHalfOnly) {
                        $penaltyDays += 0.5;
                    } else {
                        $penaltyDays += 1.0;
                    }
                    break;
                }
            }
        }
        $consecutiveNonWorkingDaysAfter = $this->getConsecutiveNonWorkingDaysAfter($endDate, $holidayDates);
        if ($consecutiveNonWorkingDaysAfter >= 3 && $insufficientNotice) {
            if ($startDate == $endDate) {
                if ($isSecondHalfOnly) {
                    $additionalPenalty = 0.5;
                } elseif ($isFirstHalfOnly) {
                    $additionalPenalty = 0.0;
                } else {
                    $additionalPenalty = $consecutiveNonWorkingDaysAfter;
                }
                if (!($containsFriday && $additionalPenalty <= 1.0)) {
                    $penaltyDays = max($penaltyDays, $additionalPenalty);
                }
            } else {
                $penaltyDays = max($penaltyDays, $consecutiveNonWorkingDaysAfter);
            }
        }
        if ($consecutiveNonWorkingDaysBefore >= 3 && $insufficientNotice) {
            if ($startDate == $endDate) {
                if ($isSecondHalfOnly) {
                    $additionalPenalty = 0.0;
                } elseif ($isFirstHalfOnly) {
                    $additionalPenalty = 0.5;
                } else {
                    $additionalPenalty = $consecutiveNonWorkingDaysBefore;
                }
                if (!($containsMonday && $additionalPenalty <= 1.0)) {
                    $penaltyDays = max($penaltyDays, $additionalPenalty);
                }
            } else {
                $penaltyDays = max($penaltyDays, $consecutiveNonWorkingDaysBefore);
            }
        }

        return [
            'base' => $baseDays,
            'penalty' => $penaltyDays,
            'total' => $baseDays,
        ];
    }

    /**
     * Get the number of consecutive non-working days immediately before the start date.
     *
     * @param \DateTimeInterface $startDate
     * @param array<string> $holidayDates
     * @return int
     */
    private function getConsecutiveNonWorkingDaysBefore(\DateTimeInterface $startDate, array $holidayDates): int
    {
        $count = 0;
        $currentDate = clone $startDate;
        for ($i = 0; $i < 10; $i++) {
            $currentDate = $currentDate->modify('-1 day');
            $dayStr = $currentDate->format('Y-m-d');
            $dayOfWeek = (int) $currentDate->format('N'); // 1-7 (Monday-Sunday)
            $isNonWorking = ($dayOfWeek >= 6) || in_array($dayStr, $holidayDates, true);

            if ($isNonWorking) {
                $count++;
            } else {
                break;
            }
        }

        return $count;
    }
    /**
     * Get the number of consecutive non-working days immediately after the end date.
     *
     * @param \DateTimeInterface $endDate
     * @param array<string> $holidayDates
     * @return int
     */
    private function getConsecutiveNonWorkingDaysAfter(\DateTimeInterface $endDate, array $holidayDates): int
    {
        $count = 0;
        $currentDate = clone $endDate;
        for ($i = 0; $i < 10; $i++) {
            $currentDate = $currentDate->modify('+1 day');
            $dayStr = $currentDate->format('Y-m-d');
            $dayOfWeek = (int) $currentDate->format('N');
            $isNonWorking = ($dayOfWeek >= 6) || in_array($dayStr, $holidayDates, true);

            if ($isNonWorking) {
                $count++;
            } else {
                break;
            }
        }

        return $count;
    }

    /**
     * Fetch all holiday dates.
     *
     * @return array<string>
     */
    public function getHolidayDates(): array
    {
        $holidays = $this->entityManager->getRepository(Holiday::class)->findAll();
        return array_map(static function(Holiday $holiday) {
            return $holiday->getDate()->format('Y-m-d');
        }, $holidays);
    }
}