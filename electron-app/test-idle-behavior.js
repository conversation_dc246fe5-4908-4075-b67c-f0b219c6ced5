const { app, BrowserWindow } = require('electron');
const IdleDetector = require('./src/idle-detector');
const Store = require('electron-store');

// Test the idle detection behavior
async function testIdleBehavior() {
    console.log('🧪 Testing Idle Detection Behavior...');
    
    const store = new Store();
    
    // Mock time logger and status tracker
    const mockTimeLogger = {
        markIdlePeriod: (start, end, reason) => {
            console.log(`📝 Mock: Idle period marked from ${start.toLocaleTimeString()} to ${end.toLocaleTimeString()} (${reason})`);
        },
        logActivity: (type, desc) => {
            console.log(`📝 Mock: Activity logged - ${type}: ${desc}`);
        }
    };
    
    const mockStatusTracker = {
        updateStatus: (status) => {
            console.log(`📊 Mock: Status updated to ${status}`);
        }
    };
    
    // Create idle detector with short timeout for testing
    const idleDetector = new IdleDetector(mockStatusTracker, mockTimeLogger, store);
    
    // Set short timeout for testing (30 seconds idle, 10 second warning)
    await idleDetector.updateConfiguration({
        idleTimeoutEnabled: true,
        idleTimeoutMinutes: 0.5, // 30 seconds
        warningDurationSeconds: 10 // 10 second warning
    });
    
    console.log('✅ Idle detector configured for testing');
    console.log('⏰ Idle timeout: 30 seconds, Warning: 10 seconds');
    console.log('🎯 Test: Go idle for 20+ seconds to see popup, then move mouse to test dismissal');
    
    // Keep the test running
    setTimeout(() => {
        console.log('🔚 Test completed');
        app.quit();
    }, 120000); // 2 minutes
}

app.whenReady().then(() => {
    testIdleBehavior();
});

app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
        testIdleBehavior();
    }
});
