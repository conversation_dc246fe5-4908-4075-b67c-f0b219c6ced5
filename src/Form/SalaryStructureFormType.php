<?php

namespace App\Form;

use App\Entity\SalaryStructure;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\CallbackTransformer;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\GreaterThanOrEqual;

class SalaryStructureFormType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('startDate', TextType::class, [
                'required' => true,
                'label' => 'Start Date',
                'attr' => [
                    'class' => 'form-control datepicker',
                    'placeholder' => 'Select date',
                    'required' => 'required',
                ],
                'constraints' => [
                    new NotBlank(['message' => 'Start date is required.'])
                ],
            ])
            ->add('endDate', TextType::class, [
                'required' => false,
                'label' => 'End Date',
                'attr' => [
                    'class' => 'form-control datepicker',
                    'placeholder' => 'Select date',
                ],
            ])
            ->add('ctc', NumberType::class, [
                'label' => 'CTC',
                'required' => true,
                'scale' => 2,
                'constraints' => [
                    new NotBlank(['message' => 'CTC is required.']),
                    new GreaterThanOrEqual(['value' => 0, 'message' => 'CTC must be non-negative.'])
                ],
            ])
            ->add('deductPF', CheckboxType::class, [
                'label' => 'Deduct PF',
                'required' => false,
            ])
            ->add('deductESIC', CheckboxType::class, [
                'label' => 'Deduct ESIC',
                'required' => false,
            ]);


        foreach (['startDate', 'endDate'] as $field) {
            $builder->get($field)
                ->addModelTransformer(new CallbackTransformer(
                    function ($dateAsObject) {
                        return $dateAsObject instanceof \DateTimeInterface ? $dateAsObject->format('d-m-Y') : '';
                    },
                    function ($dateAsString) {
                        return $dateAsString ? \DateTime::createFromFormat('d-m-Y', $dateAsString) : null;
                    }
                ));
        }
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => SalaryStructure::class,
        ]);
    }
}

