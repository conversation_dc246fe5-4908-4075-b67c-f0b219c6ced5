<div class="row">
<div class="col-xl-12">
    <div class="row">
        <div class="col-lg-6 col-xl-4">
            <div class="card shadow-sm border-0 rounded-lg">
                <div class="card-header btn-primary border-0">
                    <h5 class="card-title mb-0">
                        <i class="fa fa-birthday-cake me-2"></i>Upcoming Birthdays
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="scrollable-events" style="max-height: 140px; overflow-y: auto;">
                        {% if birthday|length > 0 %}
                            <ul class="list-group list-group-flush">
                                {% for b in birthday %}
                                    <li class="list-group-item border-0 d-flex justify-content-between align-items-center py-3">
                                        <div class="d-flex align-items-center">
                                            <div class="announcement-icon me-3 rounded-circle" style="width: 45px; height: 45px; background-color: rgba(247, 135, 57, 0.15); color: #f78739; display: flex; align-items: center; justify-content: center;">
                                                {{ b.name|slice(0,1)|upper }}
                                            </div>
                                            <div style="padding-left: 8px;">
                                                <h6 class="mb-0">{{ b.name }}</h6>
                                                <small class="text-muted">{{ b.birthDate|date('M d') }}</small>
                                            </div>
                                        </div>
                                        <span class="badge btn-primary rounded-pill">
                                            <i class="fa fa-gift"></i>
                                        </span>
                                    </li>
                                {% endfor %}
                            </ul>
                        {% else %}
                            <div class="text-center py-4">
                                <div class="empty-state-icon mb-3">
                                    <i class="fa fa-calendar-o fa-3x text-muted"></i>
                                </div>
                                <p class="text-muted">No upcoming birthdays this month.</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Work Anniversaries Card -->
        <div class="col-lg-6 col-xl-4">
            <div class="card shadow-sm border-0 rounded-lg">
                <div class="card-header btn-primary text-white border-0">
                    <h5 class="card-title mb-0">
                        <i class="fa fa-trophy me-2"></i>Work Anniversaries
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="scrollable-events" style="max-height: 140px; overflow-y: auto;">
                        {% if anniversary|length > 0 %}
                            <ul class="list-group list-group-flush">
                                {% for a in anniversary %}
                                    <li class="list-group-item border-0 d-flex justify-content-between align-items-center py-3">
                                        <div class="d-flex align-items-center">
                                            <div class="announcement-icon me-3 rounded-circle" style="width: 45px; height: 45px; background-color: rgba(247, 135, 57, 0.15); color: #f78739; display: flex; align-items: center; justify-content: center;">
                                                {{ a.name|slice(0,1)|upper }}
                                            </div>
                                            <div style="padding-left: 8px;">
                                                <h6 class="mb-0">{{ a.name }}</h6>
                                                <small class="text-muted">{{ a.joiningDate|date('M d') }}</small>
                                                <div class="badge bg-light text-primary mt-1">
                                                    {{ "now"|date("Y") - a.joiningDate|date("Y") }} {% if "now"|date("Y") - a.joiningDate|date("Y") == 1 %}Year{% else %}Years{% endif %}
                                                </div>
                                            </div>
                                        </div>
                                        <span class="badge btn-primary rounded-pill">
                                            <i class="fa fa-handshake-o"></i>
                                        </span>
                                    </li>
                                {% endfor %}
                            </ul>
                        {% else %}
                            <div class="text-center py-4">
                                <div class="empty-state-icon mb-3">
                                    <i class="fa fa-briefcase fa-3x text-muted"></i>
                                </div>
                                <p class="text-muted">No upcoming anniversaries this month.</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-xl-4">
            {% if is_granted('ROLE_ADMIN') %}
                {% include 'admin/Announcement/announcementdashboard.html.twig' %}
            {% else %}
                {% include 'Front/Announcement/announcement.html.twig' %}
            {% endif %}
        </div>
    </div>
</div>
</div>

<div class="row">
    <div class="col-md-12 col-lg-5">
        <div class="card">
            {% if is_granted('ROLE_ADMIN') %}
            {% include 'CommonTemplate/adminCalender.html.twig'%}
            {% else %}
            {% include 'CommonTemplate/calender.html.twig'%}
            {% endif %}
        </div>
    </div>
    <div class="col-xl-7">
        <div class="card">
            <div class="card-header btn-primary text-white border-0">
                <h5 class="card-title mb-0">
                    <i class="fa fa-sliders me-2"></i>Weekly Leave Requests
                </h5>
            </div>
            <div class="card-body--">
                <div class="table-stats order-table ov-h text-center">
                    <table class="table">
                        <thead>
                        <tr>
                            <th class="serial">#</th>
                            <th class="avatar">Name</th>
                            <th>Date</th>
                            <th>Status</th>
                            <th>Manager/Admin</th>
                        </tr>
                        </thead>
                        <tbody>
                        {% for leave in weeklyLeaveRequests %}
                            <tr>
                                <td class="serial">{{ loop.index }}.</td>
                                <td class="avatar">{{ leave.employee.name ?? 'N/A' }}</td>
                                <td>
                                    {% set startDate = leave.startDate ? leave.startDate|date('d-m-Y') : '-' %}
                                    {% set endDate = leave.endDate ? leave.endDate|date('d-m-Y') : '-' %}
                                    {% set isSingleDay = leave.startDate and leave.endDate and leave.startDate|date('Y-m-d') == leave.endDate|date('Y-m-d') %}
                                    {% set halfDay = isSingleDay ? leave.startHalfDay : leave.endHalfDay %}
                                    {% set indicator = '' %}
                                    {% if halfDay == 'MORNING' %}
                                        {% set indicator = '<span class="text-black" title="Morning Half-Day">*</span>' %}
                                    {% elseif halfDay == 'AFTERNOON' %}
                                        {% set indicator = '<span class="text-black" title="Afternoon Half-Day">*</span>' %}
                                    {% elseif not halfDay %}
                                        {% set indicator = '<span class="text-info" title="Full Day"></span>' %}
                                    {% endif %}
                                    {% if isSingleDay %}
                                        {{ startDate }}{{ indicator|raw }}
                                    {% else %}
                                        {{ startDate }} - {{ endDate }}{{ indicator|raw }}
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge badge-{{ leave.status == 'APPROVED' ? 'success' : leave.status == 'PENDING' ? 'warning' : leave.status|lower }}">{{ leave.status|capitalize }}</span>
                                </td>
                                <td>
                                    {{ leave.hrApprovedBy ? leave.hrApprovedBy.name :
                                    leave.teamLeadApprovedBy ? leave.teamLeadApprovedBy.name :
                                    leave.adminApprovedBy ? leave.adminApprovedBy.username : 'N/A' }}
                                </td>
                            </tr>
                        {% else %}
                            <tr>
                                <td class="text-muted" colspan="5" style="text-transform: none !important;">No leave requests found for this week.</td>
                            </tr>
                        {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="row">
    <div class="col-xl-8">
        <div class="card">
            <div class="card-header btn-primary text-white border-0">
                <h5 class="card-title mb-0">
                    <i class="fa fa-sliders me-2"></i>All Hardware
                </h5>
            </div>
            <div class="card-body--">
                <div class="table-stats order-table ov-h text-center">
                    <table class="table">
                        <thead>
                        <tr>
                            <th class="serial">#</th>
                            <th class="avatar">Hardware Name</th>
                            <th>Type</th>
                            <th>Location</th>
                            <th>Description</th>
                        </tr>
                        </thead>
                        <tbody>
                        {% for hardware in hardware %}
                            <tr>
                                <td class="serial">{{ loop.index }}.</td>
                                <td class="avatar">{{ hardware.hardwareName }}</td>
                                <td>{{ hardware.hardwareType ? hardware.hardwareType.name : 'N/A' }}</td>
                                <td>{{ hardware.location ? hardware.location: 'N/A' }}</td>
                                <td>
                                    {% set description = hardware.description ? hardware.description : '-' %}
                                    {% set maxLength = 40 %}
                                    {% if description|length > maxLength %}
                                        <span title="{{ description|e('html_attr') }}">{{ (description|slice(0, maxLength) ~ '...')|e }}</span>
                                    {% else %}
                                        {{ description|e }}
                                    {% endif %}
                                </td>
                            </tr>
                        {% else %}
                            <tr>
                                <td class="text-muted" colspan="5" style="text-transform: none !important;">No hardware found.</td>
                            </tr>
                        {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>