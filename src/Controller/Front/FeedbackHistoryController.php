<?php

namespace App\Controller\Front;

use App\Entity\MasterEmployee;
use App\Repository\FormSubmissionRepository;
use App\Traits\ExceptionLoggerTrait;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class FeedbackHistoryController extends AbstractController
{
    use ExceptionLoggerTrait;

    public function __construct(
        private readonly FormSubmissionRepository $submissionRepository
    )
    {
    }

    #[Route('/feedback-history', name: 'employee_history', methods: ['GET', 'POST'])]
    public function showHistory(Request $request): Response
    {
        return $this->handleFeedbackHistory(
            $request,
            'findSubmissionsWithDetailsByDate',
            'Front/FeedBackHistory/history.html.twig'
        );
    }

    #[Route('/self-history', name: 'employee_history_self', methods: ['GET', 'POST'])]
    public function showSelfEvaluationHistory(Request $request): Response
    {
        return $this->handleFeedbackHistory(
            $request,
            'findSubmissionsWithDetailsSelfEvaluation',
            'Front/FeedBackHistory/selfEvaluation.html.twig'
        );
    }

    private function handleFeedbackHistory(Request $request, string $repositoryMethod, string $template): Response
    {
        $user = $this->getUser();

        if (!$user instanceof MasterEmployee) {
            throw $this->createAccessDeniedException('You must be logged in as an employee to view this page.');
        }

        $employee = $user;
        $selectedDate = $request->isMethod('POST')
            ? $request->request->get('date')
            : $request->query->get('date');

        $selectedDate = $selectedDate ?: date('Y-m');

        $availableDates = $this->submissionRepository->findUniqueSubmissionDates($employee);
        $submissions = $this->submissionRepository->$repositoryMethod($employee, $selectedDate);

        $groupedSubmissions = [];
        foreach ($submissions as $submission) {
            $period = $submission->getReviewPeriod();
            if (!isset($groupedSubmissions[$period])) {
                $groupedSubmissions[$period] = [];
            }
            $groupedSubmissions[$period][] = $submission;
        }
        krsort($groupedSubmissions);

        return $this->render($template, [
            'groupedSubmissions' => $groupedSubmissions,
            'availableDates' => $availableDates,
            'selectedDate' => $selectedDate
        ]);
    }
}