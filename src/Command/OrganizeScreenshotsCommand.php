<?php

namespace App\Command;

use App\Service\ScreenshotFileOrganizer;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'app:organize-screenshots',
    description: 'Organize screenshots into employee/month folder structure',
)]
class OrganizeScreenshotsCommand extends Command
{
    public function __construct(
        private readonly ScreenshotFileOrganizer $screenshotOrganizer
    ) {
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        $io->title('Screenshot File Organizer');
        $io->text('Organizing screenshots into employee/month folder structure...');

        $organized = $this->screenshotOrganizer->organizeScreenshots();

        if ($organized > 0) {
            $io->success("Successfully organized {$organized} screenshots!");
        } else {
            $io->info('No screenshots needed organizing.');
        }

        return Command::SUCCESS;
    }
}
