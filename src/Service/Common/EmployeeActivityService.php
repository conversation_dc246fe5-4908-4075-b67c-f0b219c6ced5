<?php

namespace App\Service\Common;

use App\Entity\EmployeeActivity;
use App\Entity\EmployeeReportsTo;
use App\Entity\MasterEmployee;
use App\Helper\ModalboxHelper;
use Doctrine\ORM\QueryBuilder;
use Omines\DataTablesBundle\Adapter\Doctrine\ORMAdapter;
use Omines\DataTablesBundle\Column\DateTimeColumn;
use Omines\DataTablesBundle\Column\TextColumn;
use Omines\DataTablesBundle\DataTable;
use Omines\DataTablesBundle\DataTableFactory;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Request;
use App\Traits\ExceptionLoggerTrait;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Security\Csrf\CsrfTokenManagerInterface;

class EmployeeActivityService
{
    use ExceptionLoggerTrait;

    public function __construct(
        private readonly DataTableFactory $dataTableFactory,
        private readonly RouterInterface $router,
        private readonly CsrfTokenManagerInterface $csrfTokenManager,
        private readonly ModalboxHelper $modalboxHelper,
        ParameterBagInterface $parameterBag
    ) {
        $this->initializeLogger($parameterBag, 'EmployeeActivityService');
    }

    public function createEmployeeActivityTable(Request $request, MasterEmployee $employee): DataTable
    {
        $dataTable = $this->dataTableFactory->create();

        foreach ($this->getColumns() as $key => $column) {
            if ($key === 'createdAt') {
                continue;
            }
            $dataTable->add($key, $column['className'], array_filter($column, fn($k) => $k !== 'className', ARRAY_FILTER_USE_KEY));
        }

        return $dataTable->createAdapter(ORMAdapter::class, [
            'entity' => EmployeeActivity::class,
            'query' => fn(QueryBuilder $qb) => $this->buildQuery($qb, $employee),
        ])
            ->addOrderBy('date', 'desc')
            ->handleRequest($request);
    }

    public function createEmployeeAssinedTeamleder(Request $request, MasterEmployee $employee): DataTable
    {
        $dataTable = $this->dataTableFactory->create();

        foreach ($this->getColumns() as $key => $column) {
            if ($key === 'actions') {
                continue;
            }
            $dataTable->add($key, $column['className'], array_filter($column, fn($k) => $k !== 'className', ARRAY_FILTER_USE_KEY));
        }

        return $dataTable->createAdapter(ORMAdapter::class, [
            'entity' => EmployeeActivity::class,
            'query' => fn(QueryBuilder $qb) => $this->buildForTeamleder($qb, $employee),
        ])
            ->addOrderBy('createdAt', 'desc')
            ->handleRequest($request);
    }

    public function createForAdmin(Request $request): DataTable
    {
        $dataTable = $this->dataTableFactory->create();

        foreach ($this->getColumns() as $key => $column) {
            if ($key === 'actions') {
                continue;
            }
            $dataTable->add($key, $column['className'], array_filter($column, fn($k) => $k !== 'className', ARRAY_FILTER_USE_KEY));
        }

        return $dataTable->createAdapter(ORMAdapter::class, [
            'entity' => EmployeeActivity::class,
            'query' => fn(QueryBuilder $qb) => $this->buildForAdmin($qb),
        ])
            ->addOrderBy('createdAt', 'desc')
            ->handleRequest($request);
    }

    private function getColumns(): array
    {
        return [
            'employee' => [
                'label' => 'Employee',
                'field' => 'emp.name',
                'className' => TextColumn::class,
                'render' => fn($value, $context) => htmlspecialchars($context->getEmployee()->getName() ?? 'N/A'),
            ],
            'activityType' => [
                'label' => 'Activity Type',
                'field' => 'e.activityType',
                'className' => TextColumn::class,
                'render' => fn($value) => htmlspecialchars($value ? ucfirst($value) : 'N/A'),
            ],
            'description' => [
                'label' => 'Description',
                'field' => 'e.description',
                'className' => TextColumn::class,
                'render' => function ($value) {
                    $description = $value ?? '-';
                    $maxLength = 50;
                    if (strlen($description) > $maxLength) {
                        $truncated = substr($description, 0, $maxLength) . '...';
                        return sprintf('<span title="%s">%s</span>', htmlspecialchars($description), htmlspecialchars($truncated));
                    }
                    return htmlspecialchars($description);
                },
            ],
            'date' => [
                'label' => 'Date',
                'field' => 'e.date',
                'className' => DateTimeColumn::class,
                'format' => 'd-m-Y',
                'searchable' => false,
            ],
            'hours' => [
                'label' => 'Hours',
                'field' => 'e.hours',
                'className' => TextColumn::class,
                'render' => function ($value) {
                    if ($value === null) {
                        return '-';
                    }
                    $formattedValue = number_format((float) $value, 2, '.', '');
                    $parts = explode('.', $formattedValue);
                    $hours = (int) $parts[0];
                    $minutes = isset($parts[1]) ? (int) $parts[1] : 0;
                    $formatted = '';
                    if ($hours >0) {
                        $formatted .= $hours . ' hr';
                    }
                    if ($minutes > 0) {
                        $formatted .= ($hours > 0 ? ' ' : '') . $minutes . ' min';
                    }
                    return $formatted ?: '-';
                },
            ],
            'relatedTask' => [
                'label' => 'Task',
                'field' => 't.title',
                'className' => TextColumn::class,
                'render' => fn($value, $context) => htmlspecialchars($context->getRelatedTask()?->getTitle() ?? '-'),
            ],
            'createdAt' => [
                'label' => 'Created At',
                'field' => 'e.createdAt',
                'className' => DateTimeColumn::class,
                'format' => 'd-m-Y',
                'searchable' => false,
            ],
            'actions' => [
                'label' => 'Actions',
                'field' => 'e.id',
                'className' => TextColumn::class,
                'orderable' => false,
                'render' => fn($value, $context) => $this->renderActionColumn($context) ? : '-',
            ],
        ];
    }

    private function renderActionColumn(EmployeeActivity $activity): string
    {
        $actions = '';
        $today = new \DateTime('today');

        if ($activity->getCreatedAt() >= $today) {
            $entityId = $activity->getId();
            $editUrl = $this->router->generate('employee_activity_edit', ['id' => $entityId]);
            $deleteUrl = $this->router->generate('employee_activity_delete', ['id' => $entityId]);
            $csrfToken = $this->csrfTokenManager->getToken('delete' . $entityId)->getValue();

            $modalId = 'deleteActivityModal_' . $entityId;
            $modalBody = sprintf(
                'Are you sure you want to permanently delete this activity "%s"?<br><br><strong>Warning:</strong> This action cannot be undone.',
                htmlspecialchars($activity->getActivityType())
            );
            $footerButtons = sprintf(
                '<button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>' .
                '<button type="button" class="btn btn-primary delete-activity-btn" data-url="%s" data-delete="true" data-token="%s">Delete</button>',
                $deleteUrl,
                $csrfToken
            );

            $deleteModal = $this->modalboxHelper->renderModal(
                $modalId,
                'Confirm Activity Deletion',
                $modalBody,
                $footerButtons
            );

            $actions = sprintf(
                '<div class="d-inline-flex gap-2">
                    <a href="%s" class="bs-btn btn btn-primary btn-sm" title="Edit" style="margin: 2px;"><i class="bs-fa fa fa-edit"></i></a>
                    <button type="button" class="bs-btn btn btn-primary btn-sm" title="Delete" style="margin: 2px;" data-toggle="modal" data-target="#%s">
                        <i class="bs-fa fa fa-trash"></i>
                    </button>
                </div>%s',
                $editUrl,
                $modalId,
                $deleteModal
            );
        }

        return $actions;
    }

    private function buildQuery(QueryBuilder $qb, MasterEmployee $employee): void
    {
        $qb->select('e')
            ->addSelect('t')
            ->addSelect('emp')
            ->from(EmployeeActivity::class, 'e')
            ->leftJoin('e.relatedTask', 't')
            ->leftJoin('e.employee', 'emp')
            ->where('e.employee = :employee')
            ->setParameter('employee', $employee);
    }

    private function buildForTeamleder(QueryBuilder $qb, MasterEmployee $employee): void
    {
        $qb->select('e')
            ->addSelect('t')
            ->addSelect('emp')
            ->from(EmployeeActivity::class, 'e')
            ->leftJoin('e.relatedTask', 't')
            ->leftJoin('e.employee', 'emp')
            ->innerJoin(EmployeeReportsTo::class, 'rt', 'WITH', 'rt.employee = emp AND rt.teamLeader = :teamLeader')
            ->setParameter('teamLeader', $employee);
    }

    private function buildForAdmin(QueryBuilder $qb): void
    {
        $qb->select('e')
            ->addSelect('t')
            ->addSelect('emp')
            ->from(EmployeeActivity::class, 'e')
            ->leftJoin('e.relatedTask', 't')
            ->leftJoin('e.employee', 'emp');
    }
}