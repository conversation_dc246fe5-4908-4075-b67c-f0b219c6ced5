<?php

namespace App\Service;

use App\Entity\TimeLog;
use App\Entity\ScreenshotLog;
use App\Entity\ApplicationUsage;
use App\Entity\WebsiteUsage;
use App\Repository\TimeLogRepository;
use App\Repository\ScreenshotLogRepository;
use App\Repository\ApplicationUsageRepository;
use App\Repository\WebsiteUsageRepository;
use App\Repository\MasterEmployeeRepository;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;

class SyncService
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private TimeLogRepository $timeLogRepository,
        private ScreenshotLogRepository $screenshotLogRepository,
        private ApplicationUsageRepository $applicationUsageRepository,
        private WebsiteUsageRepository $websiteUsageRepository,
        private MasterEmployeeRepository $employeeRepository,
        private LoggerInterface $logger
    ) {}

    /**
     * Process bulk sync data from desktop application
     */
    public function processBulkSync(array $syncData): array
    {
        $results = [
            'success' => true,
            'processed' => 0,
            'errors' => [],
            'details' => []
        ];

        $this->entityManager->beginTransaction();

        try {
            // Process time logs
            if (isset($syncData['time_logs'])) {
                $timeLogResult = $this->processTimeLogs($syncData['time_logs'], $syncData['employee_id']);
                $results['details']['time_logs'] = $timeLogResult;
                $results['processed'] += $timeLogResult['processed'];
                $results['errors'] = array_merge($results['errors'], $timeLogResult['errors']);
            }

            // Process application usage
            if (isset($syncData['application_usage'])) {
                $appResult = $this->processApplicationUsage($syncData['application_usage'], $syncData['employee_id']);
                $results['details']['application_usage'] = $appResult;
                $results['processed'] += $appResult['processed'];
                $results['errors'] = array_merge($results['errors'], $appResult['errors']);
            }

            // Process website usage
            if (isset($syncData['website_usage'])) {
                $websiteResult = $this->processWebsiteUsage($syncData['website_usage'], $syncData['employee_id']);
                $results['details']['website_usage'] = $websiteResult;
                $results['processed'] += $websiteResult['processed'];
                $results['errors'] = array_merge($results['errors'], $websiteResult['errors']);
            }

            // Process screenshots
            if (isset($syncData['screenshots'])) {
                $screenshotResult = $this->processScreenshots($syncData['screenshots'], $syncData['employee_id']);
                $results['details']['screenshots'] = $screenshotResult;
                $results['processed'] += $screenshotResult['processed'];
                $results['errors'] = array_merge($results['errors'], $screenshotResult['errors']);
            }

            $this->entityManager->commit();
            
            $this->logger->info('Bulk sync completed', [
                'employee_id' => $syncData['employee_id'],
                'processed' => $results['processed'],
                'errors_count' => count($results['errors'])
            ]);

        } catch (\Exception $e) {
            $this->entityManager->rollback();
            $results['success'] = false;
            $results['errors'][] = 'Transaction failed: ' . $e->getMessage();
            
            $this->logger->error('Bulk sync failed', [
                'employee_id' => $syncData['employee_id'] ?? 'unknown',
                'error' => $e->getMessage()
            ]);
        }

        return $results;
    }

    /**
     * Process time logs
     */
    private function processTimeLogs(array $timeLogs, int $employeeId): array
    {
        $result = ['processed' => 0, 'errors' => []];
        
        $employee = $this->employeeRepository->find($employeeId);
        if (!$employee) {
            $result['errors'][] = 'Employee not found';
            return $result;
        }

        foreach ($timeLogs as $logData) {
            try {
                // Check for duplicates based on device_id and start_time
                $existing = $this->timeLogRepository->findOneBy([
                    'employee' => $employee,
                    'deviceId' => $logData['device_id'] ?? null,
                    'startTime' => new \DateTime($logData['start_time'])
                ]);

                if ($existing) {
                    continue; // Skip duplicates
                }

                $timeLog = new TimeLog();
                $timeLog->setEmployee($employee);
                $timeLog->setLogDate(new \DateTime($logData['log_date']));
                $timeLog->setStartTime(new \DateTime($logData['start_time']));
                
                if (isset($logData['end_time']) && $logData['end_time']) {
                    $timeLog->setEndTime(new \DateTime($logData['end_time']));
                }
                
                $timeLog->setLogType($logData['log_type'] ?? 'work');
                $timeLog->setDuration($logData['duration'] ?? null);
                $timeLog->setDescription($logData['description'] ?? null);
                $timeLog->setDeviceId($logData['device_id'] ?? null);
                $timeLog->setMetadata($logData['metadata'] ?? null);
                $timeLog->setIsSynced(true);

                $this->entityManager->persist($timeLog);
                $result['processed']++;
                
            } catch (\Exception $e) {
                $result['errors'][] = "Time log sync error: " . $e->getMessage();
            }
        }

        return $result;
    }

    /**
     * Process application usage data
     */
    private function processApplicationUsage(array $usageData, int $employeeId): array
    {
        $result = ['processed' => 0, 'errors' => []];
        
        $employee = $this->employeeRepository->find($employeeId);
        if (!$employee) {
            $result['errors'][] = 'Employee not found';
            return $result;
        }

        foreach ($usageData as $appData) {
            try {
                // Check for duplicates
                $existing = $this->applicationUsageRepository->findOneBy([
                    'employee' => $employee,
                    'applicationName' => $appData['application_name'],
                    'startTime' => new \DateTime($appData['start_time']),
                    'deviceId' => $appData['device_id'] ?? null
                ]);

                if ($existing) {
                    continue; // Skip duplicates
                }

                $appUsage = new ApplicationUsage();
                $appUsage->setEmployee($employee);
                $appUsage->setUsageDate(new \DateTime($appData['usage_date']));
                $appUsage->setApplicationName($appData['application_name']);
                $appUsage->setApplicationPath($appData['application_path'] ?? null);
                $appUsage->setWindowTitle($appData['window_title'] ?? null);
                $appUsage->setStartTime(new \DateTime($appData['start_time']));
                
                if (isset($appData['end_time']) && $appData['end_time']) {
                    $appUsage->setEndTime(new \DateTime($appData['end_time']));
                }
                
                $appUsage->setDuration($appData['duration'] ?? null);
                $appUsage->setCategory($appData['category'] ?? null);
                $appUsage->setSwitchCount($appData['switch_count'] ?? 1);
                $appUsage->setDeviceId($appData['device_id'] ?? null);
                $appUsage->setMetadata($appData['metadata'] ?? null);
                $appUsage->setIsSynced(true);

                $this->entityManager->persist($appUsage);
                $result['processed']++;
                
            } catch (\Exception $e) {
                $result['errors'][] = "Application usage sync error: " . $e->getMessage();
            }
        }

        return $result;
    }

    /**
     * Process website usage data
     */
    private function processWebsiteUsage(array $usageData, int $employeeId): array
    {
        $result = ['processed' => 0, 'errors' => []];
        
        $employee = $this->employeeRepository->find($employeeId);
        if (!$employee) {
            $result['errors'][] = 'Employee not found';
            return $result;
        }

        foreach ($usageData as $websiteData) {
            try {
                // Check for duplicates
                $existing = $this->websiteUsageRepository->findOneBy([
                    'employee' => $employee,
                    'url' => $websiteData['url'],
                    'visitTime' => new \DateTime($websiteData['visit_time']),
                    'deviceId' => $websiteData['device_id'] ?? null
                ]);

                if ($existing) {
                    continue; // Skip duplicates
                }

                $websiteUsage = new WebsiteUsage();
                $websiteUsage->setEmployee($employee);
                $websiteUsage->setVisitDate(new \DateTime($websiteData['visit_date']));
                $websiteUsage->setUrl($websiteData['url']);
                $websiteUsage->setDomain($websiteData['domain']);
                $websiteUsage->setPageTitle($websiteData['page_title'] ?? null);
                $websiteUsage->setVisitTime(new \DateTime($websiteData['visit_time']));
                
                if (isset($websiteData['leave_time']) && $websiteData['leave_time']) {
                    $websiteUsage->setLeaveTime(new \DateTime($websiteData['leave_time']));
                }
                
                $websiteUsage->setDuration($websiteData['duration'] ?? null);
                $websiteUsage->setCategory($websiteData['category'] ?? null);
                $websiteUsage->setProductivityLevel($websiteData['productivity_level'] ?? null);
                $websiteUsage->setBrowserName($websiteData['browser_name'] ?? null);
                $websiteUsage->setVisitCount($websiteData['visit_count'] ?? 1);
                $websiteUsage->setDeviceId($websiteData['device_id'] ?? null);
                $websiteUsage->setMetadata($websiteData['metadata'] ?? null);
                $websiteUsage->setIsSynced(true);

                $this->entityManager->persist($websiteUsage);
                $result['processed']++;
                
            } catch (\Exception $e) {
                $result['errors'][] = "Website usage sync error: " . $e->getMessage();
            }
        }

        return $result;
    }

    /**
     * Process screenshot data
     */
    private function processScreenshots(array $screenshots, int $employeeId): array
    {
        $result = ['processed' => 0, 'errors' => []];
        
        $employee = $this->employeeRepository->find($employeeId);
        if (!$employee) {
            $result['errors'][] = 'Employee not found';
            return $result;
        }

        foreach ($screenshots as $screenshotData) {
            try {
                // Check for duplicates
                $existing = $this->screenshotLogRepository->findOneBy([
                    'employee' => $employee,
                    'captureTime' => new \DateTime($screenshotData['capture_time']),
                    'deviceId' => $screenshotData['device_id'] ?? null
                ]);

                if ($existing) {
                    continue; // Skip duplicates
                }

                $screenshot = new ScreenshotLog();
                $screenshot->setEmployee($employee);
                $screenshot->setCaptureDate(new \DateTime($screenshotData['capture_date']));
                $screenshot->setCaptureTime(new \DateTime($screenshotData['capture_time']));
                $screenshot->setFileName($screenshotData['file_name'] ?? null);
                $screenshot->setFileSize($screenshotData['file_size'] ?? null);
                $screenshot->setResolution($screenshotData['resolution'] ?? null);
                $screenshot->setDeviceId($screenshotData['device_id'] ?? null);
                $screenshot->setMetadata($screenshotData['metadata'] ?? null);
                $screenshot->setIsSynced(true);

                $this->entityManager->persist($screenshot);
                $result['processed']++;
                
            } catch (\Exception $e) {
                $result['errors'][] = "Screenshot sync error: " . $e->getMessage();
            }
        }

        return $result;
    }

    /**
     * Get sync statistics for an employee
     */
    public function getSyncStats(int $employeeId): array
    {
        $employee = $this->employeeRepository->find($employeeId);
        if (!$employee) {
            throw new \InvalidArgumentException('Employee not found');
        }

        return [
            'employee_id' => $employeeId,
            'synced_counts' => [
                'time_logs' => $this->timeLogRepository->count(['employee' => $employee, 'isSynced' => true]),
                'screenshots' => $this->screenshotLogRepository->count(['employee' => $employee, 'isSynced' => true]),
                'application_usage' => $this->applicationUsageRepository->count(['employee' => $employee, 'isSynced' => true]),
                'website_usage' => $this->websiteUsageRepository->count(['employee' => $employee, 'isSynced' => true])
            ],
            'pending_counts' => [
                'time_logs' => $this->timeLogRepository->count(['employee' => $employee, 'isSynced' => false]),
                'screenshots' => $this->screenshotLogRepository->count(['employee' => $employee, 'isSynced' => false]),
                'application_usage' => $this->applicationUsageRepository->count(['employee' => $employee, 'isSynced' => false]),
                'website_usage' => $this->websiteUsageRepository->count(['employee' => $employee, 'isSynced' => false])
            ],
            'last_updated' => new \DateTime()
        ];
    }
}
