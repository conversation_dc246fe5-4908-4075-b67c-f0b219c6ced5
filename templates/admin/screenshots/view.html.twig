{% extends 'admin/dashboard/base.html.twig' %}

{% block title %}Screenshot Details{% endblock %}

{% block body %}
<div class="content">
    <div class="animated fadeIn">
        <div class="row">
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-header">
                        <strong class="card-title">
                            <i class="fa fa-camera"></i> Screenshot Details
                        </strong>
                        <div class="card-header-actions">
                            <a href="{{ path('admin_screenshot_report') }}" class="btn btn-sm btn-secondary">
                                <i class="fa fa-arrow-left"></i> Back to Screenshots
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <!-- Screenshot Image -->
                            <div class="col-lg-8">
                                <div class="screenshot-container">
                                    {% if screenshot.fileName %}
                                        {% set imagePath = screenshot.filePath %}
                                        {% if imagePath starts with '/uploads/' %}
                                            <img src="{{ asset(imagePath) }}"
                                                 alt="Screenshot" class="img-fluid screenshot-full"
                                                 onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                                            <div class="no-image-large" style="display: none;">
                                                <i class="fa fa-camera fa-5x text-primary"></i>
                                                <p class="text-muted mt-3">Screenshot captured but file upload pending</p>
                                                <small class="text-info">{{ screenshot.fileName }}</small>
                                            </div>
                                        {% else %}
                                            <div class="no-image-large">
                                                <i class="fa fa-camera fa-5x text-primary"></i>
                                                <p class="text-muted mt-3">Screenshot captured but file upload pending</p>
                                                <small class="text-info">{{ screenshot.fileName }}</small>
                                            </div>
                                        {% endif %}
                                    {% else %}
                                        <div class="no-image-large">
                                            <i class="fa fa-image fa-5x text-muted"></i>
                                            <p class="text-muted mt-3">Screenshot file not available</p>
                                        </div>
                                    {% endif %}
                                </div>
                            </div>

                            <!-- Screenshot Details -->
                            <div class="col-lg-4">
                                <div class="screenshot-details">
                                    <h5>Screenshot Information</h5>
                                    
                                    <div class="detail-group">
                                        <label>Employee:</label>
                                        <p>{{ screenshot.employee.name }} ({{ screenshot.employee.employeeCode }})</p>
                                    </div>

                                    <div class="detail-group">
                                        <label>Captured At:</label>
                                        <p>{{ screenshot.capturedAt|date('F j, Y H:i:s') }}</p>
                                    </div>

                                    <div class="detail-group">
                                        <label>Capture Type:</label>
                                        <p>
                                            <span class="badge badge-{{ screenshot.captureType == 'periodic' ? 'primary' : 'secondary' }}">
                                                {{ screenshot.captureType|title }}
                                            </span>
                                        </p>
                                    </div>

                                    <div class="detail-group">
                                        <label>Resolution:</label>
                                        <p>{{ screenshot.resolution ?: 'Unknown' }}</p>
                                    </div>

                                    <div class="detail-group">
                                        <label>File Size:</label>
                                        <p>{{ (screenshot.fileSize / 1024)|round(1) }} KB</p>
                                    </div>

                                    <div class="detail-group">
                                        <label>File Name:</label>
                                        <p><code>{{ screenshot.fileName ?: 'N/A' }}</code></p>
                                    </div>

                                    <div class="detail-group">
                                        <label>Device ID:</label>
                                        <p><small>{{ screenshot.deviceId ?: 'N/A' }}</small></p>
                                    </div>

                                    {% if screenshot.activeApplication %}
                                        <div class="detail-group">
                                            <label>Active Application:</label>
                                            <p>{{ screenshot.activeApplication }}</p>
                                        </div>
                                    {% endif %}

                                    {% if screenshot.activeWindow %}
                                        <div class="detail-group">
                                            <label>Active Window:</label>
                                            <p><small>{{ screenshot.activeWindow }}</small></p>
                                        </div>
                                    {% endif %}

                                    <div class="detail-group">
                                        <label>Sync Status:</label>
                                        <p>
                                            {% if screenshot.isSynced %}
                                                <span class="badge badge-success">Synced</span>
                                            {% else %}
                                                <span class="badge badge-warning">Pending</span>
                                            {% endif %}
                                        </p>
                                    </div>

                                    <div class="detail-group">
                                        <label>Created At:</label>
                                        <p>{{ screenshot.createdAt|date('F j, Y H:i:s') }}</p>
                                    </div>

                                    {% if screenshot.metadata %}
                                        <div class="detail-group">
                                            <label>Metadata:</label>
                                            <div class="metadata-container">
                                                <pre class="metadata-json">{{ screenshot.metadata|json_encode(constant('JSON_PRETTY_PRINT')) }}</pre>
                                            </div>
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.screenshot-container {
    text-align: center;
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
}

.screenshot-full {
    max-width: 100%;
    max-height: 600px;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.no-image-large {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 400px;
    background-color: #f8f9fa;
    border-radius: 8px;
}

.screenshot-details {
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
}

.detail-group {
    margin-bottom: 15px;
}

.detail-group label {
    font-weight: bold;
    color: #495057;
    margin-bottom: 5px;
    display: block;
}

.detail-group p {
    margin: 0;
    color: #6c757d;
}

.metadata-container {
    max-height: 200px;
    overflow-y: auto;
}

.metadata-json {
    background-color: #fff;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 10px;
    font-size: 0.8em;
    margin: 0;
}
</style>
{% endblock %}
