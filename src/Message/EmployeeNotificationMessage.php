<?php

namespace App\Message;

class EmployeeNotificationMessage
{
    public function __construct(
        private readonly int     $employeeId,
        private readonly float   $totalHours,
        private readonly int     $employeeHoursId,
        private readonly string  $reportDate,
        private readonly bool    $isApproved,
        private readonly ?string $disapprovalReason = null,
        private readonly bool    $isResend = false,
        private readonly ?string $emailSent = null,
    )
    {
    }

    /**
     * @return int
     */
    public function getEmployeeId(): int
    {
        return $this->employeeId;
    }

    /**
     * @return float
     */
    public function getTotalHours(): float
    {
        return $this->totalHours;
    }

    /**
     * @return int
     */
    public function getEmployeeHoursId(): int
    {
        return $this->employeeHoursId;
    }

    /**
     * @return string
     */
    public function getReportDate(): string
    {
        return $this->reportDate;
    }

    public function getIsApproved(): bool
    {
        return $this->isApproved;
    }

    public function getDisapprovalReason(): ?string
    {
        return $this->disapprovalReason;
    }
    public function isResend(): bool
    {
        return $this->isResend;
    }
    public function getemailSent(): ?string
    {
        return $this->emailSent;
    }
}
