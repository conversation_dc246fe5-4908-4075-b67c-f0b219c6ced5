<?php

namespace App\Service\Admin;

use App\Entity\Announcement;
use App\Helper\ModalboxHelper;
use Doctrine\ORM\QueryBuilder;
use Omines\DataTablesBundle\Adapter\Doctrine\ORMAdapter;
use Omines\DataTablesBundle\Column\DateTimeColumn;
use Omines\DataTablesBundle\Column\TextColumn;
use Omines\DataTablesBundle\DataTable;
use Omines\DataTablesBundle\DataTableFactory;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Request;
use App\Traits\ExceptionLoggerTrait;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Security\Csrf\CsrfTokenManagerInterface;

class AnnouncementService
{
    use ExceptionLoggerTrait;

    public function __construct(
        private readonly DataTableFactory $dataTableFactory,
        private readonly RouterInterface $router,
        ParameterBagInterface $parameterBag,
        private readonly CsrfTokenManagerInterface $csrfTokenManager,
        private readonly ModalboxHelper $modalboxHelper
    ) {
        $this->initializeLogger($parameterBag, 'announcement_table_service');
    }

    public function createAnnouncementTable(Request $request): DataTable
    {
        $dataTable = $this->dataTableFactory->create();

        foreach ($this->getColumns() as $key => $column) {
            $dataTable->add($key, $column['className'], array_filter($column, fn($k) => $k !== 'className', ARRAY_FILTER_USE_KEY));
        }

        return $dataTable->createAdapter(ORMAdapter::class, [
            'entity' => Announcement::class,
            'query' => fn(QueryBuilder $qb) => $this->buildQuery($qb),
        ])
            ->addOrderBy('createdAt', 'desc')
            ->handleRequest($request);
    }

    private function getColumns(): array
    {
        return [
            'title' => [
                'label' => 'Title',
                'field' => 'a.title',
                'className' => TextColumn::class,
            ],
            'status' => [
                'label' => 'Status',
                'field' => 'a.status',
                'className' => TextColumn::class,
                'render' => fn($value) => $this->renderStatusBadge($value),
            ],
            'startDate' => [
                'label' => 'Start Date',
                'field' => 'a.startDate',
                'format' => 'd-m-Y',
                'className' => DateTimeColumn::class,
                'searchable' => false,
            ],
            'createdAt' => [
                'label' => 'Created Date',
                'field' => 'a.createdAt',
                'format' => 'd-m-Y',
                'className' => DateTimeColumn::class,
                'searchable' => false,
            ],
            'actions' => [
                'label' => 'Actions',
                'field' => 'a.id',
                'className' => TextColumn::class,
                'orderable' => false,
                'render' => fn($value, $context) => $this->renderActionColumn($context),
            ],
        ];
    }

    private function renderStatusBadge(string $status): string
    {
        return match ($status) {
            'published' => '<span class="badge bg-success">Published</span>',
            'archived' => '<span class="badge bg-secondary">Archived</span>',
            default => '<span class="badge bg-warning">Draft</span>',
        };
    }

    private function renderActionColumn($context): string
    {
        $entityId = $context->getId();
        $editUrl = $this->router->generate('admin_announcement_edit', ['id' => $entityId]);
        $deleteUrl = $this->router->generate('admin_announcement_delete', ['id' => $entityId]);
        $csrfToken = $this->csrfTokenManager->getToken('delete' . $entityId)->getValue();

        $modalId = 'deleteModal_' . $entityId;
        $modalBody = 'Are you sure you want to delete this announcement?<br><br><strong>Warning:</strong> This action cannot be undone.';
        $footerButtons = sprintf(
            '<button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>' .
            '<button type="button" class="btn btn-primary delete-announcement-btn" data-delete="true" data-url="%s" data-token="%s">Delete</button>',
            $deleteUrl,
            $csrfToken
        );

        $deleteModal = $this->modalboxHelper->renderModal(
            $modalId,
            'Confirm Announcement Deletion',
            $modalBody,
            $footerButtons
        );

        return sprintf(
            '<div class="d-inline-flex gap-2">
                <a href="%s" class="bs-btn btn btn-primary btn-sm" style="margin: 2px;"><i class="bs-fa fa fa-edit"></i></a>
                <button type="button" class="bs-btn btn btn-primary btn-sm" style="margin: 2px;" data-toggle="modal" data-target="#%s">
                    <i class="bs-fa fa fa-trash"></i>
                </button>
            </div>%s',
            $editUrl,
            $modalId,
            $deleteModal
        );
    }

    private function buildQuery(QueryBuilder $qb): void
    {
        $qb->select('a')
            ->from(Announcement::class, 'a');
    }
}