<?php

namespace App\Controller\Admin;

use App\Service\Admin\WeeklyHoursService;
use App\Traits\ExceptionLoggerTrait;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\Session\SessionInterface;
use Symfony\Component\Routing\Annotation\Route;

#[Route('/admin')]
class WeeklyHoursController extends AbstractController
{
    use ExceptionLoggerTrait;

    public function __construct(
        private readonly WeeklyHoursService $weeklyHoursService,
    ) {
    }


    /**
     * @throws \Exception
     */
    #[Route(path: '/weekly-hours', name: 'weekly_hours', methods: ['GET', 'POST'])]
    public function index(Request $request): Response
    {
        try {
            $session = $request->getSession();
            $filters = $session->get('weekly_hours_filters');
            if (!$filters) {
                $startDate = new \DateTime('monday this week');
                $endDate = new \DateTime('sunday this week');
                $session->set('weekly_hours_filters', [
                    'startDate' => $startDate->format('Y-m-d'),
                    'endDate' => $endDate->format('Y-m-d'),
                ]);
            }
            $table = $this->weeklyHoursService->createWeeklyReport($request);

            if ($table->isCallback()) {
                return $table->getResponse();
            }
            return $this->render('admin/weeklyreport.html.twig', [
                'datatable' => $table,
            ]);
        } catch (\Exception $e) {
            $this->log($e);
            throw $e;
        }
    }

    #[Route('/weekly-hours/summary', name: 'weekly_hours_summary', methods: ['POST'])]
    public function fetchWeeklyHoursReport(Request $request): JsonResponse
    {
        try {
            $data = json_decode($request->getContent(), true);
            if (!is_array($data) || !isset($data['startDate'], $data['endDate'])) {
                throw new \InvalidArgumentException('Invalid input data');
            }
            $startDate = new \DateTime($data['startDate']);
            $endDate = new \DateTime($data['endDate']);

            $interval = $startDate->diff($endDate);
            if ($interval->invert) {
                throw new \InvalidArgumentException('End date must be after start date');
            }
            $request->getSession()->set('weekly_hours_filters', [
                'startDate' => $startDate->format('Y-m-d'),
                'endDate' => $endDate->format('Y-m-d'),
            ]);

            return new JsonResponse([
                'success' => true,
                'message' => 'Filters updated successfully.',
            ]);
        } catch (\Exception $e) {
            $this->log($e);
            return new JsonResponse([
                'success' => false,
                'message' => 'Error: ' . $e->getMessage(),
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
    /**
     * @param Request $request
     * @return JsonResponse
     * @throws \DateMalformedStringException
     */
    #[Route('/weekly-hours/filters', name: 'weekly_hours_filters', methods: ['GET'])]
    public function getFilters(Request $request , SessionInterface $session) : JsonResponse
    {
        $filters = $request->getSession()->get('weekly_hours_filters', []);
        if (!$filters) {
            $startDate = new \DateTime('monday this week');
            $endDate = new \DateTime('sunday this week');
            $filters = [
                'startDate' => $startDate->format('Y-m-d'),
                'endDate' => $endDate->format('Y-m-d'),
            ];
            $session->set('weekly_hours_filters', $filters);
        } else {
            $startDate = new \DateTime($filters['startDate']);
            $endDate = new \DateTime($filters['endDate']);
        }
        $dateRange = $startDate->format('d-m-Y') . ' - ' . $endDate->format('d-m-Y');
        return new JsonResponse([
            'success' => true,
            'filters' => [
                'dateRange' => $dateRange,
                'employeeId' => $filters['employeeId'] ?? null,
            ],
        ]);
    }
}
