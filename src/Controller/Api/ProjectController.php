<?php

namespace App\Controller\Api;

use App\Entity\Project;
use App\Repository\ProjectRepository;
use App\Repository\MasterEmployeeRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

#[Route('/api/projects', name: 'api_projects_')]
class ProjectController extends AbstractController
{
    public function __construct(
        private ProjectRepository $projectRepository,
        private MasterEmployeeRepository $employeeRepository,
        private EntityManagerInterface $entityManager
    ) {}

    #[Route('', name: 'list', methods: ['GET'])]
    public function getProjects(Request $request): JsonResponse
    {
        try {
            $employeeId = $request->query->get('employee_id');
            
            if (!$employeeId) {
                return new JsonResponse([
                    'success' => false,
                    'error' => 'Employee ID is required'
                ], 400);
            }

            // Get employee to check if they exist
            $employee = $this->employeeRepository->find($employeeId);
            if (!$employee) {
                return new JsonResponse([
                    'success' => false,
                    'error' => 'Employee not found'
                ], 404);
            }

            // Get active projects where employee is assigned
            $qb = $this->entityManager->createQueryBuilder();
            $projects = $qb->select('p')
                ->from(Project::class, 'p')
                ->leftJoin('p.projectTeams', 'pt')
                ->where('p.status = :status')
                ->andWhere('pt.teamMember = :employee OR p.projectManager = :employee')
                ->setParameter('status', 'active')
                ->setParameter('employee', $employee)
                ->orderBy('p.name', 'ASC')
                ->getQuery()
                ->getResult();

            $projectData = [];
            foreach ($projects as $project) {
                $projectData[] = [
                    'id' => $project->getId(),
                    'name' => $project->getName(),
                    'description' => $project->getDescription(),
                    'project_code' => $project->getProjectCode(),
                    'project_type' => $project->getProjectType(),
                    'status' => $project->getStatus(),
                    'priority' => $project->getPriority(),
                    'start_date' => $project->getStartDate()?->format('Y-m-d'),
                    'end_date' => $project->getEndDate()?->format('Y-m-d'),
                    'deadline' => $project->getDeadline()?->format('Y-m-d'),
                    'client_name' => $project->getClientName(),
                    'project_manager' => [
                        'id' => $project->getProjectManager()?->getId(),
                        'name' => $project->getProjectManager()?->getFirstName() . ' ' . $project->getProjectManager()?->getLastName()
                    ]
                ];
            }

            return new JsonResponse([
                'success' => true,
                'data' => $projectData,
                'count' => count($projectData)
            ]);

        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => 'Failed to fetch projects',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    #[Route('/{id}', name: 'get', methods: ['GET'])]
    public function getProject(int $id, Request $request): JsonResponse
    {
        try {
            $employeeId = $request->query->get('employee_id');
            
            if (!$employeeId) {
                return new JsonResponse([
                    'success' => false,
                    'error' => 'Employee ID is required'
                ], 400);
            }

            $employee = $this->employeeRepository->find($employeeId);
            if (!$employee) {
                return new JsonResponse([
                    'success' => false,
                    'error' => 'Employee not found'
                ], 404);
            }

            $project = $this->projectRepository->find($id);
            if (!$project) {
                return new JsonResponse([
                    'success' => false,
                    'error' => 'Project not found'
                ], 404);
            }

            // Check if employee has access to this project
            $hasAccess = false;
            if ($project->getProjectManager() && $project->getProjectManager()->getId() === $employee->getId()) {
                $hasAccess = true;
            } else {
                foreach ($project->getProjectTeams() as $projectTeam) {
                    if ($projectTeam->getTeamMember()->getId() === $employee->getId()) {
                        $hasAccess = true;
                        break;
                    }
                }
            }

            if (!$hasAccess) {
                return new JsonResponse([
                    'success' => false,
                    'error' => 'Access denied to this project'
                ], 403);
            }

            $projectData = [
                'id' => $project->getId(),
                'name' => $project->getName(),
                'description' => $project->getDescription(),
                'project_code' => $project->getProjectCode(),
                'project_type' => $project->getProjectType(),
                'status' => $project->getStatus(),
                'priority' => $project->getPriority(),
                'start_date' => $project->getStartDate()?->format('Y-m-d'),
                'end_date' => $project->getEndDate()?->format('Y-m-d'),
                'deadline' => $project->getDeadline()?->format('Y-m-d'),
                'client_name' => $project->getClientName(),
                'client_contact' => $project->getClientContact(),
                'budget' => $project->getBudget(),
                'budget_currency' => $project->getBudgetCurrency(),
                'project_manager' => [
                    'id' => $project->getProjectManager()?->getId(),
                    'name' => $project->getProjectManager()?->getFirstName() . ' ' . $project->getProjectManager()?->getLastName()
                ],
                'bde' => [
                    'id' => $project->getBde()?->getId(),
                    'name' => $project->getBde()?->getFirstName() . ' ' . $project->getBde()?->getLastName()
                ],
                'department' => [
                    'id' => $project->getDepartment()?->getId(),
                    'name' => $project->getDepartment()?->getName()
                ]
            ];

            return new JsonResponse([
                'success' => true,
                'data' => $projectData
            ]);

        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => 'Failed to fetch project',
                'message' => $e->getMessage()
            ], 500);
        }
    }
}
