<?php

namespace App\Controller\Admin;

use App\Entity\LeaveBalance;
use App\Entity\LeaveType;
use App\Form\LeaveTypeFormType;
use App\Service\Admin\LeaveTypeService;
use App\Traits\ExceptionLoggerTrait;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Csrf\CsrfToken;
use Symfony\Component\Security\Csrf\CsrfTokenManagerInterface;

#[Route('/admin')]
class LeaveTypeController extends AbstractController
{
    use ExceptionLoggerTrait;

    private const PROTECTED_LEAVE_NAMES = [
        'Addon Leave',
        'Sick Leave',
        'Casual Leave'
    ];

    public function __construct(
        ParameterBagInterface                       $parameterBag,
        private readonly EntityManagerInterface     $entityManager,
        private readonly LeaveTypeService          $leaveTypeService,
    )
    {
        $this->initializeLogger($parameterBag, 'LeaveType_controller');
    }

    #[Route(path: '/leave-type/list', name: 'leave_type_list', methods: ['GET', 'POST'])]
    public function index(Request $request): Response
    {
        $table = $this->leaveTypeService->createLeaveTypeTable($request);

        if ($table->isCallback()) {
            return $table->getResponse();
        }

        return $this->render('admin/LeaveType/index.html.twig', [
            'datatable' => $table,
        ]);
    }

    #[Route('/leave-type/create', name: 'leave_type_create', methods: ['GET', 'POST'])]
    public function createLeaveType(Request $request): Response
    {
        $leaveType = new LeaveType();
        $form = $this->createForm(LeaveTypeFormType::class, $leaveType);

        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $name = $leaveType->getName();
            if (in_array($name, self::PROTECTED_LEAVE_NAMES, true)) {
                $existing = $this->entityManager->getRepository(LeaveType::class)
                    ->findOneBy(['name' => $name, 'isDelete' => false]);
                if ($existing) {
                    $this->addFlash('error', 'This leave type already exists and cannot be recreated.');
                    return $this->redirectToRoute('leave_type_list');
                }
            }
            $softDeleted = $this->entityManager->getRepository(LeaveType::class)
                ->findOneBy(['name' => $name, 'isDelete' => true]);

            if ($softDeleted) {
                $softDeleted->setIsDelete(false);
                $softDeleted->setDescription($leaveType->getDescription());
                $softDeleted->setMaxDays($leaveType->getMaxDays());
                $softDeleted->setIsPaid($leaveType->isPaid());
                $softDeleted->setAppliedFrom($leaveType->getAppliedFrom());
                $softDeleted->setNoticeDaysSingle($leaveType->getNoticeDaysSingle());
                $softDeleted->setNoticeDaysLongWeekend($leaveType->getNoticeDaysLongWeekend());

                $this->entityManager->persist($softDeleted);
                $this->entityManager->flush();

                $this->addFlash('success', 'Leave type restored and updated successfully.');
                return $this->redirectToRoute('leave_type_list');
            }
            $this->entityManager->persist($leaveType);
            $this->entityManager->flush();

            $this->addFlash('success', 'Leave type created successfully.');
            return $this->redirectToRoute('leave_type_list');
        }

        return $this->render('admin/LeaveType/new.html.twig', [
            'form' => $form->createView(),
            'mode' => 'create',
        ]);
    }

    #[Route('/leave-type/edit', name: 'leave_type_edit', methods: ['POST','GET'])]
    public function editLeaveType(Request $request, CsrfTokenManagerInterface $csrfTokenManager): Response
    {
        $id = $request->request->get('id');

        if (!$id) {
            $this->addFlash('error', 'No leave type ID provided.');
            return $this->redirectToRoute('leave_type_list');
        }

        $leaveType = $this->entityManager->getRepository(LeaveType::class)->find($id);

        if (!$leaveType) {
            throw $this->createNotFoundException('Leave type not found');
        }

        $originalName = $leaveType->getName();
        $form = $this->createForm(LeaveTypeFormType::class, $leaveType);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            if (in_array($originalName, self::PROTECTED_LEAVE_NAMES, true)) {
                $leaveType->setName($originalName);
            }

            $this->entityManager->flush();

            $this->addFlash('success', 'Leave type updated successfully.');
            return $this->redirectToRoute('leave_type_list');
        }

        return $this->render('admin/LeaveType/new.html.twig', [
            'form' => $form->createView(),
            'leaveType' => $leaveType,
            'mode' => 'edit',
        ]);
    }

    #[Route('/leave-type/delete/{id}', name: 'leave_type_delete', methods: ['POST'])]
    public function deleteLeaveType(
        Request $request,
        CsrfTokenManagerInterface $csrfTokenManager,
        EntityManagerInterface $entityManager,
        LeaveType $leaveType
    ): JsonResponse {
        $token = $request->request->get('_token');

        if (!$csrfTokenManager->isTokenValid(new CsrfToken('delete' . $leaveType->getId(), $token))) {
            return new JsonResponse([
                'success' => false,
                'message' => 'Invalid CSRF token.'
            ], Response::HTTP_FORBIDDEN);
        }

        $leaveType->setIsDelete(true);
        $entityManager->persist($leaveType);
        $entityManager->flush();

        return new JsonResponse([
            'success' => true,
            'message' => 'Leave type has been deleted.'
        ], Response::HTTP_OK);
    }
    #[Route('/leave-type/check-field', name: 'leave_type_check_field', methods: ['POST'])]
    public function checkField(Request $request): JsonResponse
    {
        $field = $request->request->get('field');
        $value = $request->request->get('value');
        $currentId = $request->request->get('currentId');
        /** @phpstan-ignore-next-line */
        $existingLeaveType = $this->entityManager->getRepository(LeaveType::class)
            ->findOneByFieldActive($field, $value);
        $isUnique = !$existingLeaveType || ($currentId && $existingLeaveType->getId() == $currentId);

        return new JsonResponse(['isUnique' => $isUnique]);
    }
}