<?php

namespace App\Form;

use App\Entity\MasterEmployee;
use App\Entity\TaskAssignment;
use Doctrine\ORM\EntityRepository;
use FOS\CKEditorBundle\Form\Type\CKEditorType;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\NotBlank;

class TaskAssignmentType extends AbstractType
{
    public function __construct(private readonly Security $security)
    {
    }

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $employee = $this->security->getUser();

        $builder
            ->add('category', ChoiceType::class, [
                'label' => 'Category',
                'choices' => [
                    'UI' => 'UI',
                    'Testing' => 'TESTING',
                    'Backend' => 'BACKEND',
                    'Other' => 'OTHER',
                ],
                'required' => true,
                'constraints' => [
                    new NotBlank(['message' => 'Please select a category.']),
                ],
                'attr' => [
                    'class' => 'form-control',
                    'data-validation' => 'required',
                ],
            ])
            ->add('priority', ChoiceType::class, [
                'label' => 'Priority',
                'choices' => [
                    'Low' => 'LOW',
                    'Medium' => 'MEDIUM',
                    'High' => 'HIGH',
                ],
                'required' => true,
                'constraints' => [
                    new NotBlank(['message' => 'Please select a priority.']),
                ],
                'attr' => [
                    'class' => 'form-control',
                    'data-validation' => 'required',
                ],
            ])
            ->add('status', ChoiceType::class, [
                'label' => 'Status',
                'choices' => [
                    'New' => 'NEW',
                    'In Progress' => 'IN_PROGRESS',
                    'Completed' => 'COMPLETED',
                    'Reassigned' => 'REASSIGNED',
                ],
                'required' => true,
                'constraints' => [
                    new NotBlank(['message' => 'Please select a status.']),
                ],
                'attr' => [
                    'class' => 'form-control',
                    'data-validation' => 'required',
                ],
            ])
            ->add('assignedTo', EntityType::class, [
                'label' => 'Assigned To',
                'class' => MasterEmployee::class,
                'choice_label' => 'name',
                'autocomplete' => true,
                'required' => true,
                'placeholder' => 'Select an employee',
                'query_builder' => function (EntityRepository $er) use ($employee) {
                    return $er->createQueryBuilder('e')
                        ->where('e != :user')
                        ->andWhere('e.isDelete = :is_delete')
                        ->setParameter('user', $employee)
                        ->setParameter('is_delete', false);
                },
                'constraints' => [
                    new NotBlank(['message' => 'Please select an employee.']),
                ],
                'attr' => [
                    'class' => 'form-control',
                    'data-validation' => 'required',
                ],
            ])
            ->add('description', CKEditorType::class, [
                'label' => 'Description',
                'row_attr' => ['class' => 'col-md-12'],
                'attr' => ['rows' => '10'],
            ]);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => TaskAssignment::class,
        ]);
    }
}