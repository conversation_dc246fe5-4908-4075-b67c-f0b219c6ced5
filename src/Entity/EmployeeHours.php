<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity]
class EmployeeHours
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    /** @phpstan-ignore-next-line */
    private ?int $id = null;

    #[ORM\ManyToOne(targetEntity: Employee::class)]
    #[ORM\JoinColumn(nullable: false)]
    private ?Employee $employee = null;

    #[ORM\Column(type: 'float')]
    private ?float $onComputerHours = null;

    #[ORM\Column(type: 'float')]
    private ?float $meetingHours = null;

    #[ORM\Column(type: 'float')]
    private ?float $offComputerHours = null;

    #[ORM\Column(type: 'float')]
    private ?float $totalHours = null;

    #[ORM\Column(type: 'float')]
    private ?float $breakHours = null;

    #[ORM\Column(type: 'float')]
    private ?float $spanHours = null;

    #[ORM\Column(type: 'date')]
    private ?\DateTimeInterface $reportDate = null;

    #[ORM\Column(type: 'float')]
    private ?float $activeMinutes = null;

    #[ORM\Column(type: 'float')]
    private ?float $activeSeconds = null;

    // Getters and setters
    public function getId(): ?int
    {
        return $this->id;
    }

    public function getEmployee(): ?Employee
    {
        return $this->employee;
    }

    public function setEmployee(?Employee $employee): void
    {
        $this->employee = $employee;
    }

    public function getOnComputerHours(): ?float
    {
        return $this->onComputerHours;
    }

    public function setOnComputerHours(?float $onComputerHours): void
    {
        $this->onComputerHours = $onComputerHours;
    }

    public function getMeetingHours(): ?float
    {
        return $this->meetingHours;
    }

    public function setMeetingHours(?float $meetingHours): void
    {
        $this->meetingHours = $meetingHours;
    }

    public function getOffComputerHours(): ?float
    {
        return $this->offComputerHours;
    }

    public function setOffComputerHours(?float $offComputerHours): void
    {
        $this->offComputerHours = $offComputerHours;
    }

    public function getTotalHours(): ?float
    {
        return $this->totalHours;
    }

    public function setTotalHours(?float $totalHours): void
    {
        $this->totalHours = $totalHours;
    }

    public function getBreakHours(): ?float
    {
        return $this->breakHours;
    }

    public function setBreakHours(?float $breakHours): void
    {
        $this->breakHours = $breakHours;
    }

    public function getSpanHours(): ?float
    {
        return $this->spanHours;
    }

    public function setSpanHours(?float $spanHours): void
    {
        $this->spanHours = $spanHours;
    }

    public function getReportDate(): ?\DateTimeInterface
    {
        return $this->reportDate;
    }

    public function setReportDate(?\DateTimeInterface $reportDate): self
    {
        $this->reportDate = $reportDate;
        return $this;
    }

    public function getActiveMinutes(): ?float
    {
        return $this->activeMinutes;
    }

    public function setActiveMinutes(?float $activeMinutes): void
    {
        $this->activeMinutes = $activeMinutes;
    }
    public function getActiveSeconds(): ?float
    {
        return $this->activeSeconds;
    }

    public function setActiveSeconds(?float $activeSeconds): void
    {
        $this->activeSeconds = $activeSeconds;
    }

}
