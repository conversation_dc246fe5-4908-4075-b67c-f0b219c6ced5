<?php

namespace App\Controller\Admin;

use App\Entity\ExtraPay;
use App\Entity\User;
use App\Form\ExtraPayType;
use App\Service\Admin\ExtraPayService;
use App\Traits\ExceptionLoggerTrait;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Csrf\CsrfToken;
use Symfony\Component\Security\Csrf\CsrfTokenManagerInterface;

#[Route('/admin')]
class ExtraPayController extends AbstractController
{
    use ExceptionLoggerTrait;

    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly CsrfTokenManagerInterface $csrfTokenManager,
        private readonly ExtraPayService $extraPayService,
    ) {
    }

    #[Route('/extrapay', name: 'admin_extrapay', methods: ['GET', 'POST'])]
    public function index(Request $request): Response
    {
        $user = $this->getUser();
        if (!$user instanceof User) {
            throw $this->createAccessDeniedException();
        }
        $table = $this->extraPayService->createExtraPayTable($request);

        if ($table->isCallback()) {
            return $table->getResponse();
        }

        return $this->render('admin/ExtraPay/index.html.twig', [
            'datatable' => $table,
        ]);
    }

    #[Route('/extrapay/new', name: 'admin_extrapay_new', methods: ['GET', 'POST'])]
    public function new(Request $request): Response
    {
        $user = $this->getUser();
        if (!$user instanceof User) {
            throw $this->createAccessDeniedException();
        }
        $extraPay = new ExtraPay();
        $form = $this->createForm(ExtraPayType::class, $extraPay);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            try {
                $data = $form->getData();
                $employee = $data->getEmployee();
                $month = $data->getMonth();
                $year = $data->getYear();
                $hoursInput = (string)$data->getHours();
                $bonus = (float)$data->getBonus();
                $officeExpense = (float)$data->getOfficeExpense();
                $calc = $this->extraPayService->calculateOtAndTotalPay(
                    $employee->getId(),
                    $year,
                    $month,
                    $hoursInput,
                    $bonus,
                    $officeExpense
                );
                if ($calc['salary'] <= 0 || $calc['totalWorkingDays'] <= 0 || $calc['totalWorkingHours'] <= 0) {
                    $this->addFlash('error', 'Salary, working days, or working hours not set for this employee/month.');
                    return $this->redirectToRoute('admin_extrapay_new');
                }
                $extraPay->setTotalPay($calc['totalPay']);
                $extraPay->setHours($calc['hoursDecimal']);
                $this->entityManager->persist($extraPay);
                $this->entityManager->flush();
                $this->addFlash('success', 'Extra pay record created successfully.');
                return $this->redirectToRoute('admin_extrapay');
            } catch (\Exception $e) {
                $this->addFlash('error', 'Error creating extra pay record: ' . $e->getMessage());
                return $this->redirectToRoute('admin_extrapay_new');
            }
        }

        return $this->render('admin/ExtraPay/new.html.twig', [
            'form' => $form->createView(),
            'mode' => 'create',
        ]);
    }

    #[Route('/extrapay/{id}/edit', name: 'admin_extrapay_edit', methods: ['GET', 'POST'])]
    public function edit(Request $request, int $id): Response
    {
        $extraPay = $this->entityManager->getRepository(ExtraPay::class)->find($id);
        if (!$extraPay) {
            throw $this->createNotFoundException('Extra pay record not found');
        }
        $form = $this->createForm(ExtraPayType::class, $extraPay);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            try {
                $data = $form->getData();
                $employee = $data->getEmployee();
                $month = $data->getMonth();
                $year = $data->getYear();
                $hoursInput = (string)$data->getHours();
                $bonus = (float)$data->getBonus();
                $officeExpense = (float)$data->getOfficeExpense();
                $calc = $this->extraPayService->calculateOtAndTotalPay(
                    $employee->getId(),
                    $year,
                    $month,
                    $hoursInput,
                    $bonus,
                    $officeExpense
                );
                if ($calc['salary'] <= 0 || $calc['totalWorkingDays'] <= 0 || $calc['totalWorkingHours'] <= 0) {
                    $this->addFlash('error', 'Salary, working days, or working hours not set for this employee/month.');
                    return $this->redirectToRoute('admin_extrapay_edit', ['id' => $id]);
                }
                $extraPay->setTotalPay($calc['totalPay']);
                $extraPay->setHours($calc['hoursDecimal']);
                $this->entityManager->flush();
                $this->addFlash('success', 'Extra pay record updated successfully.');
                return $this->redirectToRoute('admin_extrapay');
            } catch (\Exception $e) {
                $this->addFlash('error', 'Error updating extra pay record: ' . $e->getMessage());
                return $this->redirectToRoute('admin_extrapay_edit', ['id' => $id]);
            }
        }

        return $this->render('admin/ExtraPay/new.html.twig', [
            'form' => $form->createView(),
            'employee' => $extraPay->getEmployee(),
            'mode' => 'edit',
        ]);
    }

    #[Route('/extrapay/{id}/delete', name: 'admin_extrapay_delete', methods: ['POST'])]
    public function delete(Request $request, int $id): Response
    {
        $extraPay = $this->entityManager->getRepository(ExtraPay::class)->find($id);
        if (!$extraPay) {
            return $this->json([
                'success' => false,
                'message' => 'Extra pay record not found.'
            ], Response::HTTP_NOT_FOUND);
        }

        $token = $request->request->get('_token');
        if (!$this->csrfTokenManager->isTokenValid(new CsrfToken('delete_extrapay_' . $id, $token))) {
            return $this->json([
                'success' => false,
                'message' => 'Invalid CSRF token.'
            ], Response::HTTP_FORBIDDEN);
        }

        $this->entityManager->remove($extraPay);
        $this->entityManager->flush();
        return $this->json([
            'success' => true,
            'message' => 'Extra pay record has been deleted.'
        ], Response::HTTP_OK);
    }

    /**
     * @throws \DateMalformedStringException
     */
    #[Route('/extrapay/salary-info', name: 'admin_extrapay_salary_info', methods: ['GET'])]
    public function salaryInfo(Request $request): Response
    {
        $employeeId = $request->query->get('employee');
        $month = $request->query->get('month');
        $year = $request->query->get('year');
        if (!$employeeId || !$month || !$year) {
            return $this->json([
                'salary' => 0,
                'totalWorkingDays' => 0,
                'totalWorkingHours' => 0,
            ]);
        }
        $calc = $this->extraPayService->calculateOtAndTotalPay(
            (int)$employeeId,
            (int)$year,
            (int)$month,
            '0',
            0,
            0
        );
        return $this->json([
            'salary' => $calc['salary'],
            'totalWorkingDays' => $calc['totalWorkingDays'],
            'totalWorkingHours' => $calc['totalWorkingHours'],
        ]);
    }
}