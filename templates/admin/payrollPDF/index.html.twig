{% extends 'admin/dashboard/base.html.twig' %}

{% block body %}
    {% include 'admin/partials/breadcrumbs.html.twig' with {
        breadcrumbs: [
            { 'label': 'Dashboard', 'url': url('admin_dashboard') },
        ],
        active: 'Payroll PDF'
    } %}
    <div class="content">
        <div class="animated fadeIn">
            <div class="row">
                <div id="flash-container"></div>
                <div id="order-loader">
                    <img src="{{ asset('images/loader.svg') }}" alt="loader">
                </div>
                <div class="col-md-12">
                    <div id="flash-container"></div>
                    <div class="card">
                        <div class="card-header d-flex justify-content-between">
                            <strong class="card-title">Payroll PDF</strong>
                        </div>
                        <div class="card-body">
                            <div class="table-stats order-table ov-h text-center">
                                <div id="payroll-pdf" class="datatable-main-wrapper">Loading...</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="clearfix"></div>
    <script src="{{ asset('assets/js/datatables.min.js') }}"></script>
    <script src="{{ asset('bundles/datatables/js/datatables.js') }}"></script>
    <script>
        $(function () {
            recordsTable = $('#payroll-pdf').initDataTables({{ datatable_settings(datatable) }});
        });
    </script>
    <script src="{{ asset('assets/js/datatableclearsearch.js') }}"></script>
    <script src="{{ asset('assets/js/admin/common.js') }}"></script>
{% endblock %}