{% extends 'admin/dashboard/base.html.twig' %}

{% block body %}
    {% include 'admin/partials/breadcrumbs.html.twig' with {
        breadcrumbs: [
            { 'label': 'Dashboard', 'url': url('admin_dashboard') },
        ],
        active: 'Weekly Report'
    } %}

    <div class="content">
        <div class="card">
            {% include 'admin/partials/filters.html.twig'%}
        </div>
        <div class="animated fadeIn">
            <div class="row">
                <div id="order-loader">
                    <img src="{{ asset('images/loader.svg') }}" alt="loader">
                </div>
                <div class="col-md-12">
                    <div id="flash-container"></div>
                    <div class="card">
                        <div class="card-header d-flex justify-content-between ">
                            <strong class="card-title">Weekly Report</strong>
                        </div>
                        <div class="card-body">
                            <div class="table-stats order-table ov-h text-center ">
                                <div id="weekly"></div>
                            </div>
                        </div>
                    </div>
                </div>


            </div>
        </div>
    </div>
    <div class="clearfix"></div>
    <script src="{{ asset('assets/js/datatables.min.js') }}"></script>
    <script src="{{ asset('bundles/datatables/js/datatables.js') }}"></script>
    <script src="{{ asset('assets/js/admin/weeklyhours.js') }}"></script>
    <script src="{{ asset('assets/js/moment.min.js') }}"></script>
    <script src="{{ asset('assets/js/daterangepicker.min.js') }}"></script>
    <script>
        $(function () {
            recordsTable = $('#weekly').initDataTables({{ datatable_settings(datatable) }},{ scrollX: true, scroller: true} );
        });
    </script>
    <script src="{{ asset('assets/js/datatableclearsearch.js') }}"></script>
{% endblock %}
