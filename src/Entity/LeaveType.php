<?php

namespace App\Entity;

use App\Repository\LeaveTypeRepository;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity(repositoryClass: LeaveTypeRepository::class)]
class LeaveType
{
    public const ACTIVE = true;
    public const INACTIVE = false;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: "integer")]
    /** @phpstan-ignore-next-line */
    private ?int $id = null;

    #[ORM\Column(type: "string", length: 50, unique: true)]
    #[Assert\NotBlank]
    #[Assert\Length(max: 50)]
    private ?string $name = null;

    #[ORM\Column(type: "text", length: 255, nullable: true)]
    private ?string $description = null;

    #[ORM\Column(type: "integer")]
    #[Assert\NotNull]
    #[Assert\Positive]
    private int $maxDays;

    #[ORM\Column(type: "boolean", options: ["default" => true])]
    private bool $isPaid = true;

    #[ORM\Column(type: "string", length: 2, nullable: true, options: ["default" => "CO"])]
    #[Assert\Choice(choices: ["CO", "JD"], message: "Invalid value for appliedFrom. Must be 'CO' or 'JD'.")]
    private ?string $appliedFrom = "CO";

    #[ORM\Column(type: "integer", nullable: true)]
    #[Assert\Positive]
    private ?int $noticeDaysSingle = null;

    #[ORM\Column(type: "integer", nullable: true)]
    #[Assert\Positive]
    private ?int $noticeDaysLongWeekend = null;

    #[ORM\Column(type: "boolean", options: ["default" => false])]
    private bool $isDelete = self::INACTIVE;

    public function getId(): int
    {
        return $this->id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;
        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): self
    {
        $this->description = $description;
        return $this;
    }

    public function getMaxDays(): int
    {
        return $this->maxDays;
    }

    public function setMaxDays(int $maxDays): self
    {
        $this->maxDays = $maxDays;
        return $this;
    }

    public function isPaid(): bool
    {
        return $this->isPaid;
    }

    public function setIsPaid(bool $isPaid): self
    {
        $this->isPaid = $isPaid;
        return $this;
    }

    public function getAppliedFrom(): ?string
    {
        return $this->appliedFrom;
    }

    public function setAppliedFrom(?string $appliedFrom): self
    {
        $this->appliedFrom = $appliedFrom;
        return $this;
    }

    public function getNoticeDaysSingle(): ?int
    {
        return $this->noticeDaysSingle;
    }

    public function setNoticeDaysSingle(?int $noticeDaysSingle): void
    {
        $this->noticeDaysSingle = $noticeDaysSingle;
    }

    public function getNoticeDaysLongWeekend(): ?int
    {
        return $this->noticeDaysLongWeekend;
    }

    public function setNoticeDaysLongWeekend(?int $noticeDaysLongWeekend): void
    {
        $this->noticeDaysLongWeekend = $noticeDaysLongWeekend;
    }

    public function isDelete(): bool
    {
        return $this->isDelete;
    }

    public function setIsDelete(bool $isDelete): void
    {
        $this->isDelete = $isDelete;
    }

}