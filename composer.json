{"type": "project", "license": "proprietary", "minimum-stability": "stable", "prefer-stable": true, "require": {"php": ">=8.2", "ext-ctype": "*", "ext-fileinfo": "*", "ext-iconv": "*", "doctrine/dbal": "^3", "doctrine/doctrine-bundle": "^2.13", "doctrine/doctrine-migrations-bundle": "^3.3", "doctrine/orm": "^3.3", "friendsofsymfony/ckeditor-bundle": "^2.5", "knplabs/knp-snappy-bundle": "^1.10", "omines/datatables-bundle": "^0.9.1", "phpdocumentor/reflection-docblock": "^5.6", "phpoffice/phpspreadsheet": "^4.4", "phpstan/phpdoc-parser": "^2.0", "ramsey/uuid": "^4.7", "ramsey/uuid-doctrine": "^2.1", "setasign/fpdi-tcpdf": "^2.3", "symfony/asset": "7.2.*", "symfony/asset-mapper": "7.2.*", "symfony/console": "7.2.*", "symfony/doctrine-messenger": "7.2.*", "symfony/dotenv": "7.2.*", "symfony/expression-language": "7.2.*", "symfony/flex": "^2", "symfony/form": "7.2.*", "symfony/framework-bundle": "7.2.*", "symfony/google-mailer": "7.2.*", "symfony/http-client": "7.2.*", "symfony/intl": "7.2.*", "symfony/mailer": "7.2.*", "symfony/messenger": "7.2.*", "symfony/mime": "7.2.*", "symfony/monolog-bundle": "^3.10", "symfony/notifier": "7.2.*", "symfony/process": "7.2.*", "symfony/property-access": "7.2.*", "symfony/property-info": "7.2.*", "symfony/runtime": "7.2.*", "symfony/security-bundle": "7.2.*", "symfony/serializer": "7.2.*", "symfony/stimulus-bundle": "^2.21", "symfony/string": "7.2.*", "symfony/translation": "7.2.*", "symfony/twig-bundle": "7.2.*", "symfony/ux-autocomplete": "^2.27", "symfony/ux-turbo": "^2.21", "symfony/validator": "7.2.*", "symfony/web-link": "7.2.*", "symfony/yaml": "7.2.*", "symfonycasts/reset-password-bundle": "^1.23", "tecnickcom/tcpdf": "^6.10", "twig/extra-bundle": "^2.12|^3.0", "twig/twig": "^2.12|^3.0"}, "config": {"allow-plugins": {"php-http/discovery": true, "symfony/flex": true, "symfony/runtime": true}, "bump-after-update": true, "sort-packages": true}, "autoload": {"psr-4": {"App\\": "src/"}}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests/"}}, "replace": {"symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php72": "*", "symfony/polyfill-php73": "*", "symfony/polyfill-php74": "*", "symfony/polyfill-php80": "*", "symfony/polyfill-php81": "*", "symfony/polyfill-php82": "*"}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd", "importmap:install": "symfony-cmd", "ckeditor:install --tag=4.20.1": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"], "ps": "vendor/bin/phpstan analyse --configuration phpstan.neon"}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": false, "require": "7.2.*"}}, "require-dev": {"phpstan/phpstan": "^2.1", "phpunit/phpunit": "^9.5", "symfony/browser-kit": "7.2.*", "symfony/css-selector": "7.2.*", "symfony/debug-bundle": "7.2.*", "symfony/maker-bundle": "^1.0", "symfony/phpunit-bridge": "^7.2", "symfony/stopwatch": "7.2.*", "symfony/web-profiler-bundle": "7.2.*"}}