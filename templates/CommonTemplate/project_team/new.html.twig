<div class="content">
    <div class="animated fadeIn">
        <div class="row">
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <strong class="card-title">Add Team Member</strong>
                        {% if is_granted('ROLE_ADMIN') %}
                        <a href="{{ path('admin_project_team_manage', {'project_id': project_id}) }}" class="btn btn-sm btn-outline-secondary">
                            <i class="fa fa-list mr-1"></i>Back to Team List
                        </a>
                        {% elseif is_granted('ROLE_EMPLOYEE') %}
                            <a href="{{ path('employee_project_team_manage', {'project_id': project_id}) }}" class="btn btn-sm btn-outline-secondary">
                                <i class="fa fa-list mr-1"></i>Back to Team List
                            </a>
                        {% endif %}
                    </div>
                    <div class="card-body">
                        <div id="project-team-form">
                            {{ form_start(form, { 'attr': { 'class': 'needs-validation use-loader', 'novalidate': 'novalidate', 'id': 'project-team-form-element','data-turbo': 'false' } }) }}
                            <div class="form-section">
                                <h5 class="section-header">
                                    <i class="fa fa-users mr-2"></i>Team Member Details
                                </h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            {{ form_label(form.teamMember, 'Team Member', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text"><i class="fa fa-user"></i></span>
                                                </div>
                                                {{ form_widget(form.teamMember, { 'attr': { 'class': 'form-control', 'required': 'required', 'id': 'project_teamMember' } }) }}
                                            </div>
                                            {{ form_errors(form.teamMember) }}
                                            <small class="form-text text-muted">Select the team member</small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            {{ form_label(form.role, 'Role', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                            <div class="input-group">
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text"><i class="fa fa-tag"></i></span>
                                                </div>
                                                {{ form_widget(form.role, { 'attr': { 'class': 'form-control', 'id': 'project_role' } }) }}
                                            </div>
                                            {{ form_errors(form.role) }}
                                            <small class="form-text text-muted">Role of the team member in the project</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="d-flex justify-content-end mt-4">
                                {% if is_granted('ROLE_ADMIN') %}
                                <a href="{{ path('admin_project_team_manage', {'project_id': project_id}) }}" class="btn btn-secondary mr-2">Cancel</a>
                                <button type="submit" class="btn btn-primary pl-3 pr-3">Add Team Member</button>
                                {% elseif is_granted('ROLE_EMPLOYEE') %}
                                    <a href="{{ path('employee_project_team_manage', {'project_id': project_id}) }}" class="btn btn-secondary mr-2">Cancel</a>
                                    <button type="submit" class="btn btn-primary pl-3 pr-3">Add Team Member</button>
                                {% endif %}
                            </div>
                            {{ form_end(form) }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="clearfix"></div>
<link rel="stylesheet" href="{{ asset('assets/css/Error/error.css') }}">
<link rel="stylesheet" href="{{ asset('assets/css/AdminForm/Form.css') }}">
<script src="{{ asset('assets/js/datatables.min.js') }}"></script>
<script src="{{ asset('bundles/datatables/js/datatables.js') }}"></script>
<script src="{{ asset('assets/js/teammember.js') }}"></script>