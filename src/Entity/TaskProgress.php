<?php
namespace App\Entity;

use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity]
#[ORM\Table(name: 'task_progress')]
class TaskProgress
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: Types::INTEGER)]
    private ?int $id = null;

    #[ORM\ManyToOne(targetEntity: Task::class, inversedBy: 'taskTimeLogs')]
    #[ORM\JoinColumn(nullable: false, onDelete: 'CASCADE')]
    private ?Task $task = null;

    #[ORM\ManyToOne(targetEntity: MasterEmployee::class)]
    #[ORM\JoinColumn(nullable: false)]
    private ?MasterEmployee $employee = null;

    #[ORM\Column(type: 'datetime')]
    #[Assert\NotBlank]
    private ?\DateTimeInterface $progressDate;

    #[ORM\Column(type: Types::FLOAT)]
    private ?float $progressPercent = null;

    #[ORM\Column(type: Types::DECIMAL, precision: 10, scale: 2 , nullable: true)]
    private ?float $hoursWorked = null;

    #[ORM\Column(type: Types::TEXT, nullable: false)]
    private ?string $comments = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $createdAt = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): void
    {
        $this->id = $id;
    }

    public function getTask(): ?Task
    {
        return $this->task;
    }

    public function setTask(?Task $task): void
    {
        $this->task = $task;
    }

    public function getEmployee(): ?MasterEmployee
    {
        return $this->employee;
    }

    public function setEmployee(?MasterEmployee $employee): void
    {
        $this->employee = $employee;
    }

    public function getProgressDate(): ?\DateTimeInterface
    {
        return $this->progressDate;
    }

    public function setProgressDate(?\DateTimeInterface $progressDate): void
    {
        $this->progressDate = $progressDate;
    }
    public function getProgressPercent(): ?float
    {
        return $this->progressPercent;
    }

    public function setProgressPercent(?float $progressPercent): void
    {
        $this->progressPercent = $progressPercent;
    }


    public function getComments(): ?string
    {
        return $this->comments;
    }

    public function setComments(?string $comments): void
    {
        $this->comments = $comments;
    }

    public function getCreatedAt(): ?\DateTimeInterface
    {
        return $this->createdAt;
    }

    public function setCreatedAt(?\DateTimeInterface $createdAt): void
    {
        $this->createdAt = $createdAt;
    }

    public function getHoursWorked(): ?float
    {
        return $this->hoursWorked;
    }

    public function setHoursWorked(?float $hoursWorked): void
    {
        $this->hoursWorked = $hoursWorked;
    }


}