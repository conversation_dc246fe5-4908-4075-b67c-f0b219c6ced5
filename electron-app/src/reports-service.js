const { machineId } = require('node-machine-id');

class ReportsService {
    constructor(dbService, store) {
        this.dbService = dbService;
        this.store = store;
        
        // Configuration
        this.config = {
            autoGenerateDaily: true,
            autoGenerateWeekly: true,
            autoGenerateMonthly: true,
            reportGenerationTime: '18:00', // 6 PM
            includeScreenshots: true,

            productivityThresholds: {
                excellent: 90,
                good: 75,
                average: 60,
                poor: 40
            }
        };
        
        // Report generation intervals
        this.dailyReportInterval = null;
        this.weeklyReportInterval = null;
        this.monthlyReportInterval = null;
        
        this.init();
    }

    async init() {
        console.log('📊 Initializing Reports Service...');
        
        try {
            // Load configuration
            await this.loadConfiguration();
            
            // Setup automatic report generation
            this.setupAutomaticReports();
            
            console.log('✅ Reports Service initialized');
        } catch (error) {
            console.error('❌ Failed to initialize Reports Service:', error);
        }
    }

    async loadConfiguration() {
        try {
            const storedConfig = await this.store.get('reportsConfig');
            if (storedConfig) {
                this.config = { ...this.config, ...storedConfig };
            }
            
            console.log('⚙️ Reports configuration loaded:', this.config);
        } catch (error) {
            console.error('Failed to load reports configuration:', error);
        }
    }

    async updateConfiguration(newConfig) {
        try {
            this.config = { ...this.config, ...newConfig };
            await this.store.set('reportsConfig', this.config);
            
            console.log('⚙️ Reports configuration updated:', this.config);
            
            // Restart automatic reports
            this.setupAutomaticReports();
            
            return true;
        } catch (error) {
            console.error('Failed to update reports configuration:', error);
            return false;
        }
    }

    setupAutomaticReports() {
        // Clear existing intervals
        if (this.dailyReportInterval) clearInterval(this.dailyReportInterval);
        if (this.weeklyReportInterval) clearInterval(this.weeklyReportInterval);
        if (this.monthlyReportInterval) clearInterval(this.monthlyReportInterval);

        if (!this.config.autoGenerateDaily) return;

        // Setup daily report generation
        const now = new Date();
        const [hour, minute] = this.config.reportGenerationTime.split(':').map(Number);
        
        const nextReportTime = new Date();
        nextReportTime.setHours(hour, minute, 0, 0);
        
        if (nextReportTime <= now) {
            nextReportTime.setDate(nextReportTime.getDate() + 1);
        }

        const msUntilNextReport = nextReportTime.getTime() - now.getTime();

        setTimeout(() => {
            this.generateDailyReport();
            
            // Set up daily interval
            this.dailyReportInterval = setInterval(() => {
                this.generateDailyReport();
            }, 24 * 60 * 60 * 1000); // Every 24 hours
        }, msUntilNextReport);

        console.log(`📊 Next daily report scheduled for: ${nextReportTime.toLocaleString()}`);
    }

    async generateDailyReport(date = null) {
        try {
            const reportDate = date || new Date().toISOString().split('T')[0];
            const currentUser = await this.store.get('currentUser');
            
            if (!currentUser) {
                console.log('⚠️ No current user for report generation');
                return null;
            }

            console.log(`📊 Generating daily report for ${reportDate}...`);

            // Get time logs for the day
            const timeLogs = await this.dbService.all(`
                SELECT * FROM time_logs 
                WHERE employee_id = ? AND log_date = ?
                ORDER BY start_time ASC
            `, [currentUser.id, reportDate]);

            // Get application usage for the day
            const appUsage = await this.dbService.all(`
                SELECT * FROM application_usage 
                WHERE employee_id = ? AND usage_date = ?
                ORDER BY start_time ASC
            `, [currentUser.id, reportDate]);

            // Get website usage for the day
            const websiteUsage = await this.dbService.all(`
                SELECT * FROM website_usage 
                WHERE employee_id = ? AND visit_date = ?
                ORDER BY visit_time ASC
            `, [currentUser.id, reportDate]);

            // Get screenshots for the day
            const screenshots = await this.dbService.all(`
                SELECT COUNT(*) as count FROM screenshot_logs 
                WHERE employee_id = ? AND DATE(captured_at) = ?
            `, [currentUser.id, reportDate]);

            // Get webcam captures for the day
            const webcamCaptures = await this.dbService.all(`
                SELECT COUNT(*) as count FROM webcam_captures 
                WHERE employee_id = ? AND DATE(captured_at) = ?
            `, [currentUser.id, reportDate]);

            // Calculate metrics
            const metrics = this.calculateDailyMetrics(timeLogs, appUsage, websiteUsage);
            
            // Create report data
            const reportData = {
                employee_id: currentUser.id,
                report_date: reportDate,
                report_type: 'daily',
                total_work_time: metrics.totalWorkTime,
                productive_time: metrics.productiveTime,
                neutral_time: metrics.neutralTime,
                distracting_time: metrics.distractingTime,
                idle_time: metrics.idleTime,
                applications_used: metrics.applicationsUsed,
                websites_visited: metrics.websitesVisited,
                screenshots_taken: screenshots[0]?.count || 0,
                webcam_captures: webcamCaptures[0]?.count || 0,
                productivity_score: metrics.productivityScore,
                metadata: JSON.stringify({
                    timeLogs: timeLogs.length,
                    appUsage: appUsage.length,
                    websiteUsage: websiteUsage.length,
                    topApplications: metrics.topApplications,
                    topWebsites: metrics.topWebsites,
                    workingHours: metrics.workingHours,
                    breakTime: metrics.breakTime,
                    generatedAt: new Date().toISOString()
                })
            };

            // Save report to database
            const result = await this.dbService.run(`
                INSERT INTO productivity_reports (
                    employee_id, report_date, report_type, total_work_time,
                    productive_time, neutral_time, distracting_time, idle_time,
                    applications_used, websites_visited, screenshots_taken,
                    webcam_captures, productivity_score, metadata
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `, [
                reportData.employee_id, reportData.report_date, reportData.report_type,
                reportData.total_work_time, reportData.productive_time, reportData.neutral_time,
                reportData.distracting_time, reportData.idle_time, reportData.applications_used,
                reportData.websites_visited, reportData.screenshots_taken, reportData.webcam_captures,
                reportData.productivity_score, reportData.metadata
            ]);

            console.log(`✅ Daily report generated successfully: ${result.lastID}`);
            return { id: result.lastID, ...reportData };

        } catch (error) {
            console.error('Failed to generate daily report:', error);
            return null;
        }
    }

    calculateDailyMetrics(timeLogs, appUsage, websiteUsage) {
        const metrics = {
            totalWorkTime: 0,
            productiveTime: 0,
            neutralTime: 0,
            distractingTime: 0,
            idleTime: 0,
            breakTime: 0,
            applicationsUsed: 0,
            websitesVisited: 0,
            topApplications: [],
            topWebsites: [],
            workingHours: 0,
            productivityScore: 0
        };

        // Calculate time metrics from time logs
        timeLogs.forEach(log => {
            const duration = log.duration || 0;
            metrics.totalWorkTime += duration;
            
            switch (log.log_type) {
                case 'work':
                    metrics.workingHours += duration;
                    break;
                case 'break':
                    metrics.breakTime += duration;
                    break;
                case 'idle':
                    metrics.idleTime += duration;
                    break;
            }
        });

        // Calculate application metrics
        const appStats = {};
        appUsage.forEach(app => {
            const duration = app.duration || 0;
            const category = app.category || 'neutral';
            
            if (!appStats[app.application_name]) {
                appStats[app.application_name] = { duration: 0, category };
            }
            appStats[app.application_name].duration += duration;
            
            switch (category) {
                case 'productive':
                    metrics.productiveTime += duration;
                    break;
                case 'distracting':
                    metrics.distractingTime += duration;
                    break;
                default:
                    metrics.neutralTime += duration;
            }
        });

        // Calculate website metrics
        const websiteStats = {};
        websiteUsage.forEach(website => {
            const duration = website.duration || 0;
            const productivityLevel = website.productivity_level || 'neutral';
            
            if (!websiteStats[website.domain]) {
                websiteStats[website.domain] = { duration: 0, productivityLevel };
            }
            websiteStats[website.domain].duration += duration;
            
            switch (productivityLevel) {
                case 'productive':
                    metrics.productiveTime += duration;
                    break;
                case 'distracting':
                    metrics.distractingTime += duration;
                    break;
                default:
                    metrics.neutralTime += duration;
            }
        });

        // Get top applications and websites
        metrics.topApplications = Object.entries(appStats)
            .sort(([,a], [,b]) => b.duration - a.duration)
            .slice(0, 10)
            .map(([name, data]) => ({ name, duration: data.duration, category: data.category }));

        metrics.topWebsites = Object.entries(websiteStats)
            .sort(([,a], [,b]) => b.duration - a.duration)
            .slice(0, 10)
            .map(([domain, data]) => ({ domain, duration: data.duration, productivityLevel: data.productivityLevel }));

        metrics.applicationsUsed = Object.keys(appStats).length;
        metrics.websitesVisited = Object.keys(websiteStats).length;

        // Calculate productivity score
        const totalActiveTime = metrics.productiveTime + metrics.neutralTime + metrics.distractingTime;
        if (totalActiveTime > 0) {
            metrics.productivityScore = Math.round(
                ((metrics.productiveTime * 1.0 + metrics.neutralTime * 0.5) / totalActiveTime) * 100
            );
        }

        return metrics;
    }

    async getRecentReports(limit = 10) {
        try {
            const currentUser = await this.store.get('currentUser');
            if (!currentUser) return [];

            return await this.dbService.all(`
                SELECT * FROM productivity_reports 
                WHERE employee_id = ? 
                ORDER BY report_date DESC 
                LIMIT ?
            `, [currentUser.id, limit]);
        } catch (error) {
            console.error('Failed to get recent reports:', error);
            return [];
        }
    }

    async getReportByDate(date, reportType = 'daily') {
        try {
            const currentUser = await this.store.get('currentUser');
            if (!currentUser) return null;

            return await this.dbService.get(`
                SELECT * FROM productivity_reports 
                WHERE employee_id = ? AND report_date = ? AND report_type = ?
            `, [currentUser.id, date, reportType]);
        } catch (error) {
            console.error('Failed to get report by date:', error);
            return null;
        }
    }

    formatDuration(seconds) {
        if (!seconds) return '0m';
        
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        
        if (hours > 0) {
            return `${hours}h ${minutes}m`;
        } else {
            return `${minutes}m`;
        }
    }

    getProductivityLevel(score) {
        if (score >= this.config.productivityThresholds.excellent) return 'excellent';
        if (score >= this.config.productivityThresholds.good) return 'good';
        if (score >= this.config.productivityThresholds.average) return 'average';
        if (score >= this.config.productivityThresholds.poor) return 'poor';
        return 'very_poor';
    }

    async destroy() {
        console.log('🔄 Destroying reports service...');
        
        if (this.dailyReportInterval) clearInterval(this.dailyReportInterval);
        if (this.weeklyReportInterval) clearInterval(this.weeklyReportInterval);
        if (this.monthlyReportInterval) clearInterval(this.monthlyReportInterval);
        
        console.log('✅ Reports service destroyed');
    }
}

module.exports = ReportsService;
