<?php

namespace App\Service\Common;

use App\Repository\LeaveRequestRepository;
use App\Repository\TaskRepository;
use Symfony\Component\HttpFoundation\RequestStack;

class FilterService
{
    private RequestStack $requestStack;
    private const SESSION_KEY_PREFIX = 'employee_task_filters_';

    public function __construct(RequestStack $requestStack)
    {
        $this->requestStack = $requestStack;
    }

    public function saveFilters(int $projectId, string $startDate, string $endDate): void
    {
        $session = $this->requestStack->getSession();
        $session->set(self::SESSION_KEY_PREFIX . $projectId, [
            'startDate' => (new \DateTime($startDate))->format('Y-m-d'),
            'endDate' => (new \DateTime($endDate))->format('Y-m-d'),
        ]);
    }

    public function getFilters(int $projectId, TaskRepository $taskRepository): array
    {
        $session = $this->requestStack->getSession();
        $filters = $session->get(self::SESSION_KEY_PREFIX . $projectId, []);

        $startDate = new \DateTime($filters['startDate'] ?? 'now');
        $endDate = new \DateTime($filters['endDate'] ?? 'now');

        $minDate = $taskRepository->getMinAssignedAtDate($projectId)?->format('d-m-Y') ?? $startDate->format('d-m-Y');
        $maxDate = $taskRepository->getMaxAssignedAtDate($projectId)?->format('d-m-Y') ?? $endDate->format('d-m-Y');
        return [
            'startDate' => $startDate,
            'endDate' => $endDate,
            'dateRange' => $minDate. ' - ' . $maxDate,
            'minDate' => $minDate,
            'maxDate' => $maxDate,
        ];
    }

    public function clearFilters(int $projectId): void
    {
        $session = $this->requestStack->getSession();
        $session->remove(self::SESSION_KEY_PREFIX . $projectId);
    }
    public function clearFilter(): void
    {
        $session = $this->requestStack->getSession();
        $session->remove(self::SESSION_KEY_PREFIX);
    }

    public function saveFilter( string $startDate, string $endDate): void
    {
        $session = $this->requestStack->getSession();
        $session->set(self::SESSION_KEY_PREFIX , [
            'startDate' => (new \DateTime($startDate))->format('Y-m-d'),
            'endDate' => (new \DateTime($endDate))->format('Y-m-d'),
        ]);
    }

    /**
     * @throws \DateMalformedStringException
     */
    public function getFiltersLeaveRequest(LeaveRequestRepository $leaveRequestRepository): array
    {
        $session = $this->requestStack->getSession();
        $filters = $session->get(self::SESSION_KEY_PREFIX , []);

        $startDate = new \DateTime($filters['startDate'] ?? 'now');
        $endDate = new \DateTime($filters['endDate'] ?? 'now');

        $minDate = $leaveRequestRepository->getMinLeaveRequestDate()?->format('d-m-Y') ?? $startDate->format('d-m-Y');
        $maxDate = $leaveRequestRepository->getMaxLeaveRequestDate()?->format('d-m-Y') ?? $endDate->format('d-m-Y');
        return [
            'startDate' => $startDate,
            'endDate' => $endDate,
            'dateRange' => $minDate. ' - ' . $maxDate,
            'minDate' => $minDate,
            'maxDate' => $maxDate,
        ];
    }
}