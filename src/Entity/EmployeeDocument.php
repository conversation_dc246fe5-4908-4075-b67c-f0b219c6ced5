<?php

namespace App\Entity;

use App\Repository\EmployeeDocumentRepository;
use App\Validator\UniqueDocumentTypeExceptOther;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;

#[UniqueDocumentTypeExceptOther]
#[ORM\Entity(repositoryClass: EmployeeDocumentRepository::class)]
class EmployeeDocument
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\ManyToOne(targetEntity: MasterEmployee::class, inversedBy: 'documents')]
    #[ORM\JoinColumn(nullable: false)]
    #[Assert\NotNull(message: 'Employee must be specified')]
    private ?MasterEmployee $masterEmployee = null;

    #[ORM\Column(length: 50)]
    #[Assert\NotBlank(message: 'Document type cannot be blank')]
    #[Assert\Choice(
        choices: [
            'PAN',
            'AADHAAR',
            'OFFER_LETTER',
            'RESUME',
            'PASSPORT',
            'DRIVING_LICENSE',
            'EDUCATION_CERTIFICATE',
            'EXPERIENCE_LETTER',
            'PROFILE_PICTURE',
            'OTHER'
        ],
        message: 'Please select a valid document type'
    )]
    private ?string $documentType = null;

    #[ORM\Column(length: 255)]
    #[Assert\Length(max: 255, maxMessage: 'File name cannot exceed 255 characters')]
    private ?string $fileName = null;

    #[ORM\Column(length: 500)]
    #[Assert\Length(max: 500, maxMessage: 'File path cannot exceed 500 characters')]
    private ?string $filePath = null;

    #[ORM\Column(type: 'datetime')]
    #[Assert\NotBlank(message: 'Upload date cannot be blank')]
    private ?\DateTimeInterface $uploadedAt = null;

    #[ORM\ManyToOne(targetEntity: MasterEmployee::class)]
    #[ORM\JoinColumn(nullable: true)]
    private ?MasterEmployee $uploadedBy = null;

    #[ORM\ManyToOne(targetEntity: User::class)]
    #[ORM\JoinColumn(nullable: true)]
    private ?User $uploadedByAdmin = null;

    #[ORM\Column(type: 'text', nullable: true)]
    private ?string $notes = null;

    public function __construct()
    {
        $this->uploadedAt = new \DateTime();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): void
    {
        $this->id = $id;
    }

    public function getMasterEmployee(): ?MasterEmployee
    {
        return $this->masterEmployee;
    }

    public function setMasterEmployee(?MasterEmployee $masterEmployee): void
    {
        $this->masterEmployee = $masterEmployee;
    }

    public function getDocumentType(): ?string
    {
        return $this->documentType;
    }

    public function setDocumentType(?string $documentType): void
    {
        $this->documentType = $documentType;
    }

    public function getFileName(): ?string
    {
        return $this->fileName;
    }

    public function setFileName(?string $fileName): void
    {
        $this->fileName = $fileName;
    }

    public function getFilePath(): ?string
    {
        return $this->filePath;
    }

    public function setFilePath(?string $filePath): void
    {
        $this->filePath = $filePath;
    }

    public function getUploadedAt(): ?\DateTimeInterface
    {
        return $this->uploadedAt;
    }

    public function setUploadedAt(?\DateTimeInterface $uploadedAt): void
    {
        $this->uploadedAt = $uploadedAt;
    }

    public function getUploadedBy(): ?MasterEmployee
    {
        return $this->uploadedBy;
    }

    public function setUploadedBy(?MasterEmployee $uploadedBy): void
    {
        $this->uploadedBy = $uploadedBy;
    }

    public function getNotes(): ?string
    {
        return $this->notes;
    }

    public function setNotes(?string $notes): void
    {
        $this->notes = $notes;
    }

    public function getUploadedByAdmin(): ?User
    {
        return $this->uploadedByAdmin;
    }

    public function setUploadedByAdmin(?User $uploadedByAdmin): void
    {
        $this->uploadedByAdmin = $uploadedByAdmin;
    }
}