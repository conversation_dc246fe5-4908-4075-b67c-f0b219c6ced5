<?php

namespace App\Entity;

use App\Repository\ProjectTeamRepository;
use App\Validator\UniqueProjectTeamMember;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: ProjectTeamRepository::class)]
#[ORM\Table(name: 'project_team', uniqueConstraints: [
    new ORM\UniqueConstraint(name: 'project_team_member_unique', columns: ['project_id', 'team_member_id'])
])]
#[UniqueProjectTeamMember]
class ProjectTeam
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private ?int $id = null;

    #[ORM\ManyToOne(targetEntity: Project::class, inversedBy: 'projectTeams')]
    #[ORM\JoinColumn(nullable: false)]
    private ?Project $project = null;

    #[ORM\ManyToOne(targetEntity: MasterEmployee::class)]
    #[ORM\JoinColumn(nullable: false)]
    private ?MasterEmployee $teamMember = null;

    #[ORM\Column(type: 'string', length: 100, nullable: true)]
    private ?string $role = null;

    #[ORM\ManyToOne(targetEntity: User::class)]
    #[ORM\JoinColumn(nullable: true)]
    private ?User $assignedByAdmin = null;

    #[ORM\ManyToOne(targetEntity: MasterEmployee::class)]
    #[ORM\JoinColumn(nullable: true)]
    private ?MasterEmployee $assignedByTeamLeader = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getProject(): ?Project
    {
        return $this->project;
    }

    public function setProject(?Project $project): self
    {
        $this->project = $project;
        return $this;
    }

    public function getTeamMember(): ?MasterEmployee
    {
        return $this->teamMember;
    }

    public function setTeamMember(MasterEmployee $teamMember): self
    {
        $this->teamMember = $teamMember;
        return $this;
    }

    public function getRole(): ?string
    {
        return $this->role;
    }

    public function setRole(?string $role): self
    {
        $this->role = $role;
        return $this;
    }

    public function getAssignedByAdmin(): ?User
    {
        return $this->assignedByAdmin;
    }

    public function setAssignedByAdmin(?User $assignedByAdmin): self
    {
        $this->assignedByAdmin = $assignedByAdmin;
        return $this;
    }

    public function getAssignedByTeamLeader(): ?MasterEmployee
    {
        return $this->assignedByTeamLeader;
    }

    public function setAssignedByTeamLeader(?MasterEmployee $assignedByTeamLeader): self
    {
        $this->assignedByTeamLeader = $assignedByTeamLeader;
        return $this;
    }
}