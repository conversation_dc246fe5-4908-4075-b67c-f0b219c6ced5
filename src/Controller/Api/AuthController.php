<?php

namespace App\Controller\Api;

use App\Entity\MasterEmployee;
use App\Repository\MasterEmployeeRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Core\Exception\BadCredentialsException;

#[Route('/api')]
class AuthController extends AbstractController
{
    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly MasterEmployeeRepository $employeeRepository,
        private readonly UserPasswordHasherInterface $passwordHasher
    ) {
    }

    #[Route('/health', name: 'api_health', methods: ['GET'])]
    public function health(): JsonResponse
    {
        return new JsonResponse([
            'status' => 'ok',
            'timestamp' => new \DateTime(),
            'service' => 'Brainee HRM API'
        ]);
    }

    #[Route('/employee/login', name: 'api_employee_login', methods: ['POST'])]
    public function login(Request $request): JsonResponse
    {
        try {
            $data = json_decode($request->getContent(), true, 512, JSON_THROW_ON_ERROR);
            
            $email = $data['email'] ?? null;
            $password = $data['password'] ?? null;
            $deviceId = $data['deviceId'] ?? null;
            $rememberMe = $data['rememberMe'] ?? false;

            if (!$email || !$password) {
                return new JsonResponse([
                    'success' => false,
                    'message' => 'Email and password are required'
                ], Response::HTTP_BAD_REQUEST);
            }

            // Find employee by email or username
            $employee = $this->employeeRepository->findOneBy(['email' => $email]);
            if (!$employee) {
                $employee = $this->employeeRepository->findOneBy(['username' => $email]);
            }

            if (!$employee) {
                return new JsonResponse([
                    'success' => false,
                    'message' => 'Invalid credentials'
                ], Response::HTTP_UNAUTHORIZED);
            }

            // Check if employee is active
            if ($employee->getIsDelete()) {
                return new JsonResponse([
                    'success' => false,
                    'message' => 'Account is deactivated'
                ], Response::HTTP_UNAUTHORIZED);
            }

            // Verify password
            if (!$this->passwordHasher->isPasswordValid($employee, $password)) {
                return new JsonResponse([
                    'success' => false,
                    'message' => 'Invalid credentials'
                ], Response::HTTP_UNAUTHORIZED);
            }

            // Generate token
            $token = $this->generateToken($employee, $deviceId, $rememberMe);

            // Store token in database (you might want to create a separate token entity)
            $this->storeToken($employee, $token, $deviceId, $rememberMe);

            return new JsonResponse([
                'success' => true,
                'message' => 'Login successful',
                'token' => $token,
                'user' => [
                    'id' => $employee->getId(),
                    'email' => $employee->getEmail(),
                    'username' => $employee->getUsername(),
                    'name' => $employee->getName(),
                    'roles' => $employee->getRoles(),
                    'employeeCode' => $employee->getEmployeeCode(),
                    'isTeamLeader' => $employee->isTeamLeader(),
                    'isHrAccount' => $employee->isHrAccount()
                ]
            ]);

        } catch (\JsonException $e) {
            return new JsonResponse([
                'success' => false,
                'message' => 'Invalid JSON data'
            ], Response::HTTP_BAD_REQUEST);
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'message' => 'An error occurred during login'
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    #[Route('/validate-token', name: 'api_validate_token', methods: ['POST'])]
    public function validateToken(Request $request): JsonResponse
    {
        try {
            $authHeader = $request->headers->get('Authorization');
            
            if (!$authHeader || !str_starts_with($authHeader, 'Bearer ')) {
                return new JsonResponse([
                    'valid' => false,
                    'message' => 'No token provided'
                ], Response::HTTP_UNAUTHORIZED);
            }

            $token = substr($authHeader, 7); // Remove 'Bearer ' prefix
            
            $tokenData = $this->validateTokenString($token);
            
            if (!$tokenData) {
                return new JsonResponse([
                    'valid' => false,
                    'message' => 'Invalid token'
                ], Response::HTTP_UNAUTHORIZED);
            }

            $employee = $this->employeeRepository->find($tokenData['user_id']);

            if (!$employee || $employee->getIsDelete()) {
                return new JsonResponse([
                    'valid' => false,
                    'message' => 'User not found or deactivated'
                ], Response::HTTP_UNAUTHORIZED);
            }

            return new JsonResponse([
                'valid' => true,
                'user' => [
                    'id' => $employee->getId(),
                    'email' => $employee->getEmail(),
                    'username' => $employee->getUsername(),
                    'name' => $employee->getName(),
                    'roles' => $employee->getRoles(),
                    'employeeCode' => $employee->getEmployeeCode(),
                    'isTeamLeader' => $employee->isTeamLeader(),
                    'isHrAccount' => $employee->isHrAccount()
                ]
            ]);

        } catch (\Exception $e) {
            return new JsonResponse([
                'valid' => false,
                'message' => 'Token validation failed'
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    #[Route('/employee/profile', name: 'api_employee_profile', methods: ['GET'])]
    public function getProfile(Request $request): JsonResponse
    {
        try {
            $authHeader = $request->headers->get('Authorization');
            
            if (!$authHeader || !str_starts_with($authHeader, 'Bearer ')) {
                return new JsonResponse([
                    'success' => false,
                    'message' => 'No token provided'
                ], Response::HTTP_UNAUTHORIZED);
            }

            $token = substr($authHeader, 7);
            $tokenData = $this->validateTokenString($token);
            
            if (!$tokenData) {
                return new JsonResponse([
                    'success' => false,
                    'message' => 'Invalid token'
                ], Response::HTTP_UNAUTHORIZED);
            }

            $employee = $this->employeeRepository->find($tokenData['user_id']);
            
            if (!$employee) {
                return new JsonResponse([
                    'success' => false,
                    'message' => 'User not found'
                ], Response::HTTP_NOT_FOUND);
            }

            return new JsonResponse([
                'success' => true,
                'user' => [
                    'id' => $employee->getId(),
                    'email' => $employee->getEmail(),
                    'username' => $employee->getUsername(),
                    'name' => $employee->getName(),
                    'employeeCode' => $employee->getEmployeeCode(),
                    'joiningDate' => $employee->getJoiningDate()?->format('Y-m-d'),
                    'birthDate' => $employee->getBirthDate()?->format('Y-m-d'),
                    'phone' => $employee->getPhone(),
                    'address' => $employee->getAddress(),
                    'isTeamLeader' => $employee->isTeamLeader(),
                    'isHrAccount' => $employee->isHrAccount(),
                    'roles' => $employee->getRoles()
                ]
            ]);

        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'message' => 'Failed to fetch profile'
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    private function generateToken(MasterEmployee $employee, ?string $deviceId, bool $rememberMe): string
    {
        $payload = [
            'user_id' => $employee->getId(),
            'email' => $employee->getEmail(),
            'device_id' => $deviceId,
            'issued_at' => time(),
            'expires_at' => $rememberMe ? time() + (30 * 24 * 60 * 60) : time() + (24 * 60 * 60), // 30 days or 1 day
            'random' => bin2hex(random_bytes(16))
        ];

        $header = [
            'typ' => 'JWT',
            'alg' => 'HS256'
        ];

        $headerEncoded = base64url_encode(json_encode($header));
        $payloadEncoded = base64url_encode(json_encode($payload));
        
        $signature = hash_hmac('sha256', $headerEncoded . '.' . $payloadEncoded, $this->getParameter('kernel.secret'), true);
        $signatureEncoded = base64url_encode($signature);

        return $headerEncoded . '.' . $payloadEncoded . '.' . $signatureEncoded;
    }

    private function validateTokenString(string $token): ?array
    {
        $parts = explode('.', $token);
        
        if (count($parts) !== 3) {
            return null;
        }

        [$headerEncoded, $payloadEncoded, $signatureEncoded] = $parts;

        // Verify signature
        $expectedSignature = hash_hmac('sha256', $headerEncoded . '.' . $payloadEncoded, $this->getParameter('kernel.secret'), true);
        $expectedSignatureEncoded = base64url_encode($expectedSignature);

        if (!hash_equals($expectedSignatureEncoded, $signatureEncoded)) {
            return null;
        }

        $payload = json_decode(base64url_decode($payloadEncoded), true);
        
        if (!$payload || !isset($payload['expires_at']) || $payload['expires_at'] < time()) {
            return null;
        }

        return $payload;
    }

    private function storeToken(MasterEmployee $employee, string $token, ?string $deviceId, bool $rememberMe): void
    {
        // In a real implementation, you might want to store tokens in a separate table
        // For now, we'll just log the token generation
        // You could create a UserToken entity to store active tokens
    }
}

// Helper function for base64url encoding/decoding
function base64url_encode($data): string
{
    return rtrim(strtr(base64_encode($data), '+/', '-_'), '=');
}

function base64url_decode($data): string
{
    return base64_decode(str_pad(strtr($data, '-_', '+/'), strlen($data) % 4, '=', STR_PAD_RIGHT));
}
