<?php

namespace App\Service\Front;

use App\Entity\MasterEmployee;
use App\Entity\TaskProgress;
use App\Helper\ModalboxHelper;
use Doctrine\ORM\QueryBuilder;
use Omines\DataTablesBundle\Adapter\Doctrine\ORMAdapter;
use Omines\DataTablesBundle\Column\DateTimeColumn;
use Omines\DataTablesBundle\Column\TextColumn;
use Omines\DataTablesBundle\DataTable;
use Omines\DataTablesBundle\DataTableFactory;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Request;
use App\Traits\ExceptionLoggerTrait;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Security\Csrf\CsrfTokenManagerInterface;

class TaskProgressService
{
    use ExceptionLoggerTrait;

    public function __construct(
        private readonly DataTableFactory $dataTableFactory,
        private readonly RouterInterface $router,
        private readonly CsrfTokenManagerInterface $csrfTokenManager,
        private readonly ModalboxHelper $modalboxHelper,
        ParameterBagInterface $parameterBag
    ) {
        $this->initializeLogger($parameterBag, 'TaskProgressService');
    }

    public function createTaskProgressTable(Request $request, int $taskId, MasterEmployee $employee): DataTable
    {
        $dataTable = $this->dataTableFactory->create();

        foreach ($this->getColumns() as $key => $column) {
            if ($key !== 'actionAdminTeamLeader') {
                $dataTable->add($key, $column['className'], array_filter($column, fn($k) => $k !== 'className', ARRAY_FILTER_USE_KEY));
            }
        }

        return $dataTable->createAdapter(ORMAdapter::class, [
            'entity' => TaskProgress::class,
            'query' => fn(QueryBuilder $qb) => $this->buildQuery($qb, $taskId, $employee),
        ])
            ->addOrderBy('progressDate', 'desc')
            ->handleRequest($request);
    }

    public function createTaskProgress(Request $request, int $taskId): DataTable
    {
        $dataTable = $this->dataTableFactory->create();

        foreach ($this->getColumns() as $key => $column) {
            if ($key !== 'actions') {
                $dataTable->add($key, $column['className'], array_filter($column, fn($k) => $k !== 'className', ARRAY_FILTER_USE_KEY));
            }
        }

        return $dataTable->createAdapter(ORMAdapter::class, [
            'entity' => TaskProgress::class,
            'query' => fn(QueryBuilder $qb) => $this->employeeBuildQuery($qb, $taskId),
        ])
            ->addOrderBy('progressDate', 'desc')
            ->handleRequest($request);
    }

    private function getColumns(): array
    {
        return [
            'employee' => [
                'label' => 'Employee',
                'field' => 'e.name',
                'className' => TextColumn::class,
            ],
            'progressDate' => [
                'label' => 'Progress Date',
                'field' => 'tp.progressDate',
                'className' => DateTimeColumn::class,
                'format' => 'd-m-Y',
                'searchable' => false,
            ],
            'progressPercent' => [
                'label' => 'Progress (%)',
                'field' => 'tp.progressPercent',
                'className' => TextColumn::class,
                'render' => fn($value) => htmlspecialchars(number_format($value, 2)) . '%',
            ],
            'hoursWorked' => [
                'label' => 'Hours Worked',
                'field' => 'tp.hoursWorked',
                'className' => TextColumn::class,
                'render' => function ($value) {
                    if ($value === null) {
                        return 'N/A';
                    }
                    $formattedValue = number_format((float) $value, 2, '.', '');
                    $parts = explode('.', $formattedValue);
                    $hours = (int) $parts[0];
                    $minutes = isset($parts[1]) ? (int) $parts[1] : 0;

                    $formatted = '';
                    if ($hours > 0) {
                        $formatted .= $hours . ' hr';
                    }
                    if ($minutes > 0) {
                        $formatted .= ($hours > 0 ? ' ' : '') . $minutes . ' min';
                    }

                    return $formatted ?: '-';
                },
            ],
            'actions' => [
                'label' => 'Actions',
                'field' => 'tp.id',
                'className' => TextColumn::class,
                'orderable' => false,
                'render' => fn($value, $context) => $this->renderActionColumn($context, false),
            ],
            'actionAdminTeamLeader' => [
                'label' => 'Actions',
                'field' => 'tp.id',
                'className' => TextColumn::class,
                'orderable' => false,
                'render' => fn($value, $context) => $this->renderActionColumn($context, true),
            ],
        ];
    }

    private function renderActionColumn(TaskProgress $taskProgress, bool $isAdminTeamLeader = false): string
    {
        $taskId = $taskProgress->getTask()->getId();
        $progressId = $taskProgress->getId();
        $viewModalId = 'viewProgressModal_' . $progressId;
        $comments = $taskProgress->getComments() ?? 'No comments available.';
        $viewModalBody = sprintf(
            '<div class="task-description-container">%s</div>',
            $comments
        );
        $viewFooterButtons = '<button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>';
        $viewModal = $this->modalboxHelper->renderModal(
            $viewModalId,
            'Progress Comments: ' . htmlspecialchars($taskProgress->getTask()->getTitle()),
            $viewModalBody,
            $viewFooterButtons
        );

        $viewButton = sprintf(
            '<button type="button" class="bs-btn btn btn-primary btn-sm bs-m" data-toggle="modal" data-target="#%s" title="View Comments">
                <i class="bs-fa fa fa-eye"></i>
            </button>',
            $viewModalId
        );

        if ($isAdminTeamLeader) {
            return sprintf(
                '<div class="d-inline-flex gap-2">%s</div>%s',
                $viewButton,
                $viewModal
            );
        }

        $actions = '';
        $today = new \DateTime('today');
        if ($taskProgress->getCreatedAt() >= $today) {
            $editUrl = $this->router->generate('employee_task_progress_edit', [
                'task_id' => $taskId,
                'id' => $progressId,
            ]);
            $actions .= sprintf(
                '<a href="%s" class="bs-btn btn btn-primary btn-sm bs-m" title="Edit Progress">
                    <i class="bs-fa fa fa-edit"></i>
                </a>',
                $editUrl
            );

            $deleteUrl = $this->router->generate('employee_task_progress_delete', [
                'task_id' => $taskId,
                'id' => $progressId,
            ]);
            $deleteToken = $this->csrfTokenManager->getToken('delete' . $progressId)->getValue();
            $deleteModalId = 'deleteProgressModal_' . $progressId;
            $deleteModalBody = sprintf(
                'Are you sure you want to delete this progress entry for "%s"?<br><br>' .
                '<strong>Warning:</strong> This action cannot be undone.',
                htmlspecialchars($taskProgress->getTask()->getTitle())
            );
            $deleteFooterButtons = sprintf(
                '<button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>' .
                '<button type="button" class="btn btn-primary delete-progress-btn" data-delete="true" data-url="%s" data-token="%s">Delete</button>',
                $deleteUrl,
                $deleteToken
            );
            $deleteModal = $this->modalboxHelper->renderModal(
                $deleteModalId,
                'Confirm Progress Deletion',
                $deleteModalBody,
                $deleteFooterButtons
            );

            $actions .= sprintf(
                '<button type="button" class="bs-btn btn btn-primary btn-sm bs-m" data-toggle="modal" data-target="#%s" title="Delete Progress">
                    <i class="bs-fa fa fa-trash"></i>
                </button>',
                $deleteModalId
            );
            $actions .= $deleteModal;
        }

        return sprintf(
            '<div class="d-inline-flex gap-2">%s%s</div>%s',
            $viewButton,
            $actions,
            $viewModal
        );
    }

    private function buildQuery(QueryBuilder $qb, int $taskId, MasterEmployee $employee): QueryBuilder
    {
        return $qb->select('tp','e')
            ->from(TaskProgress::class, 'tp')
            ->leftJoin('tp.employee', 'e')
            ->where('tp.task = :taskId')
            ->andWhere('e = :employee')
            ->setParameter('taskId', $taskId)
            ->setParameter('employee', $employee);
    }
    private function employeeBuildQuery(QueryBuilder $qb, int $taskId): QueryBuilder
    {
        return $qb->select('tp')
            ->from(TaskProgress::class, 'tp')
            ->leftJoin('tp.employee', 'e')
            ->where('tp.task = :taskId')
            ->setParameter('taskId', $taskId);
    }
}