{% extends 'Front/dashboard/base.html.twig' %}

{% block title %}Daily Screenshot Report{% endblock %}

{% block body %}
<div class="content">
    <div class="animated fadeIn">
        <div class="row">
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-header">
                        <strong class="card-title">
                            <i class="fa fa-file-pdf-o"></i> Daily Screenshot Report
                        </strong>
                        <div class="card-header-actions">
                            <a href="{{ path('employee_screenshots') }}" class="btn btn-sm btn-secondary">
                                <i class="fa fa-arrow-left"></i> Back to Screenshots
                            </a>
                            <button onclick="window.print()" class="btn btn-sm btn-primary">
                                <i class="fa fa-print"></i> Print Report
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- Report Header -->
                        <div class="report-header">
                            <div class="row">
                                <div class="col-md-6">
                                    <h4>My Daily Activity Report</h4>
                                    <p class="text-muted">Screenshot tracking summary</p>
                                </div>
                                <div class="col-md-6 text-right">
                                    <p><strong>Employee:</strong> {{ employee.name }}</p>
                                    <p><strong>Date:</strong> {{ date|date('F j, Y') }}</p>
                                    <p><strong>Generated:</strong> {{ "now"|date('F j, Y H:i:s') }}</p>
                                </div>
                            </div>
                        </div>

                        <!-- Summary Statistics -->
                        <div class="summary-stats">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="stat-card">
                                        <div class="stat-number">{{ totalScreenshots }}</div>
                                        <div class="stat-label">Total Screenshots</div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="stat-card">
                                        <div class="stat-number">{{ screenshotsByHour|length }}</div>
                                        <div class="stat-label">Active Hours</div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="stat-card">
                                        <div class="stat-number">{{ (totalScreenshots / screenshotsByHour|length)|round(1) }}</div>
                                        <div class="stat-label">Avg per Hour</div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="stat-card">
                                        <div class="stat-number">
                                            {% set firstScreenshot = screenshots|first %}
                                            {% set lastScreenshot = screenshots|last %}
                                            {% if firstScreenshot and lastScreenshot %}
                                                {{ ((lastScreenshot.capturedAt.timestamp - firstScreenshot.capturedAt.timestamp) / 3600)|round(1) }}h
                                            {% else %}
                                                0h
                                            {% endif %}
                                        </div>
                                        <div class="stat-label">Work Duration</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {% if totalScreenshots > 0 %}
                            <!-- Activity Timeline -->
                            <div class="activity-timeline">
                                <h5>Activity Timeline</h5>
                                {% for hour, hourScreenshots in screenshotsByHour %}
                                    <div class="hour-section">
                                        <div class="hour-header">
                                            <h6>
                                                <i class="fa fa-clock-o"></i> 
                                                {{ hour }} - {{ (hour|split(':')|first + 1) ~ ':00' }}
                                                <span class="badge badge-primary">{{ hourScreenshots|length }} screenshots</span>
                                            </h6>
                                        </div>
                                        <div class="hour-screenshots">
                                            <div class="row">
                                                {% for screenshot in hourScreenshots %}
                                                    <div class="col-lg-2 col-md-3 col-sm-4 col-6 mb-3">
                                                        <div class="screenshot-item">
                                                            <div class="screenshot-preview-small">
                                                                {% if screenshot.fileName %}
                                                                    {% set imagePath = screenshot.filePath %}
                                                                    {% if imagePath starts with '/uploads/' %}
                                                                        <img src="{{ asset(imagePath) }}" 
                                                                             alt="Screenshot" class="img-fluid"
                                                                             onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                                                                        <div class="no-image-small" style="display: none;">
                                                                            <i class="fa fa-camera"></i>
                                                                        </div>
                                                                    {% else %}
                                                                        <div class="no-image-small">
                                                                            <i class="fa fa-camera"></i>
                                                                        </div>
                                                                    {% endif %}
                                                                {% else %}
                                                                    <div class="no-image-small">
                                                                        <i class="fa fa-image"></i>
                                                                    </div>
                                                                {% endif %}
                                                            </div>
                                                            <div class="screenshot-time">
                                                                {{ screenshot.capturedAt|date('H:i:s') }}
                                                            </div>
                                                            {% if screenshot.activeApplication and screenshot.activeApplication != 'Unknown Application' %}
                                                                <div class="screenshot-app">
                                                                    <small>{{ screenshot.activeApplication|slice(0, 20) }}{% if screenshot.activeApplication|length > 20 %}...{% endif %}</small>
                                                                </div>
                                                            {% endif %}
                                                        </div>
                                                    </div>
                                                {% endfor %}
                                            </div>
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>

                            <!-- Work Summary -->
                            <div class="work-summary">
                                <h5>Work Summary</h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="summary-box">
                                            <h6>Most Active Hour</h6>
                                            {% set maxHour = '' %}
                                            {% set maxCount = 0 %}
                                            {% for hour, hourScreenshots in screenshotsByHour %}
                                                {% if hourScreenshots|length > maxCount %}
                                                    {% set maxCount = hourScreenshots|length %}
                                                    {% set maxHour = hour %}
                                                {% endif %}
                                            {% endfor %}
                                            <p>{{ maxHour }} - {{ (maxHour|split(':')|first + 1) ~ ':00' }} ({{ maxCount }} screenshots)</p>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="summary-box">
                                            <h6>Work Period</h6>
                                            {% set firstScreenshot = screenshots|first %}
                                            {% set lastScreenshot = screenshots|last %}
                                            {% if firstScreenshot and lastScreenshot %}
                                                <p>{{ firstScreenshot.capturedAt|date('H:i') }} - {{ lastScreenshot.capturedAt|date('H:i') }}</p>
                                            {% else %}
                                                <p>No activity recorded</p>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {% else %}
                            <div class="alert alert-warning">
                                <i class="fa fa-exclamation-triangle"></i>
                                No screenshots found for {{ date|date('F j, Y') }}.
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.report-header {
    border-bottom: 2px solid #dee2e6;
    padding-bottom: 20px;
    margin-bottom: 30px;
}

.summary-stats {
    margin-bottom: 30px;
}

.stat-card {
    text-align: center;
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 15px;
    border: 1px solid #dee2e6;
}

.stat-number {
    font-size: 2em;
    font-weight: bold;
    color: #007bff;
}

.stat-label {
    color: #6c757d;
    font-size: 0.9em;
}

.activity-timeline {
    margin-bottom: 30px;
}

.hour-section {
    margin-bottom: 25px;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    overflow: hidden;
}

.hour-header {
    background-color: #f8f9fa;
    padding: 10px 15px;
    border-bottom: 1px solid #dee2e6;
}

.hour-header h6 {
    margin: 0;
    color: #495057;
}

.hour-screenshots {
    padding: 15px;
}

.screenshot-item {
    text-align: center;
}

.screenshot-preview-small {
    height: 80px;
    background-color: #f8f9fa;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 5px;
    border: 1px solid #dee2e6;
}

.screenshot-preview-small img {
    max-height: 100%;
    max-width: 100%;
    border-radius: 4px;
}

.no-image-small {
    color: #6c757d;
}

.screenshot-time {
    font-size: 0.8em;
    font-weight: bold;
    color: #495057;
}

.screenshot-app {
    font-size: 0.7em;
    color: #6c757d;
}

.work-summary {
    border-top: 2px solid #dee2e6;
    padding-top: 20px;
}

.summary-box {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 15px;
    border: 1px solid #dee2e6;
}

.summary-box h6 {
    color: #495057;
    margin-bottom: 10px;
    font-weight: bold;
}

.card-header-actions {
    margin-left: auto;
}

@media print {
    .card-header-actions,
    .btn {
        display: none !important;
    }
    
    .screenshot-preview-small {
        height: 60px;
    }
}
</style>
{% endblock %}
