const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // Machine ID
  getMachineId: () => ipcRenderer.invoke('get-machine-id'),
  
  // Store operations
  storeGet: (key) => ipcRenderer.invoke('store-get', key),
  storeSet: (key, value) => ipcRenderer.invoke('store-set', key, value),
  storeDelete: (key) => ipcRenderer.invoke('store-delete', key),
  
  // App operations
  getAppVersion: () => ipcRenderer.invoke('app-version'),
  minimizeWindow: () => ipcRenderer.invoke('minimize-window'),
  hideWindow: () => ipcRenderer.invoke('hide-window'),
  showWindow: () => ipcRenderer.invoke('show-window'),
  
  // Authentication
  loginSuccess: () => ipc<PERSON>enderer.invoke('login-success'),
  logout: () => ipcRenderer.invoke('logout'),
  
  // Database operations (will be implemented)
  dbQuery: (sql, params) => ipcRenderer.invoke('db-query', sql, params),
  dbRun: (sql, params) => ipcRenderer.invoke('db-run', sql, params),
  dbGet: (sql, params) => ipcRenderer.invoke('db-get', sql, params),
  dbAll: (sql, params) => ipcRenderer.invoke('db-all', sql, params),
  
  // API operations
  apiRequest: (config) => ipcRenderer.invoke('api-request', config),
  
  // Utility
  log: (message) => ipcRenderer.invoke('log', message),
  showError: (title, message) => ipcRenderer.invoke('show-error', title, message),
  showInfo: (title, message) => ipcRenderer.invoke('show-info', title, message),

  // Auto-start management
  getAutoStartStatus: () => ipcRenderer.invoke('get-auto-start-status'),
  setAutoStart: (enabled) => ipcRenderer.invoke('set-auto-start', enabled),

  // System information
  getSystemInfo: () => ipcRenderer.invoke('get-system-info'),

  // Offline functionality
  offlineAuthenticate: (email, password) => ipcRenderer.invoke('offline-authenticate', email, password),
  getConnectionStatus: () => ipcRenderer.invoke('get-connection-status'),
  queueForSync: (action, data) => ipcRenderer.invoke('queue-for-sync', action, data),
  syncPendingData: () => ipcRenderer.invoke('sync-pending-data'),
  getOfflineData: (type, params) => ipcRenderer.invoke('get-offline-data', type, params),

  // Status tracking
  getStatus: () => ipcRenderer.invoke('get-status'),
  setStatus: (status) => ipcRenderer.invoke('set-status', status),
  recordActivity: () => ipcRenderer.invoke('record-activity'),
  getStatusHistory: () => ipcRenderer.invoke('get-status-history'),

  // Time tracking
  getTrackingStatus: () => ipcRenderer.invoke('get-tracking-status'),
  startTracking: () => ipcRenderer.invoke('start-tracking'),
  stopTracking: () => ipcRenderer.invoke('stop-tracking'),
  getTodayStats: () => ipcRenderer.invoke('get-today-stats'),
  getWeeklyStats: () => ipcRenderer.invoke('get-weekly-stats'),
  getDetailedTodayStats: () => ipcRenderer.invoke('get-detailed-today-stats'),
  switchSessionType: (sessionType, reason) => ipcRenderer.invoke('switch-session-type', sessionType, reason),
  updateTrackingConfig: (config) => ipcRenderer.invoke('update-tracking-config', config),
  getTimeLoggerConfig: () => ipcRenderer.invoke('get-time-logger-config'),
  updateTimeLoggerConfig: (config) => ipcRenderer.invoke('update-time-logger-config', config),

  // Idle detection
  getIdleDetectionStatus: () => ipcRenderer.invoke('get-idle-detection-status'),
  getIdleDetectionConfig: () => ipcRenderer.invoke('get-idle-detection-config'),
  updateIdleDetectionConfig: (config) => ipcRenderer.invoke('update-idle-detection-config', config),
  triggerManualActivity: () => ipcRenderer.invoke('trigger-manual-activity'),

  // Screenshot capture
  getScreenshotStatus: () => ipcRenderer.invoke('get-screenshot-status'),
  captureManualScreenshot: () => ipcRenderer.invoke('capture-manual-screenshot'),
  getRecentScreenshots: (limit) => ipcRenderer.invoke('get-recent-screenshots', limit),
  getScreenshotConfig: () => ipcRenderer.invoke('get-screenshot-config'),
  updateScreenshotConfig: (config) => ipcRenderer.invoke('update-screenshot-config', config),
  startTestScreenshots: () => ipcRenderer.invoke('start-test-screenshots'),
  getScreenshotDebug: () => ipcRenderer.invoke('get-screenshot-debug'),



  // Developer tools
  openDevTools: () => ipcRenderer.invoke('open-dev-tools'),
  closeDevTools: () => ipcRenderer.invoke('close-dev-tools'),
  forceReloadConfig: () => ipcRenderer.invoke('force-reload-config')
});
