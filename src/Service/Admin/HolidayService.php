<?php

namespace App\Service\Admin;

use App\Entity\Holiday;
use App\Helper\ModalboxHelper;
use Doctrine\ORM\QueryBuilder;
use Omines\DataTablesBundle\Adapter\Doctrine\ORMAdapter;
use Omines\DataTablesBundle\Column\DateTimeColumn;
use Omines\DataTablesBundle\Column\TextColumn;
use Omines\DataTablesBundle\DataTable;
use Omines\DataTablesBundle\DataTableFactory;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Request;
use App\Traits\ExceptionLoggerTrait;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Security\Csrf\CsrfTokenManagerInterface;

class HolidayService
{
    use ExceptionLoggerTrait;

    public function __construct(
        private readonly DataTableFactory          $dataTableFactory,
        private readonly RouterInterface           $router,
        ParameterBagInterface                      $parameterBag,
        private readonly CsrfTokenManagerInterface $csrfTokenManager,
        private readonly ModalboxHelper            $modalboxHelper
    )
    {
        $this->initializeLogger($parameterBag, 'Holiday_Table_service');
    }

    public function createHolidayTable(Request $request): DataTable
    {
        $dataTable = $this->dataTableFactory->create();

        foreach ($this->getColumns() as $key => $column) {
            $dataTable->add($key, $column['className'], array_filter($column, fn($k) => $k !== 'className', ARRAY_FILTER_USE_KEY));
        }

        return $dataTable->createAdapter(ORMAdapter::class, [
            'entity' => Holiday::class,
            'query' => fn(QueryBuilder $qb) => $this->buildQuery($qb),
        ])
            ->addOrderBy('date', 'asc')
            ->handleRequest($request);
    }

    private function getColumns(): array
    {
        return [
            'name' => [
                'label' => 'Holiday Name',
                'field' => 'h.name',
                'className' => TextColumn::class,
                'orderable' => true,
            ],
            'date' => [
                'label' => 'Date',
                'field' => 'h.date',
                'className' => DateTimeColumn::class,
                'format' => 'd-m-Y',
                'nullValue' => '-',
                'searchable' => false,
            ],
            'isPublicHoliday' => [
                'label' => 'Public Holiday',
                'field' => 'h.isPublicHoliday',
                'className' => TextColumn::class,
                'render' => fn($value) => $this->renderStatusBadge($value),
            ],
            'actions' => [
                'label' => 'Actions',
                'field' => 'h.id',
                'className' => TextColumn::class,
                'orderable' => false,
                'render' => fn($value, $context) => $this->renderActionColumn($context),
            ],
        ];
    }

    private function renderStatusBadge(bool $isPublicHoliday): string
    {
        return $isPublicHoliday
            ? '<span class="text-success fa-lg" title="Yes"><i class="fa fa-check"></span>'
            : '<span class="text-danger fa-lg" title="No"><i class="fa fa-times"></span>';
    }

    private function renderActionColumn(Holiday $holiday): string
    {
        $entityId = $holiday->getId();
        $editUrl = $this->router->generate('holiday_edit');
        $deleteUrl = $this->router->generate('holiday_delete', ['id' => $entityId]);
        $csrfToken = $this->csrfTokenManager->getToken('delete' . $entityId)->getValue();


        $modalId = 'deleteModal_' . $entityId;
        $modalBody = sprintf(
            'Are you sure you want to delete the holiday "%s" on %s?<br><br>' .
            '<strong>Warning:</strong> This action cannot be undone.',
            htmlspecialchars($holiday->getName()),
            $holiday->getDate()->format('d-m-Y')
        );

        $footerButtons = sprintf(
            '<button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>' .
            '<button type="button" class="btn btn-primary delete-holiday-btn"  data-delete="true" data-url="%s" data-token="%s">Delete</button>',
            $deleteUrl,
            $csrfToken
        );

        $deleteModal = $this->modalboxHelper->renderModal(
            $modalId,
            'Confirm Holiday Deletion',
            $modalBody,
            $footerButtons
        );

        return sprintf(
            '<div class="d-inline-flex gap-2">
            <form action="%s" method="POST" class="d-inline">
                <input type="hidden" name="id" value="%s">
                <input type="hidden" name="_token" value="%s">
                <button type="submit" class="bs-btn btn btn-primary btn-sm bs-m"  data-turbo="false" title="Edit">
                    <i class="bs-fa fa fa-edit"></i>
                </button>
            </form>
            <button type="button" class="bs-btn btn btn-primary btn-sm bs-m open-delete-holiday-modal" data-toggle="modal" data-target="#%s" title="Delete">
                <i class="bs-fa fa fa-trash"></i>
            </button>
        </div>%s',
            $editUrl,
            $entityId,
            $this->csrfTokenManager->getToken('edit' . $entityId)->getValue(),
            $modalId,
            $deleteModal
        );
    }
    private function buildQuery(QueryBuilder $qb): QueryBuilder
    {
        return $qb->select('h')
            ->from(Holiday::class, 'h');
    }
}