{% extends 'Front/dashboard/base.html.twig' %}

{% block body %}
    {% include 'Front/partials/breadcrumbs.html.twig' with {
        breadcrumbs: [
            { 'label': 'Dashboard', 'url': url('dashboard_employee') },
            {
                'label': isHistory ? 'Task History' : 'My Tasks',
                'url': isHistory ? path('employee_task_history') : path('employee_task')
            },
            { 'label': task.title, 'url': path('employee_task_assignment', { 'task_id': task.id }) },
        ],
        active: mode == 'view' ? 'View Assignment' : (mode == 'edit' ? 'Edit Assignment' : 'Add Assignment')
    } %}
    <div class="content">
        <div class="animated fadeIn">
            <div class="row">
                <div class="col-lg-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <strong class="card-title">{{ mode == 'view' ? 'View Assignment' : (mode == 'edit' ? 'Edit Assignment' : 'Add Assignment') }}</strong>
                            <div class="d-flex align-items-center">
                                <a href="{{ path('employee_task_assignment', { 'task_id': task.id }) }}" class="btn btn-sm btn-outline-secondary mr-2">
                                    <i class="fa fa-list mr-1"></i>Back to List
                                </a>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="assignment-form">
                                {% if mode == 'view' %}
                                <div class="form-section employee-details-section">
                                    <div class="details-section">
                                        <div class="details-container">
                                            <div class="detail-row">
                                                <div class="detail-item col-md-4"><strong class="detail-label">Category:</strong></div>
                                                <div class="detail-item col-md-8">{{ taskAssignment.category ?: 'N/A' }}</div>
                                            </div>
                                            <div class="detail-row">
                                                <div class="detail-item col-md-4"><strong class="detail-label">Priority:</strong></div>
                                                <div class="detail-item col-md-8">{{ taskAssignment.priority ?: 'N/A' }}</div>
                                            </div>
                                            <div class="detail-row">
                                                <div class="detail-item col-md-4"><strong class="detail-label">Status:</strong></div>
                                                <div class="detail-item col-md-8">{{ taskAssignment.status ?: 'N/A' }}</div>
                                            </div>
                                            <div class="detail-row">
                                                <div class="detail-item col-md-4"><strong class="detail-label">Assigned To:</strong></div>
                                                <div class="detail-item col-md-8">{{ taskAssignment.assignedTo ? taskAssignment.assignedTo.name : 'N/A' }}</div>
                                            </div>
                                            <div class="detail-row">
                                                <div class="detail-item col-md-4"><strong class="detail-label">Assigned By:</strong></div>
                                                <div class="detail-item col-md-8">{{ taskAssignment.assignedBy ? taskAssignment.assignedBy.name : 'N/A' }}</div>
                                            </div>
                                            <div class="detail-row">
                                                <div class="detail-item col-md-4"><strong class="detail-label">Created At:</strong></div>
                                                <div class="detail-item col-md-8">{{ taskAssignment.createdAt ? taskAssignment.createdAt|custom_date : 'N/A' }}</div>
                                            </div>
                                            <div class="detail-row">
                                                <div class="detail-item col-md-4"><strong class="detail-label">Updated At:</strong></div>
                                                <div class="detail-item col-md-8">{{ taskAssignment.updatedAt ? taskAssignment.updatedAt|custom_date : 'N/A' }}</div>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- Time Logs Section -->
                                    <div class="mt-4">
                                        <h5 class="font-weight-bold m-2">Time Logs</h5>
                                        <table class="table table-bordered table-striped">
                                            <thead>
                                            <tr>
                                                <th>Start Time</th>
                                                <th>End Time</th>
                                                <th>Duration (Minutes)</th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                            {% if timeLogs is empty %}
                                                <tr>
                                                    <td colspan="3">No time logs available</td>
                                                </tr>
                                            {% else %}
                                                {% for log in timeLogs %}
                                                    <tr>
                                                        <td>{{ log.startTime ? log.startTime|custom_dateTime : 'N/A' }}</td>
                                                        <td>{{ log.endTime ? log.endTime|custom_dateTime : 'N/A' }}</td>
                                                        <td>
                                                            {% if log.duration is not null %}
                                                                {{ (log.duration * 60)|number_format(1) }}
                                                            {% else %}
                                                                N/A
                                                            {% endif %}
                                                        </td>
                                                    </tr>
                                                {% endfor %}
                                            {% endif %}
                                            </tbody>
                                            <tfoot>
                                            <tr>
                                                <td colspan="2"><strong>Total:</strong></td>
                                                <td>
                                                    <strong>
                                                    {% set totalDuration = 0 %}
                                                    {% for log in timeLogs %}
                                                        {% if log.duration is not null %}
                                                            {% set totalDuration = totalDuration + log.duration %}
                                                        {% endif %}
                                                    {% endfor %}

                                                    <strong>{{ totalDuration | format_duration }}</strong>
                                                    </strong>
                                                </td>
                                            </tr>
                                            </tfoot>
                                        </table>
                                    </div>
                                    <!-- Status Logs Section -->
                                    <div class="mt-4">
                                        <h5 class="font-weight-bold m-2">Status Change History</h5>
                                        <table class="table table-bordered table-striped">
                                            <thead>
                                            <tr>
                                                <th>Changed By</th>
                                                <th>Old Status</th>
                                                <th>New Status</th>
                                                <th>Changed At</th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                            {% if statusHistories is empty %}
                                                <tr>
                                                    <td colspan="4">No status history available</td>
                                                </tr>
                                            {% else %}
                                                {% for history in statusHistories %}
                                                    <tr>
                                                        <td>{{ history.changedBy ? history.changedBy.name : 'Unknown' }}</td>
                                                        <td>{{ history.oldStatus }}</td>
                                                        <td>{{ history.newStatus }}</td>
                                                        <td>{{ history.changedAt ? history.changedAt|custom_dateTime : 'N/A' }}</td>
                                                    </tr>
                                                {% endfor %}
                                            {% endif %}
                                            </tbody>
                                        </table>
                                    </div>
                                    <div class="action-footer mt-4">
                                        <a href="{{ path('employee_task_assignment', { 'task_id': task.id }) }}" class="btn btn-secondary mr-2">
                                            Back to List
                                        </a>
                                        <a href="{{ path('employee_task_assignment_edit', { 'task_id': task.id, 'id': taskAssignment.id }) }}" class="btn btn-primary pr-3 pl-3" style="padding-top: 10px;">
                                            Edit Assignment
                                        </a>
                                    </div>
                                </div>
                                {% else %}
                                {{ form_start(form, { 'attr': { 'class': 'needs-validation', 'novalidate': 'novalidate' } }) }}
                                <div class="form-section">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                {{ form_label(form.category, 'Category', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                                {{ form_widget(form.category, { 'attr': { 'class': 'form-control', 'required': 'required' } }) }}
                                                {{ form_errors(form.category) }}
                                                <small class="form-text text-muted">Select the category of this assignment</small>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                {{ form_label(form.priority, 'Priority', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                                {{ form_widget(form.priority, { 'attr': { 'class': 'form-control', 'required': 'required' } }) }}
                                                {{ form_errors(form.priority) }}
                                                <small class="form-text text-muted">Select the priority level</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                {{ form_label(form.status, 'Status', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                                {{ form_widget(form.status, { 'attr': { 'class': 'form-control', 'required': 'required' } }) }}
                                                {{ form_errors(form.status) }}
                                                <small class="form-text text-muted">Select the current status</small>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                {{ form_label(form.assignedTo, 'Assigned To', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                                {{ form_widget(form.assignedTo, { 'attr': { 'class': 'form-control', 'required': 'required' } }) }}
                                                {{ form_errors(form.assignedTo) }}
                                                <small class="form-text text-muted">Select the employee to assign this task to</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="mb-3">
                                                {{ form_label(form.description, 'Description', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                {{ form_widget(form.description, { 'attr': { 'class': 'form-control' } }) }}
                                                {{ form_errors(form.description) }}
                                                <small class="form-text text-muted">Provide any additional details</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="action-footer mt-4">
                                    <a href="{{ path('employee_task_assignment', { 'task_id': task.id }) }}" class="btn btn-secondary mr-2">
                                        Cancel
                                    </a>
                                    <button type="submit" class="btn btn-primary pr-3 pl-3" id="submitButton" data-update="{{ mode == 'edit' ? 'true' : 'false' }}">
                                        {{ mode == 'edit' ? 'Update Assignment' : 'Save Assignment' }}
                                    </button>
                                </div>
                                {{ form_end(form) }}
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="clearfix"></div>
    <link rel="stylesheet" href="{{ asset('assets/css/Error/error.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/AdminForm/Form.css') }}">
    <script src="{{ asset('assets/js/admin/common.js') }}"></script>
    <script src="{{ asset('assets/js/front/taskAssignment.js') }}"></script>
{% endblock %}