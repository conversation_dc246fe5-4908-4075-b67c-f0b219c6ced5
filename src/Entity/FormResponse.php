<?php

namespace App\Entity;

use App\Repository\FormResponseRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: FormResponseRepository::class)]
class FormResponse
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private ?int $id = null;

    #[ORM\ManyToOne(targetEntity: FormSubmission::class, inversedBy: 'responses')]
    #[ORM\JoinColumn(nullable: false)]
    private FormSubmission $submission;

    #[ORM\ManyToOne(targetEntity: FormSection::class)]
    #[ORM\JoinColumn(nullable: false)]
    private FormSection $section;

    #[ORM\ManyToOne(targetEntity: FormField::class)]
    #[ORM\JoinColumn(nullable: false)]
    private FormField $field;

    #[ORM\Column(type: 'text', nullable: true)]
    private ?string $responseValue = null;

    #[ORM\Column(type: 'text', nullable: true)]
    private ?string $previousValue = null;

    #[ORM\ManyToOne(targetEntity: MasterEmployee::class)]
    #[ORM\JoinColumn(nullable: true)]
    private MasterEmployee $modifiedBy;

    #[ORM\ManyToOne(targetEntity: User::class)]
    #[ORM\JoinColumn(nullable: true)]
    private ?User $modifiedByUser = null;

    #[ORM\Column(type: 'boolean', options: ['default' => true])]
    private bool $isDraft = true;

    #[ORM\Column(type: 'datetime')]
    private \DateTimeInterface $createdAt;

    #[ORM\Column(type: 'datetime')]
    private \DateTimeInterface $updatedAt;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): void
    {
        $this->id = $id;
    }

    public function getSubmission(): FormSubmission
    {
        return $this->submission;
    }

    public function setSubmission(FormSubmission $submission): void
    {
        $this->submission = $submission;
    }

    public function getSection(): FormSection
    {
        return $this->section;
    }

    public function setSection(FormSection $section): void
    {
        $this->section = $section;
    }

    public function getField(): FormField
    {
        return $this->field;
    }

    public function setField(FormField $field): void
    {
        $this->field = $field;
    }

    public function getResponseValue(): ?string
    {
        return $this->responseValue;
    }

    public function setResponseValue(?string $responseValue): void
    {
        $this->responseValue = $responseValue;
    }

    public function getPreviousValue(): ?string
    {
        return $this->previousValue;
    }

    public function setPreviousValue(?string $previousValue): void
    {
        $this->previousValue = $previousValue;
    }

    public function getModifiedBy(): MasterEmployee
    {
        return $this->modifiedBy;
    }

    public function setModifiedBy(MasterEmployee $modifiedBy): void
    {
        $this->modifiedBy = $modifiedBy;
    }

    public function isDraft(): bool
    {
        return $this->isDraft;
    }

    public function setIsDraft(bool $isDraft): void
    {
        $this->isDraft = $isDraft;
    }

    public function getCreatedAt(): \DateTimeInterface
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTimeInterface $createdAt): void
    {
        $this->createdAt = $createdAt;
    }

    public function getUpdatedAt(): \DateTimeInterface
    {
        return $this->updatedAt;
    }

    public function setUpdatedAt(\DateTimeInterface $updatedAt): void
    {
        $this->updatedAt = $updatedAt;
    }
    public function __construct()
    {
        $this->createdAt = new \DateTime();
        $this->updatedAt = new \DateTime();
    }

    public function getModifiedByUser(): ?User
    {
        return $this->modifiedByUser;
    }

    public function setModifiedByUser(?User $modifiedByUser): void
    {
        $this->modifiedByUser = $modifiedByUser;
    }
}