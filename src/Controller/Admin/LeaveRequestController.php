<?php
namespace App\Controller\Admin;

use App\Entity\LeaveRequest;
use App\Entity\MasterEmployee;
use App\Entity\User;
use App\Entity\LeaveBalance;
use App\Form\AdminLeaveRequestFormType;
use App\Message\LeaveRequestStatusNotificationMessage;
use App\Repository\LeaveBalanceRepository;
use App\Repository\LeaveRequestRepository;
use App\Service\Common\FilterService;
use App\Service\Common\LeaveRequestService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Messenger\Exception\ExceptionInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Routing\Annotation\Route;

#[Route('/admin')]
class LeaveRequestController extends AbstractController
{
    public function __construct(
        private readonly LeaveRequestService $leaveRequestService,
        private readonly EntityManagerInterface $entityManager,
        private readonly MessageBusInterface $messageBus,
        private readonly LeaveBalanceRepository $leaveBalanceRepository,
        private readonly FilterService $filterService,
    ) {}

    #[Route('/leave-requests', name: 'admin_leave_request_list', methods: ['GET', 'POST'])]
    public function listLeaveRequests(Request $request): Response
    {
        $user = $this->getUser();
        if (!$user instanceof User) {
            throw new \LogicException('User must be an Admin');
        }

        $table = $this->leaveRequestService->createAdminLeaveRequestTable($request);
        if ($table->isCallback()) {
            return $table->getResponse();
        }

        return $this->render('admin/LeaveRequest/index.html.twig', [
            'datatable' => $table,
        ]);
    }

    /**
     * @throws ExceptionInterface
     */
    #[Route('/leave-request/{id}/approve', name: 'admin_leave_request_approve', methods: ['POST'])]
    public function approveLeaveRequest(int $id, Request $request): JsonResponse
    {
        if (!$this->isCsrfTokenValid('approve'.$id, $request->request->get('_token'))) {
            return new JsonResponse(['success' => false, 'message' => 'Invalid CSRF token'], 403);
        }

        $leaveRequest = $this->entityManager->getRepository(LeaveRequest::class)->find($id);
        if (!$leaveRequest) {
            return new JsonResponse(['success' => false, 'message' => 'Leave request not found'], 404);
        }

        if ($leaveRequest->getStatus() !== LeaveRequest::STATUS_PENDING) {
            return new JsonResponse(['success' => false, 'message' => 'This leave request has already been processed'], 400);
        }

        $penaltyDays = (float) $request->request->get('penaltyDays', $leaveRequest->getPenaltyDays());
        $applyPenalty = (bool) $request->request->get('applyPenalty', false);

        $user = $this->getUser();
        if (!$user instanceof User) {
            return new JsonResponse(['success' => false, 'message' => 'User not authenticated'], 401);
        }

        $leaveRequest->setAdminApprovedBy($user);
        $leaveRequest->setAdminApprovedOn(new \DateTime());
        $leaveRequest->setStatus(LeaveRequest::STATUS_APPROVED);
        $leaveRequest->setPenaltyDays($penaltyDays);

        $leaveBalance = $leaveRequest->getLeaveType();
        if ($leaveBalance instanceof LeaveBalance && $applyPenalty && $penaltyDays > 0) {
            $leaveRequest->setPenaltyApplied(true);
            $remainingDays = $leaveBalance->getRemainingDays();
            if ($remainingDays >= $penaltyDays) {
                $leaveBalance->setUsedDays($leaveBalance->getUsedDays() + $penaltyDays);
            } else {
                $excessPenalty = $penaltyDays - $remainingDays;
                $leaveBalance->setUsedDays($leaveBalance->getUsedDays() + $remainingDays);
                $leaveBalance->setPenaltyDays($leaveBalance->getPenaltyDays() + $excessPenalty);
            }

            $leaveBalance->updateRemainingDays();
            $this->entityManager->persist($leaveBalance);
        } else {
            $leaveRequest->setPenaltyApplied(false);
        }

        $this->entityManager->persist($leaveRequest);
        $this->entityManager->flush();

        $this->messageBus->dispatch(new LeaveRequestStatusNotificationMessage(
            $leaveRequest->getId(),
            LeaveRequest::STATUS_APPROVED
        ));

        return new JsonResponse([
            'success' => true,
            'message' => 'Leave request approved successfully',
            'penaltyApplied' => $applyPenalty,
            'penaltyDays' => $penaltyDays
        ]);
    }

    /**
     * @throws ExceptionInterface
     */
    #[Route('/leave-request/{id}/reject', name: 'admin_leave_request_reject', methods: ['POST'])]
    public function rejectLeaveRequest(int $id, Request $request): JsonResponse
    {
        if (!$this->isCsrfTokenValid('reject'.$id, $request->request->get('_token'))) {
            return new JsonResponse(['success' => false, 'message' => 'Invalid CSRF token'], 403);
        }

        $leaveRequest = $this->entityManager->getRepository(LeaveRequest::class)->find($id);
        if (!$leaveRequest) {
            return new JsonResponse(['success' => false, 'message' => 'Leave request not found'], 404);
        }

        $currentDate = new \DateTime();
        if ($leaveRequest->getStatus() === LeaveRequest::STATUS_APPROVED && $leaveRequest->getStartDate() < $currentDate) {
            return new JsonResponse(['success' => false, 'message' => 'Cannot reject an approved leave request after its start date'], 400);
        }

        if ($leaveRequest->getStatus() !== LeaveRequest::STATUS_PENDING && $leaveRequest->getStatus() !== LeaveRequest::STATUS_APPROVED) {
            return new JsonResponse(['success' => false, 'message' => 'This leave request has already been processed'], 400);
        }

        $user = $this->getUser();
        if (!$user instanceof User) {
            return new JsonResponse(['success' => false, 'message' => 'User not authenticated'], 401);
        }

        $leaveBalance = $leaveRequest->getLeaveType();
        if ($leaveBalance instanceof LeaveBalance) {
            if ($leaveRequest->getStatus() === LeaveRequest::STATUS_PENDING) {
                $daysToRefund = $leaveRequest->getDaysRequested();
                $leaveBalance->setUsedDays(max(0, $leaveBalance->getUsedDays() - $daysToRefund));
            } elseif ($leaveRequest->getStatus() === LeaveRequest::STATUS_APPROVED) {
                $daysRequested = $leaveRequest->getDaysRequested();
                if ($leaveRequest->isPenaltyApplied()) {
                    $totalPenaltyDays = $leaveRequest->getPenaltyDays();
                    $totalDays = $leaveBalance->getTotalDays();
                    $availableDaysAtApproval = $totalDays - $daysRequested;
                    if ($totalPenaltyDays > $availableDaysAtApproval) {
                        $excessPenalty = $totalPenaltyDays - $availableDaysAtApproval;
                        $leaveBalance->setPenaltyDays(max(0, $leaveBalance->getPenaltyDays() - $excessPenalty));
                    }
                    $penaltyAddedToUsed = min($totalPenaltyDays, $availableDaysAtApproval);
                    $totalToRefundFromUsed = $daysRequested + $penaltyAddedToUsed;
                    $leaveBalance->setUsedDays(max(0, $leaveBalance->getUsedDays() - $totalToRefundFromUsed));
                } else {
                    $leaveBalance->setUsedDays(max(0, $leaveBalance->getUsedDays() - $daysRequested));
                }
            }

            $leaveBalance->updateRemainingDays();
            $this->entityManager->persist($leaveBalance);
        }

        $leaveRequest->setAdminApprovedBy($user);
        $leaveRequest->setAdminApprovedOn(new \DateTime());
        $leaveRequest->setStatus(LeaveRequest::STATUS_REJECTED);
        $this->entityManager->persist($leaveRequest);
        $this->entityManager->flush();

        $this->messageBus->dispatch(new LeaveRequestStatusNotificationMessage(
            $leaveRequest->getId(),
            LeaveRequest::STATUS_REJECTED
        ));

        return new JsonResponse(['success' => true, 'message' => 'Leave request rejected successfully']);
    }
    /**
     * @throws \DateMalformedPeriodStringException
     */
    #[Route('/leave-request/create', name: 'admin_leave_request_create', methods: ['GET', 'POST'])]
    public function createLeaveRequest(Request $request): Response
    {
        $leaveRequest = new LeaveRequest();
        $leaveRequest->setStatus(LeaveRequest::STATUS_PENDING);
        $leaveRequest->setAppliedOn(new \DateTime());
        $form = $this->createForm(AdminLeaveRequestFormType::class, $leaveRequest);
        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $employee = $leaveRequest->getEmployee();
            $leaveBalance = $leaveRequest->getLeaveType();
            if (!$employee instanceof MasterEmployee) {
                $this->addFlash('error', 'Invalid employee selected.');
                return $this->render('admin/LeaveRequest/new.html.twig', [
                    'form' => $form->createView(),
                    'mode' => 'create',
                ]);
            }

            if (!$leaveBalance instanceof LeaveBalance) {
                $this->addFlash('error', 'Invalid leave type selected.');
                return $this->render('admin/LeaveRequest/new.html.twig', [
                    'form' => $form->createView(),
                    'mode' => 'create',
                ]);
            }

            if ($leaveBalance->getEmployee()->getId() !== $employee->getId()) {
                $this->addFlash('error', 'Selected leave balance does not belong to the chosen employee.');
                return $this->render('admin/LeaveRequest/new.html.twig', [
                    'form' => $form->createView(),
                    'mode' => 'create',
                ]);
            }
            /** @phpstan-ignore-next-line */
            $existingRequests = $this->entityManager->getRepository(LeaveRequest::class)->findOverlappingLeaves(
                $employee,
                $leaveRequest->getStartDate(),
                $leaveRequest->getEndDate()
            );

            if (count($existingRequests) > 0) {
                $this->addFlash('error', 'The employee already has a leave request for these dates.');
                return $this->render('admin/LeaveRequest/new.html.twig', [
                    'form' => $form->createView(),
                    'mode' => 'create',
                ]);
            }

            $holidayDates = $this->leaveRequestService->getHolidayDates();
            $daysRequested = $this->leaveRequestService->calculateDaysRequested($leaveRequest, $holidayDates);
            $currentYear = (int) (new \DateTime())->format('Y');
            if ($leaveBalance->getYear() !== $currentYear || $leaveBalance->getEmployee() !== $employee) {
                $this->addFlash('error', 'Invalid leave balance for the current year.');
                return $this->render('admin/LeaveRequest/new.html.twig', [
                    'form' => $form->createView(),
                    'mode' => 'create',
                ]);
            }

            if ($leaveBalance->getRemainingDays() < $daysRequested['base']) {
                $this->addFlash('error', 'Insufficient leave balance for this request.');
                return $this->render('admin/LeaveRequest/new.html.twig', [
                    'form' => $form->createView(),
                    'mode' => 'create',
                ]);
            }

            $leaveRequest->setDaysRequested($daysRequested['base']);
            $leaveRequest->setPenaltyDays($daysRequested['penalty']);
            $leaveRequest->setPenaltyApplied(false);

            $this->entityManager->beginTransaction();
            try {
                // Deduct base days from leave balance
                $leaveBalance->setUsedDays($leaveBalance->getUsedDays() + $daysRequested['base']);
                $leaveBalance->updateRemainingDays();
                $this->entityManager->persist($leaveBalance);
                $this->entityManager->persist($leaveRequest);
                $this->entityManager->flush();
                $this->entityManager->commit();

                if ($daysRequested['penalty'] > 0) {
                    $this->addFlash('warning', sprintf(
                        "A penalty of %.1f days has been calculated but won't be applied until approval.",
                        $daysRequested['penalty']
                    ));
                }
                $this->addFlash('success', 'Leave request submitted successfully on behalf of the employee.');
                return $this->redirectToRoute('admin_leave_request_list');
            } catch (\Exception $e) {
                $this->entityManager->rollback();
                $this->addFlash('error', 'An error occurred while submitting the leave request: ' . $e->getMessage());
                return $this->render('admin/LeaveRequest/new.html.twig', [
                    'form' => $form->createView(),
                    'mode' => 'create',
                ]);
            }
        }

        return $this->render('admin/LeaveRequest/new.html.twig', [
            'form' => $form->createView(),
            'mode' => 'create',
        ]);
    }

    #[Route('/api/employee/{id}/leave-balances', name: 'api_employee_leave_balances', methods: ['GET'])]
    public function getEmployeeLeaveBalances(MasterEmployee $employee): JsonResponse
    {
        $leaveBalances = $this->leaveBalanceRepository->getCurrentYearLeaveBalancesWithRemainingDays($employee);

        $formattedBalances = [];
        foreach ($leaveBalances as $balance) {
            $remainingDays = $balance->getRemainingDays();
            $remainingDaysText = '';
            if ($remainingDays < 100) {
                $remainingDaysText = sprintf(' (%.1f days remaining)', $remainingDays);
            }
            $formattedBalances[] = [
                'id' => $balance->getId(),
                'employeeId' => $employee->getId(),
                'name' => sprintf(
                    '%s%s',
                    $balance->getLeaveType()->getName(),
                    $remainingDaysText
                ),
                'remainingDays' => $remainingDays
            ];
        }
        return new JsonResponse($formattedBalances);
    }

    #[Route('/api/leave-request/check-duplicate', name: 'admin_api_check_duplicate_leave', methods: ['GET'])]
    public function checkDuplicateLeaveAdmin(Request $request): JsonResponse
    {
        $employeeId = $request->query->get('employee_id');
        $startDate = $request->query->get('start_date');
        $endDate = $request->query->get('end_date');

        if (!$employeeId || !$startDate || !$endDate) {
            return new JsonResponse(['error' => 'Employee ID, start date and end date are required'], Response::HTTP_BAD_REQUEST);
        }

        $employee = $this->entityManager->getRepository(MasterEmployee::class)->find($employeeId);

        if (!$employee) {
            return new JsonResponse(['error' => 'Employee not found'], Response::HTTP_NOT_FOUND);
        }

        try {
            $startDateTime = new \DateTime($startDate);
            $endDateTime = new \DateTime($endDate);
        } catch (\Exception $e) {
            return new JsonResponse(['error' => 'Invalid date format'], Response::HTTP_BAD_REQUEST);
        }
        /** @phpstan-ignore-next-line */
        $existingRequests = $this->entityManager->getRepository(LeaveRequest::class)->findOverlappingLeaves(
            $employee,
            $startDateTime,
            $endDateTime
        );

        $hasOverlap = count($existingRequests) > 0;

        return new JsonResponse([
            'duplicate_exists' => $hasOverlap,
            'count' => count($existingRequests)
        ]);
    }

    /**
     * @throws \DateMalformedPeriodStringException
     */
    #[Route('/api/leave-request/calculate-days', name: 'admin_api_calculate_leave_days', methods: ['GET'])]
    public function calculateLeaveDaysAdmin(Request $request, LeaveRequestService $leaveRequestService): JsonResponse
    {
        $employeeId = $request->query->get('employee_id');
        $startDateStr = $request->query->get('start_date');
        $endDateStr = $request->query->get('end_date');
        $leaveTypeId = $request->query->get('leave_type_id');
        $startHalfDay = $request->query->get('start_half_day');
        $endHalfDay = $request->query->get('end_half_day');

        if (!$employeeId || !$startDateStr || !$endDateStr || !$leaveTypeId) {
            return new JsonResponse(['error' => 'Employee ID, start date, end date, and leave type ID are required'], Response::HTTP_BAD_REQUEST);
        }

        $employee = $this->entityManager->getRepository(MasterEmployee::class)->find($employeeId);

        if (!$employee) {
            return new JsonResponse(['error' => 'Employee not found'], Response::HTTP_NOT_FOUND);
        }

        try {
            $startDate = new \DateTime($startDateStr);
            $endDate = new \DateTime($endDateStr);
        } catch (\Exception $e) {
            return new JsonResponse(['error' => 'Invalid date format'], Response::HTTP_BAD_REQUEST);
        }

        $leaveBalance = $this->entityManager->getRepository(LeaveBalance::class)->findOneBy([
            'id' => $leaveTypeId,
            'employee' => $employee
        ]);

        if (!$leaveBalance) {
            return new JsonResponse(['error' => 'Leave balance not found'], Response::HTTP_NOT_FOUND);
        }

        $leaveRequest = new LeaveRequest();
        $leaveRequest->setEmployee($employee);
        $leaveRequest->setStartDate($startDate);
        $leaveRequest->setEndDate($endDate);
        $leaveRequest->setLeaveType($leaveBalance);
        $leaveRequest->setAppliedOn(new \DateTime());

        if ($startHalfDay) {
            $leaveRequest->setStartHalfDay($startHalfDay);
        }
        if ($endHalfDay) {
            $leaveRequest->setEndHalfDay($endHalfDay);
        }

        $holidayDates = $leaveRequestService->getHolidayDates();
        $daysRequested = $leaveRequestService->calculateDaysRequested($leaveRequest, $holidayDates);

        return new JsonResponse([
            'base_days' => $daysRequested['base'],
            'penalty_days' => $daysRequested['penalty'],
            'total_days' => $daysRequested['base'],
            'remaining_days' => $leaveBalance->getRemainingDays()
        ]);
    }

    #[Route('/leave-requests/filter', name: 'leave_requests_filter', methods: ['POST'])]
    public function updateLeaveRequestFilter(Request $request): JsonResponse
    {
        try {
            $data = json_decode($request->getContent(), true, 512, JSON_THROW_ON_ERROR);
            if (!is_array($data) || !array_key_exists('startDate', $data) || !array_key_exists('endDate', $data)) {
                throw new \InvalidArgumentException('Invalid input data');
            }
            if (empty($data['startDate']) || empty($data['endDate'])) {
                $this->filterService->clearFilter();
            } else {
                $this->filterService->saveFilter($data['startDate'], $data['endDate']);
            }
            return new JsonResponse([
                'success' => true,
                'message' => 'Filters updated successfully.',
            ]);
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'message' => 'Error: ' . $e->getMessage(),
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * @throws \DateMalformedStringException
     */
    #[Route('/leave-requests/filter', name: 'leave_requests_filter_get', methods: ['GET'])]
    public function getFiltersLeaveRequest( LeaveRequestRepository $leaveRequestRepository): JsonResponse
    {
        $filters = $this->filterService->getFiltersLeaveRequest($leaveRequestRepository);

        return new JsonResponse([
            'success' => true,
            'filters' => [
                'dateRange' => $filters['dateRange'],
                'minDate' =>  $filters['minDate'],
                'maxDate' => $filters['maxDate'],
            ],
        ]);
    }
}