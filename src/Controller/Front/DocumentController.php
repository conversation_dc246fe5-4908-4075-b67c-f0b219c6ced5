<?php

namespace App\Controller\Front;

use App\Entity\Document;
use App\Entity\MasterEmployee;
use App\Traits\ExceptionLoggerTrait;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\ResponseHeaderBag;
use Symfony\Component\Routing\Attribute\Route;

class DocumentController extends AbstractController
{
    use ExceptionLoggerTrait;

    public function __construct(
        private readonly ParameterBagInterface $params,
    ) {
        $this->initializeLogger($params, 'document_controller');
    }

    #[Route('/documents', name: 'document_list')]
    public function list(EntityManagerInterface $em): Response
    {
        $user = $this->getUser();
        if (!$user instanceof MasterEmployee) {
            throw $this->createAccessDeniedException('You must be logged in as an employee to view this page.');
        }
        $documents = $em->getRepository(Document::class)->findAll();
        return $this->render('Front/document/index.html.twig', [
            'documents' => $documents,
        ]);
    }

    #[Route('/document/download/{id}', name: 'document_download')]
    public function download(Document $document): Response
    {
        $user = $this->getUser();
        if (!$user instanceof MasterEmployee) {
            throw $this->createAccessDeniedException('You must be logged in as an employee to download this file.');
        }

        $filePath = $this->params->get('kernel.project_dir') . '/public' . $document->getFilePath();
        if (!file_exists($filePath)) {
            throw $this->createNotFoundException('File not found: ' . $filePath);
        }
        $mimeType = mime_content_type($filePath);
        $response = new BinaryFileResponse($filePath);
        $response->headers->set('Content-Type', $mimeType);
        $response->setContentDisposition(
            ResponseHeaderBag::DISPOSITION_ATTACHMENT,
            $document->getTitle() . '.' . pathinfo($filePath, PATHINFO_EXTENSION)
        );

        return $response;
    }
}