const { app, BrowserWindow, ipc<PERSON><PERSON>, <PERSON><PERSON>, Tray, dialog } = require('electron');
const path = require('path');
const AutoLaunch = require('auto-launch');
const Store = require('electron-store');
const { machineId } = require('node-machine-id');
const axios = require('axios');
const DatabaseService = require('./database');
const OfflineService = require('./offline-service');
const StatusTracker = require('./status-tracker');
const TrackingService = require('./tracking-service');

// Initialize electron store for persistent data
const store = new Store();

// Initialize database service
const dbService = new DatabaseService();

// Initialize offline service
let offlineService;

// Initialize status tracker
let statusTracker;

// Initialize tracking service
let trackingService;

// Auto-launch configuration
const autoLauncher = new AutoLaunch({
  name: 'Brainee HRM Desktop',
  path: app.getPath('exe'),
  isHidden: true, // Start minimized to system tray
  mac: {
    useLaunchAgent: true,
  },
  linux: {
    execArgv: ['--no-sandbox']
  }
});

let mainWindow;
let tray;
let isQuitting = false;

// Prevent multiple instances
const gotTheLock = app.requestSingleInstanceLock();

if (!gotTheLock) {
  app.quit();
} else {
  app.on('second-instance', () => {
    // Someone tried to run a second instance, focus our window instead
    if (mainWindow) {
      if (mainWindow.isMinimized()) mainWindow.restore();
      mainWindow.focus();
    }
  });
}

function createWindow() {
  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js')
    },
    icon: path.join(__dirname, '../assets/icon.png'),
    show: false, // Don't show until ready
    titleBarStyle: 'default'
  });

  // Add keyboard shortcuts for developer tools
  mainWindow.webContents.on('before-input-event', (_, input) => {
    // F12 or Ctrl+Shift+I to toggle DevTools
    if (input.key === 'F12' ||
        (input.control && input.shift && input.key === 'I')) {
      mainWindow.webContents.toggleDevTools();
    }
  });

  // Load the login page
  mainWindow.loadFile(path.join(__dirname, '../renderer/login.html'));

  // Show window when ready
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
    
    // Focus the window
    if (process.platform === 'darwin') {
      app.dock.show();
    }
  });

  // Handle window closed
  mainWindow.on('close', (event) => {
    if (!isQuitting) {
      event.preventDefault();
      mainWindow.hide();
      
      // Show notification that app is still running
      if (process.platform === 'win32') {
        mainWindow.setSkipTaskbar(true);
      }
    }
  });

  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // Remove menu bar in production
  if (!process.argv.includes('--dev')) {
    mainWindow.setMenuBarVisibility(false);
  }
}

function createTray() {
  // Destroy existing tray if it exists
  if (tray) {
    tray.destroy();
    tray = null;
  }

  // Create a simple programmatic icon
  const { nativeImage } = require('electron');

  // Create a 16x16 icon with a simple design
  const iconBuffer = Buffer.from([
    0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
    0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x10,
    0x08, 0x06, 0x00, 0x00, 0x00, 0x1F, 0xF3, 0xFF, 0x61, 0x00, 0x00, 0x00,
    0x19, 0x74, 0x45, 0x58, 0x74, 0x53, 0x6F, 0x66, 0x74, 0x77, 0x61, 0x72,
    0x65, 0x00, 0x41, 0x64, 0x6F, 0x62, 0x65, 0x20, 0x49, 0x6D, 0x61, 0x67,
    0x65, 0x52, 0x65, 0x61, 0x64, 0x79, 0xCC, 0xB2, 0x73, 0x00, 0x00, 0x00,
    0x0C, 0x49, 0x44, 0x41, 0x54, 0x78, 0xDA, 0x63, 0x60, 0x00, 0x02, 0x00,
    0x00, 0x05, 0x00, 0x01, 0x0D, 0x0A, 0x2D, 0xB4, 0x00, 0x00, 0x00, 0x00,
    0x49, 0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82
  ]);

  const icon = nativeImage.createFromBuffer(iconBuffer);
  tray = new Tray(icon);

  updateTrayMenu();

  tray.setToolTip('Brainee HRM Desktop');

  // Double click to show main window
  tray.on('double-click', () => {
    showMainWindow();
  });

  console.log('System tray created successfully');
}

function updateTrayMenu() {
  if (!tray) return;

  const statusInfo = statusTracker ? statusTracker.getDetailedStatus() : {
    status: 'unknown',
    display: { text: 'Unknown', icon: '⚪' }
  };

  const contextMenu = Menu.buildFromTemplate([
    {
      label: `Status: ${statusInfo.display.icon} ${statusInfo.display.text}`,
      enabled: false
    },
    { type: 'separator' },
    {
      label: 'Show Main Window',
      click: () => {
        showMainWindow();
      }
    },
    { type: 'separator' },
    {
      label: 'Set Status',
      submenu: [
        {
          label: '🟢 Active',
          click: () => {
            if (statusTracker) {
              statusTracker.setStatus('active');
              updateTrayMenu();
            }
          }
        },
        {
          label: '🟡 Idle',
          click: () => {
            if (statusTracker) {
              statusTracker.setStatus('idle');
              updateTrayMenu();
            }
          }
        },
        {
          label: '🔴 Inactive',
          click: () => {
            if (statusTracker) {
              statusTracker.setStatus('inactive');
              updateTrayMenu();
            }
          }
        }
      ]
    },
    { type: 'separator' },
    {
      label: 'About',
      click: () => {
        dialog.showMessageBox(mainWindow, {
          type: 'info',
          title: 'About Brainee HRM Desktop',
          message: 'Brainee HRM Desktop v1.0.0',
          detail: 'Employee tracking and management application'
        });
      }
    }
  ]);

  tray.setContextMenu(contextMenu);
}

function showMainWindow() {
  if (mainWindow) {
    if (mainWindow.isMinimized()) {
      mainWindow.restore();
    }
    mainWindow.show();
    mainWindow.focus();

    if (process.platform === 'win32') {
      mainWindow.setSkipTaskbar(false);
    }

    // Record activity when user interacts with the app
    if (statusTracker) {
      statusTracker.recordActivity();
    }
  }
}

// Database initialization
async function initializeDatabase() {
  try {
    await dbService.initialize();
    console.log('Database service initialized successfully');
  } catch (error) {
    console.error('Failed to initialize database service:', error);
    throw error;
  }
}

// App event handlers
app.whenReady().then(async () => {
  // Initialize database first
  try {
    await initializeDatabase();

    // Initialize offline service
    offlineService = new OfflineService(dbService, store);
    await offlineService.initialize();
    console.log('Offline service initialized');

    // Initialize status tracker
    statusTracker = new StatusTracker();

    // Listen for status changes to update tray
    statusTracker.onStatusChange((newStatus, oldStatus, statusInfo) => {
      console.log(`Status changed from ${oldStatus} to ${newStatus}`);
      updateTrayMenu();

      // Log status change to database
      if (dbService) {
        const currentUser = store.get('currentUser');
        if (currentUser) {
          dbService.logActivity(
            currentUser.id,
            `Status changed to ${newStatus}`,
            `Changed from ${oldStatus} to ${newStatus} at ${new Date().toLocaleString()}`
          ).catch(err => console.error('Failed to log status change:', err));
        }
      }
    });

    console.log('Status tracker initialized');

    // Initialize tracking service
    trackingService = new TrackingService(dbService, statusTracker, store, offlineService);
    console.log('Tracking service initialized');
  } catch (error) {
    console.error('Failed to initialize database:', error);
    dialog.showErrorBox('Database Error', 'Failed to initialize local database. The app may not function properly.');
  }

  // Enable auto-launch
  try {
    const isEnabled = await autoLauncher.isEnabled();
    if (!isEnabled) {
      await autoLauncher.enable();
      console.log('Auto-launch enabled');
    }
  } catch (error) {
    console.error('Failed to enable auto-launch:', error);
  }

  createWindow();
  createTray();

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    } else {
      mainWindow.show();
    }
  });
});

app.on('before-quit', async () => {
  isQuitting = true;

  // Cleanup services
  try {
    if (tray) {
      tray.destroy();
      tray = null;
    }
    if (trackingService) {
      await trackingService.destroy();
    }
    if (statusTracker) {
      statusTracker.destroy();
    }
    if (offlineService) {
      await offlineService.destroy();
    }
    if (dbService) {
      await dbService.close();
    }
  } catch (error) {
    console.error('Error during cleanup:', error);
  }
});

app.on('window-all-closed', () => {
  // Keep app running in background
  // Don't quit on window close
});

// IPC handlers
ipcMain.handle('get-machine-id', async () => {
  try {
    return await machineId();
  } catch (error) {
    console.error('Failed to get machine ID:', error);
    return null;
  }
});

ipcMain.handle('store-get', (event, key) => {
  return store.get(key);
});

ipcMain.handle('store-set', (event, key, value) => {
  store.set(key, value);
});

ipcMain.handle('store-delete', (event, key) => {
  store.delete(key);
});

ipcMain.handle('app-version', () => {
  return app.getVersion();
});

ipcMain.handle('minimize-window', () => {
  if (mainWindow) {
    mainWindow.minimize();
  }
});

ipcMain.handle('hide-window', () => {
  if (mainWindow) {
    mainWindow.hide();
  }
});

ipcMain.handle('show-window', () => {
  if (mainWindow) {
    mainWindow.show();
    mainWindow.focus();
  }
});

// Handle login success - navigate to dashboard
ipcMain.handle('login-success', () => {
  if (mainWindow) {
    mainWindow.loadFile(path.join(__dirname, '../renderer/dashboard.html'));
  }
});

// Handle logout (disabled - users cannot logout once logged in)
ipcMain.handle('logout', () => {
  // Logout is disabled as per requirements
  console.log('Logout attempt blocked - users cannot logout once authenticated');
  return false;
});

// Database IPC handlers
ipcMain.handle('db-query', async (event, sql, params = []) => {
  try {
    return await dbService.query(sql, params);
  } catch (error) {
    console.error('Database query error:', error);
    throw error;
  }
});

ipcMain.handle('db-run', async (event, sql, params = []) => {
  try {
    return await dbService.run(sql, params);
  } catch (error) {
    console.error('Database run error:', error);
    throw error;
  }
});

ipcMain.handle('db-get', async (event, sql, params = []) => {
  try {
    return await dbService.get(sql, params);
  } catch (error) {
    console.error('Database get error:', error);
    throw error;
  }
});

ipcMain.handle('db-all', async (event, sql, params = []) => {
  try {
    return await dbService.all(sql, params);
  } catch (error) {
    console.error('Database all error:', error);
    throw error;
  }
});

// API request handler
ipcMain.handle('api-request', async (event, config) => {
  try {
    console.log('Making API request:', config.method, config.url);
    console.log('Request data:', config.data || config.body);

    const response = await axios(config);
    console.log('API response status:', response.status);
    console.log('API response data:', response.data);

    return response.data;
  } catch (error) {
    console.error('API request error:', error.message);
    if (error.response) {
      console.error('Error response status:', error.response.status);
      console.error('Error response data:', error.response.data);
    }
    throw error;
  }
});

// Logging handler
ipcMain.handle('log', (event, message) => {
  console.log('Renderer log:', message);
});

// Dialog handlers
ipcMain.handle('show-error', (event, title, message) => {
  dialog.showErrorBox(title, message);
});

ipcMain.handle('show-info', (event, title, message) => {
  dialog.showMessageBox(mainWindow, {
    type: 'info',
    title: title,
    message: message
  });
});

// Auto-start management
ipcMain.handle('get-auto-start-status', async () => {
  try {
    return await autoLauncher.isEnabled();
  } catch (error) {
    console.error('Failed to get auto-start status:', error);
    return false;
  }
});

ipcMain.handle('set-auto-start', async (event, enabled) => {
  try {
    if (enabled) {
      await autoLauncher.enable();
      console.log('Auto-start enabled');
    } else {
      await autoLauncher.disable();
      console.log('Auto-start disabled');
    }
    return true;
  } catch (error) {
    console.error('Failed to set auto-start:', error);
    return false;
  }
});

// System information
ipcMain.handle('get-system-info', () => {
  const os = require('os');
  return {
    platform: process.platform,
    arch: process.arch,
    osVersion: os.release(),
    totalMemory: os.totalmem(),
    freeMemory: os.freemem(),
    cpus: os.cpus().length,
    uptime: os.uptime(),
    userDataPath: app.getPath('userData'),
    appPath: app.getAppPath()
  };
});

// Offline service handlers
ipcMain.handle('offline-authenticate', async (event, email, password) => {
  try {
    if (offlineService) {
      return await offlineService.authenticateOffline(email, password);
    }
    return { success: false, message: 'Offline service not available' };
  } catch (error) {
    console.error('Offline authentication failed:', error);
    return { success: false, message: 'Offline authentication failed' };
  }
});

ipcMain.handle('get-connection-status', () => {
  if (offlineService) {
    return offlineService.getConnectionStatus();
  }
  return { isOnline: false, lastCheck: null, pendingSyncItems: 0 };
});

ipcMain.handle('queue-for-sync', async (event, action, data) => {
  try {
    if (offlineService) {
      await offlineService.queueForSync(action, data);
      return true;
    }
    return false;
  } catch (error) {
    console.error('Failed to queue for sync:', error);
    return false;
  }
});

ipcMain.handle('sync-pending-data', async () => {
  try {
    if (offlineService) {
      await offlineService.syncPendingData();
      return true;
    }
    return false;
  } catch (error) {
    console.error('Failed to sync pending data:', error);
    return false;
  }
});

ipcMain.handle('get-offline-data', async (event, type, params) => {
  try {
    if (offlineService) {
      return await offlineService.getOfflineData(type, params);
    }
    return null;
  } catch (error) {
    console.error('Failed to get offline data:', error);
    throw error;
  }
});

// Status tracking handlers
ipcMain.handle('get-status', () => {
  if (statusTracker) {
    return statusTracker.getDetailedStatus();
  }
  return { status: 'unknown', display: { text: 'Unknown', icon: '⚪' } };
});

ipcMain.handle('set-status', (event, status) => {
  if (statusTracker && ['active', 'idle', 'inactive'].includes(status)) {
    statusTracker.setStatus(status);
    return true;
  }
  return false;
});

ipcMain.handle('record-activity', () => {
  if (statusTracker) {
    statusTracker.recordActivity();
    return true;
  }
  return false;
});

ipcMain.handle('get-status-history', async () => {
  try {
    const currentUser = store.get('currentUser');
    if (currentUser && dbService) {
      return await dbService.all(
        'SELECT * FROM activity_log WHERE user_id = ? AND action LIKE "Status changed%" ORDER BY timestamp DESC LIMIT 20',
        [currentUser.id]
      );
    }
    return [];
  } catch (error) {
    console.error('Failed to get status history:', error);
    return [];
  }
});

// Tracking service handlers
ipcMain.handle('get-tracking-status', () => {
  if (trackingService) {
    return trackingService.getTrackingStatus();
  }
  return { isTracking: false, currentSession: null };
});

ipcMain.handle('start-tracking', async () => {
  if (trackingService) {
    await trackingService.startTracking();
    return true;
  }
  return false;
});

ipcMain.handle('stop-tracking', async () => {
  if (trackingService) {
    await trackingService.stopTracking();
    return true;
  }
  return false;
});

ipcMain.handle('get-today-stats', async () => {
  if (trackingService) {
    return await trackingService.getTodayStats();
  }
  return null;
});

ipcMain.handle('update-tracking-config', async (event, config) => {
  if (trackingService) {
    await trackingService.updateConfiguration(config);
    return true;
  }
  return false;
});

ipcMain.handle('get-weekly-stats', async () => {
  if (trackingService) {
    return await trackingService.getWeeklyStats();
  }
  return null;
});

ipcMain.handle('get-detailed-today-stats', async () => {
  if (trackingService) {
    return await trackingService.getDetailedTodayStats();
  }
  return null;
});

ipcMain.handle('switch-session-type', async (event, sessionType, reason) => {
  if (trackingService && trackingService.timeLogger) {
    try {
      await trackingService.timeLogger.switchSessionType(sessionType, reason);
      return true;
    } catch (error) {
      console.error('Failed to switch session type:', error);
      return false;
    }
  }
  return false;
});

ipcMain.handle('get-time-logger-config', () => {
  if (trackingService && trackingService.timeLogger) {
    return trackingService.timeLogger.getConfig();
  }
  return null;
});

ipcMain.handle('update-time-logger-config', async (event, config) => {
  if (trackingService && trackingService.timeLogger) {
    trackingService.timeLogger.updateConfig(config);
    return true;
  }
  return false;
});

// Idle detection handlers
ipcMain.handle('get-idle-detection-status', () => {
  if (trackingService) {
    return trackingService.getIdleDetectionStatus();
  }
  return null;
});

ipcMain.handle('get-idle-detection-config', () => {
  if (trackingService) {
    return trackingService.getIdleDetectionConfig();
  }
  return null;
});

ipcMain.handle('update-idle-detection-config', async (event, config) => {
  if (trackingService) {
    return await trackingService.updateIdleDetectionConfig(config);
  }
  return false;
});

ipcMain.handle('trigger-manual-activity', () => {
  if (trackingService) {
    return trackingService.triggerManualActivity();
  }
  return false;
});

// Screenshot service handlers
ipcMain.handle('get-screenshot-status', () => {
  if (trackingService) {
    return trackingService.getScreenshotStatus();
  }
  return null;
});

ipcMain.handle('capture-manual-screenshot', async () => {
  if (trackingService) {
    return await trackingService.captureManualScreenshot();
  }
  return null;
});

ipcMain.handle('get-recent-screenshots', async (_, limit) => {
  if (trackingService) {
    return await trackingService.getRecentScreenshots(limit || 10);
  }
  return [];
});

ipcMain.handle('get-screenshot-config', () => {
  if (trackingService) {
    return trackingService.getScreenshotConfig();
  }
  return null;
});

ipcMain.handle('update-screenshot-config', async (_, config) => {
  if (trackingService) {
    return await trackingService.updateScreenshotConfig(config);
  }
  return false;
});

ipcMain.handle('start-test-screenshots', async () => {
  if (trackingService) {
    return await trackingService.startTestScreenshots();
  }
  return false;
});

ipcMain.handle('get-screenshot-debug', () => {
  if (trackingService) {
    return trackingService.getScreenshotDebugInfo();
  }
  return null;
});

// Developer tools
ipcMain.handle('open-dev-tools', () => {
  if (mainWindow && mainWindow.webContents) {
    mainWindow.webContents.openDevTools();
    return true;
  }
  return false;
});



ipcMain.handle('close-dev-tools', () => {
  if (mainWindow && mainWindow.webContents) {
    mainWindow.webContents.closeDevTools();
    return true;
  }
  return false;
});

ipcMain.handle('force-reload-config', async () => {
  if (trackingService) {
    return await trackingService.forceReloadConfiguration();
  }
  return false;
});



// Application tracker handlers
ipcMain.handle('get-application-tracker-status', () => {
  if (trackingService && trackingService.applicationTracker) {
    return trackingService.applicationTracker.getStatus();
  }
  return null;
});

ipcMain.handle('get-application-today-stats', async () => {
  if (trackingService && trackingService.applicationTracker) {
    return await trackingService.applicationTracker.getTodayStats();
  }
  return [];
});

ipcMain.handle('get-recent-application-usage', async (_, limit) => {
  if (trackingService && trackingService.applicationTracker) {
    return await trackingService.applicationTracker.getRecentUsage(limit || 20);
  }
  return [];
});

ipcMain.handle('get-application-tracker-config', () => {
  if (trackingService && trackingService.applicationTracker) {
    return trackingService.applicationTracker.getConfiguration();
  }
  return null;
});

ipcMain.handle('update-application-tracker-config', async (_, config) => {
  if (trackingService && trackingService.applicationTracker) {
    return await trackingService.applicationTracker.updateConfiguration(config);
  }
  return false;
});

// Website tracker handlers
ipcMain.handle('get-website-tracker-status', () => {
  if (trackingService && trackingService.websiteTracker) {
    return trackingService.websiteTracker.getStatus();
  }
  return null;
});

ipcMain.handle('get-website-today-stats', async () => {
  if (trackingService && trackingService.websiteTracker) {
    return await trackingService.websiteTracker.getTodayStats();
  }
  return [];
});

ipcMain.handle('get-recent-website-visits', async (_, limit) => {
  if (trackingService && trackingService.websiteTracker) {
    return await trackingService.websiteTracker.getRecentVisits(limit || 20);
  }
  return [];
});

ipcMain.handle('get-website-tracker-config', () => {
  if (trackingService && trackingService.websiteTracker) {
    return trackingService.websiteTracker.getConfiguration();
  }
  return null;
});

ipcMain.handle('update-website-tracker-config', async (_, config) => {
  if (trackingService && trackingService.websiteTracker) {
    return await trackingService.websiteTracker.updateConfiguration(config);
  }
  return false;
});

// Reports service handlers
ipcMain.handle('generate-daily-report', async (_, date) => {
  if (trackingService && trackingService.reportsService) {
    return await trackingService.reportsService.generateDailyReport(date);
  }
  return null;
});

ipcMain.handle('get-recent-reports', async (_, limit) => {
  if (trackingService && trackingService.reportsService) {
    return await trackingService.reportsService.getRecentReports(limit || 10);
  }
  return [];
});

ipcMain.handle('get-report-by-date', async (_, date, reportType) => {
  if (trackingService && trackingService.reportsService) {
    return await trackingService.reportsService.getReportByDate(date, reportType);
  }
  return null;
});

ipcMain.handle('update-reports-config', async (_, config) => {
  if (trackingService && trackingService.reportsService) {
    return await trackingService.reportsService.updateConfiguration(config);
  }
  return false;
});

// File upload service handlers
ipcMain.handle('get-upload-stats', () => {
  if (trackingService && trackingService.fileUploadService) {
    return trackingService.fileUploadService.getUploadStats();
  }
  return null;
});

ipcMain.handle('queue-screenshots-upload', async () => {
  if (trackingService && trackingService.fileUploadService) {
    await trackingService.fileUploadService.queueScreenshotsForUpload();
    return true;
  }
  return false;
});



ipcMain.handle('clear-upload-queue', async () => {
  if (trackingService && trackingService.fileUploadService) {
    await trackingService.fileUploadService.clearUploadQueue();
    return true;
  }
  return false;
});

ipcMain.handle('update-upload-config', async (_, config) => {
  if (trackingService && trackingService.fileUploadService) {
    return await trackingService.fileUploadService.updateConfiguration(config);
  }
  return false;
});
