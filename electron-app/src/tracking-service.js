const { powerMonitor } = require('electron');
const TimeLogger = require('./time-logger');
const IdleDetector = require('./idle-detector');
const ScreenshotService = require('./screenshot-service');
const ApplicationTracker = require('./application-tracker');
const WebsiteTracker = require('./website-tracker');
const ReportsService = require('./reports-service');
const FileUploadService = require('./file-upload-service');

class TrackingService {
    constructor(dbService, statusTracker, store, offlineService) {
        this.dbService = dbService;
        this.statusTracker = statusTracker;
        this.store = store;
        this.offlineService = offlineService;

        // Initialize time logger
        this.timeLogger = new TimeLogger(dbService, store);

        // Initialize idle detector
        this.idleDetector = new IdleDetector(statusTracker, this.timeLogger, store);

        // Initialize screenshot service
        this.screenshotService = new ScreenshotService(dbService, store);

        // Initialize application tracker
        this.applicationTracker = new ApplicationTracker(dbService, store);

        // Initialize website tracker
        this.websiteTracker = new WebsiteTracker(dbService, store);

        // Initialize reports service
        this.reportsService = new ReportsService(dbService, store);

        // Initialize file upload service
        this.fileUploadService = new FileUploadService(dbService, store);

        // Tracking state
        this.isTracking = false;
        this.currentSession = null;
        this.trackingStartTime = null;
        this.lastActivityTime = new Date();
        
        // Configuration (will be loaded from server)
        this.config = {
            silentMode: true,
            autoStart: true,
            idleTimeout: 5 * 60 * 1000, // 5 minutes
            breakThreshold: 15 * 60 * 1000, // 15 minutes
            maxDailyHours: 8 * 60 * 60 * 1000, // 8 hours
        };
        
        // Intervals
        this.trackingInterval = null;
        this.syncInterval = null;
        
        this.init();
    }

    async init() {
        console.log('🔄 Initializing Silent Tracking Service...');
        
        // Load configuration from server
        await this.loadConfiguration();
        
        // Setup system event listeners
        this.setupSystemListeners();
        
        // Auto-start tracking if enabled
        if (this.config.autoStart) {
            await this.startTracking();
        }

        // Setup periodic sync
        this.setupPeriodicSync();

        console.log('✅ Silent Tracking Service initialized');
    }

    async loadConfiguration() {
        try {
            // Load from local storage first
            const localConfig = await this.store.get('trackingConfig');
            if (localConfig) {
                this.config = { ...this.config, ...localConfig };
            }

            // Try to fetch latest config from server
            const currentUser = await this.store.get('currentUser');
            if (currentUser) {
                await this.fetchServerConfiguration(currentUser.id);
            }
        } catch (error) {
            console.error('Failed to load configuration:', error);
        }
    }

    async fetchServerConfiguration(employeeId) {
        try {
            console.log('📡 Fetching server configuration for employee:', employeeId);

            // Try without auth token first (for testing)
            const response = await fetch(`http://127.0.0.1:8000/api/configuration/tracking?employee_id=${employeeId}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const result = await response.json();
                if (result.success && result.data) {
                    // Update configuration with server settings
                    await this.updateConfigurationFromServer(result.data);
                    console.log('📡 Configuration fetched from server successfully');
                } else {
                    console.error('Server returned error:', result.error);
                }
            } else {
                console.error('Failed to fetch configuration from server:', response.status);
            }
        } catch (error) {
            console.error('Error fetching server configuration:', error);
        }
    }

    async updateConfigurationFromServer(serverConfig) {
        try {
            // Update tracking service config
            if (serverConfig.time_tracking) {
                this.config = {
                    ...this.config,
                    silentMode: serverConfig.time_tracking.silent_mode_enabled,
                    autoStart: serverConfig.time_tracking.auto_start_tracking,
                    maxDailyHours: serverConfig.time_tracking.max_daily_hours * 60 * 60 * 1000, // Convert to milliseconds
                };
            }

            // Update idle detector config (simplified)
            if (this.idleDetector && serverConfig.idle_detection) {
                await this.idleDetector.updateConfiguration({
                    idleTimeoutEnabled: serverConfig.idle_detection.idle_timeout_enabled,
                    idleTimeoutMinutes: serverConfig.idle_detection.idle_timeout_minutes
                });
            }

            // Update time logger config
            if (this.timeLogger && serverConfig.time_tracking) {
                this.timeLogger.updateConfig({
                    minSessionDuration: serverConfig.time_tracking.min_session_duration,
                    maxSessionDuration: serverConfig.time_tracking.max_session_duration,
                    workDayStart: `${serverConfig.time_tracking.working_hours_start}:00`,
                    workDayEnd: `${serverConfig.time_tracking.working_hours_end}:00`,
                    maxDailyHours: serverConfig.time_tracking.max_daily_hours * 60 * 60 // Convert to seconds
                });
            }

            // Update screenshot service config
            if (this.screenshotService && serverConfig.screenshots) {
                await this.screenshotService.updateConfiguration({
                    enabled: serverConfig.screenshots.screenshot_enabled,
                    intervalSeconds: serverConfig.screenshots.screenshot_interval_seconds,
                    quality: serverConfig.screenshots.screenshot_quality,
                    burstMode: serverConfig.screenshots.screenshot_burst_mode,
                    randomInterval: serverConfig.screenshots.screenshot_random_interval
                });
            }



            // Update application tracker config
            if (this.applicationTracker && serverConfig.application_tracking) {
                await this.applicationTracker.updateConfiguration({
                    enabled: serverConfig.application_tracking.app_tracking_enabled,
                    trackWindowTitles: serverConfig.application_tracking.track_window_titles,
                    productivityCategorization: serverConfig.application_tracking.productivity_categorization
                });
            }

            // Update website tracker config
            if (this.websiteTracker && serverConfig.website_tracking) {
                await this.websiteTracker.updateConfiguration({
                    enabled: serverConfig.website_tracking.website_tracking_enabled,
                    trackBrowserTabs: serverConfig.website_tracking.track_browser_tabs,
                    websiteCategorization: serverConfig.website_tracking.website_categorization
                });
            }

            // Store server config locally for offline use
            await this.store.set('serverTrackingConfig', serverConfig);
            await this.store.set('configLastFetched', new Date().toISOString());

            console.log('⚙️ Configuration updated from server');
        } catch (error) {
            console.error('Failed to update configuration from server:', error);
        }
    }

    setupSystemListeners() {
        // Listen for system events
        powerMonitor.on('suspend', () => {
            console.log('💤 System suspended - pausing tracking');
            this.pauseTracking('system_suspend');
        });

        powerMonitor.on('resume', () => {
            console.log('🔄 System resumed - resuming tracking');
            this.resumeTracking('system_resume');
        });

        powerMonitor.on('lock-screen', () => {
            console.log('🔒 Screen locked - pausing tracking');
            this.pauseTracking('screen_lock');
        });

        powerMonitor.on('unlock-screen', () => {
            console.log('🔓 Screen unlocked - resuming tracking');
            this.resumeTracking('screen_unlock');
        });

        // Listen for status changes from StatusTracker
        if (this.statusTracker) {
            this.statusTracker.onStatusChange((newStatus, oldStatus) => {
                this.handleStatusChange(newStatus, oldStatus);
            });
        }
    }

    async startTracking() {
        if (this.isTracking) {
            console.log('⚠️ Tracking already started');
            return;
        }

        console.log('▶️ Starting silent tracking...');
        
        this.isTracking = true;
        this.trackingStartTime = new Date();
        this.lastActivityTime = new Date();
        
        // Create new session
        await this.createNewSession('work');

        // Start screenshot capture
        if (this.screenshotService) {
            await this.screenshotService.startCapturing();
        }

        // Start application tracking
        if (this.applicationTracker) {
            await this.applicationTracker.startTracking();
        }

        // Start website tracking
        if (this.websiteTracker) {
            await this.websiteTracker.startTracking();
        }

        // Start tracking interval (every 30 seconds)
        this.trackingInterval = setInterval(() => {
            this.trackTick();
        }, 30000);

        // Log tracking start
        await this.logActivity('tracking_started', 'Silent tracking started automatically');

        console.log('✅ Silent tracking started');
    }

    async stopTracking() {
        if (!this.isTracking) {
            console.log('⚠️ Tracking not active');
            return;
        }

        console.log('⏹️ Stopping tracking...');
        
        this.isTracking = false;
        
        // Clear intervals
        if (this.trackingInterval) {
            clearInterval(this.trackingInterval);
            this.trackingInterval = null;
        }

        // Stop screenshot capture
        if (this.screenshotService) {
            await this.screenshotService.stopCapturing();
        }

        // Stop application tracking
        if (this.applicationTracker) {
            await this.applicationTracker.stopTracking();
        }

        // Stop website tracking
        if (this.websiteTracker) {
            await this.websiteTracker.stopTracking();
        }

        // End current session
        await this.endCurrentSession();

        // Log tracking stop
        await this.logActivity('tracking_stopped', 'Silent tracking stopped');

        console.log('✅ Tracking stopped');
    }

    async pauseTracking(reason = 'manual') {
        if (!this.isTracking) return;

        console.log(`⏸️ Pausing tracking: ${reason}`);

        // Use TimeLogger's pause functionality
        await this.timeLogger.pauseSession(reason);
        this.currentSession = this.timeLogger.getCurrentSession();

        await this.logActivity('tracking_paused', `Tracking paused: ${reason}`);
    }

    async resumeTracking(reason = 'manual') {
        if (!this.isTracking) return;

        console.log(`▶️ Resuming tracking: ${reason}`);

        // Use TimeLogger's resume functionality
        await this.timeLogger.resumeSession(reason);
        this.currentSession = this.timeLogger.getCurrentSession();

        await this.logActivity('tracking_resumed', `Tracking resumed: ${reason}`);
    }

    async createNewSession(type = 'work', description = null) {
        try {
            this.currentSession = await this.timeLogger.startSession(type, description);
            console.log(`📝 New ${type} session created via TimeLogger`);
        } catch (error) {
            console.error('Failed to create session:', error);
        }
    }

    async endCurrentSession() {
        try {
            const endedSession = await this.timeLogger.endSession('tracking_service');
            this.currentSession = null;
            console.log(`✅ Session ended via TimeLogger`);
            return endedSession;
        } catch (error) {
            console.error('Failed to end session:', error);
        }
    }

    async trackTick() {
        if (!this.isTracking) return;

        try {
            // Update last activity time
            this.lastActivityTime = new Date();
            
            // Check if we need to handle idle state
            await this.checkIdleState();
            
            // Update current session metadata
            await this.updateSessionMetadata();
            
        } catch (error) {
            console.error('Tracking tick error:', error);
        }
    }

    async checkIdleState() {
        // Idle detection is now handled by IdleDetector
        // This method is kept for compatibility but delegates to IdleDetector
        try {
            const idleState = this.idleDetector.getCurrentIdleState();
            console.log(`🔍 Current idle state: ${idleState.state}`);
        } catch (error) {
            console.error('Idle check error:', error);
        }
    }

    async updateSessionMetadata() {
        if (!this.currentSession) return;

        try {
            const now = new Date();
            const metadata = {
                ...this.currentSession.metadata,
                lastUpdate: now.toISOString(),
                totalDuration: Math.floor((now - new Date(this.currentSession.startTime)) / 1000)
            };

            await this.dbService.run(`
                UPDATE time_logs 
                SET metadata = ?, updated_at = ?
                WHERE employee_id = ? AND start_time = ? AND end_time IS NULL
            `, [
                JSON.stringify(metadata),
                now.toISOString(),
                this.currentSession.employeeId,
                this.currentSession.startTime
            ]);
        } catch (error) {
            console.error('Failed to update session metadata:', error);
        }
    }

    handleStatusChange(newStatus, oldStatus) {
        console.log(`📊 Status changed: ${oldStatus} → ${newStatus}`);
        
        // Handle status-based tracking logic
        switch (newStatus) {
            case 'active':
                if (this.currentSession && this.currentSession.logType !== 'work') {
                    this.resumeTracking('status_active');
                }
                break;
            case 'idle':
                if (this.currentSession && this.currentSession.logType === 'work') {
                    this.pauseTracking('status_idle');
                }
                break;
            case 'inactive':
                if (this.currentSession && this.currentSession.logType === 'work') {
                    this.pauseTracking('status_inactive');
                }
                break;
        }
    }

    setupPeriodicSync() {
        // Sync with server every 5 minutes
        this.syncInterval = setInterval(async () => {
            await this.syncWithServer();
        }, 5 * 60 * 1000);

        // Cleanup old screenshots every hour
        this.cleanupInterval = setInterval(async () => {
            if (this.screenshotService) {
                await this.screenshotService.cleanup();
            }
        }, 60 * 60 * 1000); // 1 hour
    }

    async syncWithServer() {
        try {
            // Get unsynced time logs
            const unsyncedLogs = await this.dbService.all(`
                SELECT * FROM time_logs WHERE is_synced = 0 ORDER BY created_at ASC
            `);

            if (unsyncedLogs.length > 0) {
                console.log(`📤 Syncing ${unsyncedLogs.length} time logs...`);

                // Prepare data for sync
                const syncData = {
                    employee_id: unsyncedLogs[0].employee_id, // All records should have same employee_id
                    logs: unsyncedLogs.map(log => ({
                        log_date: log.log_date,
                        start_time: log.start_time,
                        end_time: log.end_time,
                        log_type: log.log_type,
                        duration: log.duration,
                        description: log.description,
                        device_id: log.device_id,
                        metadata: log.metadata ? JSON.parse(log.metadata) : null
                    }))
                };

                // Queue for sync using offline service
                if (this.offlineService) {
                    await this.offlineService.queueForSync('sync_time_logs', syncData);

                    // Mark as synced locally (will be updated if sync fails)
                    const logIds = unsyncedLogs.map(log => log.id);
                    await this.dbService.run(`
                        UPDATE time_logs SET is_synced = 1 WHERE id IN (${logIds.map(() => '?').join(',')})
                    `, logIds);

                    console.log(`✅ Time logs queued for sync`);
                }
            }

            // Sync screenshots
            if (this.screenshotService) {
                await this.syncScreenshots();
            }

            // Sync other tracking data
            await this.syncApplicationUsage();
            await this.syncWebsiteUsage();

            // Queue files for upload
            if (this.fileUploadService) {
                await this.fileUploadService.queueScreenshotsForUpload();
            }

            // Generate reports if needed
            if (this.reportsService) {
                const today = new Date().toISOString().split('T')[0];
                const existingReport = await this.reportsService.getReportByDate(today);
                if (!existingReport) {
                    await this.reportsService.generateDailyReport();
                }
            }

        } catch (error) {
            console.error('Sync error:', error);
        }
    }

    async syncScreenshots() {
        try {
            const unsyncedScreenshots = await this.screenshotService.getUnsyncedScreenshots();

            if (unsyncedScreenshots.length > 0) {
                console.log(`📤 Syncing ${unsyncedScreenshots.length} screenshots...`);

                // Prepare data for sync
                const syncData = {
                    screenshots: unsyncedScreenshots.map(screenshot => ({
                        employee_id: screenshot.employee_id,
                        captured_at: screenshot.captured_at,
                        capture_type: screenshot.capture_type,
                        file_path: screenshot.file_path,
                        file_name: screenshot.file_name,
                        file_size: screenshot.file_size,
                        resolution: screenshot.resolution,
                        device_id: screenshot.device_id,
                        active_application: screenshot.active_application,
                        active_window: screenshot.active_window,
                        metadata: screenshot.metadata ? JSON.parse(screenshot.metadata) : null
                    }))
                };

                // Queue for sync
                if (this.offlineService) {
                    await this.offlineService.queueForSync('sync_screenshots', syncData);

                    // Mark as synced locally
                    const screenshotIds = unsyncedScreenshots.map(s => s.id);
                    await this.screenshotService.markAsSynced(screenshotIds);

                    console.log(`✅ Screenshots queued for sync`);
                }
            }
        } catch (error) {
            console.error('Screenshot sync error:', error);
        }
    }



    async syncApplicationUsage() {
        try {
            const unsyncedUsage = await this.dbService.all(`
                SELECT * FROM application_usage WHERE is_synced = 0 ORDER BY created_at ASC
            `);

            if (unsyncedUsage.length > 0) {
                console.log(`📤 Syncing ${unsyncedUsage.length} application usage records...`);

                const syncData = {
                    employee_id: unsyncedUsage[0].employee_id, // All records should have same employee_id
                    usage_data: unsyncedUsage.map(usage => ({
                        usage_date: usage.usage_date,
                        application_name: usage.application_name,
                        application_path: usage.application_path,
                        window_title: usage.window_title,
                        start_time: usage.start_time,
                        end_time: usage.end_time,
                        duration: usage.duration,
                        category: usage.category,
                        switch_count: usage.switch_count,
                        device_id: usage.device_id,
                        metadata: usage.metadata ? JSON.parse(usage.metadata) : null
                    }))
                };

                if (this.offlineService) {
                    await this.offlineService.queueForSync('sync_application_usage', syncData);

                    // Mark as synced
                    const usageIds = unsyncedUsage.map(u => u.id);
                    await this.dbService.run(`
                        UPDATE application_usage SET is_synced = 1 WHERE id IN (${usageIds.map(() => '?').join(',')})
                    `, usageIds);

                    console.log(`✅ Application usage queued for sync`);
                }
            }
        } catch (error) {
            console.error('Application usage sync error:', error);
        }
    }

    async syncWebsiteUsage() {
        try {
            const unsyncedWebsites = await this.dbService.all(`
                SELECT * FROM website_usage WHERE is_synced = 0 ORDER BY created_at ASC
            `);

            if (unsyncedWebsites.length > 0) {
                console.log(`📤 Syncing ${unsyncedWebsites.length} website usage records...`);

                const syncData = {
                    employee_id: unsyncedWebsites[0].employee_id, // All records should have same employee_id
                    usage_data: unsyncedWebsites.map(website => ({
                        visit_date: website.visit_date,
                        url: website.url,
                        domain: website.domain,
                        page_title: website.page_title,
                        visit_time: website.visit_time,
                        leave_time: website.leave_time,
                        duration: website.duration,
                        category: website.category,
                        productivity_level: website.productivity_level,
                        browser_name: website.browser_name,
                        visit_count: website.visit_count,
                        device_id: website.device_id,
                        metadata: website.metadata ? JSON.parse(website.metadata) : null
                    }))
                };

                if (this.offlineService) {
                    await this.offlineService.queueForSync('sync_website_usage', syncData);

                    // Mark as synced
                    const websiteIds = unsyncedWebsites.map(w => w.id);
                    await this.dbService.run(`
                        UPDATE website_usage SET is_synced = 1 WHERE id IN (${websiteIds.map(() => '?').join(',')})
                    `, websiteIds);

                    console.log(`✅ Website usage queued for sync`);
                }
            }
        } catch (error) {
            console.error('Website usage sync error:', error);
        }
    }

    async logActivity(action, details) {
        try {
            const currentUser = await this.store.get('currentUser');
            if (currentUser) {
                await this.dbService.run(`
                    INSERT INTO activity_log (user_id, action, details, timestamp)
                    VALUES (?, ?, ?, ?)
                `, [currentUser.id, action, details, new Date().toISOString()]);
            }
        } catch (error) {
            console.error('Failed to log activity:', error);
        }
    }

    async getDeviceId() {
        try {
            const { machineId } = require('node-machine-id');
            return await machineId();
        } catch (error) {
            return 'unknown-device';
        }
    }

    // Public methods for external control
    getTrackingStatus() {
        return {
            isTracking: this.isTracking,
            currentSession: this.currentSession,
            trackingStartTime: this.trackingStartTime,
            config: this.config
        };
    }

    async updateConfiguration(newConfig) {
        this.config = { ...this.config, ...newConfig };
        await this.store.set('trackingConfig', this.config);
        console.log('⚙️ Configuration updated');
    }

    async getTodayStats() {
        try {
            return this.timeLogger.getDailyStats();
        } catch (error) {
            console.error('Failed to get today stats:', error);
            return null;
        }
    }

    async getWeeklyStats() {
        try {
            return await this.timeLogger.getWeeklyStats();
        } catch (error) {
            console.error('Failed to get weekly stats:', error);
            return null;
        }
    }

    async getDetailedTodayStats() {
        try {
            const currentUser = await this.store.get('currentUser');
            if (!currentUser) return null;

            const today = new Date().toISOString().split('T')[0];

            const sessions = await this.dbService.all(`
                SELECT
                    id, start_time, end_time, log_type, duration, description,
                    metadata
                FROM time_logs
                WHERE employee_id = ? AND log_date = ?
                ORDER BY start_time ASC
            `, [currentUser.id, today]);

            const dailyStats = { ...this.timeLogger.getDailyStats() };
            const currentSession = this.timeLogger.getCurrentSession();

            // Include current active session duration in stats
            if (currentSession && currentSession.startTime) {
                const now = new Date();
                const sessionStart = new Date(currentSession.startTime);
                const currentSessionDuration = Math.floor((now - sessionStart) / 1000);

                // Add current session duration to the appropriate stat
                switch (currentSession.type) {
                    case 'work':
                        dailyStats.workTime += currentSessionDuration;
                        break;
                    case 'break':
                        dailyStats.breakTime += currentSessionDuration;
                        break;
                    case 'idle':
                        dailyStats.idleTime += currentSessionDuration;
                        break;
                }
            }

            // Process sessions and add virtual break/idle sessions
            const processedSessions = sessions.map(session => {
                let duration = session.duration || 0;

                // Calculate duration for active sessions (no end_time)
                if (!session.end_time && session.start_time) {
                    const now = new Date();
                    const sessionStart = new Date(session.start_time);
                    duration = Math.floor((now - sessionStart) / 1000);
                }

                return {
                    ...session,
                    duration: duration,
                    metadata: session.metadata ? JSON.parse(session.metadata) : null,
                    durationFormatted: this.formatDuration(duration)
                };
            });

            // Add virtual break and idle sessions based on gaps
            const allSessions = this.addVirtualBreakAndIdleSessions(processedSessions);

            return {
                summary: dailyStats,
                sessions: allSessions,
                currentSession: currentSession,
                isWorkingHours: this.timeLogger.isWorkingHours()
            };
        } catch (error) {
            console.error('Failed to get detailed today stats:', error);
            return null;
        }
    }

    addVirtualBreakAndIdleSessions(workSessions) {
        const allSessions = [...workSessions];

        // Sort work sessions by start time
        const sortedWorkSessions = workSessions
            .filter(s => s.log_type === 'work' && s.end_time) // Only completed work sessions
            .sort((a, b) => new Date(a.start_time) - new Date(b.start_time));

        // Add virtual break sessions for gaps between work sessions
        for (let i = 0; i < sortedWorkSessions.length - 1; i++) {
            const currentSession = sortedWorkSessions[i];
            const nextSession = sortedWorkSessions[i + 1];

            const currentEnd = new Date(currentSession.end_time);
            const nextStart = new Date(nextSession.start_time);
            const gapDuration = Math.floor((nextStart - currentEnd) / 1000);

            // Only create break session if gap is significant (more than 30 seconds)
            if (gapDuration > 30) {
                const breakSession = {
                    id: `virtual_break_${i}`,
                    start_time: currentSession.end_time,
                    end_time: nextSession.start_time,
                    log_type: 'break',
                    duration: gapDuration,
                    description: 'Break period (calculated from gap)',
                    metadata: { virtual: true, gapBetween: [currentSession.id, nextSession.id] },
                    durationFormatted: this.formatDuration(gapDuration)
                };
                allSessions.push(breakSession);
            }
        }

        // Add virtual idle sessions for sessions that ended due to idle timeout
        workSessions.forEach(session => {
            if (session.metadata && session.metadata.endReason === 'idle_timeout_reached') {
                // Create virtual idle session that represents the idle period
                const idleSession = {
                    id: `virtual_idle_${session.id}`,
                    start_time: session.end_time,
                    end_time: session.end_time, // Instant idle detection
                    log_type: 'idle',
                    duration: 0, // Idle detection is instant
                    description: 'Idle time detected',
                    metadata: { virtual: true, triggeredBy: session.id },
                    durationFormatted: 'Instant'
                };
                allSessions.push(idleSession);
            }
        });

        // Sort all sessions by start time
        return allSessions.sort((a, b) => new Date(a.start_time) - new Date(b.start_time));
    }

    formatDuration(seconds) {
        if (!seconds) return '0s';

        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;

        if (hours > 0) {
            return `${hours}h ${minutes}m`;
        } else if (minutes > 0) {
            return `${minutes}m ${secs}s`;
        } else {
            return `${secs}s`;
        }
    }

    // Idle detection methods
    getIdleDetectionStatus() {
        if (this.idleDetector) {
            return this.idleDetector.getCurrentIdleState();
        }
        return null;
    }

    async updateIdleDetectionConfig(config) {
        if (this.idleDetector) {
            await this.idleDetector.updateConfiguration(config);
            return true;
        }
        return false;
    }

    getIdleDetectionConfig() {
        if (this.idleDetector) {
            return this.idleDetector.getConfiguration();
        }
        return null;
    }

    triggerManualActivity() {
        if (this.idleDetector) {
            this.idleDetector.manualActivityTrigger('manual_trigger');
            return true;
        }
        return false;
    }

    // Screenshot service methods
    getScreenshotStatus() {
        if (this.screenshotService) {
            return this.screenshotService.getStatistics();
        }
        return null;
    }

    async captureManualScreenshot() {
        if (this.screenshotService) {
            return await this.screenshotService.captureScreenshot('manual');
        }
        return null;
    }

    async getRecentScreenshots(limit = 10) {
        if (this.screenshotService) {
            return await this.screenshotService.getRecentScreenshots(limit);
        }
        return [];
    }

    async updateScreenshotConfig(config) {
        if (this.screenshotService) {
            await this.screenshotService.updateConfiguration(config);
            return true;
        }
        return false;
    }

    getScreenshotConfig() {
        if (this.screenshotService) {
            return this.screenshotService.getConfiguration();
        }
        return null;
    }

    async startTestScreenshots() {
        if (this.screenshotService) {
            await this.screenshotService.startTestCapture();
            return true;
        }
        return false;
    }

    getScreenshotDebugInfo() {
        if (this.screenshotService) {
            return this.screenshotService.getDebugInfo();
        }
        return null;
    }

    async forceReloadConfiguration() {
        console.log('🔄 Force reloading configuration from server...');
        try {
            const currentUser = await this.store.get('currentUser');
            if (currentUser) {
                await this.fetchServerConfiguration(currentUser.id);
                return true;
            }
            return false;
        } catch (error) {
            console.error('Failed to force reload configuration:', error);
            return false;
        }
    }

    async destroy() {
        console.log('🔄 Destroying tracking service...');

        await this.stopTracking();

        if (this.syncInterval) {
            clearInterval(this.syncInterval);
        }

        if (this.cleanupInterval) {
            clearInterval(this.cleanupInterval);
        }

        if (this.idleDetector) {
            await this.idleDetector.destroy();
        }

        if (this.screenshotService) {
            await this.screenshotService.destroy();
        }

        if (this.applicationTracker) {
            await this.applicationTracker.destroy();
        }

        if (this.websiteTracker) {
            await this.websiteTracker.destroy();
        }

        if (this.reportsService) {
            await this.reportsService.destroy();
        }

        if (this.fileUploadService) {
            await this.fileUploadService.destroy();
        }

        console.log('✅ Tracking service destroyed');
    }
}

module.exports = TrackingService;
