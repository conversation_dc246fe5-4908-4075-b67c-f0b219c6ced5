<?php

namespace App\Controller\Admin;

use App\Entity\Announcement;
use App\Entity\User;
use App\Form\AnnouncementType;
use App\Message\AnnouncementNotificationMessage;
use App\Service\Admin\AnnouncementService;
use App\Traits\ExceptionLoggerTrait;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Messenger\Exception\ExceptionInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Csrf\CsrfToken;
use Symfony\Component\Security\Csrf\CsrfTokenManagerInterface;

#[Route('/admin')]
class AnnouncementController extends AbstractController
{
    use ExceptionLoggerTrait;

    public function __construct(
        ParameterBagInterface $parameterBag,
        private readonly EntityManagerInterface $entityManager,
        private readonly AnnouncementService $announcementService,
        private readonly MessageBusInterface $messageBus
    ) {
        $this->initializeLogger($parameterBag, 'announcement_controller');
    }

    #[Route(path: '/announcement', name: 'admin_announcement', methods: ['GET', 'POST'])]
    public function index(Request $request): Response
    {
        $table = $this->announcementService->createAnnouncementTable($request);

        if ($table->isCallback()) {
            return $table->getResponse();
        }

        return $this->render('admin/Announcement/index.html.twig', [
            'datatable' => $table,
        ]);
    }

    /**
     * @throws ExceptionInterface
     */
    #[Route('/announcement/create', name: 'admin_announcement_create', methods: ['GET', 'POST'])]
    public function createAnnouncement(Request $request): Response
    {
        $admin = $this->getUser();
        if (!$admin instanceof User) {
            throw $this->createAccessDeniedException('You must be logged in as an admin to view this page.');
        }
        $announcement = new Announcement();
        $announcement->setCreatedBy($admin);

        $form = $this->createForm(AnnouncementType::class, $announcement);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $this->entityManager->persist($announcement);
            $this->entityManager->flush();

            if ($announcement->getSendEmail()) {
                $this->messageBus->dispatch(new AnnouncementNotificationMessage($announcement->getId()));
            }

            $this->addFlash('success', 'Announcement created successfully.');
            return $this->redirectToRoute('admin_announcement');
        }

        return $this->render('admin/Announcement/new.html.twig', [
            'form' => $form->createView(),
            'mode' => 'create',
        ]);
    }

    #[Route('/announcement/edit/{id}', name: 'admin_announcement_edit', methods: ['GET', 'POST'])]
    public function editAnnouncement(Request $request, Announcement $announcement): Response
    {
        $form = $this->createForm(AnnouncementType::class, $announcement);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $this->entityManager->flush();

            if ($announcement->getSendEmail() && !$announcement->getEmailSentAt()) {
                $this->messageBus->dispatch(new AnnouncementNotificationMessage($announcement->getId()));
            }

            $this->addFlash('success', 'Announcement updated successfully.');
            return $this->redirectToRoute('admin_announcement');
        }

        return $this->render('admin/Announcement/new.html.twig', [
            'form' => $form->createView(),
            'announcement' => $announcement,
            'mode' => 'edit',
        ]);
    }

    #[Route('/announcement/delete/{id}', name: 'admin_announcement_delete', methods: ['POST'])]
    public function deleteAnnouncement(
        Request $request,
        Announcement $announcement,
        CsrfTokenManagerInterface $csrfTokenManager
    ): Response {
        $token = $request->request->get('_token');

        if (!$csrfTokenManager->isTokenValid(new CsrfToken('delete' . $announcement->getId(), $token))) {
            return $this->json([
                'success' => false,
                'message' => 'Invalid CSRF token.'
            ], Response::HTTP_FORBIDDEN);
        }

        $this->entityManager->remove($announcement);
        $this->entityManager->flush();

        return $this->json([
            'success' => true,
            'message' => 'Announcement has been deleted.'
        ], Response::HTTP_OK);
    }

    #[Route('/announcement/view/{id}', name: 'admin_announcement_view', methods: ['GET'])]
    public function show(Announcement $announcement): Response
    {
        return $this->render('admin/Announcement/new.html.twig', [
            'announcement' => $announcement,
            'mode' => 'view',
        ]);
    }
}