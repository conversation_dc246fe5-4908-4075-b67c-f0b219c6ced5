<?php

namespace App\Controller\Front;

use App\Entity\EmployeeDocument;
use App\Entity\MasterEmployee;
use App\Form\EmployeeDocumentType;
use App\Service\Common\EmployeeDocumentService;
use App\Traits\ExceptionLoggerTrait;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Csrf\CsrfToken;
use Symfony\Component\Security\Csrf\CsrfTokenManagerInterface;

#[Route('/my')]
class EmployeeDocumentController extends AbstractController
{
    use ExceptionLoggerTrait;

    public function __construct(
        private readonly EntityManagerInterface    $entityManager,
        private readonly EmployeeDocumentService   $documentService,
        private readonly CsrfTokenManagerInterface $csrfTokenManager
    ) {
    }

    #[Route('/employee/documents', name: 'my_employee_documents', methods: ['GET', 'POST'])]
    public function index(Request $request): Response
    {
        $employee = $this->getUser();
        if (!$employee instanceof MasterEmployee) {
            throw $this->createAccessDeniedException('This can only be accessed by an employee.');
        }
        $table = $this->documentService->createDocumentTable($request, $employee);

        if ($table->isCallback()) {
            return $table->getResponse();
        }

        return $this->render('Front/EmployeeDocument/index.html.twig', [
            'datatable' => $table,
            'employee' => $employee,
        ]);
    }

    #[Route('/employee/documents/new', name: 'my_employee_document_new', methods: ['GET', 'POST'])]
    public function new(Request $request): Response
    {
        $employee = $this->getUser();
        if (!$employee instanceof MasterEmployee) {
            throw $this->createAccessDeniedException('This can only be accessed by an employee.');
        }
        $document = new EmployeeDocument();
        $document->setMasterEmployee($employee);

        $form = $this->createForm(EmployeeDocumentType::class, $document, ['is_new' => true]);
        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            try {
                $file = $form->get('file')->getData();
                if (!$file) {
                    $this->addFlash('error', 'Please select a file to upload.');
                    return $this->redirectToRoute('my_employee_document_new');
                }

                if ($file->getSize() > 5 * 1024 * 1024) {
                    $this->addFlash('error', 'File size must be less than 5MB.');
                    return $this->redirectToRoute('my_employee_document_new');
                }

                $this->documentService->handleFileUpload($file, $document);
                $document->setUploadedBy($employee);

                $this->entityManager->persist($document);
                $this->entityManager->flush();

                $this->addFlash('success', 'Document uploaded successfully.');
                return $this->redirectToRoute('my_employee_documents');

            } catch (\Exception $e) {
                $this->addFlash('error', 'Error uploading document: ' . $e->getMessage());
                return $this->redirectToRoute('my_employee_document_new');
            }
        }

        return $this->render('Front/EmployeeDocument/new.html.twig', [
            'form' => $form->createView(),
            'employee' => $employee,
            'mode' => 'create',
        ]);
    }

    #[Route('/employee/documents/edit/{id}', name: 'my_employee_document_edit', methods: ['GET', 'POST'])]
    public function edit(Request $request, EmployeeDocument $document): Response
    {
        $employee = $this->getUser();
        if (!$employee instanceof MasterEmployee || $document->getMasterEmployee()->getId() !== $employee->getId()) {
            throw $this->createAccessDeniedException('You are not allowed to edit this document.');
        }
        $form = $this->createForm(EmployeeDocumentType::class, $document, ['is_new' => false]);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $file = $form->get('file')->getData();
            if ($file) {
                $this->documentService->deleteFile($document);
                $this->documentService->handleFileUpload($file, $document);
            }
            $this->entityManager->flush();

            $this->addFlash('success', 'Document updated successfully.');
            return $this->redirectToRoute('my_employee_documents');
        }

        return $this->render('Front/EmployeeDocument/new.html.twig', [
            'form' => $form->createView(),
            'document' => $document,
            'employee' => $document->getMasterEmployee(),
            'mode' => 'edit',
        ]);
    }

    #[Route('/employee/documents/delete/{id}', name: 'my_employee_document_delete', methods: ['POST'])]
    public function delete(Request $request, EmployeeDocument $document): Response
    {
        $employee = $this->getUser();
        if (!$employee instanceof MasterEmployee || $document->getMasterEmployee()->getId() !== $employee->getId()) {
            throw $this->createAccessDeniedException('You are not allowed to delete this document.');
        }
        $token = $request->request->get('_token');
        if (!$this->csrfTokenManager->isTokenValid(new CsrfToken('delete' . $document->getId(), $token))) {
            return $this->json([
                'success' => false,
                'message' => 'Invalid CSRF token.'
            ], Response::HTTP_FORBIDDEN);
        }

        $this->documentService->deleteFile($document);
        $this->entityManager->remove($document);
        $this->entityManager->flush();

        return $this->json([
            'success' => true,
            'message' => 'Document has been deleted.'
        ], Response::HTTP_OK);
    }

    #[Route('/api/check-document-type', name: 'api_check_document_type', methods: ['GET'])]
    public function checkDocumentType(Request $request): JsonResponse
    {
        $employeeId = $request->query->get('employeeId');
        $documentType = $request->query->get('documentType');

        if ($documentType === 'OTHER') {
            return new JsonResponse(['exists' => false]);
        }

        $exists = $this->entityManager->getRepository(EmployeeDocument::class)
                ->findOneBy([
                    'masterEmployee' => $employeeId,
                    'documentType' => $documentType,
                ]) !== null;

        return new JsonResponse(['exists' => $exists]);
    }

}