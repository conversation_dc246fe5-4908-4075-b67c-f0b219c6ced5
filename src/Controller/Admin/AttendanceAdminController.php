<?php

namespace App\Controller\Admin;

use App\Service\Admin\AttendanceAdminService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class AttendanceAdminController extends AbstractController
{
    public function __construct(
        private readonly AttendanceAdminService $attendanceAdminService
    ) {
    }

    #[Route('/admin/attendance/calendar', name: 'admin_attendance_calendar', methods: ['GET'])]
    public function index(): Response
    {
        return $this->render('CommonTemplate/adminCalender.html.twig');
    }

    /**
     * @throws \DateMalformedStringException
     */
    #[Route('/admin/attendance/calendar-data', name: 'admin_attendance_calendar_data', methods: ['GET'])]
    public function getCalendarData(Request $request): JsonResponse
    {
        $startDate = $request->query->get('start');
        $endDate = $request->query->get('end');
        if ($startDate && strpos($startDate, ' ') !== false) {
            $startDate = preg_replace('/ /', '+', $startDate);
        }
        $startDateObj = $startDate ? new \DateTime($startDate) : new \DateTime('first day of this month 00:00:00');
        if ($endDate && strpos($endDate, ' ') !== false) {
            $endDate = preg_replace('/ /', '+', $endDate);
        }
        $endDateObj = $endDate ? new \DateTime($endDate) : new \DateTime('last day of this month 23:59:59');
        $calendarData = $this->attendanceAdminService->getAttendanceCalendarData($startDateObj, $endDateObj);

        return new JsonResponse($calendarData['events']);
    }

    #[Route('/admin/attendance/date-details/{date}', name: 'admin_attendance_date_details', methods: ['GET'])]
    public function getDateDetails(string $date): JsonResponse
    {
        if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $date)) {
            return new JsonResponse(['error' => 'Invalid date format'], 400);
        }

        $details = $this->attendanceAdminService->getDateAttendanceDetails($date);

        return new JsonResponse($details);
    }
}