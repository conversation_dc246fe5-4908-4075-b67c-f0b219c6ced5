<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{% endblock %} - BrainStream</title>
    <link rel="icon" type="image/x-icon" href="{{ asset('images/Brainstream-favicon.png') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/Login/bootstrap-login.min.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/Login/login.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/font-awesome.min.css') }}">
</head>
<body>
<div class="login-container">
    <div class="login-card">
        <div class="employee-badge">{% block badge %}{% endblock %}</div>
        <div class="row g-0">
            <div class="col-lg-6 image-side d-none d-lg-block">
                <img src="{{ asset('images/login-image.png') }}" alt="Team collaboration" class="login-image">
                <div class="image-caption">
                    <p class="welcome-quote mb-0 text-center">"Turning Ideas Into a Successful Business."</p>
                </div>
            </div>
            <div class="col-lg-6 form-side">
                <div class="logo-container">
                    <img src="{{ asset('images/GetMedia.png') }}" alt="BrainStream logo" class="img-fluid">
                </div>
                <form action="{{ path(login_route) }}" method="post" class="v-form custom-form" id="loginForm" autocomplete="off">
                    {% if error %}
                        <div class="alert alert-danger">
                            <i class="fa fa-exclamation-circle me-2"></i>
                            {{ error.messageKey|trans(error.messageData, 'security') }}
                        </div>
                    {% endif %}

                    <div class="input-group mb-3">
                        <input type="email" value="{{ last_username }}" name="_username" class="form-control input-with-icon" style="padding-top: 0.625rem;" id="email" placeholder="Email" />
                        <i class="fa fa-envelope input-icon"></i>
                    </div>

                    <div class="input-group mb-3">
                        <input type="password" class="form-control input-with-icon" style="padding-top: 0.625rem;" id="password" name="_password" placeholder="Password">
                        <i class="fa fa-lock input-icon"></i>
                        <i class="fa fa-eye toggle-password" id="togglePassword"></i>
                    </div>

                    <div class="form-footer">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" name="_remember_me" id="remember_me">
                            <label class="form-check-label" for="remember_me">Remember me</label>
                        </div>
                        <a href="{{ url(forgot_password_route) }}" class="text-orange text-decoration-none">Forgot password?</a>
                    </div>

                    <button type="submit" class="btn btn-orange w-100 mb-3">
                        <i class="fa fa-sign-in me-2"></i>Sign In
                    </button>
                    <input type="hidden" name="_csrf_token" value="{{ csrf_token('authenticate') }}">
                </form>
            </div>
        </div>
    </div>
</div>
<script src="{{ asset('assets/js/bootstrap.bundle.min.js') }}"></script>
<script src="{{ asset('assets/js/login/login.js') }}"></script>
</body>
</html>