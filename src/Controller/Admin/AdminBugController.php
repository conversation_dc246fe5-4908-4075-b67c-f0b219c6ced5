<?php

namespace App\Controller\Admin;

use App\Entity\Bug;
use App\Entity\BugStatusHistory;
use App\Entity\Status;
use App\Entity\User;
use App\Form\BugType;
use App\Message\BugNotificationMessage;
use App\Repository\TaskRepository;
use App\Service\Common\BugService;
use App\Traits\ExceptionLoggerTrait;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Messenger\Exception\ExceptionInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Csrf\CsrfToken;
use Symfony\Component\Security\Csrf\CsrfTokenManagerInterface;

#[Route('/admin')]
class AdminBugController extends AbstractController
{
    use ExceptionLoggerTrait;
    public function __construct(
        private readonly BugService $bugService,
        private readonly EntityManagerInterface $entityManager,
        private readonly CsrfTokenManagerInterface $csrfTokenManager,
        private readonly MessageBusInterface $messageBus,
    ) {}

    #[Route('/bug', name: 'admin_bug_index', methods: ['GET', 'POST'])]
    public function index(Request $request): Response
    {
        $user = $this->getUser();
        if (!$user instanceof User) {
            throw $this->createAccessDeniedException('This can only be accessed by an admin.');
        }
        $table = $this->bugService->adminBugTable($request, $user);

        if ($table->isCallback()) {
            return $table->getResponse();
        }

        return $this->render('admin/Bug/index.html.twig', [
            'datatable' => $table
        ]);
    }

    /**
     * @throws \Exception
     * @throws ExceptionInterface
     */
    #[Route('/bug/new', name: 'admin_bug_new', methods: ['GET', 'POST'])]
    public function new(Request $request): Response
    {
        $user = $this->getUser();
        if (!$user instanceof User) {
            throw $this->createAccessDeniedException('This can only be accessed by an admin.');
        }
        $bug = new Bug();
        $bug->setAssignedByAdmin($user);
        $form = $this->createForm(BugType::class, $bug);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $bug->setCreatedAt(new \DateTime());
            $this->entityManager->persist($bug);
            $this->entityManager->flush();

            $statusHistory = new BugStatusHistory();
            $statusHistory->setBug($bug);
            $statusHistory->setChangedBy(null);
            $statusHistory->setChangedByAdmin($user);
            $statusHistory->setStatus($bug->getStatus());
            $statusHistory->setChangedAt(new \DateTime());
            $this->entityManager->persist($statusHistory);
            $this->entityManager->flush();
            try {
                $this->messageBus->dispatch(new BugNotificationMessage(
                    $bug->getId(),
                    $bug->getAssignedTo()->getId(),
                    'create',
                    []
                ));
            } catch (\Throwable $e) {
                $this->log('Notification dispatch failed: ' . $e->getMessage());
            }
            $this->addFlash('success', 'Bug created successfully.');
            return $this->redirectToRoute('admin_bug_index');
        }

        return $this->render('admin/Bug/new.html.twig', [
            'form' => $form->createView(),
            'mode' => 'create',
        ]);
    }


    #[Route('/bug/{id}/edit', name: 'admin_bug_edit', methods: ['GET', 'POST'])]
    public function edit(Request $request, int $id): Response
    {
        $user = $this->getUser();
        if (!$user instanceof User) {
            throw $this->createAccessDeniedException('This can only be accessed by an admin.');
        }

        $bug = $this->entityManager->getRepository(Bug::class)->find($id);
        if (!$bug) {
            throw new NotFoundHttpException('Bug not found');
        }

        if ($bug->getAssignedBy() !== $user && !$this->isGranted('ROLE_ADMIN')) {
            throw $this->createAccessDeniedException('You are not allowed to edit this bug.');
        }

        $form = $this->createForm(BugType::class, $bug);
        $originalDescription = $bug->getComments();
        $originalStatus = $bug->getStatus();
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $employee = $this->getUser();
            $newStatus = $bug->getStatus();
            $newDescription = $bug->getComments();
            if ($originalStatus !== $newStatus) {
                $statusHistory = new BugStatusHistory();
                $statusHistory->setBug($bug);
                $statusHistory->setChangedBy(null);
                /** @var User $employee */
                $statusHistory->setChangedByAdmin($employee);
                $statusHistory->setStatus($bug->getStatus());
                $statusHistory->setChangedAt(new \DateTime());
                $this->entityManager->persist($statusHistory);
            }
            $bug->setUpdatedAt(new \DateTime());
            $this->entityManager->persist($bug);
            $this->entityManager->flush();
            try {
                $changes = [];
                if ($originalDescription !== $newDescription) {
                    $changes[] = 'description';
                }
                if ($originalStatus !== $newStatus) {
                    $changes[] = 'status';
                }

                if (!empty($changes)) {
                    $this->messageBus->dispatch(new BugNotificationMessage(
                        $bug->getId(),
                        $bug->getAssignedTo()->getId(),
                        'update',
                        $changes
                    ));
                }
            } catch (\Throwable $e) {
                $this->log('Notification dispatch failed: ' . $e->getMessage());
            }
            $this->addFlash('success', 'Bug updated successfully.');
            return $this->redirectToRoute('admin_bug_index');
        }

        return $this->render('admin/Bug/new.html.twig', [
            'form' => $form->createView(),
            'bug' => $bug,
            'mode' => 'edit',
        ]);
    }

    #[Route('/bug/{id}/update-status', name: 'admin_bug_update_status', methods: ['POST'])]
    public function updateStatus(Request $request, int $id): JsonResponse
    {
        $user = $this->getUser();
        if (!$user instanceof User) {
            throw $this->createAccessDeniedException('This can only be accessed by an admin.');
        }

        $bug = $this->entityManager->getRepository(Bug::class)->find($id);
        if (!$bug) {
            return $this->json(['success' => false, 'error' => 'Bug not found'], 404);
        }

        if ($bug->getAssignedTo() !== $user) {
            return $this->json(['success' => false, 'error' => 'You are not authorized to update this bug'], 403);
        }

        $csrfToken = $request->request->get('_csrf_token');
        if (!$this->csrfTokenManager->isTokenValid(new CsrfToken('update_status_' . $id, $csrfToken))) {
            return $this->json(['success' => false, 'error' => 'Invalid CSRF token'], 400);
        }

        $status = $request->request->get('status');
        $testerComments = $request->request->get('testerComments');

        try {
            $statusEnum = Status::from($status);
            $oldStatus = $bug->getStatus();
            $bug->setStatus($statusEnum);
            $bug->setUpdatedAt(new \DateTime());

            if ($testerComments !== null) {
                $bug->setTesterComments($testerComments);
            }
            if ($oldStatus !== $statusEnum) {
                $statusHistory = new BugStatusHistory();
                $statusHistory->setBug($bug);
                $statusHistory->setStatus($statusEnum);
                $statusHistory->setChangedBy(null);
                $statusHistory->setChangedByAdmin($user);
                $statusHistory->setChangedAt(new \DateTime());

                $this->entityManager->persist($statusHistory);
            }

            $this->entityManager->persist($bug);
            $this->entityManager->flush();

            return $this->json(['success' => true]);
        } catch (\Throwable $e) {
            return $this->json(['success' => false, 'error' => 'An error occurred'], 500);
        }
    }

    #[Route('/api/tasks', name: 'admin_api_tasks')]
    public function getAdminTasks(Request $request, TaskRepository $taskRepository): JsonResponse
    {
        $projectId = $request->query->get('project');
        $tasks = $taskRepository->findBy(['project' => $projectId]);

        $data = array_map(function ($task) {
            return [
                'id' => $task->getId(),
                'title' => $task->getTitle(),
            ];
        }, $tasks);

        return $this->json($data);
    }
}
?>