<?php

namespace App\Controller\Admin;

use App\Repository\EmployeeNotificationRepository;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Doctrine\ORM\EntityManagerInterface;

#[Route('/admin')]
class EmployeeNotificationController extends AbstractController
{
    #[Route('/justify/{uuid}', name: 'justify_hours', methods: ['GET', 'POST'])]
    public function justifyHours(
        string $uuid,
        Request $request,
        EntityManagerInterface $entityManager,
        EmployeeNotificationRepository $notificationRepository
    ): Response {
        $notification = $notificationRepository->findOneBy(['uuid' => $uuid]);

        if (!$notification) {
            return $this->redirectToRoute('error_message');
        }

        if ($notification->getStatus() === true) {
            return $this->redirectToRoute('success_message');
        }

        $now = new \DateTime();
        $interval = $now->diff($notification->getSentAt());
        if ($interval->days >= 7) {
            return $this->redirectToRoute('expired_message');
        }
        if ($request->isMethod('POST')) {
            $justificationType = trim($request->request->get('justification_type'));
            $justificationText = trim($request->request->get('justification_text'));


            if (empty($justificationType)) {
                $this->addFlash('error', 'Please select a justification type.');
                return $this->render('admin/justify_issue.html.twig', [
                    'notification' => $notification,
                ]);
            }

            $fullJustification = $this->buildJustificationText($justificationType, $justificationText);

            try {
                $notification->setJustification($fullJustification);
                $notification->setStatus(true);
                $notification->setJustifiedAt(new \DateTime());
                $entityManager->flush();
                return $this->redirectToRoute('success_message');
            } catch (\Exception $e) {
                return $this->redirectToRoute('error_message');
            }
        }

        return $this->render('admin/justify_issue.html.twig', [
            'notification' => $notification,
        ]);
    }

    #[Route('/success', name: 'success_message')]
    public function justifySuccess(): Response
    {
        return $this->render('admin/justification_response.html.twig', [
            'status' => 'success',
            'message' => 'Your justification has been submitted successfully.'
        ]);
    }

    #[Route('/expired', name: 'expired_message')]
    public function justifyExpired(): Response
    {
        return $this->render('admin/justification_response.html.twig', [
            'status' => 'expired',
            'message' => 'This justification link has expired. Please contact your supervisor.'
        ]);
    }

    #[Route('/error', name: 'error_message')]
    public function justifyError(): Response
    {
        return $this->render('admin/justification_response.html.twig', [
            'status' => 'error',
            'message' => 'Notification not found.'
        ]);
    }
    private function buildJustificationText(string $type, ?string $additionalText = null): string
    {
        $justificationMap = [
            'full_day_leave' => 'Full Day Leave',
            'half_day_leave' => 'Half Day Leave',
            'other' => 'Other'
        ];

        $justification = $justificationMap[$type] ?? 'Unknown';

        if ($type === 'other' && !empty($additionalText)) {
            $justification = $additionalText;
        }

        return $justification;
    }
}