<?php
namespace App\Controller;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\HttpExceptionInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;

class ExceptionController extends AbstractController
{
    public function show(Request $request, \Throwable $exception): Response
    {
        dd($exception);
        $statusCode = $exception instanceof HttpExceptionInterface ? $exception->getStatusCode() : 500;

        $statusText = Response::$statusTexts[$statusCode] ?? 'Internal Server Error';

        $path = $request->getPathInfo();

        if ($statusCode === 404) {
            if (str_starts_with($path, '/admin')) {
                return $this->render('bundles/TwigBundle/Exception/admin404.html.twig', [
                    'status_code' => $statusCode,
                    'status_text' => $statusText,
                ]);
            }

            return $this->render('bundles/TwigBundle/Exception/employee404.html.twig', [
                'status_code' => $statusCode,
                'status_text' => $statusText,
            ]);
        }

        if ($statusCode === 500) {
            if (str_starts_with($path, '/admin')) {
                return $this->render('bundles/TwigBundle/Exception/admin500.html.twig', [
                    'status_code' => $statusCode,
                    'status_text' => $statusText,
                ]);
            }

            return $this->render('bundles/TwigBundle/Exception/employee500.html.twig', [
                'status_code' => $statusCode,
                'status_text' => $statusText,
            ]);
        }

        return $this->render('bundles/TwigBundle/Exception/generic.html.twig', [
            'status_code' => $statusCode,
            'status_text' => $statusText,
        ]);

    }
}
