<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Brainee HRM - Employee Login</title>
    <link rel="stylesheet" href="css/login.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <div class="logo">
                    <i class="fas fa-users"></i>
                    <h1>Brainee HRM</h1>
                </div>
                <p class="subtitle">Employee Desktop Application</p>
            </div>
            
            <form id="loginForm" class="login-form">
                <div class="form-group">
                    <label for="email">Email or Username</label>
                    <div class="input-group">
                        <i class="fas fa-user"></i>
                        <input type="text" id="email" name="email" required placeholder="Enter your email or username">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="password">Password</label>
                    <div class="input-group">
                        <i class="fas fa-lock"></i>
                        <input type="password" id="password" name="password" required placeholder="Enter your password">
                        <button type="button" class="toggle-password" id="togglePassword">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>
                
                <div class="form-options">
                    <label class="remember-me">
                        <input type="checkbox" id="rememberMe" checked>
                        <span class="checkmark"></span>
                        Remember me
                    </label>
                </div>
                
                <button type="submit" class="login-btn" id="loginBtn">
                    <i class="fas fa-sign-in-alt"></i>
                    Sign In
                </button>
                
                <div class="error-message" id="errorMessage" style="display: none;">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span id="errorText"></span>
                </div>
                
                <div class="success-message" id="successMessage" style="display: none;">
                    <i class="fas fa-check-circle"></i>
                    <span id="successText"></span>
                </div>
            </form>
            
            <div class="login-footer">
                <div class="connection-status">
                    <span class="status-indicator" id="connectionStatus">
                        <i class="fas fa-circle"></i>
                        <span id="statusText">Checking connection...</span>
                    </span>
                </div>
                
                <div class="app-info">
                    <span id="appVersion">v1.0.0</span>
                    <span class="separator">•</span>
                    <span id="machineId">Loading...</span>
                </div>
            </div>
        </div>
        

    </div>
    
    <div class="loading-overlay" id="loadingOverlay" style="display: none;">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>Authenticating...</p>
        </div>
    </div>
    
    <script src="js/login.js"></script>
</body>
</html>
