<?php

namespace App\Repository;

use App\Entity\Task;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Task>
 */
class TaskRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Task::class);
    }

    //    /**
    //     * @return Task[] Returns an array of Task objects
    //     */
    //    public function findByExampleField($value): array
    //    {
    //        return $this->createQueryBuilder('t')
    //            ->andWhere('t.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->orderBy('t.id', 'ASC')
    //            ->setMaxResults(10)
    //            ->getQuery()
    //            ->getResult()
    //        ;
    //    }

    //    public function findOneBySomeField($value): ?Task
    //    {
    //        return $this->createQueryBuilder('t')
    //            ->andWhere('t.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->getQuery()
    //            ->getOneOrNullResult()
    //        ;
    //    }
    public function getMinAssignedAtDate(int $projectId): ?\DateTime
    {
        $result = $this->createQueryBuilder('t')
            ->select('MIN(t.assignedAt) as minDate')
            ->where('t.project = :projectId')
            ->setParameter('projectId', $projectId)
            ->getQuery()
            ->getOneOrNullResult();

        return isset($result['minDate']) ? new \DateTime($result['minDate']) : null;
    }

    public function getMaxAssignedAtDate(int $projectId): ?\DateTime
    {
        $result = $this->createQueryBuilder('t')
            ->select('MAX(t.assignedAt) as maxDate')
            ->where('t.project = :projectId')
            ->setParameter('projectId', $projectId)
            ->getQuery()
            ->getOneOrNullResult();

        return isset($result['maxDate']) ? new \DateTime($result['maxDate']) : null;
    }

    /**
     * Find tasks assigned to an employee
     */
    public function findTasksForEmployee($employee, ?string $status = null): array
    {
        $qb = $this->createQueryBuilder('t')
            ->where('t.assignedTo = :employee')
            ->setParameter('employee', $employee)
            ->orderBy('t.createdAt', 'DESC');

        if ($status) {
            $qb->andWhere('t.status = :status')
               ->setParameter('status', $status);
        }

        return $qb->getQuery()->getResult();
    }

    /**
     * Find tasks by project and employee
     */
    public function findByProjectAndEmployee($project, $employee): array
    {
        return $this->createQueryBuilder('t')
            ->where('t.project = :project')
            ->andWhere('t.assignedTo = :employee')
            ->setParameter('project', $project)
            ->setParameter('employee', $employee)
            ->orderBy('t.createdAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Get task statistics for an employee
     */
    public function getTaskStatsForEmployee($employee): array
    {
        return $this->createQueryBuilder('t')
            ->select('t.status, COUNT(t.id) as count')
            ->where('t.assignedTo = :employee')
            ->setParameter('employee', $employee)
            ->groupBy('t.status')
            ->getQuery()
            ->getResult();
    }

    /**
     * Get overdue tasks for an employee
     */
    public function getOverdueTasksForEmployee($employee): array
    {
        return $this->createQueryBuilder('t')
            ->where('t.assignedTo = :employee')
            ->andWhere('t.status != :completedStatus')
            ->andWhere('t.assignedAt < :yesterday')
            ->setParameter('employee', $employee)
            ->setParameter('completedStatus', 'completed')
            ->setParameter('yesterday', new \DateTime('-1 day'))
            ->orderBy('t.assignedAt', 'ASC')
            ->getQuery()
            ->getResult();
    }
}
