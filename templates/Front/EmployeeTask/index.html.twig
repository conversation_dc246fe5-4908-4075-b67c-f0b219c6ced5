{% extends 'Front/dashboard/base.html.twig' %}
{% block body %}
    {% include 'Front/partials/breadcrumbs.html.twig' with {
        breadcrumbs: [
            { 'label': 'Dashboard', 'url': url('dashboard_employee') },
        ],
        active: isHistory ? 'Task History' : 'My Tasks'
    } %}
    <div class="content">
        <div class="animated fadeIn">
            <div class="row">
                <div class="col-md-12">
                    <div id="flash-container"></div>
                    <div class="card">
                        <div class="card-header d-flex justify-content-between">
                            <strong class="card-title">
                                {{ isHistory ? 'Task History' : 'My Tasks' }}
                            </strong>
                            {% if allowSelfTask and hasProjects %}
                                <a href="{{ path('employee_task_create_self') }}" class="btn btn-primary">
                                    Add My Task
                                </a>
                            {% endif %}
                        </div>
                        <div class="card-body">
                            <div class="table-stats order-table ov-h text-center">
                                <div id="taskTable" class="datatable-main-wrapper">Loading...</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="clearfix"></div>
    <link rel="stylesheet" href="{{ asset('assets/css/task-description.css') }}">
    <script src="{{ asset('assets/js/datatables.min.js') }}"></script>
    <script src="{{ asset('bundles/datatables/js/datatables.js') }}"></script>
    <script>
        $(function () {
            recordsTable = $('#taskTable').initDataTables({{ datatable_settings(datatable) }});
        });
    </script>
    <script src="{{ asset('assets/js/datatableclearsearch.js') }}"></script>
    <script src="{{ asset('assets/js/admin/common.js') }}"></script>
    <script>
        function updateLiveDurations() {
            const now = Math.floor(Date.now() / 1000);

            document.querySelectorAll('.live-duration').forEach(span => {
                const start = parseInt(span.getAttribute('data-start'));
                if (!start || isNaN(start)) return;

                const diffSeconds = now - start;
                const hours = Math.floor(diffSeconds / 3600);
                const minutes = Math.floor((diffSeconds % 3600) / 60);
                const seconds = diffSeconds % 60;

                let formatted = '';
                if (hours > 0) formatted += `${hours}h `;
                if (minutes > 0 || hours > 0) formatted += `${minutes}m `;
                formatted += `${seconds}s`;

                span.textContent = formatted;
            });
        }
        function startLiveDurationTimer() {
            updateLiveDurations();
            setInterval(updateLiveDurations, 1000);
        }
        document.addEventListener('DOMContentLoaded', function () {
            const modal = document.getElementById('<?= $timeLogsModalId ?>');
            if (modal) {
                $(modal).on('shown.bs.modal', function () {
                    startLiveDurationTimer();
                });
            } else {
                startLiveDurationTimer();
            }
        });
    </script>

{% endblock %}