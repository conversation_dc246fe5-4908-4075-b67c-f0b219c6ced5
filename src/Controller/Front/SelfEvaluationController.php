<?php

namespace App\Controller\Front;

use App\Entity\MasterEmployee;
use App\Message\FeedbackNotificationMessage;
use App\Repository\EmployeeHoursRepository;
use App\Repository\FormTemplateRepository;
use App\Repository\MasterEmployeeRepository;
use App\Service\Common\FormFillService;
use App\Traits\ExceptionLoggerTrait;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Routing\Annotation\Route;

class SelfEvaluationController extends AbstractController
{
    use ExceptionLoggerTrait;

    public function __construct(
        private readonly MessageBusInterface $messageBus,
    )
    {
    }
    #[Route('/self-evaluation-form', name: 'app_front_self_evaluation', methods: ['GET'])]
    public function showForms(FormTemplateRepository $formTemplateRepository): \Symfony\Component\HttpFoundation\Response
    {
        $user = $this->getUser();
        if (!$user instanceof MasterEmployee) {
            throw $this->createAccessDeniedException('You must be logged in as an employee to view this page.');
        }
        $templates = $formTemplateRepository->findTemplatesByEmployee($user);

        return $this->render('Front/SelfEvaluation/form_list.html.twig', [
            'formTemplates' => $templates,
        ]);
    }


    /**
     * @throws \JsonException
     */
    #[Route('/self-evaluation-form/{templateId}/fill', name: 'self_evaluation_fill_form', methods: ['GET', 'POST'])]
    public function fillForm(
        int                      $templateId,
        Request                  $request,
        FormTemplateRepository   $formTemplateRepository,
        MasterEmployeeRepository $employeeRepository,
        EntityManagerInterface   $entityManager,
        EmployeeHoursRepository  $employeeHoursRepository,
        FormFillService          $formFillService
    ): Response|JsonResponse
    {
        $user = $this->getUser();

        $template = $formTemplateRepository->find($templateId);
        if (!$template || !$template->isStatus() || $template->getIsDelete() || !$template->isAssignToAll()) {
            throw $this->createAccessDeniedException("You do not have access to this template or it is inactive.");
        }

        $employees = [$user];

        $sections = $template->getSections()->filter(fn($section) => $section->isStatus() && !$section->getIsDelete()
            && count($formFillService->getSortedFields($section)) > 0)->toArray();
        usort($sections, fn($a, $b) => $a->getOrderNumber() <=> $b->getOrderNumber());

        $fieldTypes = $formFillService->processSections($sections);

        if ($request->isMethod('POST')) {
            $employeeId = $request->request->get('employee_id');
            $employee = $employeeRepository->find($employeeId);

            if (!$employee) {
                $this->addFlash('error', 'Invalid employee selected.');
                return $this->redirectToRoute('fill_form', ['templateId' => $templateId]);
            }

            // Calculate overall rating
            $ratingData = $formFillService->calculateOverallRating($sections, $fieldTypes, $request);
            $overallRating = $ratingData['ratingCount'] > 0
                ? number_format($ratingData['totalRating'] / $ratingData['ratingCount'], 2)
                : null;

            // Get employee activity data
            $activityData = $formFillService->getEmployeeActivityData($employeeHoursRepository, $employee);

            // Create submission
            $submission = $formFillService->createSubmission(
                $template,
                $employee,
                $user,
                $overallRating,
                $activityData['averageActiveMinutes'],
                $activityData['averageActiveSeconds'],
                false // isAdmin = false
            );

            // Process form responses
            $formFillService->processFormResponses(
                $submission,
                $sections,
                $fieldTypes,
                $request,
                $user,
                false // isAdmin = false
            );

            $entityManager->flush();

            try {
                $message = new FeedbackNotificationMessage($employee->getId());
                $this->messageBus->dispatch($message);
            } catch (\Throwable $e) {
                $this->log('Notification dispatch failed: ' . $e->getMessage());
            }

            $this->addFlash('success', 'Form submitted successfully.');
            return new JsonResponse(['redirectUrl' => $this->generateUrl('app_front_self_evaluation')], 200);
        }

        return $this->render('Front/SelfEvaluation/submit.html.twig', [
            'template' => $template,
            'employees' => $employees,
            'sections' => $sections,
            'fieldTypes' => $fieldTypes,
            'employeeActivityData' => $formFillService->getEmployeesActivityData($employeeHoursRepository, $employees),
        ]);
    }

}