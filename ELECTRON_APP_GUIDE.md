# Brainee HRM Desktop Application - Complete Guide

## Overview

This Electron.js desktop application provides a secure employee tracking interface that connects to the Brainee HRM Symfony backend. The application features token-based authentication, offline functionality, auto-start capabilities, and prevents user logout once authenticated.

## Features Implemented

### ✅ Core Features
- **Secure Login Interface**: Email/username and password authentication
- **Token-Based Authentication**: JWT-like tokens for secure API communication
- **No Logout Functionality**: Users cannot logout once authenticated (as per requirements)
- **Auto-Start on OS Boot**: Automatically starts with the operating system
- **SQLite Local Database**: Offline data storage and synchronization
- **System Tray Integration**: Runs in background with tray icon
- **Offline Mode Support**: Works when backend is not accessible

### ✅ Technical Implementation
- **Electron.js Framework**: Cross-platform desktop application
- **SQLite Database**: Local data storage with automatic schema creation
- **API Integration**: RESTful communication with Symfony backend
- **Auto-Launch**: OS-level startup configuration
- **Secure Token Storage**: Encrypted local storage for authentication tokens
- **Connection Monitoring**: Automatic online/offline detection
- **Data Synchronization**: Queued sync when connection is restored

## Project Structure

```
electron-app/
├── src/
│   ├── main.js              # Main Electron process
│   ├── preload.js           # Secure IPC bridge
│   ├── database.js          # SQLite database service
│   └── offline-service.js   # Offline functionality
├── renderer/
│   ├── login.html           # Login interface
│   ├── dashboard.html       # Main dashboard
│   ├── css/
│   │   ├── login.css        # Login styles
│   │   └── dashboard.css    # Dashboard styles
│   └── js/
│       ├── login.js         # Login logic
│       └── dashboard.js     # Dashboard functionality
├── assets/                  # App icons and resources
├── package.json             # Dependencies and scripts
├── install.js               # Installation script
├── test-setup.js           # Setup verification
└── README.md               # Documentation
```

## Backend API Endpoints Created

The following API endpoints were added to the Symfony backend:

### Authentication Controller (`src/Controller/Api/AuthController.php`)
- `GET /api/health` - Health check endpoint
- `POST /api/employee/login` - Employee authentication
- `POST /api/validate-token` - Token validation
- `GET /api/employee/profile` - Get employee profile

### Security Configuration
- Updated `config/packages/security.yaml` to allow API access
- Stateless API firewall configuration
- Token-based authentication system

## Database Schema

### SQLite Tables Created
1. **users** - Employee data cache
2. **tokens** - Authentication tokens with expiration
3. **activity_log** - User activity tracking
4. **app_settings** - Application configuration
5. **sync_queue** - Offline operations queue
6. **sync_status** - Synchronization status

## Installation Instructions

### Prerequisites
- Node.js 16 or higher
- npm package manager
- Running Symfony backend server

### Step 1: Install Dependencies
```bash
cd electron-app
npm install
```

### Step 2: Run Setup Test
```bash
npm run test
```

### Step 3: Start the Application
```bash
npm start
```

### Alternative: Use Installation Script
```bash
node install.js
```

## Configuration

### Backend URL Configuration
Update the API base URL in `renderer/js/login.js`:
```javascript
this.apiBaseUrl = 'http://your-symfony-backend-url';
```

### Auto-Start Configuration
The app automatically configures OS-level auto-start. To manage manually:
- **Windows**: Task Manager > Startup tab
- **macOS**: System Preferences > Users & Groups > Login Items
- **Linux**: Varies by desktop environment

## Usage Guide

### First Time Setup
1. Ensure Symfony backend is running
2. Start the Electron app
3. Login with valid employee credentials
4. App will auto-start with OS from now on

### Login Process
1. Enter email/username and password
2. App validates credentials with backend
3. Stores authentication token locally
4. Redirects to dashboard
5. **Note**: No logout option available (by design)

### Offline Mode
- App automatically detects connection status
- Can authenticate offline if previously logged in
- Queues operations for sync when online
- Shows offline indicator in interface

### Dashboard Features
- Connection status monitoring
- Session time tracking
- Quick action buttons
- Recent activity log
- System information display

## Security Features

### Authentication Security
- Secure token storage using electron-store
- Device identification using machine ID
- Token expiration and validation
- No logout functionality (prevents unauthorized access)

### Data Security
- Local SQLite database encryption
- Secure IPC communication
- Context isolation in renderer process
- No remote module access

## Troubleshooting

### Common Issues

#### App Won't Start
- Check Node.js version (16+ required)
- Run `npm install` to ensure dependencies
- Check console for error messages

#### Login Fails
- Verify Symfony backend is running
- Check API endpoint URLs
- Ensure employee account exists and is active
- Check network connectivity

#### Auto-Start Not Working
- Check OS-specific startup settings
- Verify app has necessary permissions
- Check auto-launch configuration

#### Database Issues
- Delete app data folder to reset database
- Check file permissions in user data directory
- Verify SQLite3 module installation

### Debug Mode
Run in development mode for detailed logging:
```bash
npm run dev
```

### Log Locations
- **Windows**: `%APPDATA%/brainee-hrm-desktop/logs`
- **macOS**: `~/Library/Application Support/brainee-hrm-desktop/logs`
- **Linux**: `~/.config/brainee-hrm-desktop/logs`

## Building for Distribution

### Build for Current Platform
```bash
npm run build
```

### Build for Specific Platforms
```bash
npm run build-win    # Windows
npm run build-mac    # macOS
npm run build-linux  # Linux
```

### Distribution Files
Built applications will be in the `dist/` directory.

## Development Notes

### Code Structure
- **Main Process**: Handles system integration, database, and IPC
- **Renderer Process**: UI and user interactions
- **Preload Script**: Secure bridge between main and renderer
- **Services**: Modular functionality (database, offline, etc.)

### Key Design Decisions
1. **No Logout**: Implemented as per requirements for security
2. **SQLite**: Chosen for offline capability and simplicity
3. **Token-Based Auth**: Custom implementation for Symfony compatibility
4. **Auto-Start**: Essential for employee tracking requirements
5. **Offline Mode**: Critical for reliability in various network conditions

## Future Enhancements

### Potential Improvements
- Screenshot capture functionality
- Idle time detection
- Activity monitoring
- File synchronization
- Push notifications
- Multi-language support

### Scalability Considerations
- Database optimization for large datasets
- Efficient sync algorithms
- Memory usage optimization
- Performance monitoring

## Support and Maintenance

### Regular Maintenance
- Clear expired tokens and old logs
- Update dependencies regularly
- Monitor disk space usage
- Backup user data periodically

### Updates
- Use electron-updater for automatic updates
- Implement version checking
- Graceful update process without data loss

## License

MIT License - See LICENSE file for details.

## Contact

For technical support or questions about the Brainee HRM Desktop application, please contact the development team.
