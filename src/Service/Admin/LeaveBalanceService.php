<?php

namespace App\Service\Admin;

use App\Repository\LeaveBalanceRepository;
use O<PERSON>s\DataTablesBundle\Adapter\ArrayAdapter;
use Omines\DataTablesBundle\Column\NumberColumn;
use Omines\DataTablesBundle\Column\TextColumn;
use Omines\DataTablesBundle\DataTable;
use Omines\DataTablesBundle\DataTableFactory;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Request;
use App\Traits\ExceptionLoggerTrait;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Security\Csrf\CsrfTokenManagerInterface;

class LeaveBalanceService
{
    use ExceptionLoggerTrait;

    public function __construct(
        private readonly DataTableFactory $dataTableFactory,
        private readonly RouterInterface $router,
        ParameterBagInterface $parameterBag,
        private readonly CsrfTokenManagerInterface $csrfTokenManager,
        private readonly LeaveBalanceRepository $leaveBalanceRepository
    ) {
        $this->initializeLogger($parameterBag, 'LeaveBalance_Table_service');
    }

    public function createLeaveBalanceTable(Request $request): DataTable
    {
        $data = $this->leaveBalanceRepository->getLeaveBalanceData();

        return $this->dataTableFactory->create()
            ->add('employeeName', TextColumn::class, [
                'label' => 'Employee Name',
                'orderable' => true,
            ])
            ->add('totalLeaveDays', NumberColumn::class, [
                'label' => 'Leave Balance',
                'orderable' => true,
                'render' => fn($value, $context) => number_format($value, 1),
            ])
            ->add('actions', TextColumn::class, [
                'label' => 'Actions',
                'orderable' => false,
                'render' => function($value, $context) {
                    return $this->renderActionColumn($context);
                },
            ])
            ->createAdapter(ArrayAdapter::class, $data)
            ->handleRequest($request);
    }

    private function renderActionColumn(array $context): string
    {
        $entityId = $context['id'];
        $editUrl = $this->router->generate('leave_edit', ['employeeId' => $entityId]);
        $editToken = $this->csrfTokenManager->getToken('edit' . $entityId)->getValue();

        return sprintf(
            '<form action="%s" method="GET" class="d-inline-flex">
            <input type="hidden" name="id" value="%s">
            <input type="hidden" name="_token" value="%s">
            <button type="submit" class="bs-btn btn btn-primary btn-sm bs-m" title="Edit">
                <i class="bs-fa fa fa-edit"></i>
            </button>
        </form>',
            $editUrl,
            $entityId,
            $editToken
        );
    }

}