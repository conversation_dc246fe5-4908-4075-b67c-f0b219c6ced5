<?php

namespace App\Repository;

use App\Entity\ScreenshotLog;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<ScreenshotLog>
 *
 * @method ScreenshotLog|null find($id, $lockMode = null, $lockVersion = null)
 * @method ScreenshotLog|null findOneBy(array $criteria, array $orderBy = null)
 * @method ScreenshotLog[]    findAll()
 * @method ScreenshotLog[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ScreenshotLogRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, ScreenshotLog::class);
    }

    /**
     * Get screenshots for a specific employee and date range
     */
    public function findByEmployeeAndDateRange($employeeId, \DateTime $startDate, \DateTime $endDate): array
    {
        return $this->createQueryBuilder('sl')
            ->where('sl.employee = :employeeId')
            ->andWhere('sl.capturedAt BETWEEN :startDate AND :endDate')
            ->setParameter('employeeId', $employeeId)
            ->setParameter('startDate', $startDate)
            ->setParameter('endDate', $endDate)
            ->orderBy('sl.capturedAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Get screenshots for a specific employee and date
     */
    public function findByEmployeeAndDate($employee, \DateTime $date): array
    {
        $startDate = clone $date;
        $startDate->setTime(0, 0, 0);

        $endDate = clone $date;
        $endDate->setTime(23, 59, 59);

        return $this->createQueryBuilder('sl')
            ->where('sl.employee = :employee')
            ->andWhere('sl.capturedAt BETWEEN :startDate AND :endDate')
            ->setParameter('employee', $employee)
            ->setParameter('startDate', $startDate)
            ->setParameter('endDate', $endDate)
            ->orderBy('sl.capturedAt', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Get unsynced screenshots
     */
    public function findUnsyncedScreenshots(): array
    {
        return $this->createQueryBuilder('sl')
            ->where('sl.isSynced = false')
            ->orderBy('sl.capturedAt', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Get screenshots by capture type
     */
    public function findByCaptureType(string $captureType, ?int $limit = null): array
    {
        $qb = $this->createQueryBuilder('sl')
            ->where('sl.captureType = :captureType')
            ->setParameter('captureType', $captureType)
            ->orderBy('sl.capturedAt', 'DESC');

        if ($limit) {
            $qb->setMaxResults($limit);
        }

        return $qb->getQuery()->getResult();
    }

    /**
     * Mark screenshots as synced
     */
    public function markAsSynced(array $screenshotIds): void
    {
        $this->createQueryBuilder('sl')
            ->update()
            ->set('sl.isSynced', 'true')
            ->where('sl.id IN (:screenshotIds)')
            ->setParameter('screenshotIds', $screenshotIds)
            ->getQuery()
            ->execute();
    }
}
