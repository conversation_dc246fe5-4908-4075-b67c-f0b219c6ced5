<?php

namespace App\Entity;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;


#[ORM\Entity]
#[ORM\Table(name: 'task')]
class Task
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: Types::INTEGER)]
    private ?int $id = null;

    #[ORM\Column(type: Types::STRING)]
    private ?string $title = null;

    #[ORM\Column(type: Types::TEXT)]
    private ?string $description = null;

    #[ORM\ManyToOne(targetEntity: Project::class,inversedBy: 'task')]
    #[ORM\JoinColumn(nullable: false,onDelete: 'CASCADE')]
    private ?Project $project = null;

    #[ORM\ManyToOne(targetEntity: MasterEmployee::class)]
    #[ORM\JoinColumn(nullable: true)]
    private ?MasterEmployee $assignedTo = null;

    #[ORM\ManyToOne(targetEntity: MasterEmployee::class)]
    #[ORM\JoinColumn(nullable: true)]
    private ?MasterEmployee $assignedBy = null;

    #[ORM\ManyToOne(targetEntity: User::class)]
    #[ORM\JoinColumn(nullable: true)]
    private ?User $assignedByAdmin = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $assignedAt = null;

    #[ORM\Column(type: Types::DECIMAL, precision: 10, scale: 2)]
    private ?float $estimatedHours = null;

    #[ORM\Column(type: Types::FLOAT, nullable: true)]
    private ?float $actualHours = null;

    #[ORM\Column(type: 'string', length: 50)]
    private string $priority;

    #[ORM\Column(type: 'string', length: 50)]
    private string $status;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $createdAt = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $updatedAt = null;

    #[ORM\OneToMany(targetEntity: TaskTimeLog::class, mappedBy: 'task', cascade: ['remove'], orphanRemoval: true)]
    /** @phpstan-ignore-next-line */
    private Collection $taskTimeLogs;

    #[ORM\OneToMany(targetEntity: TaskStatusHistory::class, mappedBy: 'task', cascade: ['remove'], orphanRemoval: true)]
    private Collection $statusHistories;

    #[ORM\OneToMany(targetEntity: Bug::class, mappedBy: 'task', cascade: ['remove'], orphanRemoval: true)]
    private Collection $bugs;

    #[ORM\OneToMany(targetEntity: EmployeeActivity::class, mappedBy: 'relatedTask', cascade: ['remove'], orphanRemoval: true)]
    private Collection $employeeActivities;

    #[ORM\OneToMany(targetEntity: TaskAssignment::class, mappedBy: 'task', cascade: ['remove'], orphanRemoval: true)]
    private Collection $taskAssignments;

    public function __construct()
    {
        $this->taskTimeLogs = new ArrayCollection();
        $this->statusHistories = new ArrayCollection();
        $this->bugs = new ArrayCollection();
        $this->employeeActivities = new ArrayCollection();
        $this->taskAssignments = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): void
    {
        $this->id = $id;
    }

    public function getTitle(): ?string
    {
        return $this->title;
    }

    public function setTitle(?string $title): void
    {
        $this->title = $title;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): void
    {
        $this->description = $description;
    }

    public function getProject(): ?Project
    {
        return $this->project;
    }

    public function setProject(?Project $project): void
    {
        $this->project = $project;
    }

    public function getAssignedTo(): ?MasterEmployee
    {
        return $this->assignedTo;
    }

    public function setAssignedTo(?MasterEmployee $assignedTo): void
    {
        $this->assignedTo = $assignedTo;
    }

    public function getAssignedBy(): ?MasterEmployee
    {
        return $this->assignedBy;
    }

    public function setAssignedBy(?MasterEmployee $assignedBy): void
    {
        $this->assignedBy = $assignedBy;
    }

    public function getAssignedByAdmin(): ?User
    {
        return $this->assignedByAdmin;
    }

    public function setAssignedByAdmin(?User $assignedByAdmin): void
    {
        $this->assignedByAdmin = $assignedByAdmin;
    }

    public function getAssignedAt(): ?\DateTimeInterface
    {
        return $this->assignedAt;
    }

    public function setAssignedAt(?\DateTimeInterface $assignedAt): void
    {
        $this->assignedAt = $assignedAt;
    }

    public function getPriority(): string
    {
        return $this->priority;
    }

    public function setPriority(string $priority): void
    {
        $this->priority = $priority;
    }

    public function getStatus(): string
    {
        return $this->status;
    }

    public function setStatus(string $status): void
    {
        $this->status = $status;
    }


    public function getCreatedAt(): ?\DateTimeInterface
    {
        return $this->createdAt;
    }

    public function setCreatedAt(?\DateTimeInterface $createdAt): void
    {
        $this->createdAt = $createdAt;
    }

    public function getUpdatedAt(): ?\DateTimeInterface
    {
        return $this->updatedAt;
    }

    public function setUpdatedAt(?\DateTimeInterface $updatedAt): void
    {
        $this->updatedAt = $updatedAt;
    }

    public function getEstimatedHours(): ?float
    {
        return $this->estimatedHours;
    }

    public function setEstimatedHours(?float $estimatedHours): void
    {
        $this->estimatedHours = $estimatedHours;
    }

    public function getActualHours(): ?float
    {
        return $this->actualHours;
    }

    public function setActualHours(?float $actualHours): void
    {
        $this->actualHours = $actualHours;
    }

    public function getStatusHistories(): Collection
    {
        return $this->statusHistories;
    }

    public function addStatusHistory(TaskStatusHistory $statusHistory): void
    {
        if (!$this->statusHistories->contains($statusHistory)) {
            $this->statusHistories[] = $statusHistory;
            $statusHistory->setTask($this);
        }
    }

    public function removeStatusHistory(TaskStatusHistory $statusHistory): void
    {
        if ($this->statusHistories->removeElement($statusHistory)) {
            if ($statusHistory->getTask() === $this) {
                $statusHistory->setTask(null);
            }
        }
    }

    public function getBugs(): Collection
    {
        return $this->bugs;
    }

    public function addBug(Bug $bug): void
    {
        if (!$this->bugs->contains($bug)) {
            $this->bugs[] = $bug;
            $bug->setTask($this);
        }
    }

    public function removeBug(Bug $bug): void
    {
        if ($this->bugs->removeElement($bug)) {
            if ($bug->getTask() === $this) {
                $bug->setTask(null);
            }
        }
    }

    public function getEmployeeActivities(): Collection
    {
        return $this->employeeActivities;
    }

    public function addEmployeeActivity(EmployeeActivity $employeeActivity): void
    {
        if (!$this->employeeActivities->contains($employeeActivity)) {
            $this->employeeActivities[] = $employeeActivity;
            $employeeActivity->setRelatedTask($this);
        }
    }

    public function removeEmployeeActivity(EmployeeActivity $employeeActivity): void
    {
        if ($this->employeeActivities->removeElement($employeeActivity)) {
            if ($employeeActivity->getRelatedTask() === $this) {
                $employeeActivity->setRelatedTask(null);
            }
        }
    }
    public function getTaskAssignments(): Collection
    {
        return $this->taskAssignments;
    }

    public function addTaskAssignment(TaskAssignment $assignment): void
    {
        if (!$this->taskAssignments->contains($assignment)) {
            $this->taskAssignments[] = $assignment;
            $assignment->setTask($this);
        }
    }

    public function removeTaskAssignment(TaskAssignment $assignment): void
    {
        if ($this->taskAssignments->removeElement($assignment)) {
            if ($assignment->getTask() === $this) {
                $assignment->setTask(null);
            }
        }
    }

}