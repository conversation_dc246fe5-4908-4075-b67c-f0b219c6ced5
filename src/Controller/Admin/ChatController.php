<?php

namespace App\Controller\Admin;

use App\Service\Admin\EmployeeChatService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

#[Route('/admin/chat')]
class ChatController extends AbstractController
{
    public function __construct(
        private readonly EmployeeChatService $chatService
    ) {}

    #[Route('/{uuid}', name: 'chat_view', methods: ['GET'])]
    public function view(string $uuid): Response
    {
        $notification = $this->chatService->getNotificationByUuid($uuid);
        $users = $this->chatService->getAllUsers();

        if (!$notification) {
            return $this->redirectToRoute('error_message');
        }

        return $this->render('Chat/index.html.twig', [
            'notification' => $notification,
            'chats' => $notification->getChats(),
            'employee' => $notification->getEmployee(),
            'user' => $users,
        ]);
    }

    #[Route('/{uuid}/messages', name: 'chat_messages', methods: ['GET'])]
    public function getMessages(string $uuid): JsonResponse
    {
        $notification = $this->chatService->getNotificationByUuid($uuid);

        if (!$notification) {
            return new JsonResponse(['error' => 'Chat not found'], Response::HTTP_NOT_FOUND);
        }

        $messages = $this->chatService->getMessagesByNotification($notification);

        return new JsonResponse($messages);
    }

    /**
     * @throws \JsonException
     */
    #[Route('/{uuid}/send', name: 'chat_send', methods: ['POST'])]
    public function sendMessage(Request $request, string $uuid): JsonResponse
    {
        $notification = $this->chatService->getNotificationByUuid($uuid);

        if (!$notification) {
            return new JsonResponse(['error' => 'Chat not found'], Response::HTTP_NOT_FOUND);
        }

        $data = json_decode($request->getContent(), true, 512, JSON_THROW_ON_ERROR);
        $message = $data['message'] ?? '';
        $isAdmin = $data['isAdmin'] ?? false;

        if (empty($message)) {
            return new JsonResponse(['error' => 'Message cannot be empty'], Response::HTTP_BAD_REQUEST);
        }

        $chat = $this->chatService->createMessage($notification, $message, $isAdmin);
        return new JsonResponse([
            'id' => $chat->getId(),
            'message' => $chat->getMessage(),
            'isAdmin' => $chat->isFromAdmin(),
            'timestamp' => $chat->getCreatedAt()->format('d-m-Y H:i:s'),
        ]);
    }

    #[Route('/error', name: 'error_message')]
    public function justifyError(): Response
    {
        return $this->render('admin/justification_response.html.twig', [
            'status' => 'error',
            'message' => 'Chat URL not found.'
        ]);
    }
}
