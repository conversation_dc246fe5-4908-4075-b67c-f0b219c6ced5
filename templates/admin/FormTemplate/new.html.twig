{% extends 'admin/dashboard/base.html.twig' %}
{% block body %}
    {% include 'admin/partials/breadcrumbs.html.twig' with {
        title: 'Form Templates',
        breadcrumbs: [
            { 'label': 'Dashboard', 'url': url('admin_dashboard') },
            { 'label': 'Performance Feedback', 'url': url('form_template') }
        ],
        active: mode == 'view' ? 'View  Form Template' : (mode == 'edit' ? 'Edit Form Template' : 'Create Form Template')
    } %}

    <div class="content">
        <div class="animated fadeIn">
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <strong class="card-title">
                                {{ mode == 'view' ? 'View Form Template' : (mode == 'edit' ? 'Edit Form Template' : 'Create Form Template') }}
                            </strong>
                            <div class="d-flex align-items-center">
                                <a href="{{ path('form_template') }}" class="btn btn-sm btn-outline-secondary mr-2">
                                    <i class="fa fa-list mr-1"></i>Back to List
                                </a>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="formTemplate-form">
                                {% if form is defined %}
                                    {{ form_start(form, { 'attr': { 'class': 'needs-validation use-loader', 'novalidate': 'novalidate' } }) }}
                                    <div class="form-section">
{#                                        <h5 class="section-header">#}
{#                                            <i class="fa fa-file-text mr-2"></i>Form Template Information#}
{#                                        </h5>#}
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.name, 'Form Name', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-file-text"></i></span>
                                                        </div>
                                                        {{ form_widget(form.name, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter form template name', 'required': 'required' } }) }}
                                                    </div>
                                                    {{ form_errors(form.name) }}
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.category, 'Form Category', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-folder"></i></span>
                                                        </div>
                                                        {{ form_widget(form.category, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter category' } }) }}
                                                    </div>
                                                    {{ form_errors(form.category) }}
                                                    <small class="form-text text-muted">Select or enter the category (e.g., Performance Feedback)</small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.status, 'Status', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-toggle-on"></i></span>
                                                        </div>
                                                        {{ form_widget(form.status, { 'attr': { 'class': 'form-control', 'aria-label': 'Select Status' } }) }}
                                                    </div>
                                                    {{ form_errors(form.status) }}
                                                    <small class="form-text text-muted">Select the form template status (Active/Inactive)</small>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.frequency, 'Form Frequency', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-calendar"></i></span>
                                                        </div>
                                                        {{ form_widget(form.frequency, { 'attr': { 'class': 'form-control', 'aria-label': 'Select Frequency' } }) }}
                                                    </div>
                                                    {{ form_errors(form.frequency) }}
                                                    <small class="form-text text-muted">Select how often the form is used (e.g., Monthly, Quarterly)</small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.description, 'Form Description', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-info-circle"></i></span>
                                                        </div>
                                                        {{ form_widget(form.description, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter description' } }) }}
                                                    </div>
                                                    {{ form_errors(form.description) }}
                                                    <small class="form-text text-muted">Provide a brief description of the form template</small>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="mb-3">
                                                    {{ form_label(form.teamLeaders, 'Team Leaders Assign', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                    <div class="checkbox-group">
                                                        {% for child in form.teamLeaders %}
                                                            <div class="input-group mb-2">
                                                                <div class="input-group-prepend">
                                                                    <div class="input-group-text">
                                                                        {{ form_widget(child, { 'attr': { 'class': 'custom-checkbox', 'aria-label': 'Checkbox for ' ~ child.vars.label|default('team leader') } }) }}
                                                                    </div>
                                                                </div>
                                                                <div class="form-control checkbox-label">
                                                                    {{ form_label(child, null, { 'label_attr': { 'class': 'ml-2' } }) }}
                                                                </div>
                                                            </div>
                                                        {% endfor %}
                                                    </div>
                                                    {{ form_errors(form.teamLeaders) }}
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="mb-3">
                                                    {{ form_label(form.assignToAll, 'Assign to All', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                    <div class="input-group mb-2">
                                                        <div class="input-group-prepend">
                                                            <div class="input-group-text">
                                                                {{ form_widget(form.assignToAll, { 'attr': { 'class': 'custom-checkbox', 'aria-label': 'Checkbox for Assign to All' } }) }}
                                                            </div>
                                                        </div>
                                                        <div class="form-control checkbox-label">
                                                            {{ form_label(form.assignToAll, null, { 'label_attr': { 'class': 'ml-2' } }) }}
                                                        </div>
                                                    </div>
                                                    {{ form_errors(form.assignToAll) }}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="action-footer mt-4">
                                        <a href="{{ path('form_template') }}" class="btn btn-secondary mr-2">
                                            Cancel
                                        </a>
                                        <button type="submit" class="btn btn-primary pr-3 pl-3">
                                            {{ formTemplate.id is defined ? 'Update Form Template' : 'Create Form Template' }}
                                        </button>
                                    </div>
                                    {{ form_end(form) }}
                                {% else %}
                                    <!-- View Mode -->
                                    <div class="form-section employee-details-section">
                                        <div class="details-section">
{#                                            <h5 class="section-header">#}
{#                                                <i class="fa fa-file-text mr-2"></i>Form Template Information#}
{#                                            </h5>#}
                                            <div class="details-container">
                                                <div class="detail-row">
                                                    <div class="detail-item col-md-4"><strong class="detail-label">Form Name:</strong></div>
                                                    <div class="detail-item col-md-8">{{ formTemplate.name ?: 'N/A' }}</div>
                                                </div>
                                                <div class="detail-row">
                                                    <div class="detail-item col-md-4"><strong class="detail-label">Form Category:</strong></div>
                                                    <div class="detail-item col-md-8">{{ formTemplate.category|capitalize ?: 'N/A' }}</div>
                                                </div>
                                                <div class="detail-row">
                                                    <div class="detail-item col-md-4"><strong class="detail-label">Status:</strong></div>
                                                    <div class="detail-item col-md-8">{{ formTemplate.status ? 'Active' : 'Inactive' }}</div>
                                                </div>
                                                <div class="detail-row">
                                                    <div class="detail-item col-md-4"><strong class="detail-label">Form Frequency:</strong></div>
                                                    <div class="detail-item col-md-8">{{ formTemplate.frequency|capitalize ?: 'N/A' }}</div>
                                                </div>
                                                <div class="detail-row">
                                                    <div class="detail-item col-md-4"><strong class="detail-label">Form Description:</strong></div>
                                                    <div class="detail-item col-md-8">{{ formTemplate.description ?: 'N/A' }}</div>
                                                </div>
                                                <div class="detail-row">
                                                    <div class="detail-item col-md-4"><strong class="detail-label">Team Leaders Assigned:</strong></div>
                                                    <div class="detail-item col-md-8">
                                                        {% for teamLeader in formTemplate.teamLeaders %}
                                                            {{ teamLeader.name }}{% if not loop.last %}, {% endif %}
                                                        {% else %}
                                                            N/A
                                                        {% endfor %}
                                                    </div>
                                                </div>
                                                <div class="detail-row">
                                                    <div class="detail-item col-md-4"><strong class="detail-label">Assign to All:</strong></div>
                                                    <div class="detail-item col-md-8">{{ formTemplate.assignToAll ? 'Yes' : 'No' }}</div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="action-footer mt-4">
                                            <a href="{{ path('form_template') }}" class="btn btn-secondary mr-2">
                                                Back to List
                                            </a>
                                            <a href="{{ path('form_template_edit', {'id': formTemplate.id}) }}" class="btn btn-primary pr-3 pl-3" style="padding-top: 10px;">
                                                Edit Form Template
                                            </a>
                                        </div>
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div> <!-- .card -->
                </div><!--/.col-->
            </div>
        </div>
    </div>
    <div class="clearfix"></div>
    <link rel="stylesheet" href="{{ asset('assets/css/Error/error.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/AdminForm/Form.css') }}">
    <script src="{{ asset('assets/js/admin/formTemplate.js') }}"></script>
{% endblock %}