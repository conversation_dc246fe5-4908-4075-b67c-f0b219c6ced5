{% extends 'admin/dashboard/base.html.twig' %}

{% block body %}
    {% include 'admin/partials/breadcrumbs.html.twig' with {
        breadcrumbs: [
            { 'label': 'Admin Dashboard', 'url': path('admin_dashboard') },
            { 'label': ' Leave Request', 'url': path('admin_leave_request_list') }
        ],
        active: 'Apply Leave for Employee'
    } %}
    <div class="content">
        <div class="animated fadeIn">
            <div class="row">
                <div class="col-lg-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <strong class="card-title">Apply Leave for Employee</strong>
                            <div class="d-flex align-items-center">
                                <a href="{{ path('admin_leave_request_list') }}" class="btn btn-sm btn-outline-secondary mr-2">
                                    <i class="fa fa-list mr-1"></i>Back to List
                                </a>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="leave-request-form">
                                {{ form_start(form, {
                                    'action': path('admin_leave_request_create'),
                                    'method': 'POST',
                                    'attr': { 'class': 'needs-validation', 'novalidate': 'novalidate' }
                                }) }}
                                <div class="form-section">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                {{ form_label(form.employee, 'Employee', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field ' } }) }}
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text"><i class="fa fa-user"></i></span>
                                                    </div>
                                                    {{ form_widget(form.employee, {
                                                        'attr': {
                                                            'class': 'form-control employee-select',
                                                            'required': 'required',
                                                            'data-leave-balance-url': path('api_employee_leave_balances', {'id': '__employee_id__'})
                                                        }
                                                    }) }}

                                                </div>
                                                {{ form_errors(form.employee) }}
                                                <small class="form-text text-muted">Select the employee for whom you are applying the leave</small>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                {{ form_label(form.leaveType, 'Leave Type', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text"><i class="fa fa-list-alt"></i></span>
                                                    </div>
                                                    {{ form_widget(form.leaveType, { 'attr': { 'class': 'form-control leave-type-select', 'required': 'required' } }) }}
                                                </div>
                                                {{ form_errors(form.leaveType) }}
                                                <small class="form-text text-muted">Select the type of leave</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                {{ form_label(form.startDate, 'Start Date', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text"><i class="fa fa-calendar-check-o"></i></span>
                                                    </div>
                                                    {{ form_widget(form.startDate, { 'attr': { 'class': 'form-control datepicker' } }) }}
                                                </div>
                                                {{ form_errors(form.startDate) }}
                                                <small class="form-text text-muted">Select the start date of the leave</small>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                {{ form_label(form.startHalfDay, 'Start Half Day', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text"><i class="fa fa-clock-o"></i></span>
                                                    </div>
                                                    {{ form_widget(form.startHalfDay, { 'attr': { 'class': 'form-control' } }) }}
                                                </div>
                                                {{ form_errors(form.startHalfDay) }}
                                                <small class="form-text text-muted">Optional: Select if starting with a half day</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                {{ form_label(form.endDate, 'End Date', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text"><i class="fa fa-calendar-check-o"></i></span>
                                                    </div>
                                                    {{ form_widget(form.endDate, { 'attr': { 'class': 'form-control datepicker' } }) }}
                                                </div>
                                                {{ form_errors(form.endDate) }}
                                                <small class="form-text text-muted">Select the end date of the leave</small>
                                            </div>
                                        </div>
                                        <div class="col-md-6" data-same-date-hide>
                                            <div class="mb-3">
                                                {{ form_label(form.endHalfDay, 'End Half Day', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text"><i class="fa fa-clock-o"></i></span>
                                                    </div>
                                                    {{ form_widget(form.endHalfDay, { 'attr': { 'class': 'form-control' } }) }}
                                                </div>
                                                {{ form_errors(form.endHalfDay) }}
                                                <small class="form-text text-muted">Optional: Select if ending with a half day</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="mb-3">
                                                {{ form_label(form.reason, 'Reason', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text"><i class="fa fa-comment"></i></span>
                                                    </div>
                                                    {{ form_widget(form.reason, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter reason' } }) }}
                                                </div>
                                                {{ form_errors(form.reason) }}
                                                <small class="form-text text-muted">Provide a reason for the leave</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="d-flex justify-content-end mt-4">
                                    <a href="{{ path('admin_leave_request_list') }}" class="btn btn-secondary mr-2">
                                        Cancel
                                    </a>
                                    <button type="submit" class="btn btn-primary pr-3 pl-3">
                                        Submit Leave Request
                                    </button>
                                </div>
                                {{ form_end(form) }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="clearfix"></div>
    <link rel="stylesheet" href="{{ asset('assets/css/Error/error.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/AdminForm/Form.css') }}">
    <script src="{{ asset('assets/js/datatables.min.js') }}"></script>
    <script src="{{ asset('bundles/datatables/js/datatables.js') }}"></script>
    <script src="{{ asset('assets/js/admin/leaveRequestAdmin.js') }}"></script>
    <script src="{{ asset('assets/js/moment.min.js') }}"></script>
    <script src="{{ asset('assets/js/daterangepicker.min.js') }}"></script>
{% endblock %}