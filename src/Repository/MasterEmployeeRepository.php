<?php

namespace App\Repository;

use App\Entity\EmployeeReportsTo;
use App\Entity\MasterEmployee;
use App\Traits\ExceptionLoggerTrait;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<MasterEmployee>
 */
class MasterEmployeeRepository extends ServiceEntityRepository
{
    use ExceptionLoggerTrait;
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, MasterEmployee::class);
    }

    public function findEmployeesReportingTo(MasterEmployee $teamLeader): array
    {
        return $this->createQueryBuilder('e')
            ->join('e.reportsTo', 'r')
            ->where('r.teamLeader = :teamLeader')
            ->andWhere('e.isDelete = false')
            ->setParameter('teamLeader', $teamLeader)
            ->getQuery()
            ->getResult();
    }

    /**
     * @throws \DateMalformedStringException
     */
    public function findUpcomingBirthdays(): array
    {
        $today = new \DateTime();
        $nextMonth = (clone $today)->modify('+30 days');
        $allEmployees = $this->createQueryBuilder('e')
            ->select('e.id, e.name, e.birthDate')
            ->where('e.isDelete = :isDelete')
            ->setParameter('isDelete', false)
            ->getQuery()
            ->getResult();
        $upcomingBirthdays = [];
        foreach ($allEmployees as $employee) {
            if (!$employee['birthDate'] instanceof \DateTimeInterface) {
                continue;
            }

            $birthdateThisYear = new \DateTime();
            $birthdateThisYear->setDate(
                (int) $today->format('Y'),
                (int) $employee['birthDate']->format('n'),
                (int) $employee['birthDate']->format('j')
            );
            if ($birthdateThisYear < $today) {
                $birthdateThisYear->modify('+1 year');
            }
            if ($birthdateThisYear <= $nextMonth && $birthdateThisYear >= $today) {
                $upcomingBirthdays[] = $employee;
            }
        }
        usort($upcomingBirthdays, static function($a, $b) {
            $dateA = $a['birthDate'];
            $dateB = $b['birthDate'];
            if ($dateA->format('n') !== $dateB->format('n')) {
                return $dateA->format('n') - $dateB->format('n');
            }
            return $dateA->format('j') - $dateB->format('j');
        });

        return $upcomingBirthdays;
    }

    /**
     * @throws \DateMalformedStringException
     */
    public function findUpcomingAnniversaries(): array
    {
        $today = new \DateTime();
        $nextMonth = (clone $today)->modify('+30 days');
        $allEmployees = $this->createQueryBuilder('e')
            ->select('e.id, e.name, e.joiningDate')
            ->where('e.isDelete = :isDelete')
            ->setParameter('isDelete', false)
            ->getQuery()
            ->getResult();
        $upcomingAnniversaries = [];
        foreach ($allEmployees as $employee) {
            if (!$employee['joiningDate'] instanceof \DateTimeInterface) {
                continue;
            }

            $anniversaryThisYear = new \DateTime();
            $anniversaryThisYear->setDate(
                (int) $today->format('Y'),
                (int) $employee['joiningDate']->format('n'),
                (int) $employee['joiningDate']->format('j')
            );
            if ($anniversaryThisYear < $today) {
                $anniversaryThisYear->modify('+1 year');
            }
            if ($anniversaryThisYear <= $nextMonth && $anniversaryThisYear >= $today) {
                $upcomingAnniversaries[] = $employee;
            }
        }
        usort($upcomingAnniversaries, static function($a, $b) {
            $dateA = $a['joiningDate'];
            $dateB = $b['joiningDate'];
            if ($dateA->format('n') !== $dateB->format('n')) {
                return $dateA->format('n') - $dateB->format('n');
            }
            return $dateA->format('j') - $dateB->format('j');
        });

        return $upcomingAnniversaries;
    }

    /**
     * Find employees by department IDs and optional team leader status.
     *
     * @param array $departmentIds
     * @param bool $isTeamLeaderOnly
     * @param bool $isDelete
     * @return MasterEmployee[]
     */
    public function findByDepartmentsAndTeamLeader(array $departmentIds, bool $isTeamLeaderOnly, bool $isDelete = false): array
    {
        $queryBuilder = $this->createQueryBuilder('e')
            ->innerJoin('e.departments', 'd')
            ->where('e.isDelete = :isDelete')
            ->andWhere('d.id IN (:departmentIds)')
            ->setParameter('isDelete', $isDelete)
            ->setParameter('departmentIds', $departmentIds);

        if ($isTeamLeaderOnly) {
            $queryBuilder->andWhere('e.teamLeader = :teamLeader')
                ->setParameter('teamLeader', true);
        }

        $query = $queryBuilder->getQuery();
        $sql = $query->getSQL();
        $parameters = $query->getParameters();

        $this->log('Executing findByDepartmentsAndTeamLeader query', [
            'sql' => $sql,
            'parameters' => $parameters,
            'departmentIds' => $departmentIds,
            'isTeamLeaderOnly' => $isTeamLeaderOnly,
            'isDelete' => $isDelete,
        ]);

        return $query->getResult();
    }
    public function findHrStaff(): array
    {
        return $this->createQueryBuilder('e')
            ->andWhere('e.isHrAccount = :isHr')
            ->andWhere('e.isDelete = :isDelete')
            ->setParameter('isHr', true)
            ->setParameter('isDelete', false)
            ->getQuery()
            ->getResult();
    }

    /**
     * Find the specific team leader for an employee
     */
    public function findTeamLeaderForEmployee(MasterEmployee $employee): ?MasterEmployee
    {
        return $this->getEntityManager()
            ->createQueryBuilder()
            ->select('rt', 'tl')
            ->from(EmployeeReportsTo::class, 'rt')
            ->innerJoin('rt.teamLeader', 'tl')
            ->where('rt.employee = :employee')
            ->andWhere('tl.isDelete = :isDelete')
            ->setParameter('employee', $employee)
            ->setParameter('isDelete', false)
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult()
            ?->getTeamLeader();
    }
    public function findNonDeletedDesigners(): array
    {
        return $this->createQueryBuilder('e')
            ->join('e.departments', 'd')
            ->where('d.depName = :depName')
            ->andWhere('e.isDelete = false')
            ->setParameter('depName', 'Designer')
            ->getQuery()
            ->getResult();
    }
}
