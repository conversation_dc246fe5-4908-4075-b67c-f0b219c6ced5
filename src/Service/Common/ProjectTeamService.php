<?php

namespace App\Service\Common;

use App\Entity\MasterEmployee;
use App\Entity\ProjectTeam;
use App\Entity\User;
use App\Helper\ModalboxHelper;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\QueryBuilder;
use Omines\DataTablesBundle\Adapter\Doctrine\ORMAdapter;
use Omines\DataTablesBundle\Column\TextColumn;
use Omines\DataTablesBundle\DataTable;
use Omines\DataTablesBundle\DataTableFactory;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Security\Csrf\CsrfTokenManagerInterface;
use App\Traits\ExceptionLoggerTrait;

class ProjectTeamService
{
    use ExceptionLoggerTrait;

    public function __construct(
        private readonly DataTableFactory $dataTableFactory,
        private readonly RouterInterface $router,
        private readonly EntityManagerInterface $entityManager,
        private readonly CsrfTokenManagerInterface $csrfTokenManager,
        private readonly ModalboxHelper $modalboxHelper,
        ParameterBagInterface $parameterBag,
        private readonly Security $security

    ) {
        $this->initializeLogger($parameterBag, 'project_team_service');
    }

    /**
     * Creates a DataTable for project team members.
     *
     * @throws \Exception
     */
    public function createProjectTeamTable(Request $request, int $project_id,User|MasterEmployee $employee): DataTable
    {
        try {
            $dataTable = $this->dataTableFactory->create();

            foreach ($this->getColumns($employee) as $key => $column) {
                $dataTable->add($key, $column['className'], array_filter($column, fn($k) => $k !== 'className', ARRAY_FILTER_USE_KEY));
            }

            return $dataTable->createAdapter(ORMAdapter::class, [
                'entity' => ProjectTeam::class,
                'query' => fn(QueryBuilder $qb) => $this->buildQuery($qb, $project_id),
            ])
                ->addOrderBy('teamMember', 'asc')
                ->handleRequest($request);
        } catch (\Exception $e) {
            $this->log(
                'Error creating project team table for project ID.',
                [
                    'project_id' => $project_id,
                    'error_message' => $e->getMessage(),
                ]
            );
            throw $e;
        }
    }

    /**
     * Defines the columns for the project team DataTable.
     *
     * @return array
     */
    private function getColumns($employee): array
    {
        return [
            'teamMember' => [
                'label' => 'Team Member',
                'field' => 'm.name',
                'className' => TextColumn::class,
                'render' => fn($value, ProjectTeam $pt) => htmlspecialchars($pt->getTeamMember()?->getName() ?? 'N/A'),
                'searchable' => true,
            ],
            'role' => [
                'label' => 'Role',
                'field' => 'pt.role',
                'className' => TextColumn::class,
                'render' => function ($value) {
                    return $value !== null && trim($value) !== ''
                        ? htmlspecialchars($value)
                        : '-';
                },
            ],
            'assignedBy' => [
                'label' => 'Assigned By',
                'field' => 'pt.id',
                'className' => TextColumn::class,
                'render' => fn($value, ProjectTeam $pt) => htmlspecialchars(
                    $pt->getAssignedByAdmin()?->getUsername() ??
                    $pt->getAssignedByTeamLeader()?->getName() ??
                    'N/A'
                ),
            ],
            'actions' => [
                'label' => 'Actions',
                'field' => 'pt.id',
                'className' => TextColumn::class,
                'orderable' => false,
                'render' => fn($value, ProjectTeam $pt) => $this->renderActionColumn($pt,$employee),
            ],
        ];
    }

    /**
     * Renders the action column with a delete button and modal.
     *
     * @param ProjectTeam $context
     * @return string
     */
    private function renderActionColumn(ProjectTeam $context,User|MasterEmployee $employee): string
    {
        $entityId = $context->getId();
        $isAdmin = $employee instanceof User && $this->security->isGranted('ROLE_ADMIN');
        $baseRoute = $isAdmin ? 'admin' : 'employee';
        $deleteUrl = $this->router->generate("{$baseRoute}_project_team_delete", ['id' => $entityId]);
        $csrfToken = $this->csrfTokenManager->getToken('delete' . $entityId)->getValue();
        $modalId = 'deleteTeamModal_' . $entityId;

        $modalBody = 'Are you sure you want to remove this team member from the project?<br><br><strong>Warning:</strong> This action cannot be undone.';
        $footerButtons = sprintf(
            '<button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>' .
            '<button type="button" class="btn btn-primary delete-team-btn" data-url="%s" data-delete="true" data-token="%s">Delete</button>',
            $deleteUrl,
            $csrfToken
        );

        $deleteModal = $this->modalboxHelper->renderModal(
            $modalId,
            'Confirm Team Member Removal',
            $modalBody,
            $footerButtons
        );

        return sprintf(
            '<div class="d-inline-flex gap-2">
                <button type="button" class="bs-btn btn btn-primary btn-sm" title="Remove" style="margin: 2px;" data-toggle="modal" data-target="#%s">
                    <i class="bs-fa fa fa-trash"></i>
                </button>
            </div>%s',
            $modalId,
            $deleteModal
        );
    }

    /**
     * Builds the query for the project team DataTable.
     *
     * @param QueryBuilder $qb
     * @param int $project_id
     */
    private function buildQuery(QueryBuilder $qb, int $project_id): void
    {
        $qb->select('pt', 'm', 'admin', 'leader')
            ->from(ProjectTeam::class, 'pt')
            ->leftJoin('pt.teamMember', 'm')
            ->leftJoin('pt.assignedByAdmin', 'admin')
            ->leftJoin('pt.assignedByTeamLeader', 'leader')
            ->where('pt.project = :project_id')
            ->setParameter('project_id', $project_id);
    }
}