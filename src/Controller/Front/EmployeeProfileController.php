<?php

namespace App\Controller\Front;

use App\Entity\MasterEmployee;
use App\Form\EmployeePersonalInfoType;
use App\Repository\HardwareRepository;
use App\Traits\ExceptionLoggerTrait;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\Request;

class EmployeeProfileController extends AbstractController
{
    use ExceptionLoggerTrait;

    public function __construct(
        ParameterBagInterface $parameterBag,
        private readonly HardwareRepository $hardwareRepository,
        private readonly EntityManagerInterface $entityManager,
    ) {
        $this->initializeLogger($parameterBag, 'employee_profile');
    }

    #[Route(path: '/employee-profile', name: 'employee_profile', methods: ['GET'])]
    public function employeeProfile(): Response
    {
        $user = $this->getUser();
        if (!$user instanceof MasterEmployee) {
            throw $this->createAccessDeniedException('You must be logged in as an employee to view this page.');
        }
        $hardware = $this->hardwareRepository->findBy(['employee' => $user]);
        return $this->render('Front/profile/employee-profile.html.twig', [
            'employee' => $user,
            'hardware' => $hardware,
        ]);
    }

    #[Route(path: '/employee-profile/edit-personal-info', name: 'employee_profile_edit_personal_info', methods: ['GET', 'POST'])]
    public function editPersonalInfo(Request $request): Response
    {
        $user = $this->getUser();
        if (!$user instanceof MasterEmployee) {
            throw $this->createAccessDeniedException('You must be logged in as an employee to edit this page.');
        }
        $form = $this->createForm(EmployeePersonalInfoType::class, $user);
        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $this->entityManager->persist($user);
            $this->entityManager->flush();
            $this->addFlash('success', 'Personal information updated successfully.');
            return $this->redirectToRoute('employee_profile');
        }
        return $this->render('Front/profile/edit-personal-info.html.twig', [
            'form' => $form->createView(),
        ]);
    }
}