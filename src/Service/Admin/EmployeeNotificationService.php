<?php

namespace App\Service\Admin;

use App\Entity\Employee;
use App\Entity\EmployeeNotification;
use App\Entity\LeaveRequest;
use App\Helper\TimeHelper;
use App\Message\EmployeeNotificationMessage;
use App\Repository\EmployeeRepository;
use App\Repository\EmployeeHoursRepository;
use App\Repository\LeaveRequestRepository;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\Messenger\Exception\ExceptionInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use App\Traits\ExceptionLoggerTrait;
use App\Helper\SettingHelper;

class EmployeeNotificationService
{
    use ExceptionLoggerTrait;

    public function __construct(
        private readonly EmployeeRepository      $employeeRepository,
        private readonly EmployeeHoursRepository $employeeHoursRepository,
        private readonly LeaveRequestRepository  $leaveRequestRepository,
        private readonly MessageBusInterface     $messageBus,
        ParameterBagInterface                    $parameterBag,
        private readonly SettingHelper           $settingHelper,
        private readonly TimeHelper              $timeHelper,
    )
    {
        $this->initializeLogger($parameterBag, 'email_notifications_service');
    }

    /**
     * @param float $threshold
     * @return void
     * @throws ExceptionInterface
     */
    public function sendNotification(float $threshold, \DateTimeImmutable $date): void
    {
        $employees = $this->employeeRepository->findAll();
        $today = $date;

        foreach ($employees as $employee) {
            if ($employee->getMasterEmployee()->getIsDelete()) {
                continue;
            }
            $leaveRequest = $this->leaveRequestRepository->findOneBy([
                'employee' => $employee->getMasterEmployee(),
                'startDate' => $today,
                'status' => LeaveRequest::STATUS_APPROVED
            ]);
            if ($leaveRequest && !$leaveRequest->getStartHalfDay() && !$leaveRequest->getEndHalfDay()) {
                $this->log(
                    'Employee is on full-day leave, skipping notification.',
                    [
                        'employee_id' => $employee->getId(),
                        'employee_name' => $employee->getMasterEmployee()?->getName() ?? '',
                        'date' => $today->format('Y-m-d'),
                    ]
                );
                continue;
            }

            $employeeHours = $this->employeeHoursRepository->findBy([
                'employee' => $employee,
                'reportDate' => $today,
            ]);
            $totalHours = 0;

            foreach ($employeeHours as $hoursRecord) {
                $totalHours += $hoursRecord->getTotalHours();
            }
            if ($totalHours === 0 && !$leaveRequest) {
                $this->log(
                    'Employee is considered on leave (no hours recorded).',
                    [
                        'employee_id' => $employee->getId(),
                        'employee_name' => $employee->getMasterEmployee()?->getName() ?? '',
                        'date' => $today->format('Y-m-d'),
                    ]
                );
                continue;
            }

            $MinHours = $this->settingHelper->getSettingConfiguration()['MinHours'];
            $formattedTotalHours = $this->timeHelper->convertDecimalToTimeFormat($totalHours);
            $effectiveThreshold = $threshold;
            if ($leaveRequest && ($leaveRequest->getStartHalfDay() || $leaveRequest->getEndHalfDay())) {
                $effectiveThreshold = $threshold / 2;
            }

            if ($formattedTotalHours > $MinHours && $formattedTotalHours < $effectiveThreshold && !empty($employeeHours)) {
                try {
                    $message = new EmployeeNotificationMessage(
                        $employee->getId(),
                        $totalHours,
                        $employeeHours[0]->getId(),
                        $today->format('Y-m-d'),
                        false,
                        null,
                        false,
                        null
                    );

                    $this->messageBus->dispatch($message);
                }catch (\Exception $e){
                    $this->log('Notification dispatch failed: ' . $e->getMessage());
                }

                $this->log(
                    'Notification sent for insufficient hours.',
                    [
                        'employee_id' => $employee->getId(),
                        'total_hours' => $totalHours,
                        'threshold' => $effectiveThreshold,
                        'date' => $today->format('Y-m-d'),
                    ]
                );
            }
        }
    }

    /**
     * @throws ExceptionInterface
     */
    public function resendJustificationLink(EmployeeNotification $notification): void
    {
        $employee = $notification->getEmployee();
        if(!$employee instanceof Employee) {
            return;
        }
        if ($employee->getMasterEmployee()->getIsDelete()) {
            $this->log(
                'Cannot resend justification link for deleted employee.',
                [
                    'notification_id' => $notification->getId(),
                    'employee_id' => $employee->getId(),
                ]
            );
            return;
        }
        $employeeId = $notification->getEmployee()->getId();
        $totalHours = $notification->getEmployeeHours()->getTotalHours();
        $hoursRecordId = $notification->getEmployeeHours()->getId();
        $reportDate = $notification->getEmployeeHours()->getReportDate()->format('Y-m-d');
        if (!$employeeId || !$totalHours || !$hoursRecordId || !$reportDate) {
            $this->log(
                'Invalid notification data for resending justification link.',
                ['notification_id' => $notification->getId()]
            );
            return;
        }

        try {
            $message = new EmployeeNotificationMessage(
                $employeeId,
                $totalHours,
                $hoursRecordId,
                $reportDate,
                false,
                null,
                true,
                null
            );

            $this->messageBus->dispatch($message);
        }catch (\Exception $e){
            $this->log('Notification dispatch failed: ' . $e->getMessage());
        }
    }
}