{% extends 'admin/dashboard/base.html.twig' %}


{% block title %}
    {{ emailTemplate.id is defined ? 'Edit Email Template' : 'Create Email Template' }}
{% endblock %}

{% block body %}
    {% include 'admin/partials/breadcrumbs.html.twig' with {
        title: 'Email',
        breadcrumbs: [
            { 'label': 'Dashboard', 'url': url('admin_dashboard') },
        ],
        active: 'Email Template'
    } %}
    <div class="content">
        <div class="animated fadeIn">
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <strong class="card-title">{{ emailTemplate.id is defined ? 'Edit Email Template' : 'Create Email Template' }}</strong>
                        </div>
                        <div class="card-body">
                            <div id="pay-invoice">
                                <div class="card-body">
                                    {{ form_start(form, { 'attr': { 'class': 'needs-validation', 'novalidate': 'novalidate' } }) }}

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                {{ form_label(form.identifier, 'Template Identifier', { 'label_attr': { 'class': 'form-label control-label mb-1', } }) }}
                                                {{ form_widget(form.identifier, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter unique identifier' } }) }}
                                                {% if form.identifier.vars.errors|length > 0 %}
                                                    <div class="invalid-feedback">
                                                        {{ form_errors(form.identifier) }}
                                                    </div>
                                                {% endif %}
                                            </div>
                                        </div>

                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                {{ form_label(form.name, 'Template Name', { 'label_attr': { 'class': 'form-label control-label mb-1 ' } }) }}
                                                {{ form_widget(form.name, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter template name' } }) }}
                                                {% if form.identifier.vars.errors|length > 0 %}
                                                    <div class="invalid-feedback">
                                                        {{ form_errors(form.name) }}
                                                    </div>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="mb-3">
                                                {{ form_label(form.content, 'Template Content', { 'label_attr': { 'class': 'form-label control-label mb-1 ' } }) }}
                                                {{ form_widget(form.content, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter template content' } }) }}
                                                {% if form.identifier.vars.errors|length > 0 %}
                                                    <div class="invalid-feedback">
                                                        {{ form_errors(form.content) }}
                                                    </div>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>

                                    <div class="d-flex justify-content-between">
                                        <a href="{{ path('email_template_list') }}" class="btn btn-secondary">Back to List</a>
                                        <button type="submit" class="btn btn-primary pl-3 pr-3">
                                            {{ emailTemplate.id is defined ? 'Update Template' : 'Create Template' }}
                                        </button>
                                    </div>

                                    {{ form_end(form) }}

                                </div>
                            </div>
                        </div>
                    </div> <!-- .card -->
                </div><!--/.col-->
            </div>
        </div>
    </div>
    <div class="clearfix"></div>
    <script src="{{ asset('assets/js/admin/emailTemplate.js') }}"></script>
{% endblock %}
