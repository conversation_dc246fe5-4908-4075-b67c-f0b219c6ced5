{% extends 'admin/dashboard/base.html.twig' %}

{% block body %}
    {% include 'admin/partials/breadcrumbs.html.twig' with {
        title: 'Departments',
        breadcrumbs: [
            { 'label': 'Dashboard', 'url': url('admin_dashboard') },
            { 'label': 'Department', 'url': url('department') }
        ],
        active: mode == 'view' ? 'View Department' : (mode == 'edit' ? 'Edit Department' : 'Create Department')
    } %}

    <div class="content">
        <div class="animated fadeIn">
            <div class="row">
                <div class="col-lg-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <strong class="card-title">
                                {{ mode == 'view' ? 'View Department' : (mode == 'edit' ? 'Edit Department' : 'Create Department') }}
                            </strong>
                            <div class="d-flex align-items-center">
                                <a href="{{ path('department') }}" class="btn btn-sm btn-outline-secondary mr-2">
                                    <i class="fa fa-list mr-1"></i>Back to List
                                </a>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="department-form">
                                {% if form is defined %}
                                    {{ form_start(form, { 'attr': { 'class': 'needs-validation use-loader', 'novalidate': 'novalidate' } }) }}
                                    <div class="form-section">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.depName, 'Department Name', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-building"></i></span>
                                                        </div>
                                                        {{ form_widget(form.depName, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter department name', 'required': 'required' } }) }}
                                                    </div>
                                                    {{ form_errors(form.depName) }}
                                                    <small class="form-text text-muted">Enter a department name</small>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.depDescription, 'Description', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-info-circle"></i></span>
                                                        </div>
                                                        {{ form_widget(form.depDescription, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter department description' } }) }}
                                                    </div>
                                                    {{ form_errors(form.depDescription) }}
                                                    <small class="form-text text-muted">Provide a brief description of the department</small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.depStatus, 'Status', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-toggle-on"></i></span>
                                                        </div>
                                                        {{ form_widget(form.depStatus, { 'attr': { 'class': 'form-control', 'aria-label': 'Select Status' } }) }}
                                                    </div>
                                                    {{ form_errors(form.depStatus) }}
                                                    <small class="form-text text-muted">Select the department's status (Active/Inactive)</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="d-flex justify-content-end mt-4">
                                        <a href="{{ path('department') }}" class="btn btn-secondary mr-2">
                                           Cancel
                                        </a>
                                        <button type="submit" class="btn btn-primary pr-3 pl-3">
                                            {{ department.id is defined ? 'Update Department' : 'Create Department' }}
                                        </button>
                                    </div>
                                    {{ form_end(form) }}
                                {% else %}
                                    <!-- View Mode -->
                                    <div class="form-section employee-details-section">
                                        <div class="details-section">
                                            <div class="details-container">
                                                <div class="detail-row">
                                                    <div class="detail-item col-md-4"><strong class="detail-label">Department Name:</strong></div>
                                                    <div class="detail-item col-md-8">{{ department.depName ?: 'N/A' }}</div>
                                                </div>
                                                <div class="detail-row">
                                                    <div class="detail-item col-md-4"><strong class="detail-label">Description:</strong></div>
                                                    <div class="detail-item col-md-8">{{ department.depDescription ?: 'N/A' }}</div>
                                                </div>
                                                <div class="detail-row">
                                                    <div class="detail-item col-md-4"><strong class="detail-label">Status:</strong></div>
                                                    <div class="detail-item col-md-8">{{ department.depStatus ? 'Active' : 'Inactive' }}</div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="action-footer mt-4">
                                            <a href="{{ path('department') }}" class="btn btn-secondary mr-2">
                                                Back to List
                                            </a>
                                            <a href="{{ path('department_edit', {'id': department.id}) }}" class="btn btn-primary pr-3 pl-3" style="padding-top: 10px;">
                                               Edit Department
                                            </a>
                                        </div>
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div> <!-- .card -->
                </div><!--/.col-->
            </div>
        </div>
    </div>
    <div class="clearfix"></div>
    <link rel="stylesheet" href="{{ asset('assets/css/Error/error.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/AdminForm/Form.css') }}">
    <script src="{{ asset('assets/js/admin/department.js') }}"></script>
{% endblock %}