<?php

namespace App\Controller\Admin;

use AllowDynamicProperties;
use App\Repository\AnnouncementRepository;
use App\Repository\EmployeeNotificationRepository;
use App\Repository\EmployeeRemainingHoursRepository;
use App\Repository\EmployeeRepository;
use App\Repository\LeaveRequestRepository;
use App\Repository\HardwareRepository;
use App\Repository\MasterEmployeeRepository;
use App\Traits\ExceptionLoggerTrait;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

#[AllowDynamicProperties]
class DashboardController extends AbstractController
{
    use ExceptionLoggerTrait;

    public function __construct(
        ParameterBagInterface                           $parameterBag,
        private readonly EmployeeRepository             $employeeRepository,
        private readonly EmployeeNotificationRepository $employeeNotificationRepository,
        private readonly EmployeeRemainingHoursRepository $employeeRemainingHoursRepository,
        private readonly MasterEmployeeRepository         $masterEmployeeRepository,
        private readonly AnnouncementRepository           $announcementRepository,
        private readonly LeaveRequestRepository           $leaveRequestRepository,
        private readonly HardwareRepository                $hardwareRepository,
    )
    {
        $this->initializeLogger($parameterBag, 'dashboard');
    }

    /**
     * @return Response
     * @throws \DateMalformedStringException
     */
    #[Route(path: '/admin/dashboard', name: 'admin_dashboard', methods: ['GET'])]
    public function index(): Response
    {
        $employeeCount = $this->employeeRepository->count([]);
        $employeeHours =$this->employeeNotificationRepository->countJustificationGiven();
        $justificationNotGivenCount = $this->employeeNotificationRepository->countJustificationNotGiven();
        $remaining = $this->employeeRemainingHoursRepository->countUnapprovedRemainingHours();
        $birthday = $this->masterEmployeeRepository->findUpcomingBirthdays();
        $anniversary = $this->masterEmployeeRepository->findUpcomingAnniversaries();

        $justifiable = $this->employeeNotificationRepository->findAllLatestSentAt();
        $announcements = $this->announcementRepository->findImportantAnnouncements(10);

        $today = new \DateTime('today');
        $endOfWeek = (clone $today)->modify('+7 days');
        $weeklyLeaveRequests = $this->leaveRequestRepository->findByDateRange($today, $endOfWeek);

        $hardware = $this->hardwareRepository->findBy(['showOnDashboard' => true]);

        return $this->render('admin/include/main.html.twig', [
            'employeeCount' => $employeeCount,
            'justificationGivenCount' => $employeeHours,
            'justificationNotGivenCount' => $justificationNotGivenCount,
            'justifiable'=>$justifiable,
            'remainingHours'=>$remaining,
            'birthday'=>$birthday,
            'anniversary'=>$anniversary,
            'announcements' => $announcements,
            'weeklyLeaveRequests' => $weeklyLeaveRequests,
            'hardware' => $hardware,
        ]);
    }
}
