{% extends 'admin/dashboard/base.html.twig' %}

{% block body %}
    {% include 'admin/partials/breadcrumbs.html.twig' with {
        title: 'Holidays',
        breadcrumbs: [
            { 'label': 'Dashboard', 'url': url('admin_dashboard') },
            { 'label': 'Holidays', 'url': url('holiday_list') }
        ],
        active: mode == 'edit' ? 'Edit Holiday' : 'Create Holiday'
    } %}

    <div class="content">
        <div class="animated fadeIn">
            <div class="row">
                <div class="col-lg-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <strong class="card-title">
                                {{ mode == 'edit' ? 'Edit Holiday' : 'Create Holiday' }}
                            </strong>
                            <div class="d-flex align-items-center">
                                <a href="{{ path('holiday_list') }}" class="btn btn-sm btn-outline-secondary mr-2">
                                    <i class="fa fa-list mr-1"></i>Back to List
                                </a>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="holiday-form">
                                {{ form_start(form, {
                                    'action': mode == 'edit' ? path('holiday_edit') : path('holiday_create'),
                                    'method': 'POST',
                                    'attr': { 'class': 'needs-validation use-loader', 'novalidate': 'novalidate' },
                                    'data-turbo': 'false'
                                }) }}
                                {% if mode == 'edit' and holiday.id is defined %}
                                    <input type="hidden" name="id" value="{{ holiday.id }}">
                                {% endif %}
                                <div class="form-section">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                {{ form_label(form.name, 'Holiday Name', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text"><i class="fa fa-hand-o-right"></i></span>
                                                    </div>
                                                    {{ form_widget(form.name, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter holiday name', 'required': 'required' } }) }}
                                                </div>
                                                {{ form_errors(form.name) }}
                                                <small class="form-text text-muted">Enter the name of the holiday</small>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                {{ form_label(form.description, 'Description', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text"><i class="fa fa-info-circle"></i></span>
                                                    </div>
                                                    {{ form_widget(form.description, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter holiday description' } }) }}
                                                </div>
                                                {{ form_errors(form.description) }}
                                                <small class="form-text text-muted">Provide a brief description of the holiday (optional)</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                {{ form_label(form.date, 'Date', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text"><i class="fa fa-calendar-check-o"></i></span>
                                                    </div>
                                                    {{ form_widget(form.date, {
                                                        'attr': {
                                                            'class': 'form-control datepicker',
                                                            'data-initial-date': holiday.date is defined and holiday.date ? holiday.date|date('d-m-Y') : ''
                                                        }
                                                    }) }}
                                                </div>
                                                {{ form_errors(form.date) }}
                                                <small class="form-text text-muted">Select the date of the holiday</small>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                {{ form_label(form.isPublicHoliday, 'Public Holiday', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text">
                                                            {{ form_widget(form.isPublicHoliday, { 'attr': { 'class': 'custom-checkbox', 'aria-label': 'Checkbox for Public Holiday' } }) }}
                                                        </span>
                                                    </div>
                                                    <div class="form-control checkbox-label">
                                                        {{ form_label(form.isPublicHoliday, null, { 'label_attr': { 'class': 'ml-2' } }) }}
                                                    </div>
                                                </div>
                                                {{ form_errors(form.isPublicHoliday) }}
                                                <small class="form-text text-muted">Check if this is a public holiday</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="d-flex justify-content-end mt-4">
                                    <a href="{{ path('holiday_list') }}" class="btn btn-secondary mr-2">
                                        Cancel
                                    </a>
                                    <button type="submit" class="btn btn-primary pr-3 pl-3">
                                        {{ mode == 'edit' ? 'Update Holiday' : 'Create Holiday' }}
                                    </button>
                                </div>
                                {{ form_end(form) }}
                            </div>
                        </div>
                    </div> <!-- .card -->
                </div><!--/.col-->
            </div>
        </div>
    </div>
    <div class="clearfix"></div>
    <link rel="stylesheet" href="{{ asset('assets/css/Error/error.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/AdminForm/Form.css') }}">
    <script src="{{ asset('assets/js/datatables.min.js') }}"></script>
    <script src="{{ asset('bundles/datatables/js/datatables.js') }}"></script>
    <script src="{{ asset('assets/js/HolidayDatepicker.js') }}"></script>
    <script src="{{ asset('assets/js/moment.min.js') }}"></script>
    <script src="{{ asset('assets/js/daterangepicker.min.js') }}"></script>
    <script src="{{ asset('assets/js/admin/holiday.js') }}"></script>
{% endblock %}