<?php

namespace App\Service\Admin;

use App\Entity\EmployeeRemainingHours;
use App\Entity\EmployeeHours;
use App\Helper\SettingHelper;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

class EmployeeHoursTrackerService
{
    private float $standardHours;

    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly SettingHelper $settingHelper
    ) {
        $this->standardHours = (float)$this->settingHelper->getSettingConfiguration()['hours'];
    }

    public function processRemainingHours(SymfonyStyle $io): array
    {
        $io->info("Fetching remaining hours entities...");
        $remainingHoursEntities = $this->entityManager->getRepository(EmployeeRemainingHours::class)
            ->createQueryBuilder('rh')
            ->leftJoin('rh.employeeNotification', 'en')
            ->where('rh.isCompleted = :isCompleted')
            ->andWhere('rh.isApproved = :isApproved')
            ->setParameter('isCompleted', false)
            ->setParameter('isApproved', false)
            ->orderBy('rh.createdAt', 'ASC')
            ->addOrderBy('rh.id', 'ASC')
            ->getQuery()
            ->getResult();

        if (empty($remainingHoursEntities)) {
            $io->info("No remaining hours entities found.");
            return ['processedCount' => 0];
        }

        $io->info(sprintf("Found %d remaining hours entities.", count($remainingHoursEntities)));
        $currentDate = new \DateTime('now', new \DateTimeZone('Asia/Kolkata'));
        $processedCount = 0;

        $groupedEntries = [];
        foreach ($remainingHoursEntities as $remainingHours) {
            $employeeId = $remainingHours->getEmployeeNotification()->getEmployee()->getId();
            $createdAtKey = $remainingHours->getCreatedAt()->format('Y-m-d');
            $groupedEntries[$employeeId][$createdAtKey][] = $remainingHours;
            $io->info(sprintf(
                "Grouped entry: Employee ID %d, Created At %s, Entry ID %d, Remaining Hours %.2f",
                $employeeId,
                $remainingHours->getCreatedAt()->format('Y-m-d H:i:s'),
                $remainingHours->getId(),
                $remainingHours->getRemainingHoursToComplete()
            ));
        }

        foreach ($groupedEntries as $employeeId => $createdAtGroups) {
            foreach ($createdAtGroups as $createdAtKey => $entries) {
                $io->info(sprintf("Processing group: Employee ID %d, Created At %s, %d entries", $employeeId, $createdAtKey, count($entries)));
                if ($this->processEmployeeGroup($entries, $currentDate, $io)) {
                    $processedCount += count($entries);
                }
            }
        }

        $io->info("Flushing changes to the database...");
        $this->entityManager->flush();
        return ['processedCount' => $processedCount];
    }

    private function processEmployeeGroup(array $remainingHoursGroup, \DateTime $currentDate, SymfonyStyle $io): bool
    {
        $employee = $remainingHoursGroup[0]->getEmployeeNotification()->getEmployee();
        $io->info(sprintf("Processing employee: %s", $employee->getMasterEmployee()?->getName() ?? "Unknown"));
        $startDate = null;

        // Find earliest start date
        foreach ($remainingHoursGroup as $remainingHours) {
            $dailyCompletions = $remainingHours->getDailyHoursCompletion();
            $lastProcessedDate = !empty($dailyCompletions)
                ? (new \DateTime(end($dailyCompletions)['date']))->setTime(0, 0, 0)
                : null;

            $entryStartDate = $lastProcessedDate
                ? (clone $lastProcessedDate)->modify('+1 day')
                : clone $remainingHours->getCreatedAt();

            if (!$startDate || $entryStartDate < $startDate) {
                $startDate = $entryStartDate;
            }
        }

        $startDate = (new \DateTime())->setTimestamp($startDate->getTimestamp())->setTime(0, 0, 0);
        $currentDate = (new \DateTime())->setTimestamp($currentDate->getTimestamp())->setTime(0, 0, 0);
        $io->info(sprintf("Date range: Start %s, End %s", $startDate->format('Y-m-d'), $currentDate->format('Y-m-d')));

        $dailyHours = $this->entityManager->getRepository(EmployeeHours::class)
            ->createQueryBuilder('eh')
            ->where('eh.employee = :employee')
            ->andWhere('eh.reportDate BETWEEN :startDate AND :endDate')
            ->setParameter('employee', $employee)
            ->setParameter('startDate', $startDate)
            ->setParameter('endDate', $currentDate)
            ->orderBy('eh.reportDate', 'ASC')
            ->getQuery()
            ->getResult();

        $io->info(sprintf("Found %d daily hours records.", count($dailyHours)));

        foreach ($dailyHours as $dailyHour) {
            $date = clone $dailyHour->getReportDate();
            $dateString = $date->format('Y-m-d');
            $totalHoursWorked = $dailyHour->getTotalHours();

            $io->info(sprintf("Processing date %s: Total hours worked %.2f, Standard hours %.2f", $dateString, $totalHoursWorked, $this->standardHours));

            if ($totalHoursWorked <= $this->standardHours) {
                $io->info("No extra hours available for this date.");
                continue;
            }

            $extraHoursAvailable = $totalHoursWorked - $this->standardHours;
            $io->info(sprintf("Extra hours available: %.2f", $extraHoursAvailable));

            // Process each remaining hours entry sequentially for this date
            foreach ($remainingHoursGroup as $remainingHours) {
                $io->info(sprintf("Checking entry ID %d: Remaining hours to complete %.2f", $remainingHours->getId(), $remainingHours->getRemainingHoursToComplete()));

                if ($extraHoursAvailable <= 0 || $remainingHours->isCompleted()) {
                    $io->info("Skipping: No extra hours available or entry already completed.");
                    continue;
                }

                $remainingToComplete = $remainingHours->getRemainingHoursToComplete();
                if ($remainingToComplete <= 0) {
                    $io->info("Skipping: No remaining hours to complete.");
                    continue;
                }

                // Check if this date already exists in the current entry's daily completions
                $dateExists = false;
                foreach ($remainingHours->getDailyHoursCompletion() as $completion) {
                    if ($completion['date'] === $dateString) {
                        $dateExists = true;
                        break;
                    }
                }

                if ($dateExists) {
                    $io->info("Skipping: Date already exists in daily completions.");
                    continue;
                }

                $hoursToAdd = min($extraHoursAvailable, $remainingToComplete);
                $io->info(sprintf("Applying %.2f hours to entry ID %d", $hoursToAdd, $remainingHours->getId()));
                $remainingHours->addDailyCompletion($date, $hoursToAdd);
                $extraHoursAvailable -= $hoursToAdd;

                $io->info(sprintf(
                    'Added %.2f extra hours from %s for employee %s (Worked: %.2f, Standard: %.2f) to entry ID %d',
                    $hoursToAdd,
                    $date->format('Y-m-d'),
                    $employee->getMasterEmployee()?->getName() ?? "Unknown",
                    $totalHoursWorked,
                    $this->standardHours,
                    $remainingHours->getId()
                ));

                $io->info(sprintf("Remaining extra hours after application: %.2f", $extraHoursAvailable));

                if ($remainingHours->getCompletedHours() >= $remainingHours->getTotalRemainingHours()) {
                    $this->handleCompletion($remainingHours);
                    $io->info(sprintf(
                        'Employee %s has completed all remaining hours for entry ID %d',
                        $employee->getMasterEmployee()?->getName() ?? "Unknown",
                        $remainingHours->getId()
                    ));
                }

                if ($extraHoursAvailable <= 0) {
                    $io->info("No more extra hours available for this date. Breaking loop.");
                    break;
                }
            }
        }

        return true;
    }

    private function handleCompletion(EmployeeRemainingHours $remainingHours): void
    {
        $notification = $remainingHours->getEmployeeNotification();

        $remainingHours->approve();

        if ($notification !== null) {
            $notification->setIsApproved(true);
            $notification->setStatus(true);
            $notification->setJustifiedAt(new \DateTime());
            $notification->setDisapprovalReason(null);
        }
    }
}