<?php

namespace App\Controller\Admin;

use App\Entity\TrackingConfiguration;
use App\Repository\TrackingConfigurationRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

#[Route('/admin/tracking-config', name: 'admin_tracking_config_')]
class TrackingConfigurationController extends AbstractController
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private TrackingConfigurationRepository $configRepository
    ) {}

    #[Route('/', name: 'index')]
    public function index(): Response
    {
        // Get global configs where employee is NULL
        $globalConfigs = $this->configRepository->findBy([
            'configType' => 'global',
            'employee' => null  // Global configs have null employee
        ]);

        // Organize configs by category
        $organizedConfigs = $this->organizeConfigs($globalConfigs);

        return $this->render('admin/tracking_configuration/index.html.twig', [
            'configs' => $organizedConfigs,
            'categories' => $this->getConfigCategories()
        ]);
    }

    #[Route('/update', name: 'update', methods: ['POST'])]
    public function update(Request $request): Response
    {
        try {
            $configs = $request->request->all('configs');
            
            foreach ($configs as $key => $value) {
                $config = $this->configRepository->findOneBy([
                    'configKey' => $key,
                    'configType' => 'global',
                    'employee' => null  // Global configs have null employee
                ]);

                if (!$config) {
                    $config = new TrackingConfiguration();
                    $config->setConfigKey($key);
                    $config->setConfigType('global');
                    $config->setEmployee(null);  // Explicitly set to null for global configs
                    $config->setDescription($this->getConfigDescription($key));
                }

                $config->setConfigValue((string) $value);
                $this->entityManager->persist($config);
            }
            
            $this->entityManager->flush();
            
            $this->addFlash('success', 'Tracking configuration updated successfully!');
        } catch (\Exception $e) {
            $this->addFlash('error', 'Failed to update configuration: ' . $e->getMessage());
        }
        
        return $this->redirectToRoute('admin_tracking_config_index');
    }

    #[Route('/reset-defaults', name: 'reset_defaults', methods: ['POST'])]
    public function resetDefaults(): Response
    {
        try {
            $defaultConfigs = $this->getDefaultConfigs();
            
            foreach ($defaultConfigs as $key => $value) {
                $config = $this->configRepository->findOneBy([
                    'configKey' => $key,
                    'configType' => 'global',
                    'employee' => null  // Global configs have null employee
                ]);

                if (!$config) {
                    $config = new TrackingConfiguration();
                    $config->setConfigKey($key);
                    $config->setConfigType('global');
                    $config->setEmployee(null);  // Explicitly set to null for global configs
                    $config->setDescription($this->getConfigDescription($key));
                }

                $config->setConfigValue((string) $value);
                $this->entityManager->persist($config);
            }
            
            $this->entityManager->flush();
            
            $this->addFlash('success', 'Configuration reset to defaults successfully!');
        } catch (\Exception $e) {
            $this->addFlash('error', 'Failed to reset configuration: ' . $e->getMessage());
        }
        
        return $this->redirectToRoute('admin_tracking_config_index');
    }

    private function organizeConfigs(array $configs): array
    {
        $organized = [];
        $categories = $this->getConfigCategories();
        
        // Initialize categories
        foreach ($categories as $category => $info) {
            $organized[$category] = [
                'title' => $info['title'],
                'description' => $info['description'],
                'configs' => []
            ];
        }
        
        // Add existing configs
        foreach ($configs as $config) {
            $category = $this->getConfigCategory($config->getConfigKey());
            if (isset($organized[$category])) {
                $organized[$category]['configs'][$config->getConfigKey()] = $config;
            }
        }
        
        // Add missing configs with defaults
        $defaults = $this->getDefaultConfigs();
        foreach ($defaults as $key => $value) {
            $category = $this->getConfigCategory($key);
            if (isset($organized[$category]) && !isset($organized[$category]['configs'][$key])) {
                $config = new TrackingConfiguration();
                $config->setConfigKey($key);
                $config->setConfigValue((string) $value);
                $config->setDescription($this->getConfigDescription($key));
                $organized[$category]['configs'][$key] = $config;
            }
        }
        
        return $organized;
    }

    private function getConfigCategories(): array
    {
        return [
            'idle_detection' => [
                'title' => 'Idle Detection',
                'description' => 'Configure how the system detects user inactivity'
            ],
            'time_tracking' => [
                'title' => 'Time Tracking',
                'description' => 'General time tracking settings'
            ],
            'screenshots' => [
                'title' => 'Screenshot Capture',
                'description' => 'Configure automatic screenshot capture'
            ],

            'application_tracking' => [
                'title' => 'Application Tracking',
                'description' => 'Monitor application usage and productivity'
            ],
            'website_tracking' => [
                'title' => 'Website Tracking',
                'description' => 'Monitor website visits and browsing activity'
            ],
            'productivity' => [
                'title' => 'Productivity Settings',
                'description' => 'Configure productivity analysis and categorization'
            ]
        ];
    }

    private function getConfigCategory(string $key): string
    {
        $categoryMap = [
            // Idle Detection (Simplified)
            'idle_timeout_enabled' => 'idle_detection',
            'idle_timeout_minutes' => 'idle_detection',
            
            // Time Tracking
            'silent_mode_enabled' => 'time_tracking',
            'auto_start_tracking' => 'time_tracking',
            'max_daily_hours' => 'time_tracking',
            'min_session_duration' => 'time_tracking',
            'max_session_duration' => 'time_tracking',
            'working_hours_start' => 'time_tracking',
            'working_hours_end' => 'time_tracking',
            
            // Screenshots
            'screenshot_enabled' => 'screenshots',
            'screenshot_interval_seconds' => 'screenshots',
            'screenshot_quality' => 'screenshots',
            'screenshot_burst_mode' => 'screenshots',
            'screenshot_random_interval' => 'screenshots',
            

            
            // Application Tracking
            'app_tracking_enabled' => 'application_tracking',
            'track_window_titles' => 'application_tracking',
            'productivity_categorization' => 'application_tracking',
            
            // Website Tracking
            'website_tracking_enabled' => 'website_tracking',
            'track_browser_tabs' => 'website_tracking',
            'website_categorization' => 'website_tracking',
            
            // Productivity
            'productivity_analysis_enabled' => 'productivity',
            'break_reminder_interval' => 'productivity',
            'focus_time_threshold' => 'productivity'
        ];
        
        return $categoryMap[$key] ?? 'general';
    }

    private function getDefaultConfigs(): array
    {
        return [
            // Idle Detection (Simplified)
            'idle_timeout_enabled' => '1',
            'idle_timeout_minutes' => '5', // 5 minutes default
            
            // Time Tracking
            'silent_mode_enabled' => '1',
            'auto_start_tracking' => '1',
            'max_daily_hours' => '8',
            'min_session_duration' => '60', // 1 minute
            'max_session_duration' => '14400', // 4 hours
            'working_hours_start' => '9',
            'working_hours_end' => '18',
            
            // Screenshots
            'screenshot_enabled' => '1',
            'screenshot_interval_seconds' => '300', // 5 minutes
            'screenshot_quality' => '80',
            'screenshot_burst_mode' => '0',
            'screenshot_random_interval' => '1',
            

            
            // Application Tracking
            'app_tracking_enabled' => '1',
            'track_window_titles' => '1',
            'productivity_categorization' => '1',
            
            // Website Tracking
            'website_tracking_enabled' => '1',
            'track_browser_tabs' => '1',
            'website_categorization' => '1',
            
            // Productivity
            'productivity_analysis_enabled' => '1',
            'break_reminder_interval' => '60', // 1 hour
            'focus_time_threshold' => '25' // 25 minutes
        ];
    }

    private function getConfigDescription(string $key): string
    {
        $descriptions = [
            'idle_timeout_enabled' => 'Enable idle time detection and warnings',
            'idle_timeout_minutes' => 'Minutes of inactivity before showing idle warning',
            
            'silent_mode_enabled' => 'Run tracking silently without user interface controls',
            'auto_start_tracking' => 'Automatically start tracking when application launches',
            'max_daily_hours' => 'Maximum hours per day that can be tracked',
            'min_session_duration' => 'Minimum session duration (seconds) to be recorded',
            'max_session_duration' => 'Maximum session duration (seconds) before auto-break',
            'working_hours_start' => 'Start of working hours (24-hour format)',
            'working_hours_end' => 'End of working hours (24-hour format)',
            
            'screenshot_enabled' => 'Enable automatic screenshot capture',
            'screenshot_interval_seconds' => 'Interval between screenshots (seconds)',
            'screenshot_quality' => 'Screenshot quality (1-100)',
            'screenshot_burst_mode' => 'Enable burst mode for frequent screenshots',
            'screenshot_random_interval' => 'Randomize screenshot intervals',
            

            
            'app_tracking_enabled' => 'Track application usage and window titles',
            'track_window_titles' => 'Record window titles for detailed tracking',
            'productivity_categorization' => 'Automatically categorize applications by productivity',
            
            'website_tracking_enabled' => 'Track website visits and browsing activity',
            'track_browser_tabs' => 'Monitor individual browser tabs',
            'website_categorization' => 'Automatically categorize websites by productivity',
            
            'productivity_analysis_enabled' => 'Enable productivity analysis and insights',
            'break_reminder_interval' => 'Interval for break reminders (minutes)',
            'focus_time_threshold' => 'Minimum focus time for productivity scoring (minutes)'
        ];
        
        return $descriptions[$key] ?? 'Configuration setting';
    }
}
