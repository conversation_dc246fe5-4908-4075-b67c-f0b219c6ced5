<?php

namespace App\Entity;

use DateTimeZone;
use Doctrine\ORM\Mapping as ORM;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use App\Service\TimezoneService;

#[ORM\Entity]
class EmployeeRemainingHours
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    /** @phpstan-ignore-next-line */
    private ?int $id = null;

    #[ORM\ManyToOne(targetEntity: EmployeeNotification::class)]
    #[ORM\JoinColumn(nullable: false)]
    private ?EmployeeNotification $employeeNotification = null;

    #[ORM\Column(type: 'decimal', precision: 10, scale: 2)]
    private float $totalRemainingHours;

    #[ORM\Column(type: 'json')]
    private array $dailyHoursCompletion = [];

    #[ORM\Column(type: 'boolean')]
    private bool $isCompleted = false;

    #[ORM\Column(type: 'datetime', nullable: true)]
    private ?\DateTimeInterface $completedAt = null;

    #[ORM\Column(type: 'boolean')]
    private bool $isApproved = false;

    #[ORM\Column(type: 'datetime', nullable: true)]
    private ?\DateTimeInterface $approvedAt = null;

    #[ORM\Column(type: 'datetime')]
    private \DateTimeInterface $createdAt;

    #[ORM\Column(type: 'json')]
    private array $emailContent = [];

    public function __construct(EmployeeNotification $notification, float $remainingHours)
    {
        $this->employeeNotification = $notification;
        $this->totalRemainingHours = $remainingHours;
        $this->dailyHoursCompletion = [];
        $timezone = new DateTimeZone('Asia/Kolkata');
        $this->createdAt = new \DateTime('now', $timezone);
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getEmployeeNotification(): ?EmployeeNotification
    {
        return $this->employeeNotification;
    }

    public function getTotalRemainingHours(): float
    {
        return $this->totalRemainingHours;
    }

    public function getCompletedHours(): float
    {
        return array_sum(array_map(function($entry) {
            return $entry['time'];
        }, $this->dailyHoursCompletion));
    }

    public function getRemainingHoursToComplete(): float
    {
        return $this->totalRemainingHours - $this->getCompletedHours();
    }

    public function addDailyCompletion(\DateTime $date, float $hours): self
    {
        $dateString = $date->format('Y-m-d');
        $this->dailyHoursCompletion[] = [
            'date' => $dateString,
            'time' => $hours
        ];

        if ($this->getCompletedHours() >= $this->totalRemainingHours) {
            $this->isCompleted = true;
            $this->completedAt = new \DateTime();
        }

        return $this;
    }

    public function getDailyHoursCompletion(): array
    {
        return $this->dailyHoursCompletion;
    }

    public function isCompleted(): bool
    {
        return $this->isCompleted;
    }

    public function getCompletedAt(): ?\DateTimeInterface
    {
        return $this->completedAt;
    }

    public function isApproved(): bool
    {
        return $this->isApproved;
    }

    public function approve(): self
    {
        $this->isApproved = true;
        $this->approvedAt = new \DateTime();
        return $this;
    }

    public function getApprovedAt(): ?\DateTimeInterface
    {
        return $this->approvedAt;

    }
    public function getCreatedAt(): \DateTimeInterface
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTimeInterface $createdAt): void
    {
        $this->createdAt = $createdAt;
    }

    public function getEmailContent(): array
    {
        return $this->emailContent ?? [];
    }

    public function addEmailContent(string $content): void
    {
        $timezone = new DateTimeZone('Asia/Kolkata');
        $currentDate = new \DateTime('now', $timezone);

        $newEntry = [
            'content' => $content,
            'sent_at' => $currentDate->format('Y-m-d H:i:s'),
        ];

        $existingContent = $this->getEmailContent();
        $existingContent[] = $newEntry;

        $this->emailContent = $existingContent;
    }
}
