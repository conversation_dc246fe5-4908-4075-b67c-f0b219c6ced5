<?php

namespace App\Service\Front;

use App\Entity\MasterEmployee;
use App\Entity\Task;
use App\Entity\TaskAssignment;
use App\Helper\ModalboxHelper;
use Doctrine\ORM\EntityManagerInterface;
use Omines\DataTablesBundle\Adapter\Doctrine\ORMAdapter;
use Omines\DataTablesBundle\Column\DateTimeColumn;
use Omines\DataTablesBundle\Column\TextColumn;
use Omines\DataTablesBundle\DataTable;
use Omines\DataTablesBundle\DataTableFactory;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Security\Csrf\CsrfTokenManagerInterface;
use App\Traits\ExceptionLoggerTrait;

class TaskAssignmentService
{
    use ExceptionLoggerTrait;

    public function __construct(
        private readonly DataTableFactory $dataTableFactory,
        private readonly EntityManagerInterface $entityManager,
        private readonly RouterInterface $router,
        private readonly CsrfTokenManagerInterface $csrfTokenManager,
        private readonly ModalboxHelper $modalboxHelper,
        ParameterBagInterface $parameterBag
    ) {
        $this->initializeLogger($parameterBag, 'task_assignment_service');
    }

    public function createTaskAssignmentTable(Request $request, Task $task, MasterEmployee $employee): DataTable
    {
        return $this->dataTableFactory->create()
            ->add('category', TextColumn::class, ['label' => 'Category'])
            ->add('priority', TextColumn::class, [
                'label' => 'Priority',
                'render' => fn($value) => $this->renderPriorityBadge($value),
            ])
            ->add('status', TextColumn::class, [
                'label' => 'Status',
                'render' => fn($value) => $this->renderStatusBadge($value),
            ])
            ->add('assignedTo', TextColumn::class, [
                'label' => 'Assigned To',
                'field' => 'assignedTo.name',
            ])
            ->add('assignedBy', TextColumn::class, [
                'label' => 'Assigned By',
                'field' => 'assignedBy.name',
            ])
            ->add('createdAt', DateTimeColumn::class, [
                'label' => 'Created At',
                'format' => 'd-m-Y',
                'searchable' => false,
            ])
            ->add('actions', TextColumn::class, [
                'label' => 'Actions',
                'orderable' => false,
                'searchable' => false,
                'render' => fn($value, TaskAssignment $assignment) => $this->formatActionColumn($assignment, $employee),
            ])
            ->createAdapter(ORMAdapter::class, [
                'entity' => TaskAssignment::class,
                'query' => fn($qb) => $this->buildQuery($qb, $task),
            ])
            ->handleRequest($request);
    }

    private function renderStatusBadge(string $status): string
    {
        return match ($status) {
            'NEW' => '<span class="badge btn-primary">New</span>',
            'IN_PROGRESS' => '<span class="badge btn-primary">In Progress</span>',
            'COMPLETED' => '<span class="badge bg-success">Completed</span>',
            'REASSIGNED' => '<span class="badge bg-danger">Reassigned</span>',
            default => '<span class="badge bg-secondary">Unknown</span>',
        };
    }

    private function renderPriorityBadge(string $priority): string
    {
        $colors = [
            'LOW' => 'badge btn-primary',
            'MEDIUM' => 'badge btn-primary',
            'HIGH' => 'badge bg-danger',
        ];

        return sprintf('<span class="%s">%s</span>', $colors[$priority] ?? 'badge bg-secondary', $priority);
    }

    private function formatActionColumn(TaskAssignment $assignment, MasterEmployee $employee): string
    {
        if ($assignment->getAssignedBy() !== $employee) {
            return '<span class="text-muted">No actions</span>';
        }

        $assignmentId = $assignment->getId();
        $taskId = $assignment->getTask()->getId();
        $taskAssignmentStatus = strtolower($assignment->getStatus());
        $viewUrl = $this->router->generate('employee_taskAssignment_view', ['task_id' => $taskId,
            'id' => $assignmentId]);
        $editUrl = $this->router->generate('employee_task_assignment_edit', [
            'task_id' => $taskId,
            'id' => $assignmentId
        ]);
        $deleteUrl = $this->router->generate('employee_task_assignment_delete', [
            'task_id' => $taskId,
            'id' => $assignmentId
        ]);
        $csrfToken = $this->csrfTokenManager->getToken('delete' . $assignmentId)->getValue();

        $modalId = 'deleteAssignmentModal_' . $assignmentId;
        $modalBody = sprintf(
            'Are you sure you want to delete this task assignment?<br><br>' .
            '<strong>Task:</strong> %s<br>' .
            '<strong>Assigned To:</strong> %s<br><br>' .
            '<strong>Warning:</strong> This action cannot be undone.',
            htmlspecialchars($assignment->getTask()->getTitle() ?? 'N/A'),
            htmlspecialchars($assignment->getAssignedTo()->getName() ?? 'N/A')
        );

        $footerButtons = sprintf(
            '<button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>' .
            '<button type="button" class="btn btn-primary delete-department-btn" data-delete="true" data-url="%s" data-token="%s">Delete</button>',
            $deleteUrl,
            $csrfToken
        );

        $deleteModal = $this->modalboxHelper->renderModal(
            $modalId,
            'Confirm Task Assignment Deletion',
            $modalBody,
            $footerButtons
        );

        $deleteButton = '';
        if ($taskAssignmentStatus !== 'completed') {
            $deleteButton = sprintf(
                '<button type="button" class="bs-btn btn btn-primary btn-sm" style="margin: 2px;" data-toggle="modal" data-target="#%s" title="Delete Assignment">
            <i class="bs-fa fa fa-trash"></i>
        </button>%s',
                $modalId,
                $deleteModal
            );
        }
        return sprintf(
            '<div class="d-inline-flex gap-2">
        <a href="%s" class="bs-btn btn btn-primary btn-sm" title="View Assignment" style="margin: 2px;"><i class="bs-fa fa fa-eye"></i></a>
        <a href="%s" class="bs-btn btn btn-primary btn-sm" style="margin: 2px;" title="Edit Assignment">
            <i class="bs-fa fa fa-edit"></i>
        </a>
        %s
       </div>',
            $viewUrl,
            $editUrl,
            $deleteButton
        );
    }

    private function buildQuery($qb, Task $task)
    {
        return $qb->select('ta')
            ->from(TaskAssignment::class, 'ta')
            ->leftJoin('ta.assignedTo', 'assignedTo')
            ->leftJoin('ta.assignedBy', 'assignedBy')
            ->where('ta.task = :task')
            ->setParameter('task', $task);
    }
}