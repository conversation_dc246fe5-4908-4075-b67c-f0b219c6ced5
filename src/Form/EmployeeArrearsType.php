<?php

namespace App\Form;

use App\Entity\EmployeeArrears;
use App\Entity\MasterEmployee;
use Doctrine\ORM\EntityRepository;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Extension\Core\Type\DateType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\GreaterThanOrEqual;
use Symfony\Component\Validator\Constraints\NotBlank;

class EmployeeArrearsType extends AbstractType
{
    public function __construct(private readonly Security $security)
    {
    }
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $employee = $this->security->getUser();
        $builder
            ->add('employee', EntityType::class, [
                'label' => 'Employee Name',
                'class' => MasterEmployee::class,
                'choice_label' => 'name',
                'autocomplete' => true,
                'required' => true,
                'placeholder' => 'Select an employee',
                'query_builder' => function (EntityRepository $er) use ($employee) {
                    return $er->createQueryBuilder('e')
                        ->where('e != :user')
                        ->andWhere('e.isDelete = :is_delete')
                        ->setParameter('user', $employee)
                        ->setParameter('is_delete', false);
                },
                'constraints' => [
                    new NotBlank(['message' => 'Please select an employee.']),
                ],
                'attr' => [
                    'class' => 'form-control',
                    'data-validation' => 'required',
                ],
            ])
            ->add('month', ChoiceType::class, [
                'label' => 'Month',
                'choices' => [
                    'January' => 1,
                    'February' => 2,
                    'March' => 3,
                    'April' => 4,
                    'May' => 5,
                    'June' => 6,
                    'July' => 7,
                    'August' => 8,
                    'September' => 9,
                    'October' => 10,
                    'November' => 11,
                    'December' => 12,
                ],
                'placeholder' => 'Select a month',
                'required' => true,
                'autocomplete' => true,
                'attr' => [
                    'class' => 'form-control',
                    'data-validation' => 'required',
                ],
                'constraints' => [
                    new NotBlank(['message' => 'Please select a month.']),
                ],
            ])
            ->add('year', IntegerType::class, [
                'label' => 'Year',
                'attr' => ['min' => 2024, 'max' => 2100],
                'constraints' => [
                    new NotBlank(['message' => 'Please select a year.']),
                ],
            ])
            ->add('oldSalary', NumberType::class, [
                'label' => 'Previous Salary',
                'scale' => 2,
                'constraints' => [
                    new GreaterThanOrEqual(['value' => 0, 'message' => 'Salary must be greater than or equal to 0.']),
                ],
                'required' => true,
            ])
            ->add('newSalary', NumberType::class, [
                'label' => 'New Salary',
                'scale' => 2,
                'constraints' => [
                    new GreaterThanOrEqual(['value' => 0, 'message' => 'Salary must be greater than or equal to 0.']),
                ],
                'required' => true,
            ])
            ->add('arrearsDays', IntegerType::class, [
                'label' => 'Arrears Days',
                'constraints' => [
                    new GreaterThanOrEqual(['value' => 0, 'message' => 'Days must be greater than or equal to 0.']),
                ],
                'required' => true,
            ])
            ->add('totalArrears', TextType::class, [
                'label' => 'Total Arrears',
                'constraints' => [
                    new GreaterThanOrEqual(['value' => 0, 'message' => 'Total arrears must be greater than or equal to 0.']),
                ],
                'required' => false,
            ])
        ;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => EmployeeArrears::class,
        ]);
    }
} 