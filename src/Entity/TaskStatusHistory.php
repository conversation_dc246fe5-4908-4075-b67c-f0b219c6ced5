<?php

namespace App\Entity;

use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity]
#[ORM\Table(name: 'task_status_history')]
class TaskStatusHistory
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: Types::INTEGER)]
    private ?int $id = null;

    #[ORM\ManyToOne(targetEntity: Task::class, inversedBy: 'statusHistories')]
    #[ORM\JoinColumn(nullable: false, onDelete: 'CASCADE')]
    private ?Task $task = null;

    #[ORM\ManyToOne(targetEntity: MasterEmployee::class)]
    #[ORM\JoinColumn(nullable: true)]
    private ?MasterEmployee $changedBy = null;

    #[ORM\ManyToOne(targetEntity: User::class)]
    #[ORM\JoinColumn(nullable: true)]
    private ?User $changedByAdmin = null;

    #[ORM\Column(type: Types::STRING, length: 50)]
    private string $oldStatus;

    #[ORM\Column(type: Types::STRING, length: 50)]
    private string $newStatus;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $changedAt = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): void
    {
        $this->id = $id;
    }

    public function getTask(): ?Task
    {
        return $this->task;
    }

    public function setTask(?Task $task): void
    {
        $this->task = $task;
    }

    public function getChangedBy(): ?MasterEmployee
    {
        return $this->changedBy;
    }

    public function setChangedBy(?MasterEmployee $changedBy): void
    {
        $this->changedBy = $changedBy;
    }

    public function getChangedByAdmin(): ?User
    {
        return $this->changedByAdmin;
    }

    public function setChangedByAdmin(?User $changedByAdmin): void
    {
        $this->changedByAdmin = $changedByAdmin;
    }

    public function getOldStatus(): string
    {
        return $this->oldStatus;
    }

    public function setOldStatus(string $oldStatus): void
    {
        $this->oldStatus = $oldStatus;
    }

    public function getNewStatus(): string
    {
        return $this->newStatus;
    }

    public function setNewStatus(string $newStatus): void
    {
        $this->newStatus = $newStatus;
    }

    public function getChangedAt(): ?\DateTimeInterface
    {
        return $this->changedAt;
    }

    public function setChangedAt(?\DateTimeInterface $changedAt): void
    {
        $this->changedAt = $changedAt;
    }
}