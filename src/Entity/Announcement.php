<?php

namespace App\Entity;

use App\Repository\AnnouncementRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Context\ExecutionContextInterface;

#[ORM\Entity(repositoryClass: AnnouncementRepository::class)]
#[ORM\Table(name: 'announcement')]
#[ORM\HasLifecycleCallbacks]
#[ORM\Index(columns: ['start_date', 'end_date', 'status'])]
class Announcement
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    /** @phpstan-ignore-next-line */
    private ?int $id = null;

    #[ORM\Column(type: 'string', length: 255)]
    #[Assert\NotBlank]
    private string $title;

    #[ORM\Column(type: 'text')]
    #[Assert\NotBlank]
    private string $content;

    #[ORM\Column(type: 'boolean', options: ['default' => false])]
    private bool $targetAllEmployees = false;

    #[ORM\ManyToMany(targetEntity: Department::class)]
    #[ORM\JoinTable(name: 'announcement_department')]
    private Collection $departments;

    #[ORM\Column(type: 'boolean', options: ['default' => false])]
    private bool $targetTeamLeadersOnly = false;

    #[ORM\Column(type: 'datetime')]
    #[Assert\NotBlank]
    private \DateTimeInterface $startDate;

    #[ORM\Column(type: 'datetime')]
    #[Assert\NotBlank]
    private \DateTimeInterface $endDate;

    #[ORM\Column(type: 'boolean')]
    private bool $isImportant = false;

    #[ORM\Column(type: 'integer', options: ['default' => 0])]
    private int $priority = 0;

    #[ORM\Column(type: 'boolean')]
    private bool $sendEmail = false;

    #[ORM\Column(type: 'datetime', nullable: true)]
    private ?\DateTimeInterface $emailSentAt = null;

    #[ORM\Column(type: 'datetime')]
    private \DateTimeInterface $createdAt;

    #[ORM\Column(type: 'datetime', nullable: true)]
    private ?\DateTimeInterface $updatedAt = null;

    #[ORM\ManyToOne(targetEntity: User::class)]
    #[ORM\JoinColumn(nullable: false)]
    private User $createdBy;

    #[ORM\Column(type: 'string', length: 20, options: ['default' => 'draft'])]
    private string $status = 'draft';

    public function __construct()
    {
        $this->departments = new ArrayCollection();
    }

    #[ORM\PrePersist]
    public function setCreatedAtValue(): void
    {
        $this->createdAt = new \DateTime();
    }

    #[ORM\PreUpdate]
    public function setUpdatedAtValue(): void
    {
        $this->updatedAt = new \DateTime();
    }

    #[Assert\Callback]
    public function validateDates(ExecutionContextInterface $context): void
    {
        if ($this->endDate <= $this->startDate) {
            $context->buildViolation('The end date must be after the start date.')
                ->atPath('endDate')
                ->addViolation();
        }
    }

    #[Assert\Callback]
    public function validateAudience(ExecutionContextInterface $context): void
    {
        if ($this->targetAllEmployees && ($this->targetTeamLeadersOnly || !$this->departments->isEmpty())) {
            $context->buildViolation('Cannot target all employees and also specify team leaders or departments.')
                ->atPath('targetAllEmployees')
                ->addViolation();
        }
        if ($this->targetTeamLeadersOnly && $this->targetAllEmployees) {
            $context->buildViolation('Cannot target team leaders only and all employees simultaneously.')
                ->atPath('targetTeamLeadersOnly')
                ->addViolation();
        }
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getTitle(): string
    {
        return $this->title;
    }

    public function setTitle(string $title): self
    {
        $this->title = $title;
        return $this;
    }

    public function getContent(): string
    {
        return $this->content;
    }

    public function setContent(string $content): self
    {
        $this->content = $content;
        return $this;
    }

    public function isTargetAllEmployees(): bool
    {
        return $this->targetAllEmployees;
    }

    public function setTargetAllEmployees(bool $targetAllEmployees): self
    {
        $this->targetAllEmployees = $targetAllEmployees;
        return $this;
    }

    public function getDepartments(): Collection
    {
        return $this->departments;
    }

    public function addDepartment(Department $department): self
    {
        if (!$this->departments->contains($department)) {
            $this->departments[] = $department;
        }
        return $this;
    }

    public function removeDepartment(Department $department): self
    {
        $this->departments->removeElement($department);
        return $this;
    }

    public function isTargetTeamLeadersOnly(): bool
    {
        return $this->targetTeamLeadersOnly;
    }

    public function setTargetTeamLeadersOnly(bool $targetTeamLeadersOnly): self
    {
        $this->targetTeamLeadersOnly = $targetTeamLeadersOnly;
        return $this;
    }

    public function getStartDate(): \DateTimeInterface
    {
        return $this->startDate;
    }

    public function setStartDate(\DateTimeInterface $startDate): self
    {
        $this->startDate = $startDate;
        return $this;
    }

    public function getEndDate(): \DateTimeInterface
    {
        return $this->endDate;
    }

    public function setEndDate(\DateTimeInterface $endDate): self
    {
        $this->endDate = $endDate;
        return $this;
    }

    public function isImportant(): bool
    {
        return $this->isImportant;
    }

    public function setIsImportant(bool $isImportant): self
    {
        $this->isImportant = $isImportant;
        return $this;
    }

    public function getPriority(): int
    {
        return $this->priority;
    }

    public function setPriority(int $priority): self
    {
        $this->priority = $priority;
        return $this;
    }

    public function getSendEmail(): bool
    {
        return $this->sendEmail;
    }

    public function setSendEmail(bool $sendEmail): self
    {
        $this->sendEmail = $sendEmail;
        return $this;
    }

    public function getEmailSentAt(): ?\DateTimeInterface
    {
        return $this->emailSentAt;
    }

    public function setEmailSentAt(?\DateTimeInterface $emailSentAt): self
    {
        $this->emailSentAt = $emailSentAt;
        return $this;
    }

    public function getCreatedAt(): \DateTimeInterface
    {
        return $this->createdAt;
    }

    public function getUpdatedAt(): ?\DateTimeInterface
    {
        return $this->updatedAt;
    }

    public function getCreatedBy(): User
    {
        return $this->createdBy;
    }

    public function setCreatedBy(User $createdBy): self
    {
        $this->createdBy = $createdBy;
        return $this;
    }

    public function getStatus(): string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;
        return $this;
    }
}