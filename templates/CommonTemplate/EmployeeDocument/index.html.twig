 <div class="content">
        <div class="animated fadeIn">
            <div class="row">
                <div id="flash-container"></div>
                <div id="order-loader">
                    <img src="{{ asset('images/loader.svg') }}" alt="loader">
                </div>
                <div class="col-md-12">
                    <div id="flash-container"></div>
                    <div class="card">
                        <div class="card-header d-flex justify-content-between">
                            {% if is_granted('ROLE_ADMIN') %}
                            <strong class="card-title">Documents for {{ employee.name }}</strong>
                            <a href="{{ path('admin_employee_document_new', {'employeeId': employee.id}) }}" class="btn btn-primary">Upload New Document</a>
                            {% elseif is_granted('ROLE_EMPLOYEE') %}
                            <strong class="card-title">My Documents</strong>
                            <a href="{{ path('my_employee_document_new') }}" class="btn btn-primary">Upload New Document</a>
                            {% endif %}
                        </div>
                        <div class="card-body">
                            <div class="table-stats order-table ov-h text-center">
                                <div id="documents" class="datatable-main-wrapper">Loading...</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="pdfViewerModal" tabindex="-1" aria-labelledby="pdfViewerModalLabel" aria-hidden="true">
        {% include 'CommonTemplate/modalBox.html.twig' %}
    </div>
    <div class="clearfix"></div>
    <script src="{{ asset('assets/js/datatables.min.js') }}"></script>
    <script src="{{ asset('bundles/datatables/js/datatables.js') }}"></script>
    <script>
        $(function () {
            recordsTable = $('#documents').initDataTables({{ datatable_settings(datatable) }});
        });
    </script>
    <script src="{{ asset('assets/js/datatableclearsearch.js') }}"></script>
    <script src="{{ asset('assets/js/admin/common.js') }}"></script>
    <link rel="stylesheet" href="{{ asset('assets/css/document/document.css') }}">
    <script src="{{ asset('assets/js/front/document.js') }}"></script>