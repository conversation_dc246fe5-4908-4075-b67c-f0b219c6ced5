<?php

namespace App\Form;

use App\Entity\HardwareType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\Length;
use Symfony\Component\Validator\Constraints\NotBlank;

class HardwareTypeFormType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('name', TextType::class, [
                'label' => 'Hardware Type Name',
                'required' => true,
                'constraints' => [
                    new NotBlank([
                        'message' => 'Hardware type name is required.',
                    ]),
                    new Length([
                        'min' => 2,
                        'max' => 255,
                        'minMessage' => 'Hardware type name must be at least {{ limit }} characters long',
                        'maxMessage' => 'Hardware type name cannot be longer than {{ limit }} characters',
                    ]),
                ],
                'attr' => [
                    'maxlength' => 255,
                    'class' => 'form-control',
                    'data-validation' => 'required|min:2|max:255',
                    'placeholder' => 'Enter hardware type name',
                ],
            ])
            ->add('dataStringSmall', TextType::class, [
                'label' => 'Short Code',
                'required' => true,
                'constraints' => [
                    new NotBlank([
                        'message' => 'Short code is required.',
                    ]),
                    new Length([
                        'max' => 3,
                        'maxMessage' => 'Short code cannot be longer than {{ limit }} characters',
                    ]),
                ],
                'attr' => [
                    'maxlength' => 3,
                    'class' => 'form-control',
                    'data-validation' => 'required|max:3',
                    'placeholder' => 'Enter short code (e.g., LAP)',
                ],
            ])
            ->add('description', TextType::class, [
                'label' => 'Description',
                'required' => false,
                'constraints' => [
                    new Length([
                        'min' => 2,
                        'max' => 255,
                        'minMessage' => 'Description must be at least {{ limit }} characters long',
                        'maxMessage' => 'Description cannot be longer than {{ limit }} characters',
                    ]),
                ],
                'attr' => [
                    'maxlength' => 255,
                    'class' => 'form-control',
                    'data-validation' => 'min:2|max:255',
                    'placeholder' => 'Enter description',
                ],
            ]);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => HardwareType::class,
        ]);
    }
}