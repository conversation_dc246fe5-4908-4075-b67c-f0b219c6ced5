<?php

namespace App\Form;

use App\Entity\TaskProgress;
use FOS\CKEditorBundle\Form\Type\CKEditorType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\CallbackTransformer;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\PositiveOrZero;
use Symfony\Component\Validator\Constraints\Range;

class TaskProgressType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('progressPercent', NumberType::class, [
                'label' => 'Progress (%)',
                'required' => true,
                'constraints' => [
                    new NotBlank([
                        'message' => 'Progress percentage is required.',
                    ]),
                    new Range([
                        'min' => 0,
                        'max' => 100,
                        'notInRangeMessage' => 'Progress must be between {{ min }} and {{ max }}.',
                    ]),
                ],
                'attr' => [
                    'class' => 'form-control',
                    'data-validation' => 'required|min:0|max:100',
                    'placeholder' => 'Enter progress percentage',
                    'min' => 0,
                    'max' => 100,
                    'step' => 1,
                ],
            ])
            ->add('hoursWorked', NumberType::class, [
                'label' => 'Hours Worked',
                'required' => false,
                'scale' => 2,
                'constraints' => [
                    new PositiveOrZero([
                        'message' => 'Hours worked must be zero or positive.',
                    ]),
                    new Range([
                        'max' => 24,
                        'notInRangeMessage' => 'Hours not be greater than {{ max }}.',
                    ]),
                ],
                'attr' => [
                    'class' => 'form-control',
                    'data-validation' => 'min:0',
                    'placeholder' => 'Enter hours worked',
                    'min' => 0,
                    'step' => 0.1,
                ],
            ])
            ->add('comments', CKEditorType::class, [
                'constraints' => [new NotBlank()],
                'row_attr' => [
                    'class' => 'col-md-12'
                ],
                'attr' => ['rows' => '10'],
            ])
            ->add('progressDate', TextType::class, [
                'required' => true,
                'label' => 'Date',
                'attr' => [
                    'class' => 'form-control datepicker',
                    'placeholder' => 'Select date',
                    'required' => 'required',
                ],
                'constraints' => [new NotBlank()],
            ]);
        $dateTransformer = new CallbackTransformer(
            function ($dateAsObject) {
                return $dateAsObject instanceof \DateTimeInterface ? $dateAsObject->format('d-m-Y') : '';
            },
            function ($dateAsString) {
                return $dateAsString ? \DateTime::createFromFormat('d-m-Y', $dateAsString) : null;
            }
        );

        $builder->get('progressDate')->addModelTransformer($dateTransformer);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => TaskProgress::class,
        ]);
    }
}