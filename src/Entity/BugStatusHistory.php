<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;
use Doctrine\DBAL\Types\Types;

#[ORM\Entity]
#[ORM\Table(name: 'bug_status_history')]
class BugStatusHistory
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: Types::INTEGER)]
    /** @phpstan-ignore-next-line */
    private ?int $id = null;

    #[ORM\ManyToOne(targetEntity: Bug::class, inversedBy: 'statusHistory')]
    #[ORM\JoinColumn(nullable: false, onDelete: 'CASCADE')]
    private ?Bug $bug = null;

    #[ORM\Column(type: Types::STRING, enumType: Status::class)]
    private Status $status;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private \DateTimeInterface $changedAt;

    #[ORM\ManyToOne(targetEntity: MasterEmployee::class)]
    #[ORM\JoinColumn(nullable: true)]
    private ?MasterEmployee $changedBy = null;

    #[ORM\ManyToOne(targetEntity: User::class)]
    #[ORM\JoinColumn(nullable: true)]
    private ?User $changedByAdmin = null;


    public function getId(): ?int
    {
        return $this->id;
    }
    public function getBug(): ?Bug
    {
        return $this->bug;
    }

    public function setBug(?Bug $bug): void
    {
        $this->bug = $bug;
    }

    public function getStatus(): Status
    {
        return $this->status;
    }

    public function setStatus(Status $status): void
    {
        $this->status = $status;
    }

    public function getChangedAt(): \DateTimeInterface
    {
        return $this->changedAt;
    }

    public function setChangedAt(\DateTimeInterface $changedAt): void
    {
        $this->changedAt = $changedAt;
    }

    public function getChangedBy(): ?MasterEmployee
    {
        return $this->changedBy;
    }

    public function setChangedBy(?MasterEmployee $changedBy): void
    {
        $this->changedBy = $changedBy;
    }

    public function getChangedByAdmin(): ?User
    {
        return $this->changedByAdmin;
    }

    public function setChangedByAdmin(?User $changedByAdmin): void
    {
        $this->changedByAdmin = $changedByAdmin;
    }
}
?>