<?php

namespace App\Entity;

use App\Repository\FormSectionRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: FormSectionRepository::class)]
class FormSection
{
    public const INACTIVE = false;
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private ?int $id = null;

    #[ORM\ManyToOne(targetEntity: FormTemplate::class, inversedBy: 'sections')]
    #[ORM\JoinColumn(nullable: false)]
    private FormTemplate $template;

    #[ORM\Column(type: 'string', length: 255)]
    private string $name;

    #[ORM\Column(type: 'text', nullable: true)]
    private ?string $description = null;

    #[ORM\Column(type: 'integer')]
    private int $orderNumber;

    #[ORM\Column(type: 'boolean')]
    private bool $isRepeatable = false;

    #[ORM\Column(type: 'boolean')]
    private bool $isMandatory = true;

    #[ORM\Column(type: 'boolean')]
    private bool $status = true;

    #[ORM\Column(type: 'string', length: 100 , nullable: true)]
    private ?string $icon = null;

    #[ORM\OneToMany(targetEntity: FormField::class, mappedBy: 'section', orphanRemoval: true)]
    private Collection $fields;

    #[ORM\Column(type: 'datetime')]
    private \DateTimeInterface $createdAt;

    #[ORM\Column(type: 'datetime')]
    private \DateTimeInterface $updatedAt;

    #[ORM\Column(type: Types::BOOLEAN, options: ["default" => false])]
    private ?bool $isDelete = self::INACTIVE;

    public function __construct()
    {
        $this->fields = new ArrayCollection();
        $this->createdAt = new \DateTime();
        $this->updatedAt = new \DateTime();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): void
    {
        $this->id = $id;
    }

    public function getTemplate(): FormTemplate
    {
        return $this->template;
    }

    public function setTemplate(FormTemplate $template): void
    {
        $this->template = $template;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): void
    {
        $this->name = $name;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): void
    {
        $this->description = $description;
    }

    public function getOrderNumber(): int
    {
        return $this->orderNumber;
    }

    public function setOrderNumber(int $orderNumber): void
    {
        $this->orderNumber = $orderNumber;
    }

    public function isRepeatable(): bool
    {
        return $this->isRepeatable;
    }

    public function setIsRepeatable(bool $isRepeatable): void
    {
        $this->isRepeatable = $isRepeatable;
    }

    public function isMandatory(): bool
    {
        return $this->isMandatory;
    }

    public function setIsMandatory(bool $isMandatory): void
    {
        $this->isMandatory = $isMandatory;
    }



    public function getFields(): Collection
    {
        return $this->fields;
    }

    public function setFields(Collection $fields): void
    {
        $this->fields = $fields;
    }

    public function getCreatedAt(): \DateTimeInterface
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTimeInterface $createdAt): void
    {
        $this->createdAt = $createdAt;
    }

    public function getUpdatedAt(): \DateTimeInterface
    {
        return $this->updatedAt;
    }

    public function setUpdatedAt(\DateTimeInterface $updatedAt): void
    {
        $this->updatedAt = $updatedAt;
    }

    public function isStatus(): bool
    {
        return $this->status;
    }

    public function setStatus(bool $status): void
    {
        $this->status = $status;
    }

    public function getIcon(): ?string
    {
        return $this->icon;
    }

    public function setIcon(?string $icon): void
    {
        $this->icon = $icon;
    }

    public function getIsDelete(): ?bool
    {
        return $this->isDelete;
    }

    public function setIsDelete(?bool $isDelete): void
    {
        $this->isDelete = $isDelete;
    }
}
