<?php

namespace App\Service\Common;

use App\Entity\LeaveRequest;
use App\Entity\MasterEmployee;
use App\Entity\Status;
use App\Entity\Task;
use App\Entity\EmployeeHours;
use App\Entity\FormSubmission;
use App\Entity\Bug;
use App\Entity\TaskAssignment;
use App\Helper\TimeHelper;
use App\Repository\SettingRepository;
use Doctrine\ORM\EntityManagerInterface;

readonly class ReportGeneratorService
{


    public function __construct(
        private EntityManagerInterface $entityManager,
        private SettingRepository      $configurationRepository,
        private TimeHelper             $timeHelper,
    )
    {

    }

    /**
     * @param string $fieldName
     * @param $defaultValue
     * @return string|null
     */
    private function getConfigurationValue(string $fieldName, $defaultValue): ?string
    {
        $configuration = $this->configurationRepository->findOneBy(['fieldName' => $fieldName]);
        return $configuration ? $configuration->getFieldValue() : $defaultValue;
    }
    /**
     * @throws \DateMalformedStringException
     */
    public function generate360Report(int $employeeId, string $periodType, ?int $year = null, ?int $month = null): array
    {
        $employee = $this->entityManager->getRepository(MasterEmployee::class)->find($employeeId);
        if (!$employee) {
            throw new \RuntimeException('Employee not found');
        }

        $startDate = new \DateTime("$year-01-01 00:00:00");
        $endDate = new \DateTime("$year-12-31 23:59:59");

        if ($periodType === 'month' && $month) {
            $startDate = new \DateTime("$year-$month-01 00:00:00");
            $endDate = (clone $startDate)->modify('last day of this month')->setTime(23, 59, 59);
        }
        $threshold = $this->getConfigurationValue('Total_Working_Hours', $_ENV['THRESHOLD']);
        $threshold = is_numeric($threshold) ? (float) $threshold : 0.0;
        $taskData = $this->calculateTaskScore($employee, $startDate, $endDate);
        $teamLoggerData = $this->calculateTeamLoggerScore($employee, $startDate, $endDate, $threshold);
        $feedbackData = $this->calculateFeedbackScore($employee, $startDate, $endDate);
        $bugData = $this->calculateBugScore($employee, $startDate, $endDate);
        $weekendHoursData = $this->getWeekendLoggedHours($employee, $startDate, $endDate);
        foreach ($weekendHoursData['days'] as $day) {
            $day['formatted'] = $this->timeHelper->convertDecimalToTime($day['hours']);
        }
        $weekendHoursData['formatted_total'] = $this->timeHelper->convertDecimalToTime($weekendHoursData['total_weekend_hours']);
        $finalScore = ($taskData['score'] * 0.50) +
            ($teamLoggerData['score'] * 0.20) +
            ($feedbackData['score'] * 0.20) +
            ($bugData['score'] * 0.10);

        return [
            'task_score' => $taskData,
            'team_logger_score' => $teamLoggerData,
            'performance_feedback_score' => $feedbackData,
            'bug_tracker_score' => $bugData,
            'final_score' => number_format($finalScore, 2),
            'weekend_hours' => $weekendHoursData['formatted_total'] ,
        ];
    }

    private function calculateTaskScore(MasterEmployee $employee, \DateTime $startDate, \DateTime $endDate): array
    {
        try {
            $taskRepo = $this->entityManager->getRepository(Task::class);
            $taskAssignmentRepo = $this->entityManager->getRepository(TaskAssignment::class);

            $completedTasks = $taskRepo->createQueryBuilder('t')
                ->where('t.assignedTo = :employee')
                ->andWhere('t.createdAt BETWEEN :start AND :end')
                ->andWhere('LOWER(t.status) = :status')
                ->andWhere('t.estimatedHours IS NOT NULL')
                ->andWhere('t.actualHours IS NOT NULL')
                ->setParameter('employee', $employee)
                ->setParameter('start', $startDate)
                ->setParameter('end', $endDate)
                ->setParameter('status', 'completed')
                ->getQuery()
                ->getResult();
            $completedSubTasks = $taskAssignmentRepo->createQueryBuilder('ta')
                ->where('ta.assignedTo = :employee')
                ->andWhere('ta.createdAt BETWEEN :start AND :end')
                ->andWhere('LOWER(ta.status) = :status')
                ->setParameter('employee', $employee)
                ->setParameter('start', $startDate)
                ->setParameter('end', $endDate)
                ->setParameter('status', 'completed')
                ->getQuery()
                ->getResult();

            $onTimeCompleted = 0;
            $total = 0;
            foreach ($completedTasks as $task) {
                $estimatedMinutes = $this->timeHelper->clockStyleToMinutes($task->getEstimatedHours());
                $actualMinutes = $this->timeHelper->decimalHoursToMinutes($task->getActualHours());

                if ($actualMinutes <= $estimatedMinutes) {
                    $onTimeCompleted++;
                }

                $total++;
            }

            foreach ($completedSubTasks as $subTask) {
                $reassignments = 0;

                foreach ($subTask->getStatusHistories() as $history) {
                    if (strtolower($history->getNewStatus()) === 'reassigned') {
                        $reassignments++;
                    }
                }

                if ($reassignments <= 0) {
                    $onTimeCompleted++;
                }

                $total++;
            }
            if ($total === 0) {
                return [
                    'score' => 0,
                    'details' => 'No completed tasks or subtasks in this period',
                    'total_tasks' => 0,
                    'on_time_completed' => 0
                ];
            }
            $onTimePercentage = ($total > 0) ? ($onTimeCompleted / $total) * 100 : 0;

            return [
                'score' => $onTimePercentage,
                'details' => sprintf('%d out of %d total tasks and subtasks completed on time (%.2f%%)', $onTimeCompleted, $total, $onTimePercentage),
                'total_tasks' => $total,
                'on_time_completed' => $onTimeCompleted
            ];

        } catch (\Exception $e) {
            return [
                'score' => 0,
                'details' => 'Error calculating task score: ' . $e->getMessage(),
                'total_tasks' => 0,
                'on_time_completed' => 0
            ];
        }
    }

    private function calculateTeamLoggerScore(MasterEmployee $masterEmployee, \DateTime $startDate, \DateTime $endDate, float $threshold): array
    {
        try {
            $employee = $masterEmployee->getEmployees();
            $hoursRepo = $this->entityManager->getRepository(EmployeeHours::class);

            $entries = $hoursRepo->createQueryBuilder('h')
                ->where('h.employee = :employee')
                ->andWhere('h.reportDate BETWEEN :start AND :end')
                ->setParameter('employee', $employee)
                ->setParameter('start', $startDate->format('Y-m-d'))
                ->setParameter('end', $endDate->format('Y-m-d'))
                ->getQuery()
                ->getResult();

            if (empty($entries)) {
                return [
                    'score' => 0,
                    'details' => 'No time tracking data available for this period',
                    'total_days' => 0,
                    'avg_total_hours' => 0
                ];
            }

            $leaveRepo = $this->entityManager->getRepository(LeaveRequest::class);

            $totalHours = 0;
            $validDays = 0;

            foreach ($entries as $entry) {
                $reportDate = $entry->getReportDate();
                $dayOfWeek = (int) $reportDate->format('N');
                if ($dayOfWeek >= 6) {
                    continue;
                }

                $leave = $leaveRepo->createQueryBuilder('l')
                    ->where('l.employee = :employee')
                    ->andWhere('l.status = :status')
                    ->andWhere('l.startDate <= :date AND l.endDate >= :date')
                    ->setParameter('employee', $masterEmployee)
                    ->setParameter('status', LeaveRequest::STATUS_APPROVED)
                    ->setParameter('date', $reportDate->format('Y-m-d'))
                    ->getQuery()
                    ->getOneOrNullResult();

                if ($leave && !$leave->getStartHalfDay() && !$leave->getEndHalfDay()) {
                    continue;
                }

                $dayValue = ($leave && ($leave->getStartHalfDay() || $leave->getEndHalfDay())) ? 0.5 : 1;

                $totalHours += $entry->getTotalHours();
                $validDays += $dayValue;
            }

            if ($validDays === 0) {
                return [
                    'score' => 0,
                    'details' => 'No valid working days (after filtering weekends & leaves)',
                    'total_days' => 0,
                    'avg_total_hours' => 0
                ];
            }

            $avgHours = $totalHours / $validDays;
            $score = min(($avgHours / $threshold) * 100, 100);

            return [
                'score' => round($score, 2),
                'details' => sprintf('Average total hours: %.2f%% of %.2f-hour workday over %.1f valid working days', $score, $threshold, $validDays),
                'total_days' => $validDays,
                'avg_total_hours' => round($avgHours, 2)
            ];
        } catch (\Exception $e) {
            return [
                'score' => 0,
                'details' => 'Error calculating team logger score: ' . $e->getMessage(),
                'total_days' => 0,
                'avg_total_hours' => 0
            ];
        }
    }


    private function calculateFeedbackScore(MasterEmployee $employee, \DateTime $startDate, \DateTime $endDate): array
    {
        try {
            $formRepo = $this->entityManager->getRepository(FormSubmission::class);
            $result = $formRepo->createQueryBuilder('f')
                ->select('AVG(f.rating) as avgRating, COUNT(f.id) as totalSubmissions')
                ->where('f.employee = :employee')
                ->andWhere('f.submissionDate BETWEEN :start AND :end')
                ->andWhere('f.rating IS NOT NULL')
                ->setParameter('employee', $employee)
                ->setParameter('start', $startDate)
                ->setParameter('end', $endDate)
                ->getQuery()
                ->getSingleResult();

            $avgRating = $result['avgRating'] ?? 0;
            $totalSubmissions = $result['totalSubmissions'] ?? 0;

            if ($totalSubmissions === 0) {
                return [
                    'score' => 0,
                    'details' => 'No feedback submissions found for this period',
                    'total_submissions' => 0,
                    'avg_rating' => 0
                ];
            }

            $feedbackScore = ($avgRating / 5) * 100;

            return [
                'score' => $feedbackScore,
                'details' => sprintf('Average feedback rating: %.2f out of 5 (%.2f%%) from %d submissions', $avgRating, $feedbackScore, $totalSubmissions),
                'total_submissions' => $totalSubmissions,
                'avg_rating' => $avgRating
            ];
        } catch (\Exception $e) {
            return [
                'score' => 0,
                'details' => 'Error calculating feedback score: ' . $e->getMessage(),
                'total_submissions' => 0,
                'avg_rating' => 0
            ];
        }
    }

    private function calculateBugScore(MasterEmployee $employee, \DateTime $startDate, \DateTime $endDate): array
    {
        try {
            $bugRepo = $this->entityManager->getRepository(Bug::class);
            $bugs = $bugRepo->createQueryBuilder('b')
                ->where('b.assignedTo = :employee')
                ->andWhere('b.createdAt BETWEEN :start AND :end')
                ->setParameter('employee', $employee)
                ->setParameter('start', $startDate)
                ->setParameter('end', $endDate)
                ->getQuery()
                ->getResult();

            if (empty($bugs)) {
                return [
                    'score' => 100,
                    'details' => 'No bugs assigned in this period',
                    'total_bugs' => 0,
                    'reopened_bugs' => 0
                ];
            }

            $reopenedCount = 0;
            foreach ($bugs as $bug) {
                if ($bug->getStatusHistory()) {
                    $reopenCount = 0;
                    foreach ($bug->getStatusHistory() as $history) {
                        if ($history->getStatus() === Status::REOPENED) {
                            $reopenCount++;
                        }
                    }
                    if ($reopenCount >= 1) {
                        $reopenedCount++;
                    }
                }
            }

            $totalBugs = count($bugs);
            $reopenRate = ($reopenedCount / $totalBugs) * 100;
            $bugScore = 100 - $reopenRate;

            return [
                'score' => $bugScore,
                'details' => sprintf('Bugs reopened more than twice (%.2f%% reopen rate)', $reopenRate),
                'total_bugs' => $totalBugs,
                'reopened_bugs' => $reopenedCount
            ];
        } catch (\Exception $e) {
            return [
                'score' => 100,
                'details' => 'Error calculating bug score: ' . $e->getMessage(),
                'total_bugs' => 0,
                'reopened_bugs' => 0
            ];
        }
    }

    private function getWeekendLoggedHours(MasterEmployee $masterEmployee, \DateTime $startDate, \DateTime $endDate): array
    {
        try {
            $employee = $masterEmployee->getEmployees();
            $hoursRepo = $this->entityManager->getRepository(EmployeeHours::class);

            $entries = $hoursRepo->createQueryBuilder('h')
                ->where('h.employee = :employee')
                ->andWhere('h.reportDate BETWEEN :start AND :end')
                ->setParameter('employee', $employee)
                ->setParameter('start', $startDate->format('Y-m-d'))
                ->setParameter('end', $endDate->format('Y-m-d'))
                ->getQuery()
                ->getResult();

            $weekendHours = 0;
            $weekendDays = [];

            foreach ($entries as $entry) {
                $reportDate = $entry->getReportDate();
                $dayOfWeek = (int) $reportDate->format('N');

                if ($dayOfWeek === 6 || $dayOfWeek === 7) {
                    $weekendHours += $entry->getTotalHours();
                    $weekendDays[] = [
                        'date' => $reportDate->format('Y-m-d'),
                        'hours' => $entry->getTotalHours()
                    ];
                }
            }

            return [
                'total_weekend_hours' => round($weekendHours, 2),
                'days' => $weekendDays
            ];
        } catch (\Exception $e) {
            return [
                'total_weekend_hours' => 0,
                'days' => [],
                'error' => 'Error fetching weekend hours: ' . $e->getMessage()
            ];
        }
    }
}