<?php

namespace App\Service\Common;
use App\Repository\EmailTemplateRepository;
use App\Repository\SettingRepository;
use Symfony\Component\Mailer\Exception\TransportExceptionInterface;
use Symfony\Component\Mailer\Mailer;
use Symfony\Component\Mailer\Transport;
use Symfony\Component\Mime\Address;
use Symfony\Component\Mime\Email;

readonly class EmailService
{
    public function __construct(
        private EmailTemplateRepository $emailTemplateRepository,
        private SettingRepository       $settingRepository,
    ) {
    }

    public function getEmailConfiguration(): array
    {
        $dsnSetting = $this->settingRepository->findOneBy(['fieldName' => 'MAILER_DSN']);
        $senderSetting = $this->settingRepository->findOneBy(['fieldName' => 'MAILER_FROM_ADDRESS']);
        if (!$dsnSetting || !$senderSetting) {
            throw new \RuntimeException('Email configuration is incomplete.');
        }

        return [
            'dsn' => $dsnSetting->getFieldValue(),
            'sender' => $senderSetting->getFieldValue(),
        ];
    }

    public function createMailer(): Mailer
    {
        $emailConfig = $this->getEmailConfiguration();
        $transport = Transport::fromDsn($emailConfig['dsn']);
        return new Mailer($transport);
    }

    /**
     * @throws TransportExceptionInterface
     */
    public function sendEmail(string $toEmail, string $subject, string $htmlContent): void
    {
        $emailConfig = $this->getEmailConfiguration();
        $mailer = $this->createMailer();

        $email = (new Email())
            ->from($emailConfig['sender'])
            ->to(new Address($toEmail))
            ->subject($subject)
            ->html($htmlContent);

        $mailer->send($email);
    }

    public function generateEmailContent(string $templateIdentifier, array $placeholders): string
    {
        $template = $this->emailTemplateRepository->findOneBy(['identifier' => $templateIdentifier]);
        if (!$template) {
            throw new \RuntimeException("Email template '$templateIdentifier' not found.");
        }
        return str_replace(array_keys($placeholders), array_values($placeholders), $template->getContent());
    }

    /**
     * @throws TransportExceptionInterface
     */
    public function sendEmailPDF(string $toEmail, string $subject, string $htmlContent, array $attachments = []): void
    {
        $emailConfig = $this->getEmailConfiguration();
        $mailer = $this->createMailer();

        $email = (new Email())
            ->from($emailConfig['sender'])
            ->to(new Address($toEmail))
            ->subject($subject)
            ->html($htmlContent);

        foreach ($attachments as $attachment) {
            $email->attach($attachment['content'], $attachment['filename'], $attachment['mime']);
        }

        $mailer->send($email);
    }
}