{% extends 'admin/dashboard/base.html.twig' %}
{% block body %}
    <div class="content">
        <div class="animated fadeIn">
            <div class="row">
                <div class="card-body">

                    <div class="chat-container">
                        <div class="card-header d-flex justify-content-between "
                             style="background-color: #f78739;color: #FFFFFF">
                            <h4>Chat With {{ employee.masterEmployee.name }} -
                                Report: {{ notification.employeeHours.reportDate|custom_date }}</h4>
                            <a href="{{ path('report',{ 'keep_filters': 1 }) }}" class="btn btn-primary" style="background: white;color: black;"><i class="fa fa-long-arrow-left"></i></a>
                        </div>

                        <div class="messages-container" id="messages">
                            {% for chat in chats %}
                                <div class="message {% if chat.isFromEmployee %}employee-message{% else %}admin-message{% endif %} {% if chat.isDisapprovalReason %}disapproval-reason{% endif %}">
                                    <div class="message-content">{{ chat.message }}</div>
                                    <div class="timestamp">{{ chat.sentAt|date('d-m-Y H:i:s') }}</div>
                                    {% if chat.readAt %}
                                        <div class="read-status">Read {{ chat.readAt|date('d-m-Y H:i:s') }}</div>
                                    {% endif %}
                                </div>
                            {% endfor %}
                        </div>

                        <div class="message-input">
                            <div class="input-group">
                                <input type="text" id="messageInput" class="form-control mr-2"
                                       placeholder="Type your message...">
                                <button class="btn btn-primary message-btn" id="sendButton"><i class="fa fa-send"
                                                                                               style="
    margin: 0;
"></i></button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="clearfix"></div>
    <link rel="stylesheet" href="{{ asset('assets/css/Chat/adminchat.css') }}">
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        window.notificationid = '{{ notification.id }}';
    </script>
    <script src="{{ asset('assets/js/chat/admin.js') }}"></script>
{% endblock %}