<?php

namespace App\MessageHandler;

use App\Entity\Employee;
use App\Entity\EmployeeHours;
use App\Entity\EmployeeNotification;
use App\Entity\EmployeeRemainingHours;
use App\Message\EmployeeNotificationMessage;
use App\Repository\EmployeeRepository;
use App\Repository\EmployeeHoursRepository;
use App\Service\Common\EmailService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Mailer\Exception\TransportExceptionInterface;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use App\Traits\ExceptionLoggerTrait;
use App\Helper\TimeHelper;

#[AsMessageHandler]
class EmployeeNotificationHandler
{
    use ExceptionLoggerTrait;

    public function __construct(
        private readonly EmployeeRepository $employeeRepository,
        private readonly EmployeeHoursRepository $employeeHoursRepository,
        private readonly EmailService $emailService,
        private readonly ParameterBagInterface $parameterBag,
        private readonly EntityManagerInterface $entityManager,
        private readonly TimeHelper $timeHelper,
    ) {
        $this->initializeLogger($parameterBag, 'email_notifications_handler');
    }

    public function __invoke(EmployeeNotificationMessage $message): void
    {
        $employee = $this->employeeRepository->find($message->getEmployeeId());
        $employeeHours = $this->employeeHoursRepository->find($message->getEmployeeHoursId());

        if (!$employee || !$employeeHours) {
            $this->log('Employee or EmployeeHours not found', [
                'employee_id' => $message->getEmployeeId(),
                'employee_hours_id' => $message->getEmployeeHoursId(),
            ]);
            return;
        }

        $existingNotification = $this->entityManager->getRepository(EmployeeNotification::class)
            ->findOneBy([
                'employee' => $employee,
                'employeeHours' => $employeeHours
            ]);

        if (!$existingNotification && !$message->isResend() && !$message->getDisapprovalReason() && !$message->getemailSent()) {
            $notification = new EmployeeNotification();
            $notification->setEmployee($employee);
            $notification->setEmployeeHours($employeeHours);
            $notification->setSentAt(new \DateTime());
            $this->entityManager->persist($notification);
        } else {
            $notification = $existingNotification;
            if ($message->isResend()) {
                $notification->setSentAt(new \DateTime());
            }
        }

        if ($message->getDisapprovalReason()) {
            $notification->setDisapprovalReason($message->getDisapprovalReason());
            $this->sendDisapprovalEmail($employee, $employeeHours, $notification);
        } elseif ($message->isResend()) {
            $this->resendJustification($employee, $employeeHours, $notification);
        } elseif ($message->getemailSent()) {
            $this->sendIncompleteHoursNotice($employee, $employeeHours, $notification);
        } else {
            $this->sendSummaryReport($employee, $employeeHours, $notification);
        }
    }

    private function sendSummaryReport(Employee $employee, EmployeeHours $employeeHours, EmployeeNotification $notification): void
    {
        $email = $employee->getMasterEmployee()?->getEmail();
        if (!$email) {
            $this->log('No email address for employee', [
                'employee_id' => $employee->getId(),
                'notification_id' => $notification->getId(),
            ]);
            return;
        }

        try {
            $placeholders = [
                '{{employee_name}}' => $employee->getMasterEmployee()?->getName() ?? '',
                '{{total_hours}}' => $this->timeHelper->convertDecimalToTime($employeeHours->getTotalHours()),
                '{{break_hours}}' => $this->timeHelper->convertDecimalToTime($employeeHours->getBreakHours()),
                '{{on_computer_hours}}' => $this->timeHelper->convertDecimalToTime($employeeHours->getOnComputerHours()),
                '{{off_computer_hours}}' => $this->timeHelper->convertDecimalToTime($employeeHours->getOffComputerHours()),
                '{{report_date}}' => $employeeHours->getReportDate()->format('d-M-Y'),
                '{{justification_url}}' => rtrim($this->parameterBag->get('app_base_url'), '/') . "/admin/justify/" . $notification->getUuid(),
            ];

            $emailContent = $this->emailService->generateEmailContent('employee_summary_report', $placeholders);
            $this->emailService->sendEmail($email, 'TeamLogger: Employee Summary Report', $emailContent);

            $this->entityManager->flush();
        } catch (TransportExceptionInterface|\Exception $e) {
            $this->logError($e, 'Error sending summary report email', $notification);
        }
    }

    private function sendDisapprovalEmail(Employee $employee, EmployeeHours $employeeHours, EmployeeNotification $notification): void
    {
        $email = $employee->getMasterEmployee()?->getEmail();
        if (!$email) {
            $this->log('No email address for employee', [
                'employee_id' => $employee->getId(),
                'notification_id' => $notification->getId(),
            ]);
            return;
        }

        try {
            $chatUrl = rtrim($this->parameterBag->get('app_base_url'), '/') . "/admin/chat/" . $notification->getUuid();
            $placeholders = [
                '{{employee_name}}' => $employee->getMasterEmployee()?->getName() ?? '',
                '{{report_date}}' => $employeeHours->getReportDate()->format('d-M-Y'),
                '{{disapproval_reason}}' => $notification->getDisapprovalReason() ?? 'No reason provided',
                '{{total_hours}}' => $this->timeHelper->convertDecimalToTime($employeeHours->getTotalHours()),
                '{{chat_url}}' => $chatUrl,
            ];

            $emailContent = $this->emailService->generateEmailContent('employee_disapproval_report', $placeholders);
            $this->emailService->sendEmail($email, 'TeamLogger: Hours Report Disapproved', $emailContent);
            $this->entityManager->flush();
        } catch (TransportExceptionInterface|\Exception $e) {
            $this->logError($e, 'Error sending disapproval email', $notification);
        }
    }

    private function resendJustification(Employee $employee, EmployeeHours $employeeHours, EmployeeNotification $notification): void
    {
        $email = $employee->getMasterEmployee()?->getEmail();
        if (!$email) {
            $this->log('No email address for employee', [
                'employee_id' => $employee->getId(),
                'notification_id' => $notification->getId(),
            ]);
            return;
        }

        try {
            $placeholders = [
                '{{employee_name}}' => $employee->getMasterEmployee()?->getName() ?? '',
                '{{report_date}}' => $employeeHours->getReportDate()->format('d-M-Y'),
                '{{total_hours}}' => $this->timeHelper->convertDecimalToTime($employeeHours->getTotalHours()),
                '{{break_hours}}' => $this->timeHelper->convertDecimalToTime($employeeHours->getBreakHours()),
                '{{on_computer_hours}}' => $this->timeHelper->convertDecimalToTime($employeeHours->getOnComputerHours()),
                '{{off_computer_hours}}' => $this->timeHelper->convertDecimalToTime($employeeHours->getOffComputerHours()),
                '{{justification_url}}' => rtrim($this->parameterBag->get('app_base_url'), '/') . "/admin/justify/" . $notification->getUuid(),
            ];

            $emailContent = $this->emailService->generateEmailContent('employee_resend_justification', $placeholders);
            $this->emailService->sendEmail($email, 'TeamLogger: Pending Hours Justification Reminder', $emailContent);
            $this->entityManager->flush();
        } catch (TransportExceptionInterface|\Exception $e) {
            $this->logError($e, 'Error sending resend justification email', $notification);
        }
    }

    private function sendIncompleteHoursNotice(Employee $employee, EmployeeHours $employeeHours, EmployeeNotification $notification): void
    {
        $email = $employee->getMasterEmployee()?->getEmail();
        if (!$email) {
            $this->log('No email address for employee', [
                'employee_id' => $employee->getId(),
                'notification_id' => $notification->getId(),
            ]);
            return;
        }

        try {
            $remainingHours = $this->entityManager->getRepository(EmployeeRemainingHours::class)
                ->findOneBy(['employeeNotification' => $notification]);

            $emailContent = 'No previous email content available.';

            if ($remainingHours) {
                $emailContentsArray = $remainingHours->getEmailContent();
                if (!empty($emailContentsArray)) {
                    $latestEmailEntry = end($emailContentsArray);
                    $emailContent = $latestEmailEntry['content'] ?? 'No previous email content available.';
                }
            }

            $placeholders = [
                '{{employee_name}}' => $employee->getMasterEmployee()?->getName() ?? '',
                '{{email_content}}' => $emailContent,
                '{{report_date}}' => $employeeHours->getReportDate()->format('d-M-Y'),
            ];

            $emailContent = $this->emailService->generateEmailContent('employee_incomplete_hours_notice', $placeholders);
            $this->emailService->sendEmail($email, 'TeamLogger: Pending Hours Justification Reminder', $emailContent);
            $this->entityManager->flush();
        } catch (TransportExceptionInterface|\Exception $e) {
            $this->logError($e, 'Error sending incomplete hours notice', $notification);
        }
    }

    private function logError(\Exception $e, string $message, EmployeeNotification $notification): void
    {
        $this->log($message, [
            'error_message' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString(),
            'employee_id' => $notification->getEmployee()->getId(),
            'notification_id' => $notification->getId(),
        ]);
        $this->entityManager->flush();
    }
}