<?php

namespace App\Form;

use App\Entity\LeaveBalance;
use App\Entity\LeaveType;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class LeaveBalanceType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('leaveType', EntityType::class, [
                'label' => false,
                'class' => LeaveType::class,
                'choice_label' => 'name',
                'disabled' => true,
                'attr' => [
                    'class' => 'form-control d-inline-block w-50',
                ],
            ])
            ->add('usedDays', NumberType::class, [
                'label' => false,
                'attr' => [
                    'class' => 'form-control d-inline-block w-25',
                ],
            ])
            ->add('totalDays', NumberType::class, [
                'label' => false,
                'attr' => [
                    'class' => 'form-control d-inline-block w-25',
                ],
            ])
            ->add('remainingDays', NumberType::class, [
                'label' => false,
                'attr' => [
                    'class' => 'form-control d-inline-block w-25',
                    'readonly' => true,
                ],
            ]);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => LeaveBalance::class,
        ]);
    }
}