<?php
namespace App\Controller\Front;

use App\Entity\Holiday;
use App\Entity\LeaveRequest;
use App\Entity\MasterEmployee;
use App\Entity\LeaveBalance;
use App\Form\LeaveRequestFormType;
use App\Message\LeaveRequestNotificationMessage;
use App\Repository\LeaveBalanceRepository;
use App\Service\Common\LeaveRequestService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Messenger\Exception\ExceptionInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Routing\Annotation\Route;

#[Route('/employee')]
class LeaveRequestController extends AbstractController
{
    public function __construct(
        private readonly LeaveRequestService    $leaveRequestService,
        private readonly EntityManagerInterface $entityManager,
        private readonly leaveBalanceRepository $leaveBalanceRepository,
    ) {}

    #[Route('/leave-request/list', name: 'employee_leave_request_list', methods: ['GET', 'POST'])]
    public function listLeaveRequests(Request $request): Response
    {
        $employee = $this->getUser();
        if (!$employee instanceof MasterEmployee) {
            $this->addFlash('error', 'You must be logged in as an employee to request a leave.');
            return $this->redirectToRoute('app_login_employee');
        }
        $leaveBalances = $this->leaveBalanceRepository->getCurrentYearLeaveBalance($employee);
        $leaveData = [];
        foreach ($leaveBalances as $balance) {
            $leaveTypeName = strtoupper($balance->getLeaveType()->getName());
            $leaveData[$leaveTypeName] = $balance->getRemainingDays();
        }
        $table = $this->leaveRequestService->createLeaveRequestTable($request, $employee);
        if ($table->isCallback()) {
            return $table->getResponse();
        }

        return $this->render('Front/LeaveRequest/index.html.twig', [
            'datatable' => $table,
            'leaveData' => $leaveData,
        ]);
    }

    /**
     * @throws \DateMalformedPeriodStringException
     */
    #[Route('/leave-request/create', name: 'employee_leave_request_create', methods: ['GET', 'POST'])]
    public function createLeaveRequest(Request $request , MessageBusInterface $messageBus): Response
    {
        $employee = $this->getUser();
        if (!$employee instanceof MasterEmployee) {
            $this->addFlash('error', 'You must be logged in as an employee to request a leave.');
            return $this->redirectToRoute('app_login_employee');
        }

        $leaveRequest = new LeaveRequest();
        $leaveRequest->setEmployee($employee);
        $leaveRequest->setStatus(LeaveRequest::STATUS_PENDING);
        $leaveRequest->setAppliedOn(new \DateTime());

        $form = $this->createForm(LeaveRequestFormType::class, $leaveRequest, [
            'employee' => $employee,
        ]);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $leaveBalance = $form->get('leaveType')->getData();
            if (!$leaveBalance instanceof LeaveBalance) {
                $this->addFlash('error', 'Invalid leave type selected.');
                return $this->render('Front/LeaveRequest/new.html.twig', [
                    'form' => $form->createView(),
                    'mode' => 'create',
                ]);
            }
            /** @phpstan-ignore-next-line */
            $existingRequests = $this->entityManager->getRepository(LeaveRequest::class)->findOverlappingLeaves(
                $employee,
                $leaveRequest->getStartDate(),
                $leaveRequest->getEndDate()
            );
            if (count($existingRequests) > 0) {
                $this->addFlash('error', 'You already have a leave request for these dates.');
                return $this->render('Front/LeaveRequest/new.html.twig', [
                    'form' => $form->createView(),
                    'mode' => 'create',
                ]);
            }
            $holidayDates = $this->leaveRequestService->getHolidayDates();
            $daysRequested = $this->leaveRequestService->calculateDaysRequested($leaveRequest, $holidayDates);

            $currentYear = (int) (new \DateTime())->format('Y');
            if ($leaveBalance->getYear() !== $currentYear || $leaveBalance->getEmployee() !== $employee) {
                $this->addFlash('error', 'Invalid leave balance for the current year.');
                return $this->render('Front/LeaveRequest/new.html.twig', [
                    'form' => $form->createView(),
                    'mode' => 'create',
                ]);
            }

            if ($leaveBalance->getRemainingDays() < $daysRequested['base']) {
                $this->addFlash('error', 'Insufficient leave balance for this request.');
                return $this->render('Front/LeaveRequest/new.html.twig', [
                    'form' => $form->createView(),
                    'mode' => 'create',
                ]);
            }
            $leaveRequest->setLeaveType($leaveBalance);
            $leaveRequest->setDaysRequested($daysRequested['base']);
            $leaveRequest->setPenaltyDays($daysRequested['penalty']);
            $leaveRequest->setPenaltyApplied(false);
            $this->entityManager->beginTransaction();
            try {
                $leaveBalance->setUsedDays($leaveBalance->getUsedDays() + $daysRequested['base']);
                $leaveBalance->updateRemainingDays();
                $this->entityManager->persist($leaveBalance);
                $this->entityManager->persist($leaveRequest);
                $this->entityManager->flush();
                $message = new LeaveRequestNotificationMessage(
                    (int)$employee->getId(),
                    (int)$leaveRequest->getId()
                );
                $messageBus->dispatch($message);

                $this->entityManager->commit();

                if ($daysRequested['penalty'] > 0) {
                    $this->addFlash('warning', "A penalty of {$daysRequested['penalty']} days has been applied due to an unplanned leave on Friday/Monday.");
                }
                $this->addFlash('success', 'Your leave request has been submitted successfully.');
                return $this->redirectToRoute('employee_leave_request_list');
            } catch (\Exception $e) {
                $this->entityManager->rollback();
                $this->addFlash('error', 'An error occurred while submitting your leave request.');
                return $this->render('Front/LeaveRequest/new.html.twig', [
                    'form' => $form->createView(),
                    'mode' => 'create',
                ]);
            } catch (ExceptionInterface $e) {
                $this->entityManager->rollback();
                $this->addFlash('error', 'An error occurred with the notification system: ' . $e->getMessage());
                return $this->render('Front/LeaveRequest/new.html.twig', [
                    'form' => $form->createView(),
                    'mode' => 'create',
                ]);
            }
        }

        return $this->render('Front/LeaveRequest/new.html.twig', [
            'form' => $form->createView(),
            'mode' => 'create',
        ]);
    }
    #[Route('/api/holidays/dates', name: 'api_holiday_dates', methods: ['GET'])]
    public function getHolidayDates(EntityManagerInterface $entityManager): JsonResponse
    {
        $holidays = $entityManager->getRepository(Holiday::class)->findAll();
        $dates = array_map(static function(Holiday $holiday) {
            return $holiday->getDate()->format('d-m-Y');
        }, $holidays);

        return new JsonResponse($dates);
    }

    #[Route('/api/leave-request/check-duplicate', name: 'api_check_duplicate_leave', methods: ['GET'])]
    public function checkDuplicateLeave(Request $request): JsonResponse
    {
        $employee = $this->getUser();
        if (!$employee instanceof MasterEmployee) {
            return new JsonResponse(['error' => 'Unauthorized'], Response::HTTP_UNAUTHORIZED);
        }
        $startDate = $request->query->get('start_date');
        $endDate = $request->query->get('end_date');

        if (!$startDate || !$endDate) {
            return new JsonResponse(['error' => 'Start date and end date are required'], Response::HTTP_BAD_REQUEST);
        }

        try {
            $startDateTime = new \DateTime($startDate);
            $endDateTime = new \DateTime($endDate);
        } catch (\Exception $e) {
            return new JsonResponse(['error' => 'Invalid date format'], Response::HTTP_BAD_REQUEST);
        }
        /** @phpstan-ignore-next-line */
        $existingRequests = $this->entityManager->getRepository(LeaveRequest::class)->findOverlappingLeaves(
            $employee,
            $startDateTime,
            $endDateTime
        );

        $hasOverlap = count($existingRequests) > 0;

        return new JsonResponse([
            'duplicate_exists' => $hasOverlap,
            'count' => count($existingRequests)
        ]);
    }

    /**
     * @throws \DateMalformedPeriodStringException
     */
    #[Route('/api/leave-request/calculate-days', name: 'api_calculate_leave_days', methods: ['GET'])]
    public function calculateLeaveDays(Request $request, LeaveRequestService $leaveRequestService): JsonResponse
    {
        $employee = $this->getUser();
        if (!$employee instanceof MasterEmployee) {
            return new JsonResponse(['error' => 'Unauthorized'], Response::HTTP_UNAUTHORIZED);
        }

        $startDateStr = $request->query->get('start_date');
        $endDateStr = $request->query->get('end_date');
        $leaveTypeId = $request->query->get('leave_type_id');
        $startHalfDay = $request->query->get('start_half_day');
        $endHalfDay = $request->query->get('end_half_day');

        if (!$startDateStr || !$endDateStr || !$leaveTypeId) {
            return new JsonResponse(['error' => 'Start date, end date, and leave type ID are required'], Response::HTTP_BAD_REQUEST);
        }

        try {
            $startDate = new \DateTime($startDateStr);
            $endDate = new \DateTime($endDateStr);
        } catch (\Exception $e) {
            return new JsonResponse(['error' => 'Invalid date format'], Response::HTTP_BAD_REQUEST);
        }

        $leaveBalance = $this->entityManager->getRepository(LeaveBalance::class)->findOneBy([
            'id' => $leaveTypeId,
            'employee' => $employee
        ]);

        if (!$leaveBalance) {
            return new JsonResponse(['error' => 'Leave balance not found'], Response::HTTP_NOT_FOUND);
        }

        $leaveRequest = new LeaveRequest();
        $leaveRequest->setEmployee($employee);
        $leaveRequest->setStartDate($startDate);
        $leaveRequest->setEndDate($endDate);
        $leaveRequest->setLeaveType($leaveBalance);
        $leaveRequest->setAppliedOn(new \DateTime());
        if ($startHalfDay) {
            $leaveRequest->setStartHalfDay($startHalfDay);
        }
        if ($endHalfDay) {
            $leaveRequest->setEndHalfDay($endHalfDay);
        }

        $holidayDates = $leaveRequestService->getHolidayDates();
        $daysRequested = $leaveRequestService->calculateDaysRequested($leaveRequest, $holidayDates);

        return new JsonResponse([
            'base_days' => $daysRequested['base'],
            'penalty_days' => $daysRequested['penalty'],
            'total_days' => $daysRequested['total'],
            'remaining_days' => $leaveBalance->getRemainingDays()
        ]);
    }
}