<?php

namespace App\Command;

use App\Entity\Project;
use App\Entity\User;
use App\Service\AutoAssignTasksService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(name: 'app:generate-monthly-tasks')]
class GenerateMonthlyTasksCommand extends Command
{
    private AutoAssignTasksService $autoAssignTasksService;
    private EntityManagerInterface $entityManager;

    public function __construct(AutoAssignTasksService $autoAssignTasksService, EntityManagerInterface $entityManager)
    {
        parent::__construct();
        $this->autoAssignTasksService = $autoAssignTasksService;
        $this->entityManager = $entityManager;
    }

    protected function configure(): void
    {
        $this->setDescription('Generates social media tasks for the next 15 days and assigns them to designers.');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        try {
            $project = $this->entityManager
                ->getRepository(Project::class)
                ->findOneBy(['name' => 'Graphic Designer']);

            if (!$project) {
                throw new \RuntimeException('Project "Graphic Designer" not found.');
            }
            $assignedByAdmin = $this->entityManager
                ->getRepository(User::class)
                ->findOneBy(['username' => 'HR']);

            if (!$assignedByAdmin) {
                throw new \RuntimeException('User with username "HR" not found.');
            }
            $this->autoAssignTasksService->generateUpcomingTasks($project, $assignedByAdmin);

            $output->writeln('<info>Tasks generated and assigned successfully.</info>');
            return Command::SUCCESS;

        } catch (\Throwable $e) {
            $output->writeln("<error>Error: {$e->getMessage()}</error>");
            return Command::FAILURE;
        }
    }
}