{% extends 'admin/dashboard/base.html.twig' %}

{% block body %}
    {% include 'admin/partials/breadcrumbs.html.twig' with {
        title: 'Documents',
        breadcrumbs: [
            { 'label': 'Dashboard', 'url': url('admin_dashboard') },
            { 'label': 'Documents', 'url': url('admin_document_index') }
        ],
        active: mode == 'view' ? 'View Document' : (mode == 'edit' ? 'Edit Document' : 'Create Document')
    } %}

    <div class="content">
        <div class="animated fadeIn">
            <div class="row">
                <div class="col-lg-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <strong class="card-title">
                                {{ mode == 'view' ? 'View Document' : (mode == 'edit' ? 'Edit Document' : 'Create Document') }}
                            </strong>
                            <div class="d-flex align-items-center">
                                <a href="{{ path('admin_document_index') }}" class="btn btn-sm btn-outline-secondary mr-2">
                                    <i class="fa fa-list mr-1"></i>Back to List
                                </a>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="document-form">
                                {% if form is defined %}
                                    {{ form_start(form, { 'attr': { 'class': 'needs-validation use-loader', 'novalidate': 'novalidate' } }) }}
                                    <div class="form-section">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.title, 'Document Title', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-file-text"></i></span>
                                                        </div>
                                                        {{ form_widget(form.title, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter document title', 'required': 'required' } }) }}
                                                    </div>
                                                    {{ form_errors(form.title) }}
                                                    <small class="form-text text-muted">Enter a document title</small>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.file, 'Upload Document', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-upload"></i></span>
                                                        </div>
                                                        {{ form_widget(form.file, {
                                                            'attr': {
                                                                'class': 'form-control',
                                                                'accept': '.pdf,.jpg,.jpeg,.png',
                                                                'style': 'line-height:1.2 !important'
                                                            }
                                                        }) }}
                                                    </div>
                                                    {{ form_errors(form.file) }}
                                                    <small class="form-text text-muted">Upload a PDF, JPEG, or PNG file (max 5MB)</small>
                                                    {% if mode == 'edit' and document.filePath %}
                                                        <small class="form-text text-muted">
                                                            Current file: <a href="{{ document.filePath }}" target="_blank" class="text-primary">Download</a>
                                                        </small>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="d-flex justify-content-end mt-4">
                                        <a href="{{ path('admin_document_index') }}" class="btn btn-secondary mr-2">Cancel</a>
                                        <button type="submit" class="btn btn-primary pr-3 pl-3">
                                            {{ document.id is defined ? 'Update Document' : 'Create Document' }}
                                        </button>
                                    </div>
                                    {{ form_end(form) }}
                                {% else %}
                                    <!-- View Mode -->
                                    <div class="form-section document-details-section">
                                        <div class="details-section">
                                            <div class="details-container">
                                                <div class="detail-row">
                                                    <div class="detail-item col-md-4"><strong class="detail-label">Document Title:</strong></div>
                                                    <div class="detail-item col-md-8">{{ document.title ?: 'N/A' }}</div>
                                                </div>
                                                <div class="detail-row">
                                                    <div class="detail-item col-md-4"><strong class="detail-label">File:</strong></div>
                                                    <div class="detail-item col-md-8">
                                                        {% if document.filePath %}
                                                            <a href="{{ document.filePath }}" target="_blank">Download</a>
                                                        {% else %}
                                                            N/A
                                                        {% endif %}
                                                    </div>
                                                </div>
                                                <div class="detail-row">
                                                    <div class="detail-item col-md-4"><strong class="detail-label">Created At:</strong></div>
                                                    <div class="detail-item col-md-8">{{ document.createdAt ? document.createdAt|date('d-m-Y') : 'N/A' }}</div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="action-footer mt-4">
                                            <a href="{{ path('admin_document_index') }}" class="btn btn-secondary mr-2">Back to List</a>
                                            <a href="{{ path('admin_document_edit', {'id': document.id}) }}" class="btn btn-primary pr-3 pl-3">Edit Document</a>
                                        </div>
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="clearfix"></div>
    <link rel="stylesheet" href="{{ asset('assets/css/Error/error.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/AdminForm/Form.css') }}">
    <script src="{{ asset('assets/js/admin/document.js') }}"></script>
{% endblock %}