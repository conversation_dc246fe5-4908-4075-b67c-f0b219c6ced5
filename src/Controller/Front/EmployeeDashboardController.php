<?php

namespace App\Controller\Front;

use App\Entity\MasterEmployee;
use App\Repository\AnnouncementRepository;
use App\Repository\EmployeeHoursRepository;
use App\Repository\EmployeeNotificationRepository;
use App\Repository\HardwareRepository;
use App\Repository\LeaveRequestRepository;
use App\Repository\MasterEmployeeRepository;
use App\Traits\ExceptionLoggerTrait;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
class EmployeeDashboardController extends AbstractController
{
    use ExceptionLoggerTrait;

    public function __construct(
        ParameterBagInterface                           $parameterBag,
        private readonly EmployeeHoursRepository        $employeeHoursRepository,
        private readonly EmployeeNotificationRepository $employeeNotificationRepository,
        private readonly MasterEmployeeRepository         $masterEmployeeRepository,
        private readonly AnnouncementRepository         $announcementRepository,
        private readonly LeaveRequestRepository           $leaveRequestRepository,
        private readonly HardwareRepository                $hardwareRepository,
    )
    {
        $this->initializeLogger($parameterBag, 'employee_dashboard');
    }

    /**
     * @return Response
     * @throws \DateMalformedStringException
     */
    #[Route(path: '/dashboard-employee', name: 'dashboard_employee', methods: ['GET'])]
    public function index(): Response
    {
        $user = $this->getUser();
        if (!$user instanceof MasterEmployee) {
            throw $this->createAccessDeniedException('You must be logged in as an employee to view this page.');
        }
        $totalHours = $this->employeeHoursRepository->getTotalHoursForCurrentMonth($user);
        $employeeNotified = $this->employeeNotificationRepository->countNotificationsForEmployee($user);
        $counts = $this->employeeNotificationRepository->countJustifiedAndNotJustifiedForEmployee($user);
        $birthday = $this->masterEmployeeRepository->findUpcomingBirthdays();
        $anniversary = $this->masterEmployeeRepository->findUpcomingAnniversaries();
        $announcements = $this->announcementRepository->findAnnouncementsForEmployee($user, 10);

        $justifiedCount = $counts['justifiedCount'];
        $notJustifiedCount = $counts['notJustifiedCount'];
        $today = new \DateTime('today');
        $endOfWeek = (clone $today)->modify('+7 days');
        $weeklyLeaveRequests = $this->leaveRequestRepository->findByDateRange($today, $endOfWeek);
        $hardware = $this->hardwareRepository->findBy(['showOnDashboard' => true]);
        return $this->render('Front/include/main.html.twig',[
            'totalHours' => $totalHours,
            'employeeNotified' => $employeeNotified,
            'justifiedCount' => $justifiedCount,
            'notJustifiedCount' => $notJustifiedCount,
            'birthday'=>$birthday,
            'anniversary'=>$anniversary,
            'announcements' => $announcements,
            'weeklyLeaveRequests' => $weeklyLeaveRequests,
            'hardware' => $hardware,
        ]);
    }
}
