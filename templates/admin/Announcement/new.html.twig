{% extends 'admin/dashboard/base.html.twig' %}

{% block body %}
    {% include 'admin/partials/breadcrumbs.html.twig' with {
        title: 'Announcements',
        breadcrumbs: [
            { 'label': 'Dashboard', 'url': url('admin_dashboard') },
            { 'label': 'Announcements', 'url': url('admin_announcement') }
        ],
        active: mode == 'view' ? 'View Announcement' : (mode == 'edit' ? 'Edit Announcement' : 'Create Announcement')
    } %}

    <div class="content">
        <div class="animated fadeIn">
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <strong class="card-title">
                                {{ mode == 'view' ? 'View Announcement' : (mode == 'edit' ? 'Edit Announcement' : 'Create Announcement') }}
                            </strong>
                            <div class="d-flex align-items-center">
                                <a href="{{ path('admin_announcement') }}" class="btn btn-sm btn-outline-secondary mr-2">
                                    <i class="fa fa-list mr-1"></i>Back to List
                                </a>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="announcement-form">
                                    {{ form_start(form, { 'attr': { 'class': 'needs-validation', 'novalidate': 'novalidate' } }) }}
                                    <div class="form-section">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.title, 'Title', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-file-text"></i></span>
                                                        </div>
                                                        {{ form_widget(form.title, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter announcement title', 'required': 'required' } }) }}
                                                    </div>
                                                    {{ form_errors(form.title) }}
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.status, 'Status', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-toggle-on"></i></span>
                                                        </div>
                                                        {{ form_widget(form.status, { 'attr': { 'class': 'form-control', 'aria-label': 'Select Status' } }) }}
                                                    </div>
                                                    {{ form_errors(form.status) }}
                                                    <small class="form-text text-muted">Select the announcement status (Draft, Published, Archived)</small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-3">
                                                <div class="mb-3">
                                                    {{ form_label(form.targetAllEmployees, 'Target All Employees', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                    <div class="input-group mb-2">
                                                        <div class="input-group-prepend">
                                                            <div class="input-group-text">
                                                                {{ form_widget(form.targetAllEmployees, { 'attr': { 'class': 'custom-checkbox', 'aria-label': 'Checkbox for Target All Employees' } }) }}
                                                            </div>
                                                        </div>
                                                        <div class="form-control checkbox-label">
                                                            {{ form_label(form.targetAllEmployees, null, { 'label_attr': { 'class': 'ml-2' } }) }}
                                                        </div>
                                                    </div>
                                                    {{ form_errors(form.targetAllEmployees) }}
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="mb-3">
                                                    {{ form_label(form.targetTeamLeadersOnly, 'Target Team Leaders Only', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                    <div class="input-group mb-2">
                                                        <div class="input-group-prepend">
                                                            <div class="input-group-text">
                                                                {{ form_widget(form.targetTeamLeadersOnly, { 'attr': { 'class': 'custom-checkbox', 'aria-label': 'Checkbox for Target Team Leaders Only' } }) }}
                                                            </div>
                                                        </div>
                                                        <div class="form-control checkbox-label">
                                                            {{ form_label(form.targetTeamLeadersOnly, null, { 'label_attr': { 'class': 'ml-2' } }) }}
                                                        </div>
                                                    </div>
                                                    {{ form_errors(form.targetTeamLeadersOnly) }}
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="mb-3">
                                                    {{ form_label(form.sendEmail, 'Send Email Notification', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                    <div class="input-group mb-2">
                                                        <div class="input-group-prepend">
                                                            <div class="input-group-text">
                                                                {{ form_widget(form.sendEmail, { 'attr': { 'class': 'custom-checkbox', 'aria-label': 'Checkbox for Send Email' } }) }}
                                                            </div>
                                                        </div>
                                                        <div class="form-control checkbox-label">
                                                            {{ form_label(form.sendEmail, null, { 'label_attr': { 'class': 'ml-2' } }) }}
                                                        </div>
                                                    </div>
                                                    {{ form_errors(form.sendEmail) }}
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="mb-3">
                                                    {{ form_label(form.isImportant, 'Mark as Important', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                    <div class="input-group mb-2">
                                                        <div class="input-group-prepend">
                                                            <div class="input-group-text">
                                                                {{ form_widget(form.isImportant, { 'attr': { 'class': 'custom-checkbox', 'aria-label': 'Checkbox for Important' } }) }}
                                                            </div>
                                                        </div>
                                                        <div class="form-control checkbox-label">
                                                            {{ form_label(form.isImportant, null, { 'label_attr': { 'class': 'ml-2' } }) }}
                                                        </div>
                                                    </div>
                                                    {{ form_errors(form.isImportant) }}
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.departments, 'Departments', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                    {% if form.departments.children is not empty %}
                                                    <div class="checkbox-group">
                                                        {% for child in form.departments %}
                                                            <div class="input-group mb-2">
                                                                <div class="input-group-prepend">
                                                                    <div class="input-group-text">
                                                                        {{ form_widget(child, { 'attr': { 'class': 'custom-checkbox', 'aria-label': 'Checkbox for ' ~ child.vars.label|default('department') } }) }}
                                                                    </div>
                                                                </div>
                                                                <div class="form-control checkbox-label">
                                                                    {{ form_label(child, null, { 'label_attr': { 'class': 'ml-2' } }) }}
                                                                </div>
                                                            </div>
                                                        {% endfor %}
                                                    </div>
                                                    {{ form_errors(form.departments) }}
                                                    {% else %}
                                                        <p class="text-muted">No departments available.</p>
                                                    {% endif %}
                                                    <small class="form-text text-muted">Select departments to target (ignored if Target All Employees is checked)</small>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.priority, 'Priority', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-star"></i></span>
                                                        </div>
                                                        {{ form_widget(form.priority, { 'attr': { 'class': 'form-control', 'aria-label': 'Select Priority' } }) }}
                                                    </div>
                                                    {{ form_errors(form.priority) }}
                                                    <small class="form-text text-muted">Select the announcement priority (Normal, High, Pinned)</small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.startDate, 'Start Date', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-calendar"></i></span>
                                                        </div>
                                                        {{ form_widget(form.startDate, {
                                                            'attr': {
                                                                'class': 'form-control datepicker',
                                                                'required': 'required',
                                                                'data-initial-date': announcement.startDate is defined and announcement.startDate ? announcement.startDate|date('d-m-Y') : ''
                                                            }
                                                        }) }}
                                                    </div>
                                                    {{ form_errors(form.startDate) }}
                                                    <small class="form-text text-muted">Select the start date of the announcement</small>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.endDate, 'End Date', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-calendar"></i></span>
                                                        </div>
                                                        {{ form_widget(form.endDate, {
                                                            'attr': {
                                                                'class': 'form-control datepicker',
                                                                'required': 'required',
                                                                'data-initial-date': announcement.endDate is defined and announcement.endDate ? announcement.endDate|date('d-m-Y') : ''
                                                            }
                                                        }) }}
                                                    </div>
                                                    {{ form_errors(form.endDate) }}
                                                    <small class="form-text text-muted">Select the end date of the announcement</small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-12">
                                                <div class="mb-3">
                                                    {{ form_label(form.content, 'Content', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                                    {{ form_widget(form.content, { 'attr': { 'class': 'form-control', 'required': 'required' } }) }}
                                                    {{ form_errors(form.content) }}
                                                    <small class="form-text text-muted">Enter the announcement content (supports rich text)</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="action-footer mt-4">
                                        <a href="{{ path('admin_announcement') }}" class="btn btn-secondary mr-2">Cancel</a>
                                        <button type="submit" class="btn btn-primary pr-3 pl-3">
                                            {{ announcement.id is defined ? 'Update Announcement' : 'Create Announcement' }}
                                        </button>
                                    </div>
                                    {{ form_end(form) }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="clearfix"></div>
    <link rel="stylesheet" href="{{ asset('assets/css/Error/error.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/AdminForm/Form.css') }}">
    <script src="{{ asset('assets/js/datatables.min.js') }}"></script>
    <script src="{{ asset('bundles/datatables/js/datatables.js') }}"></script>
    <script src="{{ asset('assets/js/moment.min.js') }}"></script>
    <script src="{{ asset('assets/js/daterangepicker.min.js') }}"></script>
    <script src="{{ asset('assets/js/admin/Announcement.js') }}"></script>
{% endblock %}