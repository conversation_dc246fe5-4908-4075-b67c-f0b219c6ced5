<?php

namespace App\Form;

use App\Entity\Bug;
use App\Entity\Category;
use App\Entity\Priority;
use App\Entity\Project;
use App\Entity\Severity;
use App\Entity\Status;
use App\Entity\Task;
use App\Entity\MasterEmployee;
use App\Repository\MasterEmployeeRepository;
use Doctrine\ORM\EntityRepository;
use FOS\CKEditorBundle\Form\Type\CKEditorType;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\EnumType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormEvent;
use Symfony\Component\Form\FormEvents;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\NotBlank;

class BugType extends AbstractType
{
    private Security $security;

    public function __construct(Security $security,private readonly MasterEmployeeRepository $masterEmployeeRepository)
    {
        $this->security = $security;
    }

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $employee = $this->security->getUser();
        $isAdmin = $this->security->isGranted('ROLE_ADMIN');

        $builder
            ->add('project', EntityType::class, [
                'class' => Project::class,
                'choice_label' => 'name',
                'autocomplete'=>true,
                'placeholder' => 'Select a Project',
                'required' => true,
                'query_builder' => function ($er) use ($employee, $isAdmin) {
                    $qb = $er->createQueryBuilder('p')
                        ->where('p.status = :activeStatus')
                        ->setParameter('activeStatus', 'active')
                        ->orderBy('p.name', 'ASC');
                    if (!$isAdmin) {
                        $qb->innerJoin('p.task', 't')
                            ->andWhere('t.assignedTo = :employee')
                            ->setParameter('employee', $employee);
                    }
                    return $qb;
                },
                'attr' => ['class' => 'form-control project-select'],
            ])
            ->add('task', EntityType::class, [
                'class' => Task::class,
                'choice_label' => 'title',
                'placeholder' => 'Select a Project first',
                'required' => true,
                'choices' => [],
                'attr' => ['class' => 'form-control task-select'],
            ])
            ->add('assignedTo', EntityType::class, [
                'class' => MasterEmployee::class,
                'autocomplete'=>true,
                'choices' => $this->masterEmployeeRepository->findBy(['isDelete' => false]),
                'choice_label' => 'name',
                'placeholder' => 'Select an Employee',
                'required' => true,
                'constraints' => [
                    new NotBlank([
                        'message' => 'Assigned to name is required.',
                    ]),
                ],
                'attr' => ['class' => 'form-control'],
            ])
            ->add('status', EnumType::class, [
                'class' =>Status::class,
                'autocomplete' => true,
                'required' => true,
                'attr' => [
                    'class' => 'form-control',
                ],
            ])
            ->add('priority', EnumType::class, [
                'class' => Priority::class,
                'autocomplete' => true,
                'placeholder' => 'Select Priority',
                'required' => true,
                'attr' => ['class' => 'form-control'],
            ])
            ->add('severity', EnumType::class, [
                'class' => Severity::class,
                'placeholder' => 'Select Severity',
                'autocomplete' => true,
                'required' => true,
                'attr' => ['class' => 'form-control'],
            ])
            ->add('category', EnumType::class, [
                'class' => Category::class,
                'placeholder' => 'Select Category',
                'autocomplete' => true,
                'required' => true,
                'attr' => ['class' => 'form-control'],
            ])
            ->add('comments', CKEditorType::class, [
                'required' => true,
                'constraints' => [new NotBlank()],
                'label' => 'Comments (What to test)',
                'row_attr' => ['class' => 'col-md-12'],
                'attr' => ['rows' => '10'],
            ]);

        $builder->addEventListener(FormEvents::POST_SET_DATA, function (FormEvent $event) {
            $bug = $event->getData();
            $form = $event->getForm();
            $employee = $this->security->getUser();
            $isAdmin = $this->security->isGranted('ROLE_ADMIN');

            if ($bug && $bug->getProject()) {
                $project = $bug->getProject();
                $form->add('task', EntityType::class, [
                    'class' => Task::class,
                    'choice_label' => 'title',
                    'placeholder' => 'Select a Task',
                    'required' => true,
                    'query_builder' => function ($er) use ($project, $employee, $isAdmin) {
                        $qb = $er->createQueryBuilder('t')
                            ->where('t.project = :project')
                            ->setParameter('project', $project)
                            ->orderBy('t.title', 'ASC');
                        if (!$isAdmin) {
                            $qb->andWhere('t.assignedTo = :employee')
                                ->setParameter('employee', $employee);
                        }
                        return $qb;
                    },
                    'attr' => ['class' => 'form-control task-select'],
                ]);
            }
        });

        $builder->addEventListener(FormEvents::PRE_SUBMIT, function (FormEvent $event) {
            $form = $event->getForm();
            $data = $event->getData();
            $employee = $this->security->getUser();
            $isAdmin = $this->security->isGranted('ROLE_ADMIN');

            if (!empty($data['project'])) {
                $projectId = $data['project'];
                $form->add('task', EntityType::class, [
                    'class' => Task::class,
                    'choice_label' => 'title',
                    'placeholder' => 'Select a Task',
                    'required' => true,
                    'query_builder' => function ($er) use ($projectId, $employee, $isAdmin) {
                        $qb = $er->createQueryBuilder('t')
                            ->where('t.project = :projectId')
                            ->setParameter('projectId', $projectId)
                            ->orderBy('t.title', 'ASC');
                        if (!$isAdmin) {
                            $qb->andWhere('t.assignedTo = :employee')
                                ->setParameter('employee', $employee);
                        }
                        return $qb;
                    },
                    'attr' => ['class' => 'form-control task-select'],
                ]);
            }
        });
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => Bug::class,
        ]);
    }
}