{% extends 'Front/dashboard/base.html.twig' %}

{% block title %}Tasks - {{ project.name }}{% endblock %}
{% block body %}
    {% include 'Front/partials/breadcrumbs.html.twig' with {
        breadcrumbs: [
            { 'label': 'Dashboard', 'url': url('dashboard_employee') },
            { 'label': 'Projects', 'url': path('employee_projects') },
            { 'label': project.name ~ ' Project', 'url': path('employee_project_task', { 'project_id': project.id }) }
        ],
        active: mode == 'view' ? 'View Task' : (mode == 'edit' ? 'Edit Task' : 'Create Task')
    } %}
    {% include 'CommonTemplate/task/new.html.twig' %}
{% endblock %}