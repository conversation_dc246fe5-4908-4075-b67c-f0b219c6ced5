{% extends 'admin/dashboard/base.html.twig' %}

{% block body %}
    {% include 'admin/partials/breadcrumbs.html.twig' with {
        breadcrumbs: [
            { 'label': 'Dashboard', 'url': url('admin_dashboard') },
        ],
        active: 'Employee'
    } %}
    <div class="content">
        <div class="animated fadeIn">
            <div class="row">
                <div id="order-loader">
                    <img src="{{ asset('images/loader.svg') }}" alt="loader">
                </div>
                <div class="col-md-12">
                    <div id="flash-container"></div>
                    <div class="card">
                        <div class="card-header d-flex justify-content-between ">
                            <strong class="card-title">Employee</strong>
                            <button id="sync-employees" class="btn btn-primary pr-3 pl-3">Sync Teamlogger Employees</button>
                        </div>
                        <div class="card-body">
                            <div class="table-stats order-table ov-h text-center">
                            <div id="employee" class="datatable-main-wrapper brainst-datattable">Loading...</div>
                            </div>
                        </div>
                    </div>
                </div>


            </div>
        </div>
    </div>
    <div class="clearfix"></div>
    <script src="{{ asset('assets/js/datatables.min.js') }}"></script>
    <script src="{{ asset('bundles/datatables/js/datatables.js') }}"></script>
    <script src="{{ asset('assets/js/admin/employee_list.js') }}"></script>
    <script>
        $(document).ready(function () {
            recordsTable = $('#employee').initDataTables({{ datatable_settings(datatable) }});
        })
    </script>
    <script src="{{ asset('assets/js/datatableclearsearch.js') }}"></script>
{% endblock %}
