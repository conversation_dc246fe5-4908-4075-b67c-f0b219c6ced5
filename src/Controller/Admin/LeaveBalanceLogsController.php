<?php

namespace App\Controller\Admin;

use App\Service\Admin\LeaveBalanceManagerService;
use App\Service\Admin\LeaveBalanceLogService;
use App\Traits\ExceptionLoggerTrait;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

#[Route('/admin')]
class LeaveBalanceLogsController extends AbstractController
{
    use ExceptionLoggerTrait;

    public function __construct(
        ParameterBagInterface                   $parameterBag,
        private readonly LeaveBalanceLogService $leaveBalanceService,
    ) {
        $this->initializeLogger($parameterBag, 'LeaveBalance_controller');
    }

    #[Route(path: '/leave-balance/list', name: 'leave_balance_list', methods: ['GET', 'POST'])]
    public function index(Request $request): Response
    {
        $table = $this->leaveBalanceService->createLeaveBalanceTable($request);

        if ($table->isCallback()) {
            return $table->getResponse();
        }

        return $this->render('admin/LeaveBalance/index.html.twig', [
            'datatable' => $table,
        ]);
    }

    /**
     * @throws \DateMalformedStringException
     */
    #[Route('/leave-balance/populate', name: 'leave_balance_populate', methods: ['GET'])]
    public function populateLeaveBalances(LeaveBalanceManagerService $leaveBalanceManager): Response
    {
        $createdCount = $leaveBalanceManager->populateAllLeaveBalances();

        $this->addFlash('success', sprintf('Successfully populated %d leave balance records.', $createdCount));

        return $this->redirectToRoute('leave_balance_list');
    }
}