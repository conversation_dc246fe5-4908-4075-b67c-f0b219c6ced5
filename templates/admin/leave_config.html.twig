{% extends 'admin/dashboard/base.html.twig' %}

{% block body %}
    {% include 'admin/partials/breadcrumbs.html.twig' with {
        breadcrumbs: [
            { 'label': 'Dashboard', 'url': url('admin_dashboard') },
        ],
        active: 'Leave Policy'
    } %}

    <div class="content">
        <div class="animated fadeIn">
            <div class="row">
                <div id="order-loader">
                    <img src="{{ asset('images/loader.svg') }}" alt="loader">
                </div>
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between ">
                        <strong class="card-title">Leave Policy</strong>
                            <div>
                                <button id="populate-leave-balances" class="btn btn-primary pl-3 pr-3">Populate-Leave-Balance</button>
                            </div>
                        </div>
                        <div class="card-body">
                            <form id="update-settings" novalidate>
                                <div class="form-group">
                                    <label for="LeaveRenewalMonth">Leave Renewal Month</label>
                                    {% set months = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'] %}

                                    <select name="LeaveRenewalMonth" id="LeaveRenewalMonth" class="form-control">
                                        {% for month in months %}
                                            <option value="{{ month }}" {% if settings['LeaveRenewalMonth'] == month %}selected{% endif %}>
                                                {{ month }}
                                            </option>
                                        {% endfor %}
                                    </select>
                                    <small class="form-text text-muted">Default is April month</small>
                                    <div class="invalid-feedback" id="remaining-error"></div>
                                </div>
                                <div>
                                    <button
                                            id="email-config"
                                            type="submit"
                                            class="btn btn-block btn-primary"
                                            disabled>
                                        Update Settings
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="clearfix"></div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script src="{{ asset('assets/js/admin/leaveconfig.js') }}"></script>
    <script>
        const csrfToken = '{{ csrf_token('setting_update') }}';
    </script>

{% endblock %}