<?php

namespace App\Controller\Admin;

use App\Entity\User;
use App\Message\PayslipEmailMessage;
use App\Service\Admin\PayrollPDFService;
use App\Traits\ExceptionLoggerTrait;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Messenger\Exception\ExceptionInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Routing\Attribute\Route;
use App\Repository\PayrollRepository;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

#[Route('/admin')]
class PayrollPDFController extends AbstractController
{
    use ExceptionLoggerTrait;

    public function __construct(
        private readonly EntityManagerInterface    $entityManager,
        private readonly PayrollPDFService $payrollPDFService,
        private readonly PayrollRepository $payrollRepository,
        private readonly MessageBusInterface $messageBus,
    )
    {
    }

    #[Route('/Payroll/PDF', name: 'admin_payroll_pdf', methods: ['GET', 'POST'])]
    public function index(Request $request): Response
    {
        $user = $this->getUser();
        if (!$user instanceof User) {
            throw $this->createAccessDeniedException();
        }
        $table = $this->payrollPDFService->createExtraPayTable($request);

        if ($table->isCallback()) {
            return $table->getResponse();
        }

        return $this->render('admin/payrollPDF/index.html.twig', [
            'datatable' => $table,
        ]);
    }

    #[Route('/payroll/payslip/pdf/{month}/{year}/{employeeId}', name: 'admin_payroll_payslip_pdf', methods: ['GET'])]
    public function downloadPayslipPdf(int $month, int $year, int $employeeId): Response
    {
        $payroll = $this->payrollRepository->findOneBy([
            'employee' => $employeeId,
            'month' => $month,
            'year' => $year,
        ]);

        if (!$payroll) {
            throw new NotFoundHttpException('Payroll record not found.');
        }

        $employee = $payroll->getEmployee();
        if(!$employee) {
            throw new NotFoundHttpException('Employee not found.');
        }
        $employeeCode = $employee->getEmployeeCode();

        $payslipData = $this->payrollPDFService->preparePayslipData($payroll);
        $pdfContent = $this->payrollPDFService->generatePayslipPdf($payslipData);

        $fileName = sprintf('%s_%02d_%d_payslip.pdf', $employeeCode, $month, $year);

        return new Response($pdfContent, 200, [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'inline; filename="' . $fileName . '"',
        ]);
    }

    /**
     * @throws ExceptionInterface
     */
    #[Route('/payroll/payslip/share-all/{month}/{year}', name: 'admin_payroll_payslip_share_all', methods: ['POST'])]
    public function shareAllPayslips(int $month, int $year, PayrollRepository $payrollRepository): JsonResponse
    {
        $payrolls = $payrollRepository->findBy(['month' => $month, 'year' => $year]);
        $count = 0;
        foreach ($payrolls as $payroll) {
            $employee = $payroll->getEmployee();
            if ($employee) {
                $this->messageBus->dispatch(new PayslipEmailMessage($employee->getId(), $month, $year));
                $count++;
            }
        }
        return new JsonResponse(['status' => 'success', 'message' => "Payslip emails sent for $count employees."]);
    }

    /**
     * @throws \DateMalformedStringException
     */
    #[Route('/payroll/bank-sheet/{month}/{year}', name: 'admin_payroll_bank_sheet', methods: ['GET'])]
    public function exportBankSheet(int $month, int $year): Response
    {
        $payrolls = $this->payrollRepository->findBy(['month' => $month, 'year' => $year]);

        if (empty($payrolls)) {
            throw $this->createNotFoundException('No payrolls found for the given month and year.');
        }

        return $this->payrollPDFService->export($payrolls);
    }

}