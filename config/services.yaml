# This file is the entry point to configure your own services.
# Files in the packages/ subdirectory configure your dependencies.

# Put parameters here that don't need to change on each machine where the app is deployed
# https://symfony.com/doc/current/best_practices.html#use-parameters-for-application-configuration
parameters:
    app.teamlogger_api_key: '%env(TEAMLOGGER_API_KEY)%'
    app.teamlogger_base_url: '%env(BASE_URL)%'
    app.teamlogger_threshold: '%env(THRESHOLD)%'
    mailer_from_address: '%env(MAILER_FROM_ADDRESS)%'
    app.timezone: 'Asia/Kolkata'
    uploads_directory: '%kernel.project_dir%/public/uploads/documents'
services:
    App\Helper\TimeHelper: ~

    App\Twig\TimeExtension:
        arguments:
            $timeHelper: '@App\Helper\TimeHelper'
        tags: ['twig.extension']

    App\Twig\DateExtension:
        tags: [ 'twig.extension' ]

    App\Twig\GlobalEmployeeExtension:
        tags: [ 'twig.extension' ]

    App\Twig\ModalboxExtension:
            tags: [ 'twig.extension' ]

    App\Twig\DurationFormatterExtension:
            tags: [ 'twig.extension' ]

    App\Repository\FormTemplateRepository:
        autowire: true
        tags: [ 'doctrine.repository_service' ]

    App\Repository\FormSectionRepository:
        autowire: true
        tags: [ 'doctrine.repository_service' ]

    App\Repository\CustomResetPasswordRequestRepository:
        arguments:
            $adminRepository: '@App\Repository\ResetPasswordRequestRepository'
            $employeeRepository: '@App\Repository\ResetPasswordRequestEmployeeRepository'

    App\Service\Admin\LeaveBalanceManagerService:
        arguments:
            $entityManager: '@doctrine.orm.entity_manager'

    Knp\Snappy\Pdf:
        arguments:
            $binary: '/usr/bin/wkhtmltopdf'

    App\Form\DataTransformer\JsonToArrayTransformer: ~

    # default configuration for services in *this* file
    _defaults:
        autowire: true      # Automatically injects dependencies in your services.
        autoconfigure: true # Automatically registers your services as commands, event subscribers, etc.

    # makes classes in src/ available to be used as services
    # this creates a service per class whose id is the fully-qualified class name
    App\:
        resource: '../src/'
        exclude:
            - '../src/DependencyInjection/'
            - '../src/Entity/'
            - '../src/Kernel.php'

    # add more service definitions when explicit configuration is needed
    # please note that last definitions always *replace* previous ones
