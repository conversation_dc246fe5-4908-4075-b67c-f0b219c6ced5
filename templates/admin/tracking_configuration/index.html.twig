{% extends '/admin/dashboard/base.html.twig' %}

{% block title %}Tracking Configuration - Admin{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <style>
        .config-section {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .config-section h3 {
            color: #333;
            margin-bottom: 10px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        
        .config-section p {
            color: #666;
            margin-bottom: 20px;
        }
        
        .config-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px 0;
            border-bottom: 1px solid #eee;
        }
        
        .config-item:last-child {
            border-bottom: none;
        }
        
        .config-label {
            flex: 1;
            margin-right: 20px;
        }
        
        .config-label strong {
            display: block;
            color: #333;
            margin-bottom: 5px;
        }
        
        .config-label small {
            color: #666;
            font-size: 0.9em;
        }
        
        .config-input {
            flex: 0 0 200px;
        }
        
        .config-input input,
        .config-input select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .config-input input[type="checkbox"] {
            width: auto;
            transform: scale(1.2);
        }
        
        .btn-group {
            display: flex;
            gap: 10px;
            margin-top: 30px;
        }
        
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid transparent;
            border-radius: 4px;
        }
        
        .alert-success {
            color: #155724;
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        
        .alert-error {
            color: #721c24;
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        
        .page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px 0;
            margin-bottom: 30px;
        }
        
        .page-header h1 {
            margin: 0;
            font-size: 2.5rem;
        }
        
        .page-header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
    </style>
{% endblock %}

{% block body %}
<div class="page-header">
    <div class="container">
        <h1><i class="fas fa-cogs"></i> Tracking Configuration</h1>
        <p>Configure all employee tracking settings from this central admin panel</p>
    </div>
</div>

<div class="container">
    {% for message in app.flashes('success') %}
        <div class="alert alert-success">
            <i class="fas fa-check-circle"></i> {{ message }}
        </div>
    {% endfor %}

    {% for message in app.flashes('error') %}
        <div class="alert alert-error">
            <i class="fas fa-exclamation-circle"></i> {{ message }}
        </div>
    {% endfor %}

    <form method="POST" action="{{ path('admin_tracking_config_update') }}">
        {% for categoryKey, category in configs %}
            <div class="config-section">
                <h3>
                    <i class="fas fa-cogs"></i>
                    {{ category.title }}
                </h3>
                <p>{{ category.description }}</p>
                
                {% for configKey, config in category.configs %}
                    <div class="config-item">
                        <div class="config-label">
                            <strong>{{ configKey|replace({'_': ' '})|title }}</strong>
                            <small>{{ config.description }}</small>
                        </div>
                        <div class="config-input">
                            {% if configKey in ['idle_timeout_enabled', 'silent_mode_enabled', 'auto_start_tracking', 'screenshot_enabled', 'screenshot_burst_mode', 'screenshot_random_interval', 'webcam_enabled', 'webcam_verification_required', 'webcam_consent_required', 'app_tracking_enabled', 'track_window_titles', 'productivity_categorization', 'website_tracking_enabled', 'track_browser_tabs', 'website_categorization', 'productivity_analysis_enabled'] %}
                                <input type="checkbox"
                                       name="configs[{{ configKey }}]"
                                       value="1"
                                       {% if config.configValue == '1' %}checked{% endif %}>
                                <input type="hidden" name="configs[{{ configKey }}]" value="0">
                            {% elseif configKey == 'screenshot_quality' %}
                                <select name="configs[{{ configKey }}]">
                                    <option value="60" {% if config.configValue == '60' %}selected{% endif %}>Low (60%)</option>
                                    <option value="80" {% if config.configValue == '80' %}selected{% endif %}>Medium (80%)</option>
                                    <option value="90" {% if config.configValue == '90' %}selected{% endif %}>High (90%)</option>
                                    <option value="100" {% if config.configValue == '100' %}selected{% endif %}>Maximum (100%)</option>
                                </select>
                            {% else %}
                                <input type="number"
                                       name="configs[{{ configKey }}]"
                                       value="{{ config.configValue }}"
                                       min="1"
                                       max="86400">
                            {% endif %}
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% endfor %}
        
        <div class="btn-group">
            <button type="submit" class="btn btn-primary btn-lg">
                <i class="fas fa-save"></i> Save Configuration
            </button>
            
            <button type="button" class="btn btn-warning btn-lg" onclick="resetToDefaults()">
                <i class="fas fa-undo"></i> Reset to Defaults
            </button>
            
            <a href="{{ path('admin_dashboard') }}" class="btn btn-secondary btn-lg">
                <i class="fas fa-arrow-left"></i> Back to Dashboard
            </a>
        </div>
    </form>
</div>

<script>
function resetToDefaults() {
    if (confirm('Are you sure you want to reset all settings to their default values? This action cannot be undone.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{{ path('admin_tracking_config_reset_defaults') }}';
        
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '{{ csrf_token('reset_defaults') }}';
        form.appendChild(csrfToken);
        
        document.body.appendChild(form);
        form.submit();
    }
}

// Auto-save indication
document.addEventListener('DOMContentLoaded', function() {
    const inputs = document.querySelectorAll('input, select');
    inputs.forEach(input => {
        input.addEventListener('change', function() {
            this.style.borderColor = '#ffc107';
            setTimeout(() => {
                this.style.borderColor = '#ddd';
            }, 1000);
        });
    });
});
</script>
{% endblock %}
