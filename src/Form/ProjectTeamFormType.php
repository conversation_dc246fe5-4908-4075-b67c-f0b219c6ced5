<?php
namespace App\Form;

use App\Entity\MasterEmployee;
use App\Entity\ProjectTeam;
use Doctrine\ORM\EntityRepository;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\Length;
use Symfony\Component\Validator\Constraints\NotBlank;

class ProjectTeamFormType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $teamLeader = $options['team_leader'];
        $admin = $options['admin'];
        $projectId = $options['project_id'];
        $project = $options['project'];

        $builder
            ->add('teamMember', EntityType::class, [
                'class' => MasterEmployee::class,
                'choice_label' => 'name',
                'label' => 'Team Member',
                'autocomplete' => true,
                'required' => true,
                'constraints' => [
                    new NotBlank(['message' => 'Team member is required.']),
                ],
                'query_builder' => function (EntityRepository $er) use ($teamLeader, $admin, $projectId, $project) {
                    $qb = $er->createQueryBuilder('m')
                        ->where('m.isDelete = :is_delete')
                        ->setParameter('is_delete', false);
                    if ($project && $project->getProjectManager()) {
                        $qb->andWhere('m.id != :project_manager_id')
                            ->setParameter('project_manager_id', $project->getProjectManager()->getId());
                    }
                    if ($teamLeader !== null) {
                        $qb->innerJoin('m.reportsTo', 'rt')
                            ->andWhere('rt.teamLeader = :team_leader')
                            ->andWhere('m.id != :team_leader_id')
                            ->setParameter('team_leader', $teamLeader)
                            ->setParameter('team_leader_id', $teamLeader->getId());
                    }
                    $qb->andWhere('m.id NOT IN (
                        SELECT IDENTITY(pt.teamMember)
                        FROM App\Entity\ProjectTeam pt
                        WHERE pt.project = :project_id
                    )')
                        ->setParameter('project_id', $projectId);

                    return $qb->orderBy('m.name', 'ASC');
                },
                'attr' => [
                    'class' => 'form-control',
                    'data-validation' => 'required',
                ],
            ])
            ->add('role', TextType::class, [
                'label' => 'Role',
                'required' => false,
                'attr' => [
                    'placeholder' => 'Enter role (e.g., Developer, Designer)',
                    'class' => 'form-control',
                    'maxlength' => 100,
                    'data-validation' => 'max:100',
                ],
                'constraints' => [
                    new Length([
                        'max' => 100,
                        'maxMessage' => 'Role cannot be longer than {{ limit }} characters.',
                    ]),
                ],
            ]);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => ProjectTeam::class,
            'csrf_protection' => true,
            'csrf_field_name' => '_token',
            'csrf_token_id' => 'project_team_form',
            'project_id' => null,
            'team_leader' => null,
            'admin' => null,
            'project' => null,
        ]);
    }
}
