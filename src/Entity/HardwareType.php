<?php

namespace App\Entity;

use App\Repository\HardwareTypeRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: HardwareTypeRepository::class)]
class HardwareType
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    /** @phpstan-ignore-next-line */
    private ?int $id = null;

    #[ORM\Column(length: 255)]
    private ?string $name = null;

    #[ORM\Column(length: 3)]
    private ?string $dataStringSmall = null;

    #[ORM\Column(type: "text", length: 255, nullable: true)]
    private ?string $description = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(?string $name): void
    {
        $this->name = $name;
    }

    public function getDataStringSmall(): ?string
    {
        return $this->dataStringSmall;
    }

    public function setDataStringSmall(?string $dataStringSmall): void
    {
        $this->dataStringSmall = $dataStringSmall;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): void
    {
        $this->description = $description;
    }

    public function __toString(): string
    {
        return $this->name ?? '';
    }
}