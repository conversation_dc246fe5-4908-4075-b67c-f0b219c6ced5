<?php

namespace App\MessageHandler;

use App\Entity\Announcement;
use App\Entity\Department;
use App\Message\AnnouncementNotificationMessage;
use App\Repository\MasterEmployeeRepository;
use App\Service\Common\EmailService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Mailer\Exception\TransportExceptionInterface;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use App\Traits\ExceptionLoggerTrait;

#[AsMessageHandler]
class AnnouncementNotificationHandler
{
    use ExceptionLoggerTrait;

    public function __construct(
        private readonly MasterEmployeeRepository $employeeRepository,
        private readonly ParameterBagInterface $parameterBag,
        private readonly EntityManagerInterface $entityManager,
        private readonly EmailService  $emailService,
    ) {
        $this->initializeLogger($parameterBag, 'announcement_notifications_handler');
    }

    /**
     * @throws \Exception
     */
    public function __invoke(AnnouncementNotificationMessage $message): void
    {
        $announcement = $this->entityManager->getRepository(Announcement::class)
            ->find($message->getAnnouncementId());

        if (!$announcement) {
            $this->log('Announcement not found', ['announcement_id' => $message->getAnnouncementId()]);
            return;
        }

        if (!$announcement->getSendEmail()) {
            $this->log('Email sending not enabled for announcement', ['announcement_id' => $message->getAnnouncementId()]);
            return;
        }
        $this->log('Audience criteria', [
            'targetAllEmployees' => $announcement->isTargetAllEmployees(),
            'targetTeamLeadersOnly' => $announcement->isTargetTeamLeadersOnly(),
            'departmentCount' => $announcement->getDepartments()->count(),
        ]);

        $employees = [];
        $criteria = ['isDelete' => false];
        $departmentIds = [];

        if ($announcement->isTargetAllEmployees()) {
            $employees = $this->employeeRepository->findBy($criteria);
        } else {
            if ($announcement->isTargetTeamLeadersOnly()) {
                $criteria['teamLeader'] = true;
            }
            if (!$announcement->getDepartments()->isEmpty()) {
                $departmentIds = array_map(static fn(Department $dept) => $dept->getId(), $announcement->getDepartments()->toArray());
                $employees = $this->employeeRepository->findByDepartmentsAndTeamLeader(
                    $departmentIds,
                    $announcement->isTargetTeamLeadersOnly()
                );
            } elseif ($announcement->isTargetTeamLeadersOnly()) {
                $employees = $this->employeeRepository->findBy($criteria);
            } else {
                $this->log('No valid audience defined', ['announcement_id' => $message->getAnnouncementId()]);
                return;
            }
        }

        $uniqueEmployees = [];
        foreach ($employees as $employee) {
            $uniqueEmployees[$employee->getId()] = $employee;
        }
        $employees = array_values($uniqueEmployees);
        $this->log('Fetching employees with criteria', [
            'criteria' => $criteria,
            'departmentIds' => $departmentIds,
            'employeeCount' => count($employees),
        ]);

        if (empty($employees)) {
            $this->log('No employees found for announcement', [
                'announcement_id' => $message->getAnnouncementId(),
                'warning' => $announcement->isTargetTeamLeadersOnly() ? 'No team leaders found in selected departments' : 'No employees found',
            ]);
            return;
        }

        $placeholders = [
            '{{title}}' => $announcement->getTitle(),
            '{{content}}' => $announcement->getContent(),
            '{{start_date}}' => $announcement->getStartDate()->format('d-M-Y'),
            '{{end_date}}' => $announcement->getEndDate()->format('d-M-Y'),
            '{{app_base_url}}' => rtrim($this->parameterBag->get('app_base_url'), '/') ."/dashboard-employee",
        ];

        foreach ($employees as $employee) {
            $employeePlaceholders = array_merge($placeholders, [
                '{{employee_name}}' => $employee->getName(),
            ]);

            try {
                $emailContent = $this->emailService->generateEmailContent('announcement_notification', $employeePlaceholders);
                $this->emailService->sendEmail($employee->getEmail(), $announcement->getTitle(), $emailContent);
            } catch (\Exception $e) {
                $this->log('Failed to send email', [
                    'employee_id' => $employee->getId(),
                    'error' => $e->getMessage(),
                ]);
            } catch (TransportExceptionInterface $e) {
                $this->log('Failed to send email', [
                    'error' => $e->getMessage()
                ]);
            }
        }

        $announcement->setEmailSentAt(new \DateTime());
        $this->entityManager->flush();
    }
}