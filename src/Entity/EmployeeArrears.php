<?php

namespace App\Entity;

use App\Repository\EmployeeArrearsRepository;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity(repositoryClass: EmployeeArrearsRepository::class)]
#[ORM\Table(name: 'employee_arrears', uniqueConstraints: [
    new ORM\UniqueConstraint(name: 'arrears_unique', columns: ['employee_id', 'month', 'year'])
])]
#[ORM\HasLifecycleCallbacks]
class EmployeeArrears
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private ?int $id = null;

    #[ORM\ManyToOne(targetEntity: MasterEmployee::class)]
    #[ORM\JoinColumn(nullable: false)]
    private ?MasterEmployee $employee = null;

    #[ORM\Column(type: 'integer')]
    #[Assert\Range(min: 1, max: 12)]
    private int $month;

    #[ORM\Column(type: 'integer')]
    #[Assert\Range(min: 2024, max: 2100)]
    private int $year;

    #[ORM\Column(type: 'decimal', precision: 12, scale: 2)]
    #[Assert\GreaterThan(0)]
    private string $oldSalary;

    #[ORM\Column(type: 'decimal', precision: 12, scale: 2)]
    #[Assert\GreaterThan(0)]
    private string $newSalary;

    #[ORM\Column(type: 'float')]
    #[Assert\GreaterThan(0)]
    private float $arrearsDays;

    #[ORM\Column(type: 'decimal', precision: 12, scale: 2)]
    private string $totalArrears;

    #[ORM\Column(type: 'datetime')]
    private \DateTimeInterface $createdAt;

    #[ORM\Column(type: 'datetime')]
    private \DateTimeInterface $updatedAt;

    public function __construct()
    {
        $this->createdAt = new \DateTime();
        $this->updatedAt = new \DateTime();
    }

    #[ORM\PreUpdate]
    public function onPreUpdate(): void
    {
        $this->updatedAt = new \DateTime();
    }

    public function getId(): ?int { return $this->id; }

    public function getEmployee(): ?MasterEmployee { return $this->employee; }
    public function setEmployee(?MasterEmployee $employee): void { $this->employee = $employee; }

    public function getMonth(): int { return $this->month; }
    public function setMonth(int $month): void { $this->month = $month; }

    public function getYear(): int { return $this->year; }
    public function setYear(int $year): void { $this->year = $year; }

    public function getOldSalary(): string { return $this->oldSalary; }
    public function setOldSalary(string $oldSalary): void { $this->oldSalary = $oldSalary; }

    public function getNewSalary(): string { return $this->newSalary; }
    public function setNewSalary(string $newSalary): void { $this->newSalary = $newSalary; }

    public function getArrearsDays(): float
    {
        return $this->arrearsDays;
    }
    public function setArrearsDays(float $arrearsDays): void
    {
        $this->arrearsDays = $arrearsDays;
    }

    public function getTotalArrears(): string { return $this->totalArrears; }
    public function setTotalArrears(string $totalArrears): void { $this->totalArrears = $totalArrears; }

    public function getCreatedAt(): \DateTimeInterface { return $this->createdAt; }
    public function setCreatedAt(\DateTimeInterface $createdAt): void { $this->createdAt = $createdAt; }

    public function getUpdatedAt(): \DateTimeInterface { return $this->updatedAt; }
    public function setUpdatedAt(\DateTimeInterface $updatedAt): void { $this->updatedAt = $updatedAt; }
}