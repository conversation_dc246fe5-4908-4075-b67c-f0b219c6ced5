<?php

namespace App\Repository;

use App\Entity\LeaveBalance;
use App\Entity\MasterEmployee;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;
/**
 * @extends ServiceEntityRepository<LeaveBalance>
 */
class LeaveBalanceRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, LeaveBalance::class);
    }

    /**
     * Get employee names and their total leave days.
     *
     * @return array
     */
    public function getLeaveBalanceData(): array
    {
        return $this->getLeaveBalanceDataQueryBuilder()
            ->getQuery()
            ->getArrayResult();
    }

    /**
     * Get QueryBuilder for leave balance data.
     *
     * @return QueryBuilder
     */
    public function getLeaveBalanceDataQueryBuilder(): QueryBuilder
    {
        $subqb = $this->getEntityManager()->createQueryBuilder()
            ->select('MAX(lbSub.year)')
            ->from(LeaveBalance::class, 'lbSub');
        return $this->createQueryBuilder('lb')
            ->select('e.id AS id')
            ->addSelect('e.name AS employeeName')
            ->addSelect('COALESCE(SUM(lb.remainingDays), 0) AS totalLeaveDays')
            ->leftJoin('lb.employee', 'e')
            ->leftJoin('lb.leaveType', 'lt')
            ->where('lt.maxDays < 100')
            ->andWhere('lb.year = (' . $subqb->getDQL() . ')')
            ->andWhere('lt.isDelete = :isDelete')
            ->setParameter('isDelete', false)
            ->groupBy('e.id')
            ->addGroupBy('e.name')
            ->orderBy('e.name', 'ASC');
    }

    public function getMaxYearForEmployee(MasterEmployee $employee): ?int
    {
        return $this->createQueryBuilder('lb')
            ->select('MAX(lb.year)')
            ->where('lb.employee = :employee')
            ->setParameter('employee', $employee)
            ->getQuery()
            ->getSingleScalarResult();
    }

    /**
     * Get leave balances for the latest year for a specific employee with remaining days.
     *
     * @param MasterEmployee $employee
     * @return LeaveBalance[]
     */
    public function getCurrentYearLeaveBalancesWithRemainingDays(MasterEmployee $employee): array
    {
        return $this->createQueryBuilder('lb')
            ->leftJoin('lb.leaveType', 'lt')
            ->where('lb.employee = :employee')
            ->andWhere('lb.year = (
                SELECT MAX(lb2.year)
                FROM App\Entity\LeaveBalance lb2
                WHERE lb2.employee = :employee
            )')
            ->andWhere('lb.remainingDays > 0')
            ->andWhere('lt.isDelete = :isDelete')
            ->setParameter('employee', $employee)
            ->setParameter('isDelete', false)
            ->getQuery()
            ->getResult();
    }

    /**
     * Find leave balances for an employee and year with active leave types.
     *
     * @param MasterEmployee $employee
     * @param int $year
     * @return LeaveBalance[]
     */
    public function findByEmployeeAndYearActive(MasterEmployee $employee, int $year): array
    {
        return $this->createQueryBuilder('lb')
            ->leftJoin('lb.leaveType', 'lt')
            ->where('lb.employee = :employee')
            ->andWhere('lb.year = :year')
            ->andWhere('lt.isDelete = :isDelete')
            ->setParameter('employee', $employee)
            ->setParameter('year', $year)
            ->setParameter('isDelete', false)
            ->getQuery()
            ->getResult();
    }

    /**
     * Get leave balances for the latest year for a specific employee with remaining days.
     *
     * @param MasterEmployee $employee
     * @return LeaveBalance[]
     */
    public function getCurrentYearLeaveBalance(MasterEmployee $employee): array
    {
        return $this->createQueryBuilder('lb')
            ->leftJoin('lb.leaveType', 'lt')
            ->where('lb.employee = :employee')
            ->andWhere('lb.year = (
                SELECT MAX(lb2.year)
                FROM App\Entity\LeaveBalance lb2
                WHERE lb2.employee = :employee
            )')
            ->andWhere('lb.remainingDays < 100')
//            ->andWhere('lb.remainingDays > 0')
            ->andWhere('lt.isDelete = :isDelete')
            ->setParameter('employee', $employee)
            ->setParameter('isDelete', false)
            ->getQuery()
            ->getResult();
    }
}