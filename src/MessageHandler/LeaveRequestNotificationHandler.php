<?php
namespace App\MessageHandler;

use App\Entity\LeaveRequest;
use App\Message\LeaveRequestNotificationMessage;
use App\Repository\MasterEmployeeRepository;
use App\Repository\UserRepository;
use App\Service\Common\EmailService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Mailer\Exception\TransportExceptionInterface;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use App\Traits\ExceptionLoggerTrait;

#[AsMessageHandler]
class LeaveRequestNotificationHandler
{
    use ExceptionLoggerTrait;

    public function __construct(
        private readonly MasterEmployeeRepository $employeeRepository,
        private readonly ParameterBagInterface $parameterBag,
        private readonly EntityManagerInterface $entityManager,
        private readonly UserRepository $userRepository,
        private readonly EmailService $emailService,
    ) {
        $this->initializeLogger($parameterBag, 'leave_notifications_handler');
    }

    public function __invoke(LeaveRequestNotificationMessage $message): void
    {
        $employee = $this->employeeRepository->find($message->getEmployeeId());
        if (!$employee) {
            $this->log('Employee not found', ['employee_id' => $message->getEmployeeId()]);
            return;
        }

        $leaveRequest = $this->entityManager->getRepository(LeaveRequest::class)
            ->find($message->getLeaveRequestId());
        if (!$leaveRequest) {
            $this->log('Leave request not found', ['leave_request_id' => $message->getLeaveRequestId()]);
            return;
        }

        $teamLeader = $this->employeeRepository->findTeamLeaderForEmployee($employee);
        $hrStaff = $this->employeeRepository->findHrStaff();
        $admins = $this->userRepository->findAll();

        $recipients = array_merge(
            $admins ?: [],
            $hrStaff ?: [],
            $teamLeader ? [$teamLeader] : []
        );

        $placeholders = [
            '{{employee_name}}' => $employee->getName(),
            '{{start_date}}' => $leaveRequest->getStartDate()->format('d-m-Y'),
            '{{end_date}}' => $leaveRequest->getEndDate()->format('d-m-Y'),
            '{{days_requested}}' => $leaveRequest->getDaysRequested(),
            '{{leave_type}}' => $leaveRequest->getLeaveType()->getLeaveType()->getName(),
            '{{applied_on}}' => $leaveRequest->getAppliedOn()->format('d-m-Y'),
            '{{app_base_url}}' => rtrim($this->parameterBag->get('app_base_url'), '/') . "/admin/leave-requests",
        ];

        $emailContent = $this->emailService->generateEmailContent('leave_request_notification', $placeholders);

        foreach ($recipients as $recipient) {
            if ($recipient && $recipient->getEmail()) {
                try {
                    $this->emailService->sendEmail(
                        $recipient->getEmail(),
                        "New Leave Request from {$employee->getName()}",
                        $emailContent
                    );
                } catch (\Exception $e) {
                    $this->log('Failed to send email', [
                        'recipient' => $recipient->getEmail(),
                        'error' => $e->getMessage()
                    ]);
                } catch (TransportExceptionInterface $e) {
                    $this->log('Failed to send email', [
                        'recipient' => $recipient->getEmail(),
                        'error' => $e->getMessage()
                    ]);
                }
            }
        }
    }
}