<?php

namespace App\Controller\Admin;

use App\Entity\Department;
use App\Form\DepartmentType;
use App\Service\Admin\DepartmentService;
use App\Traits\ExceptionLoggerTrait;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Csrf\CsrfToken;
use Symfony\Component\Security\Csrf\CsrfTokenManagerInterface;

#[Route('/admin')]
class DepartmentController extends AbstractController
{
    use ExceptionLoggerTrait;

    public function __construct(
        ParameterBagInterface                   $parameterBag,
        private readonly EntityManagerInterface $entityManager,
        private readonly DepartmentService      $departmentService,
    )
    {
        $this->initializeLogger($parameterBag, 'department_controller');
    }

    /**
     * @param Request $request
     * @return Response
     */
    #[Route(path: '/department', name: 'department', methods: ['GET', 'POST'])]
    public function index(Request $request): Response
    {
        $table = $this->departmentService->createMasterDepartmentTable($request);

        if ($table->isCallback()) {
            return $table->getResponse();
        }

        return $this->render('admin/Department/index.html.twig', [
            'datatable' => $table,
        ]);
    }

    #[Route('/department/create', name: 'department_create', methods: ['GET', 'POST'])]
    public function createDepartment(Request $request): Response
    {
        $department = new Department();
        $form = $this->createForm(DepartmentType::class, $department);

        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $this->entityManager->persist($department);
            $this->entityManager->flush();

            $this->addFlash('success', 'Department created successfully.');
            return $this->redirectToRoute('department');
        }

        return $this->render('admin/Department/new.html.twig', [
            'form' => $form->createView(),
            'mode' => 'create',
        ]);
    }

    #[Route('/department/edit/{id}', name: 'department_edit', methods: ['GET', 'POST'])]
    public function editDepartment(
        Request                $request,
        Department             $department,
        EntityManagerInterface $entityManager
    ): Response
    {
        $form = $this->createForm(DepartmentType::class, $department);

        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $entityManager->flush();

            $this->addFlash('success', 'Department updated successfully.');
            return $this->redirectToRoute('department');
        }

        return $this->render('admin/Department/new.html.twig', [
            'form' => $form->createView(),
            'department' => $department,
            'mode' => 'edit',
        ]);
    }
    #[Route('/department/delete/{id}', name: 'department_delete', methods: ['POST'])]
    public function deleteDepartment(
        Request $request,
        Department $department,
        EntityManagerInterface $entityManager,
        CsrfTokenManagerInterface $csrfTokenManager
    ): Response {
        $token = $request->request->get('_token');

        if (!$csrfTokenManager->isTokenValid(new CsrfToken('delete' . $department->getId(), $token))) {
            return $this->json([
                'success' => false,
                'message' => 'Invalid CSRF token.'
            ], Response::HTTP_FORBIDDEN);
        }

        if ($department->hasEmployees()) {
            return $this->json([
                'success' => false,
                'message' => 'Cannot delete department because it has associated employees.'
            ], Response::HTTP_OK);
        }

        $department->setIsDelete(true);
        $entityManager->persist($department);
        $entityManager->flush();

        return $this->json([
            'success' => true,
            'message' => 'Department has been deleted.'
        ], Response::HTTP_OK);
    }

    #[Route('/department/view/{id}', name: 'department_view', methods: ['GET'])]
    public function show(Department $department): Response
    {
        return $this->render('admin/Department/new.html.twig', [
            'department' => $department,
            'mode' => 'view',
        ]);
    }
}