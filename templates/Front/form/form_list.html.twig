{% extends 'Front/dashboard/base.html.twig' %}

{% block title %}Form Templates{% endblock %}

{% block body %}
    {% include 'Front/partials/breadcrumbs.html.twig' with {
        title: 'Forms',
        breadcrumbs: [
            { 'label': 'Dashboard', 'url': path('dashboard_employee') },
        ],
        active: 'Performance Feedback'
    } %}

    <div class="content">
        <div class="animated fadeIn">
            <div class="row">
                <div class="col-md-12">
                    <div id="flash-container"></div>
                    <div class="card">
                        <div class="card-header d-flex justify-content-between">
                            <strong class="card-title">Performance Feedback</strong>
                        </div>
                        <div class="card-body">
                            {% if formTemplates is not empty %}
                                <table class="table">
                                    <thead>
                                    <tr class="text-center">
                                        <th class="serial">#</th>
                                        <th>Name</th>
                                        <th>Description</th>
                                        <th>Actions</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {% for template in formTemplates %}
                                        <tr class="text-center">
                                        <th class="serial">{{ loop.index }}</th>
                                            <td>{{ template.name }}</td>
                                            <td>{{ template.description }}</td>
                                            <td>
                                                <a href="{{ path('fill_form', { templateId: template.id }) }}" class="btn btn-primary" title="Fill"><i class="bs-fa fa fa-pencil"></i></a>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                    </tbody>
                                </table>
                            {% else %}
                                <p>No templates available.</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="clearfix"></div>
{% endblock %}
