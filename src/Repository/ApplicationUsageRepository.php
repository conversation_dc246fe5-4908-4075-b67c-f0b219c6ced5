<?php

namespace App\Repository;

use App\Entity\ApplicationUsage;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<ApplicationUsage>
 *
 * @method ApplicationUsage|null find($id, $lockMode = null, $lockVersion = null)
 * @method ApplicationUsage|null findOneBy(array $criteria, array $orderBy = null)
 * @method ApplicationUsage[]    findAll()
 * @method ApplicationUsage[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ApplicationUsageRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, ApplicationUsage::class);
    }

    /**
     * Get application usage for a specific employee and date range
     */
    public function findByEmployeeAndDateRange($employeeId, \DateTime $startDate, \DateTime $endDate): array
    {
        return $this->createQueryBuilder('au')
            ->where('au.employee = :employeeId')
            ->andWhere('au.usageDate BETWEEN :startDate AND :endDate')
            ->setParameter('employeeId', $employeeId)
            ->setParameter('startDate', $startDate->format('Y-m-d'))
            ->setParameter('endDate', $endDate->format('Y-m-d'))
            ->orderBy('au.usageDate', 'DESC')
            ->addOrderBy('au.startTime', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Get top applications by usage time
     */
    public function getTopApplicationsByUsage($employeeId, \DateTime $date, int $limit = 10): array
    {
        return $this->createQueryBuilder('au')
            ->select('au.applicationName, SUM(au.duration) as totalDuration, SUM(au.switchCount) as totalSwitches')
            ->where('au.employee = :employeeId')
            ->andWhere('au.usageDate = :date')
            ->andWhere('au.duration IS NOT NULL')
            ->setParameter('employeeId', $employeeId)
            ->setParameter('date', $date->format('Y-m-d'))
            ->groupBy('au.applicationName')
            ->orderBy('totalDuration', 'DESC')
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();
    }

    /**
     * Get productivity statistics
     */
    public function getProductivityStats($employeeId, \DateTime $date): array
    {
        $result = $this->createQueryBuilder('au')
            ->select('au.category, SUM(au.duration) as totalDuration, COUNT(au.id) as sessionCount')
            ->where('au.employee = :employeeId')
            ->andWhere('au.usageDate = :date')
            ->andWhere('au.duration IS NOT NULL')
            ->andWhere('au.category IS NOT NULL')
            ->setParameter('employeeId', $employeeId)
            ->setParameter('date', $date->format('Y-m-d'))
            ->groupBy('au.category')
            ->getQuery()
            ->getResult();

        $stats = [
            'productive' => ['duration' => 0, 'sessions' => 0],
            'neutral' => ['duration' => 0, 'sessions' => 0],
            'distracting' => ['duration' => 0, 'sessions' => 0]
        ];

        foreach ($result as $row) {
            $category = $row['category'];
            if (isset($stats[$category])) {
                $stats[$category]['duration'] = (int) $row['totalDuration'];
                $stats[$category]['sessions'] = (int) $row['sessionCount'];
            }
        }

        return $stats;
    }

    /**
     * Get unsynced application usage records
     */
    public function findUnsyncedRecords(): array
    {
        return $this->createQueryBuilder('au')
            ->where('au.isSynced = false')
            ->orderBy('au.startTime', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Mark records as synced
     */
    public function markAsSynced(array $recordIds): void
    {
        $this->createQueryBuilder('au')
            ->update()
            ->set('au.isSynced', 'true')
            ->where('au.id IN (:recordIds)')
            ->setParameter('recordIds', $recordIds)
            ->getQuery()
            ->execute();
    }
}
