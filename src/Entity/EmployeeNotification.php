<?php

namespace App\Entity;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Ramsey\Uuid\Uuid;

#[ORM\Entity]
class EmployeeNotification
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private ?int $id = null;

    #[ORM\Column(type: 'string', length: 36, unique: true)]
    private string $uuid;

    #[ORM\ManyToOne(targetEntity: Employee::class)]
    #[ORM\JoinColumn(nullable: false)]
    private ?Employee $employee = null;

    #[ORM\ManyToOne(targetEntity: EmployeeHours::class)]
    #[ORM\JoinColumn(nullable: false)]
    private ?EmployeeHours $employeeHours = null;

    #[ORM\Column(type: 'text', nullable: true)]
    private ?string $justification = null;

    #[ORM\Column(type: 'date')]
    private \DateTimeInterface $sentAt;

    #[ORM\Column(type: 'datetime', nullable: true)]
    private ?\DateTimeInterface $justifiedAt = null;

    #[ORM\Column(type: 'boolean')]
    private bool $status;

    #[ORM\Column(type: 'boolean', nullable: true)]
    private ?bool $isApproved = null;

    #[ORM\Column(type: 'text', nullable: true)]
    private ?string $disapprovalReason = null;

    #[ORM\OneToMany(targetEntity: Chat::class, mappedBy: 'employeeNotification', cascade: ['persist', 'remove'])]
    private Collection $chats;

    #[ORM\Column(type: 'datetime', nullable: true)]
    private ?\DateTimeInterface $completionDeadline = null;

    #[ORM\Column(type: 'integer', options: ['default' => 0])]
    private int $justificationCount = 0;

    public function __construct()
    {
        $this->uuid = Uuid::uuid4();
        $this->status = false;
        $this->sentAt = new \DateTime();
        $this->chats = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): void
    {
        $this->id = $id;
    }

    public function getEmployee(): ?Employee
    {
        return $this->employee;
    }

    public function setEmployee(?Employee $employee): void
    {
        $this->employee = $employee;
    }

    public function getEmployeeHours(): ?EmployeeHours
    {
        return $this->employeeHours;
    }

    public function setEmployeeHours(?EmployeeHours $employeeHours): void
    {
        $this->employeeHours = $employeeHours;
        if ($employeeHours !== null) {
            $reportDate = $employeeHours->getReportDate();
        }
    }

    public function getJustification(): string
    {
        return $this->justification ?? '';
    }

    public function setJustification(string $justification): void
    {
        $this->justification = $justification;
    }

    public function getSentAt(): \DateTimeInterface
    {
        return $this->sentAt;
    }

    public function setSentAt(\DateTimeInterface $sentAt): void
    {
        $this->sentAt = $sentAt;
    }

    public function getStatus(): bool
    {
        return $this->status;
    }

    public function setStatus(bool $status): void
    {
        $this->status = $status;
    }

    public function getJustifiedAt(): ?\DateTimeInterface
    {
        return $this->justifiedAt;
    }

    public function setJustifiedAt(?\DateTimeInterface $justifiedAt): void
    {
        $this->justifiedAt = $justifiedAt;
    }

    public function getUuid(): string
    {
        /** @phpstan-ignore-next-line */
        return $this->uuid instanceof Uuid ? $this->uuid->toString() : $this->uuid;
    }

    public function setUuid(string $uuid): self
    {
        $this->uuid = $uuid;
        return $this;
    }
    public function getIsApproved(): ?bool
    {
        return $this->isApproved;
    }

    public function setIsApproved(?bool $isApproved): void
    {
        $this->isApproved = $isApproved;
    }

    public function setDisapprovalReason(?string $disapprovalReason): void
    {
        $this->disapprovalReason = $disapprovalReason;


        if ($disapprovalReason !== null) {
            $existingDisapprovalChat = $this->chats->filter(function(Chat $chat) {
                return $chat->isDisapprovalReason() && $chat->getMessage() === $this->disapprovalReason;
            })->first();

            if (!$existingDisapprovalChat) {
                $chat = new Chat(
                    message: $disapprovalReason,
                    isFromEmployee: false,
                    employeeNotification: $this,
                    isDisapprovalReason: true
                );
                $this->chats->add($chat);
            }
        }
    }
    public function getDisapprovalReason(): ?string
    {
        return $this->disapprovalReason;
    }


    public function getAllDisapprovalReasons(): array
    {
        return $this->chats
            ->filter(fn (Chat $chat) => $chat->isDisapprovalReason())
            ->map(fn (Chat $chat) => $chat->getMessage())
            ->toArray();
    }

    public function getChats(): Collection
    {
        return $this->chats;
    }

    public function setChats(Collection $chats): void
    {
        $this->chats = $chats;
    }
    public function getCompletionDeadline(): ?\DateTimeInterface
    {
        return $this->completionDeadline;
    }

    public function setCompletionDeadline(?\DateTimeInterface $completionDeadline): void
    {
        $this->completionDeadline = $completionDeadline;
    }

    public function getJustificationCount(): int
    {
        return $this->justificationCount;
    }

    public function setJustificationCount(int $justificationCount): void
    {
        $this->justificationCount = $justificationCount;
    }
}