<?php
namespace App\Entity;

use SymfonyCasts\Bundle\ResetPassword\Model\ResetPasswordRequestInterface;
use Doctrine\ORM\Mapping as ORM;
use DateTimeInterface;
use App\Repository\ResetPasswordRequestEmployeeRepository;

#[ORM\Entity(repositoryClass: ResetPasswordRequestEmployeeRepository::class)]
class ResetPasswordRequestEmployee implements ResetPasswordRequestInterface
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: "integer")]
    /** @phpstan-ignore-next-line */
    private ?int $id = null;

    #[ORM\ManyToOne(targetEntity: MasterEmployee::class)]
    #[ORM\JoinColumn(nullable: false)]
    private MasterEmployee $user;

    #[ORM\Column(type: "datetime")]
    private DateTimeInterface $expiresAt;

    #[ORM\Column(type: "string", length: 100)]
    private string $selector;

    #[ORM\Column(type: "string", length: 255)]
    private string $hashedToken;

    #[ORM\Column(type: "datetime")]
    private DateTimeInterface $requestedAt; // <-- Add this field

    public function __construct(MasterEmployee $user, DateTimeInterface $expiresAt, string $selector, string $hashedToken)
    {
        $this->user = $user;
        $this->expiresAt = $expiresAt;
        $this->selector = $selector;
        $this->hashedToken = $hashedToken;
        $this->requestedAt = new \DateTime(); // Set current time
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getUser(): object
    {
        return $this->user;
    }

    public function getExpiresAt(): DateTimeInterface
    {
        return $this->expiresAt;
    }

    public function getSelector(): string
    {
        return $this->selector;
    }

    public function getHashedToken(): string
    {
        return $this->hashedToken;
    }

    public function getRequestedAt(): DateTimeInterface
    {
        return $this->requestedAt;
    }
    public function isExpired(): bool
    {
        return $this->expiresAt < new \DateTime();
    }

}
