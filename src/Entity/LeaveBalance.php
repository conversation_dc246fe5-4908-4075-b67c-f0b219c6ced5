<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;
use App\Repository\LeaveBalanceRepository;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity(repositoryClass: LeaveBalanceRepository::class)]
#[ORM\Table(name: "leave_balance")]
#[ORM\HasLifecycleCallbacks]
class LeaveBalance
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: "integer")]
    /** @phpstan-ignore-next-line */
    private ?int $id = null;

    #[ORM\ManyToOne(targetEntity: MasterEmployee::class)]
    #[ORM\JoinColumn(nullable: false, onDelete: "CASCADE")]
    private ?MasterEmployee $employee = null;

    #[ORM\ManyToOne(targetEntity: LeaveType::class)]
    #[ORM\JoinColumn(nullable: false, onDelete: "CASCADE")]
    private ?LeaveType $leaveType = null;

    #[ORM\Column(type: "integer")]
    #[Assert\NotBlank(message: "Year cannot be blank")]
    private int $year;

    #[ORM\Column(type: "float")]
    #[Assert\NotBlank(message: "Total days cannot be blank")]
    #[Assert\GreaterThanOrEqual(value: 0, message: "Total days cannot be negative")]
    private float $totalDays = 0.0;

    #[ORM\Column(type: "float", options: ["default" => 0])]
    #[Assert\GreaterThanOrEqual(value: 0, message: "Used days cannot be negative")]
    private float $usedDays = 0.0;

    #[ORM\Column(type: "float")]
    #[Assert\GreaterThanOrEqual(value: 0, message: "Remaining days cannot be negative")]
    private float $remainingDays = 0.0;

    #[ORM\Column(type: "float", options: ["default" => 0])]
    #[Assert\GreaterThanOrEqual(value: 0, message: "Penalty days cannot be negative")]
    private float $penaltyDays = 0.0;

    public function __construct()
    {
        $this->year = (int)date('Y');
        $this->totalDays = 0.0;
        $this->usedDays = 0.0;
        $this->remainingDays = 0.0;
        $this->penaltyDays = 0.0;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getEmployee(): ?MasterEmployee
    {
        return $this->employee;
    }

    public function setEmployee(?MasterEmployee $employee): self
    {
        $this->employee = $employee;
        return $this;
    }

    public function getLeaveType(): ?LeaveType
    {
        return $this->leaveType;
    }

    public function setLeaveType(?LeaveType $leaveType): self
    {
        $this->leaveType = $leaveType;
        return $this;
    }

    public function getYear(): int
    {
        return $this->year;
    }

    public function setYear(int $year): self
    {
        $this->year = $year;
        return $this;
    }

    public function getTotalDays(): float
    {
        return $this->totalDays;
    }

    public function setTotalDays(float $totalDays): self
    {
        $this->totalDays = $totalDays;
        $this->updateRemainingDays();
        return $this;
    }

    public function getUsedDays(): float
    {
        return $this->usedDays;
    }

    public function setUsedDays(float $usedDays): self
    {
        $this->usedDays = $usedDays;
        $this->updateRemainingDays();
        return $this;
    }

    public function getRemainingDays(): float
    {
        return $this->remainingDays;
    }

    public function setRemainingDays(float $remainingDays): self
    {
        $this->remainingDays = $remainingDays;
        return $this;
    }

    public function getPenaltyDays(): float
    {
        return $this->penaltyDays;
    }

    public function setPenaltyDays(float $penaltyDays): self
    {
        $this->penaltyDays = $penaltyDays;
        return $this;
    }

    public function updateRemainingDays(): void
    {
        $availableDays = $this->totalDays - $this->usedDays;
        $this->remainingDays = max(0, $availableDays);
    }

    #[ORM\PrePersist]
    #[ORM\PreUpdate]
    public function ensureRemainingDaysConsistency(): void
    {
        $this->updateRemainingDays();
    }
}