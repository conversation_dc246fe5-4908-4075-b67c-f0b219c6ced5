<?php

namespace App\Service\Admin;

use App\Entity\MasterEmployee;
use App\Entity\SalaryStructure;
use App\Helper\ModalboxHelper;
use Doctrine\ORM\QueryBuilder;
use Omines\DataTablesBundle\Adapter\Doctrine\ORMAdapter;
use Omines\DataTablesBundle\Column\DateTimeColumn;
use Omines\DataTablesBundle\Column\NumberColumn;
use Omines\DataTablesBundle\DataTable;
use Omines\DataTablesBundle\DataTableFactory;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Request;
use App\Traits\ExceptionLoggerTrait;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Security\Csrf\CsrfTokenManagerInterface;

class SalaryStructureService
{
    use ExceptionLoggerTrait;
    public function __construct(
        private readonly DataTableFactory      $dataTableFactory,
        private readonly ParameterBagInterface $parameterBag,
        private readonly RouterInterface $router,
        private readonly CsrfTokenManagerInterface $csrfTokenManager,
        private readonly ModalboxHelper $modalboxHelper,
        private readonly Security $security
    )
    {
        $this->initializeLogger($parameterBag, 'salary_structure_service');
    }

    public function createSalaryStructureTable(Request $request, ?MasterEmployee $employee = null): DataTable
    {
        $dataTable = $this->dataTableFactory->create();

        foreach ($this->getColumns() as $key => $column) {
            $dataTable->add($key, $column['className'], array_filter($column, fn($k) => $k !== 'className', ARRAY_FILTER_USE_KEY));
        }

        return $dataTable->createAdapter(ORMAdapter::class, [
            'entity' => SalaryStructure::class,
            'query' => fn(QueryBuilder $qb) => $this->buildQuery($qb, $employee),
        ])
            ->addOrderBy('startDate', 'desc')
            ->handleRequest($request);
    }

    private function getColumns(): array
    {
        return [
            'startDate' => [
                'label' => 'Start Date',
                'field' => 's.startDate',
                'format' => 'd-m-Y',
                'className' => DateTimeColumn::class,
                'searchable' => false,
            ],
            'endDate' => [
                'label' => 'End Date',
                'field' => 's.endDate',
                'format' => 'd-m-Y',
                'className' => DateTimeColumn::class,
                'nullValue' => '-',
                'searchable' => false,
            ],
            'ctc' => [
                'label' => 'Cost to Company (CTC)',
                'field' => 's.ctc',
                'className' => NumberColumn::class,
                'render' => fn($value) => number_format($value, 2),
            ],
            'id' => [
                'label' => 'Actions',
                'field' => 's.id',
                'className' => NumberColumn::class,
                'orderable' => false,
                'render' => fn($value, $context) => $this->renderActionColumn($context),
            ],
        ];
    }

    private function renderActionColumn($context): string
    {
        $salaryStructure = $context;
        $entityId = $salaryStructure->getId();
        $employeeId = $salaryStructure->getEmployee()->getId();
        $editUrl = $this->router->generate('admin_employee_salary_edit', ['employeeId' => $employeeId, 'id' => $entityId]);
        $deleteUrl = $this->router->generate('admin_employee_salary_delete', ['employeeId' => $employeeId, 'id' => $entityId]);
        $csrfToken = $this->csrfTokenManager->getToken('delete_salary_structure_' . $entityId)->getValue();

        $modalId = 'deleteModal_' . $entityId;
        $modalBody = 'Are you sure you want to delete the salary structure?<br><br>' .
        '<strong>Warning:</strong> This action cannot be undone.';

        $footerButtons = sprintf(
            '<button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>' .
            '<button type="button" class="btn btn-primary delete-salary-structure-btn" data-delete="true" data-url="%s" data-token="%s">Delete</button>',
            $deleteUrl,
            $csrfToken
        );

        $deleteModal = $this->modalboxHelper->renderModal(
            $modalId,
            'Confirm Salary Structure Deletion',
            $modalBody,
            $footerButtons
        );

        return sprintf(
            '<div class="d-inline-flex gap-2">
           <a href="%s" class="bs-btn btn btn-primary btn-sm bs-m" title="Edit">
                <i class="bs-fa fa fa-edit"></i>
            </a>
            <button type="button" class="bs-btn btn btn-primary btn-sm bs-m open-delete-salary-structure-modal" data-toggle="modal" data-target="#%s" title="Delete">
                <i class="bs-fa fa fa-trash"></i>
            </button>
        </div>%s',
            $editUrl,
            $modalId,
            $deleteModal
        );
    }

    private function buildQuery(QueryBuilder $qb, ?MasterEmployee $employee = null): void
    {
        $qb->select('s')
            ->from(SalaryStructure::class, 's');

        if ($employee) {
            $qb->andWhere('s.employee = :employee')
                ->setParameter('employee', $employee);
        }
    }
}
