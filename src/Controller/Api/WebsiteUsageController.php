<?php

namespace App\Controller\Api;

use App\Entity\WebsiteUsage;
use App\Repository\WebsiteUsageRepository;
use App\Repository\MasterEmployeeRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

#[Route('/api/website-usage', name: 'api_website_usage_')]
class WebsiteUsageController extends AbstractController
{
    public function __construct(
        private WebsiteUsageRepository $websiteUsageRepository,
        private MasterEmployeeRepository $employeeRepository,
        private EntityManagerInterface $entityManager
    ) {}

    #[Route('/sync', name: 'sync', methods: ['POST'])]
    public function syncWebsiteUsage(Request $request): JsonResponse
    {
        try {
            $data = json_decode($request->getContent(), true);
            
            if (!$data || !isset($data['websites'])) {
                return new JsonResponse([
                    'success' => false,
                    'error' => 'Invalid data format'
                ], 400);
            }

            $syncedCount = 0;
            $errors = [];

            foreach ($data['websites'] as $websiteData) {
                try {
                    // Validate required fields
                    if (!isset($websiteData['employee_id'], $websiteData['visit_date'], $websiteData['url'], $websiteData['domain'])) {
                        $errors[] = 'Missing required fields in website data';
                        continue;
                    }

                    // Find the employee
                    $employee = $this->employeeRepository->find($websiteData['employee_id']);
                    if (!$employee) {
                        $errors[] = "Employee not found: {$websiteData['employee_id']}";
                        continue;
                    }

                    // Create new WebsiteUsage entity
                    $websiteUsage = new WebsiteUsage();
                    $websiteUsage->setEmployee($employee);
                    $websiteUsage->setVisitDate(new \DateTime($websiteData['visit_date']));
                    $websiteUsage->setUrl($websiteData['url']);
                    $websiteUsage->setDomain($websiteData['domain']);
                    
                    if (isset($websiteData['page_title'])) {
                        $websiteUsage->setPageTitle($websiteData['page_title']);
                    }
                    
                    if (isset($websiteData['visit_time'])) {
                        $websiteUsage->setVisitTime(new \DateTime($websiteData['visit_time']));
                    }
                    
                    if (isset($websiteData['leave_time']) && $websiteData['leave_time']) {
                        $websiteUsage->setLeaveTime(new \DateTime($websiteData['leave_time']));
                    }
                    
                    if (isset($websiteData['duration'])) {
                        $websiteUsage->setDuration($websiteData['duration']);
                    }
                    
                    if (isset($websiteData['category'])) {
                        $websiteUsage->setCategory($websiteData['category']);
                    }
                    
                    if (isset($websiteData['productivity_level'])) {
                        $websiteUsage->setProductivityLevel($websiteData['productivity_level']);
                    }
                    
                    if (isset($websiteData['browser_name'])) {
                        $websiteUsage->setBrowserName($websiteData['browser_name']);
                    }
                    
                    if (isset($websiteData['visit_count'])) {
                        $websiteUsage->setVisitCount($websiteData['visit_count']);
                    }
                    
                    if (isset($websiteData['device_id'])) {
                        $websiteUsage->setDeviceId($websiteData['device_id']);
                    }
                    
                    if (isset($websiteData['metadata'])) {
                        // Handle metadata - convert JSON string to array if needed
                        $metadata = $websiteData['metadata'];
                        if (is_string($metadata)) {
                            $metadata = json_decode($metadata, true);
                        }
                        $websiteUsage->setMetadata($metadata);
                    }
                    
                    $websiteUsage->setIsSynced(true);
                    $websiteUsage->setCreatedAt(new \DateTime());
                    $websiteUsage->setUpdatedAt(new \DateTime());

                    // Persist to database
                    $this->entityManager->persist($websiteUsage);
                    $syncedCount++;
                    
                } catch (\Exception $e) {
                    $errors[] = $e->getMessage();
                }
            }

            // Flush all changes to database
            if ($syncedCount > 0) {
                $this->entityManager->flush();
            }

            return new JsonResponse([
                'success' => true,
                'data' => [
                    'synced_count' => $syncedCount,
                    'errors' => $errors
                ]
            ]);
            
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => 'Failed to sync website usage',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    #[Route('/stats', name: 'stats', methods: ['GET'])]
    public function getWebsiteStats(Request $request): JsonResponse
    {
        try {
            $employeeId = $request->query->get('employee_id');
            $date = $request->query->get('date', date('Y-m-d'));
            
            if (!$employeeId) {
                return new JsonResponse([
                    'success' => false,
                    'error' => 'Employee ID is required'
                ], 400);
            }

            // Get website usage stats for the day
            $stats = $this->websiteUsageRepository->getWebsiteStatsForDate($employeeId, $date);

            return new JsonResponse([
                'success' => true,
                'data' => $stats
            ]);
            
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => 'Failed to fetch website stats',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    #[Route('/productivity', name: 'productivity', methods: ['GET'])]
    public function getProductivityStats(Request $request): JsonResponse
    {
        try {
            $employeeId = $request->query->get('employee_id');
            $date = $request->query->get('date', date('Y-m-d'));
            
            if (!$employeeId) {
                return new JsonResponse([
                    'success' => false,
                    'error' => 'Employee ID is required'
                ], 400);
            }

            // Get productivity stats for the day
            $stats = $this->websiteUsageRepository->getProductivityStatsForDate($employeeId, $date);

            return new JsonResponse([
                'success' => true,
                'data' => $stats
            ]);
            
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => 'Failed to fetch productivity stats',
                'message' => $e->getMessage()
            ], 500);
        }
    }
}
