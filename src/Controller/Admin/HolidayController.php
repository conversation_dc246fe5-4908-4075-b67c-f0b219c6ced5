<?php

namespace App\Controller\Admin;

use App\Entity\Holiday;
use App\Form\HolidayType;
use App\Service\Admin\HolidayService;
use App\Traits\ExceptionLoggerTrait;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Csrf\CsrfToken;
use Symfony\Component\Security\Csrf\CsrfTokenManagerInterface;

#[Route('/admin')]
class HolidayController extends AbstractController
{
    use ExceptionLoggerTrait;

    public function __construct(
        ParameterBagInterface                       $parameterBag,
        private readonly EntityManagerInterface     $entityManager,
        private readonly HolidayService             $holidayService,
    )
    {
        $this->initializeLogger($parameterBag, 'Holiday_list_controller');
    }

    #[Route(path: '/holiday/list', name: 'holiday_list', methods: ['GET', 'POST'])]
    public function index(Request $request): Response
    {
        $table = $this->holidayService->createHolidayTable($request);

        if ($table->isCallback()) {
            return $table->getResponse();
        }

        return $this->render('admin/holiday/index.html.twig', [
            'datatable' => $table,
        ]);
    }

    #[Route('/holiday/create', name: 'holiday_create', methods: ['GET', 'POST'])]
    public function createHoliday(Request $request): Response
    {
        $holiday = new Holiday();
        $form = $this->createForm(HolidayType::class, $holiday);

        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $this->entityManager->persist($holiday);
            $this->entityManager->flush();

            $this->addFlash('success', 'Holiday created successfully.');
            return $this->redirectToRoute('holiday_list');
        }

        return $this->render('admin/holiday/new.html.twig', [
            'form' => $form->createView(),
            'mode' => 'create',
        ]);
    }

    #[Route('/holiday/edit', name: 'holiday_edit', methods: ['POST','GET'])]
    public function editHoliday(Request $request, CsrfTokenManagerInterface $csrfTokenManager): Response
    {
        // Get the id from the POST data
        $id = $request->request->get('id');

        if (!$id) {
            $this->addFlash('error', 'No holiday ID provided.');
            return $this->redirectToRoute('holiday_list');
        }

        $holiday = $this->entityManager->getRepository(Holiday::class)->find($id);

        if (!$holiday) {
            throw $this->createNotFoundException('Holiday not found');
        }

        $form = $this->createForm(HolidayType::class, $holiday);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $holiday->setUpdatedAt(new \DateTime());
            $this->entityManager->flush();

            $this->addFlash('success', 'Holiday updated successfully.');
            return $this->redirectToRoute('holiday_list');
        }

        return $this->render('admin/holiday/new.html.twig', [
            'form' => $form->createView(),
            'holiday' => $holiday,
            'mode' => 'edit',
        ]);
    }

    #[Route('/holiday/delete/{id}', name: 'holiday_delete', methods: ['POST'])]
    public function deleteHoliday(
        Request                   $request,
        CsrfTokenManagerInterface $csrfTokenManager,
        Holiday                   $holiday
    ): JsonResponse
    {
        $token = $request->request->get('_token');

        if (!$csrfTokenManager->isTokenValid(new CsrfToken('delete' . $holiday->getId(), $token))) {
            return new JsonResponse([
                'success' => false,
                'message' => 'Invalid CSRF token.'
            ], Response::HTTP_FORBIDDEN);
        }

        $this->entityManager->remove($holiday);
        $this->entityManager->flush();

        return new JsonResponse([
            'success' => true,
            'message' => 'Holiday has been deleted.'
        ], Response::HTTP_OK);
    }

    #[Route('/holiday/check-field', name: 'holiday_check_field', methods: ['POST'])]
    public function checkField(Request $request): JsonResponse
    {
        $field = $request->request->get('field');
        $value = $request->request->get('value');
        $currentId = $request->request->get('currentId');

        if ($field === 'date') {
            try {
                $value = new \DateTime($value);
            } catch (\Exception $e) {
                return new JsonResponse(['isUnique' => false, 'error' => 'Invalid date format'], 400);
            }
        }

        $criteria = [$field => $value];
        $holidayDate = $this->entityManager->getRepository(Holiday::class)->findOneBy($criteria);
        $isUnique = !$holidayDate || ($currentId && $holidayDate->getId() == $currentId);

        return new JsonResponse(['isUnique' => $isUnique]);
    }
}