<?php

namespace App\Controller\Front;

use App\Service\Common\FeedbackHistoryService;
use App\Traits\ExceptionLoggerTrait;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Symfony\Component\Routing\Annotation\Route;

class EmployeeFeedbackController extends AbstractController
{
    use ExceptionLoggerTrait;
    #[Route('/employee-feedback-history', name: 'employee_employee_history', methods: ['GET', 'POST'])]
    public function showHistory(Request $request, FeedbackHistoryService $feedbackService): Response
    {
        $teamLeader = $this->getUser();
        $employees = $feedbackService->getEmployeesReportingTo($teamLeader);

        $selectedEmployee = null;
        $selectedDate = date('Y-m');
        $groupedSubmissions = [];

        if ($request->isMethod('POST')) {
            $employeeId = $request->request->get('employee');
            $selectedDate = $request->request->get('date') ?: date('Y-m');

            if ($employeeId) {
                $selectedEmployee = $feedbackService->getSelectedEmployee($employeeId);

                if (!in_array($selectedEmployee, $employees, true)) {
                    throw new AccessDeniedHttpException('You are not authorized to view this employee\'s feedback.');
                }

                $groupedSubmissions = $feedbackService->getGroupedSubmissions($selectedEmployee, $selectedDate);
            } else {
                $this->addFlash('warning', 'Please select an employee to view feedback history.');
            }
        }

        $availableDates = $feedbackService->getAvailableDates();

        return $this->render('Front/FeedBackHistory/EmployeeFeedbackHistory.html.twig', [
            'groupedSubmissions' => $groupedSubmissions,
            'availableDates' => $availableDates,
            'selectedDate' => $selectedDate,
            'employees' => $employees,
            'selectedEmployee' => $selectedEmployee
        ]);
    }
}