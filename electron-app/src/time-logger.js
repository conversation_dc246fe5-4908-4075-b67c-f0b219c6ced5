class TimeLogger {
    constructor(dbService, store) {
        this.dbService = dbService;
        this.store = store;
        
        // Current session tracking
        this.currentSession = null;
        this.sessionStartTime = null;
        this.lastActivityTime = new Date();
        
        // Daily tracking
        this.dailyStats = {
            workTime: 0,
            breakTime: 0,
            idleTime: 0,
            totalSessions: 0,
            lastUpdated: new Date().toDateString()
        };
        
        // Configuration
        this.config = {
            minSessionDuration: 60, // Minimum 1 minute to count as a session
            maxSessionDuration: 4 * 60 * 60, // Maximum 4 hours per session
            autoBreakThreshold: 15 * 60, // Auto break after 15 minutes idle
            shortBreakDuration: 5 * 60, // 5 minute short breaks
            longBreakDuration: 15 * 60, // 15 minute long breaks
            workDayStart: '09:00',
            workDayEnd: '18:00',
            maxDailyHours: 8 * 60 * 60 // 8 hours max per day
        };
        
        this.init();
    }

    // Mark idle periods for exclusion from reports (but don't stop the session)
    async markIdlePeriod(startTime, endTime, reason = 'idle_detected') {
        try {
            if (!this.currentSession) {
                console.log('⚠️ No active session to mark idle period for');
                return;
            }

            const duration = Math.floor((endTime - startTime) / 1000);
            console.log(`⏸️ Marking idle period: ${duration}s (${reason})`);

            // Insert idle period record
            await this.dbService.run(`
                INSERT INTO idle_periods (
                    session_id, start_time, end_time, duration, reason, created_at
                ) VALUES (?, ?, ?, ?, ?, ?)
            `, [
                this.currentSession.id,
                startTime.toISOString(),
                endTime.toISOString(),
                duration,
                reason,
                new Date().toISOString()
            ]);

            // Update daily stats
            this.dailyStats.totalIdleTime += duration;
            await this.saveDailyStats();

            console.log(`✅ Idle period marked: ${duration}s will be excluded from reports`);

        } catch (error) {
            console.error('Failed to mark idle period:', error);
        }
    }

    // End session due to idle timeout (implements automatic session management)
    async endSessionDueToIdle(idleStartTime, reason = 'idle_timeout') {
        try {
            if (!this.currentSession) {
                console.log('⚠️ No active session to end due to idle');
                return null;
            }

            console.log(`⏹️ Ending session due to idle: ${reason}`);

            // End the session at the time when idle period started (not now)
            const sessionEndTime = idleStartTime;
            const duration = Math.floor((sessionEndTime - this.currentSession.startTime) / 1000);

            // Update the session in database with end time
            await this.dbService.run(`
                UPDATE time_logs
                SET end_time = ?, duration = ?, description = ?
                WHERE id = ?
            `, [
                sessionEndTime.toISOString(),
                duration,
                `${this.currentSession.description} (ended due to ${reason})`,
                this.currentSession.id
            ]);

            console.log(`✅ Session #${this.currentSession.id} ended at ${sessionEndTime.toISOString()}, duration: ${duration}s`);

            // Update daily stats
            this.updateDailyStats(this.currentSession.logType, duration);

            // Clear current session
            const endedSession = { ...this.currentSession, endTime: sessionEndTime, duration };
            this.currentSession = null;

            return endedSession;

        } catch (error) {
            console.error('Failed to end session due to idle:', error);
            return null;
        }
    }

    // Start new session after returning from idle
    async startSessionAfterIdle(reason = 'returned_from_idle') {
        try {
            console.log(`▶️ Starting new session after idle: ${reason}`);

            // Start a new work session
            await this.startSession('work', `New session after ${reason}`);

            return this.currentSession;

        } catch (error) {
            console.error('Failed to start session after idle:', error);
            return null;
        }
    }

    // Log activity without stopping session
    async logActivity(activityType, description = null) {
        try {
            if (!this.currentSession) {
                return;
            }

            await this.dbService.run(`
                INSERT INTO activity_logs (
                    session_id, activity_type, description, timestamp, created_at
                ) VALUES (?, ?, ?, ?, ?)
            `, [
                this.currentSession.id,
                activityType,
                description,
                new Date().toISOString(),
                new Date().toISOString()
            ]);

            console.log(`📝 Activity logged: ${activityType}`);

        } catch (error) {
            console.error('Failed to log activity:', error);
        }
    }

    // Calculate break time from gaps between sessions
    async calculateBreakTimeFromGaps(employeeId, date) {
        try {
            const sessions = await this.dbService.all(`
                SELECT start_time, end_time
                FROM time_logs
                WHERE employee_id = ? AND log_date = ? AND end_time IS NOT NULL
                ORDER BY start_time ASC
            `, [employeeId, date]);

            let totalBreakTime = 0;

            for (let i = 1; i < sessions.length; i++) {
                const previousSessionEnd = new Date(sessions[i-1].end_time);
                const currentSessionStart = new Date(sessions[i].start_time);

                // Calculate gap between sessions
                const gapDuration = Math.floor((currentSessionStart - previousSessionEnd) / 1000);

                // Only count gaps longer than 1 minute as breaks
                if (gapDuration > 60) {
                    totalBreakTime += gapDuration;
                    console.log(`📊 Break detected: ${gapDuration}s between ${previousSessionEnd.toISOString()} and ${currentSessionStart.toISOString()}`);
                }
            }

            return totalBreakTime;

        } catch (error) {
            console.error('Failed to calculate break time from gaps:', error);
            return 0;
        }
    }

    async init() {
        console.log('🕐 Initializing Time Logger...');
        
        // Load today's stats
        await this.loadDailyStats();
        
        // Setup database tables if needed
        await this.setupDatabase();
        
        console.log('✅ Time Logger initialized');
    }

    async setupDatabase() {
        try {
            // Ensure time_logs table exists with proper structure
            await this.dbService.run(`
                CREATE TABLE IF NOT EXISTS time_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    employee_id INTEGER NOT NULL,
                    log_date TEXT NOT NULL,
                    start_time TEXT NOT NULL,
                    end_time TEXT,
                    log_type TEXT NOT NULL,
                    duration INTEGER,
                    description TEXT,
                    device_id TEXT,
                    metadata TEXT,
                    is_synced INTEGER DEFAULT 0,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            `);

            // Create indexes for better performance
            await this.dbService.run(`
                CREATE INDEX IF NOT EXISTS idx_time_logs_employee_date 
                ON time_logs(employee_id, log_date)
            `);

            await this.dbService.run(`
                CREATE INDEX IF NOT EXISTS idx_time_logs_type
                ON time_logs(log_type)
            `);

            // Create idle_periods table for tracking idle time exclusions
            await this.dbService.run(`
                CREATE TABLE IF NOT EXISTS idle_periods (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_id INTEGER NOT NULL,
                    start_time TEXT NOT NULL,
                    end_time TEXT NOT NULL,
                    duration INTEGER NOT NULL,
                    reason TEXT,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (session_id) REFERENCES time_logs (id)
                )
            `);

            // Create activity_logs table for tracking user activities
            await this.dbService.run(`
                CREATE TABLE IF NOT EXISTS activity_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_id INTEGER NOT NULL,
                    activity_type TEXT NOT NULL,
                    description TEXT,
                    timestamp TEXT NOT NULL,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (session_id) REFERENCES time_logs (id)
                )
            `);

            console.log('📊 Time logging database setup complete');
        } catch (error) {
            console.error('Failed to setup time logging database:', error);
        }
    }

    async startSession(type = 'work', description = null) {
        try {
            const currentUser = await this.store.get('currentUser');
            if (!currentUser) {
                throw new Error('No current user found');
            }

            // Check if there's already an active session today
            const today = new Date().toISOString().split('T')[0];
            const existingSession = await this.dbService.get(`
                SELECT * FROM time_logs
                WHERE employee_id = ? AND log_date = ? AND end_time IS NULL
                ORDER BY id DESC LIMIT 1
            `, [currentUser.id, today]);

            if (existingSession) {
                // Resume existing session
                console.log(`🔄 Resuming existing session #${existingSession.id} from ${existingSession.start_time}`);
                this.currentSession = {
                    id: existingSession.id,
                    employeeId: existingSession.employee_id,
                    logDate: existingSession.log_date,
                    startTime: new Date(existingSession.start_time),
                    logType: existingSession.log_type,
                    description: existingSession.description,
                    deviceId: existingSession.device_id,
                    metadata: existingSession.metadata ? JSON.parse(existingSession.metadata) : {}
                };
                return this.currentSession;
            }

            // Create new session only if none exists today
            const now = new Date();
            const deviceId = await this.getDeviceId();

            this.currentSession = {
                id: null, // Will be set after database insert
                employeeId: currentUser.id,
                logDate: today,
                startTime: now,
                logType: type,
                description: description || 'Daily work session',
                deviceId: deviceId,
                metadata: {
                    platform: process.platform,
                    version: '1.0.0',
                    autoStarted: true,
                    sessionNumber: await this.getTodaySessionCount() + 1
                }
            };

            // Insert into database
            const result = await this.dbService.run(`
                INSERT INTO time_logs (
                    employee_id, log_date, start_time, log_type, 
                    description, device_id, metadata, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            `, [
                this.currentSession.employeeId,
                this.currentSession.logDate,
                this.currentSession.startTime.toISOString(),
                this.currentSession.logType,
                this.currentSession.description,
                this.currentSession.deviceId,
                JSON.stringify(this.currentSession.metadata),
                now.toISOString(),
                now.toISOString()
            ]);

            this.currentSession.id = result.lastID;
            this.sessionStartTime = now;
            this.lastActivityTime = now;

            console.log(`▶️ Started ${type} session #${this.currentSession.metadata.sessionNumber}`);

            // Update daily stats
            this.dailyStats.totalSessions++;

            // Recalculate break time from gaps when starting a new session
            const gapBreakTime = await this.calculateBreakTimeFromGaps(currentUser.id, today);
            this.dailyStats.breakTime = gapBreakTime; // Reset and recalculate
            console.log(`📊 Break time recalculated: ${gapBreakTime}s from session gaps`);

            await this.saveDailyStats();

            return this.currentSession;
        } catch (error) {
            console.error('Failed to start session:', error);
            throw error;
        }
    }

    async endSession(reason = 'manual') {
        if (!this.currentSession) {
            console.log('⚠️ No active session to end');
            return null;
        }

        try {
            const now = new Date();
            const duration = Math.floor((now - this.currentSession.startTime) / 1000);

            // Only save sessions longer than minimum duration
            if (duration >= this.config.minSessionDuration) {
                // Update session in database
                await this.dbService.run(`
                    UPDATE time_logs 
                    SET end_time = ?, duration = ?, updated_at = ?, metadata = ?
                    WHERE id = ?
                `, [
                    now.toISOString(),
                    duration,
                    now.toISOString(),
                    JSON.stringify({
                        ...this.currentSession.metadata,
                        endReason: reason,
                        actualDuration: duration
                    }),
                    this.currentSession.id
                ]);

                // Update daily stats
                await this.updateDailyStats(this.currentSession.logType, duration);

                console.log(`⏹️ Ended ${this.currentSession.logType} session: ${this.formatDuration(duration)} (${reason})`);
            } else {
                // Delete short sessions
                await this.dbService.run('DELETE FROM time_logs WHERE id = ?', [this.currentSession.id]);
                console.log(`🗑️ Deleted short session: ${duration}s < ${this.config.minSessionDuration}s`);
            }

            const endedSession = { ...this.currentSession, endTime: now, duration };
            this.currentSession = null;
            this.sessionStartTime = null;

            return endedSession;
        } catch (error) {
            console.error('Failed to end session:', error);
            throw error;
        }
    }

    async pauseSession(reason = 'manual') {
        if (!this.currentSession) return null;

        console.log(`⏸️ Pausing session: ${reason}`);

        // Record idle period start time
        this.currentSession.lastIdleStart = new Date();
        this.currentSession.isPaused = true;

        return this.currentSession;
    }

    async resumeSession(reason = 'manual') {
        if (!this.currentSession || !this.currentSession.isPaused) return null;

        console.log(`▶️ Resuming session: ${reason}`);

        // Calculate idle duration and record it
        if (this.currentSession.lastIdleStart) {
            const now = new Date();
            const idleDuration = Math.floor((now - this.currentSession.lastIdleStart) / 1000);

            // Record idle period in database
            await this.recordIdlePeriod(idleDuration, reason);

            this.currentSession.lastIdleStart = null;
        }

        this.currentSession.isPaused = false;

        return this.currentSession;
    }

    // Record idle period within a session (for internal tracking)
    async recordIdlePeriod(duration, reason) {
        try {
            if (!this.currentSession) return;

            await this.dbService.run(`
                INSERT INTO idle_periods (
                    session_id, start_time, end_time, duration, reason, created_at
                ) VALUES (?, ?, ?, ?, ?, ?)
            `, [
                this.currentSession.id,
                this.currentSession.lastIdleStart.toISOString(),
                new Date().toISOString(),
                duration,
                reason,
                new Date().toISOString()
            ]);

            console.log(`📝 Idle period recorded: ${duration}s (${reason})`);

        } catch (error) {
            console.error('Failed to record idle period:', error);
        }
    }

    async switchSessionType(newType, reason = 'manual') {
        if (!this.currentSession) {
            return await this.startSession(newType, reason);
        }

        if (this.currentSession.logType === newType) {
            console.log(`⚠️ Already in ${newType} session`);
            return this.currentSession;
        }

        console.log(`🔄 Switching from ${this.currentSession.logType} to ${newType}: ${reason}`);
        
        // End current session
        await this.endSession(`switched_to_${newType}`);
        
        // Start new session
        return await this.startSession(newType, reason);
    }

    async updateDailyStats(sessionType, duration) {
        try {
            // Reset stats if it's a new day
            const today = new Date().toDateString();
            if (this.dailyStats.lastUpdated !== today) {
                await this.loadDailyStats();
            }

            // Update stats based on session type
            switch (sessionType) {
                case 'work':
                    this.dailyStats.workTime += duration;
                    break;
                case 'break':
                    this.dailyStats.breakTime += duration;
                    break;
                case 'idle':
                    this.dailyStats.idleTime += duration;
                    break;
            }

            this.dailyStats.lastUpdated = today;
            await this.saveDailyStats();
        } catch (error) {
            console.error('Failed to update daily stats:', error);
        }
    }

    async loadDailyStats() {
        try {
            const currentUser = await this.store.get('currentUser');
            if (!currentUser) return;

            const today = new Date().toISOString().split('T')[0];

            // Get local stats including active sessions
            const localStats = await this.dbService.all(`
                SELECT
                    log_type,
                    SUM(
                        CASE
                            WHEN duration IS NOT NULL THEN duration
                            WHEN end_time IS NULL AND start_time IS NOT NULL THEN
                                CAST((julianday('now') - julianday(start_time)) * 86400 AS INTEGER)
                            ELSE 0
                        END
                    ) as total_duration,
                    COUNT(*) as session_count
                FROM time_logs
                WHERE employee_id = ? AND log_date = ?
                GROUP BY log_type
            `, [currentUser.id, today]);

            // Get server stats for synced data
            const serverStats = await this.getServerDailyStats(currentUser.id, today);

            // Reset daily stats
            this.dailyStats = {
                workTime: 0,
                breakTime: 0,
                idleTime: 0,
                totalSessions: 0,
                lastUpdated: new Date().toDateString()
            };

            // Combine local and server stats
            const combinedStats = this.combineStats(localStats, serverStats);

            combinedStats.forEach(stat => {
                switch (stat.log_type) {
                    case 'work':
                        this.dailyStats.workTime = stat.total_duration || 0;
                        // Only count work sessions for total session count
                        this.dailyStats.totalSessions += stat.session_count || 0;
                        break;
                    case 'break':
                        // Don't count break sessions - they shouldn't exist in new system
                        // this.dailyStats.breakTime = stat.total_duration || 0;
                        break;
                    case 'idle':
                        this.dailyStats.idleTime = stat.total_duration || 0;
                        break;
                }
            });

            // Calculate break time from session gaps
            const gapBreakTime = await this.calculateBreakTimeFromGaps(currentUser.id, today);
            this.dailyStats.breakTime += gapBreakTime;

            // Check for active session on server
            await this.checkForActiveServerSession(currentUser.id);

            console.log('📊 Daily stats loaded (local + server + gaps):', this.dailyStats);
        } catch (error) {
            console.error('Failed to load daily stats:', error);
        }
    }

    async getServerDailyStats(employeeId, date) {
        try {
            const authToken = await this.store.get('authToken');
            const response = await fetch(`http://127.0.0.1:8000/api/time-logs/daily-stats?employee_id=${employeeId}&date=${date}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    ...(authToken && { 'Authorization': `Bearer ${authToken}` })
                }
            });

            if (response.ok) {
                const result = await response.json();
                return result.success ? (result.data || []) : [];
            }
        } catch (error) {
            console.log('📡 Server stats not available, using local only');
        }
        return [];
    }

    async checkForActiveServerSession(employeeId) {
        try {
            const authToken = await this.store.get('authToken');
            const response = await fetch(`http://127.0.0.1:8000/api/time-logs/active-session?employee_id=${employeeId}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    ...(authToken && { 'Authorization': `Bearer ${authToken}` })
                }
            });

            if (response.ok) {
                const result = await response.json();
                if (result.success && result.data) {
                    // Restore active session from server
                    const serverSession = result.data;
                    this.currentSession = {
                        id: null, // Local ID will be different
                        employeeId: serverSession.employee_id,
                        logDate: serverSession.log_date,
                        startTime: new Date(serverSession.start_time),
                        logType: serverSession.log_type,
                        description: serverSession.description,
                        deviceId: serverSession.device_id,
                        metadata: serverSession.metadata ? JSON.parse(serverSession.metadata) : {}
                    };

                    this.sessionStartTime = this.currentSession.startTime;
                    console.log(`🔄 Restored active ${serverSession.log_type} session from server`);
                }
            }
        } catch (error) {
            console.log('📡 Could not check for active server session');
        }
    }

    combineStats(localStats, serverStats) {
        const combined = {};

        // Add local stats
        localStats.forEach(stat => {
            combined[stat.log_type] = {
                log_type: stat.log_type,
                total_duration: stat.total_duration || 0,
                session_count: stat.session_count || 0
            };
        });

        // Add server stats
        serverStats.forEach(stat => {
            if (combined[stat.log_type]) {
                combined[stat.log_type].total_duration += stat.total_duration || 0;
                combined[stat.log_type].session_count += stat.session_count || 0;
            } else {
                combined[stat.log_type] = {
                    log_type: stat.log_type,
                    total_duration: stat.total_duration || 0,
                    session_count: stat.session_count || 0
                };
            }
        });

        return Object.values(combined);
    }

    async saveDailyStats() {
        try {
            await this.store.set('dailyStats', this.dailyStats);
        } catch (error) {
            console.error('Failed to save daily stats:', error);
        }
    }

    async getTodaySessionCount() {
        try {
            const currentUser = await this.store.get('currentUser');
            if (!currentUser) return 0;

            const today = new Date().toISOString().split('T')[0];
            
            const result = await this.dbService.get(`
                SELECT COUNT(*) as count 
                FROM time_logs 
                WHERE employee_id = ? AND log_date = ?
            `, [currentUser.id, today]);

            return result ? result.count : 0;
        } catch (error) {
            console.error('Failed to get session count:', error);
            return 0;
        }
    }

    async getWeeklyStats() {
        try {
            const currentUser = await this.store.get('currentUser');
            if (!currentUser) return null;

            const today = new Date();
            const weekStart = new Date(today);
            weekStart.setDate(today.getDate() - today.getDay()); // Start of week (Sunday)
            
            const stats = await this.dbService.all(`
                SELECT 
                    log_date,
                    log_type,
                    SUM(COALESCE(duration, 0)) as total_duration,
                    COUNT(*) as session_count
                FROM time_logs 
                WHERE employee_id = ? 
                AND log_date >= ? 
                AND duration IS NOT NULL
                GROUP BY log_date, log_type
                ORDER BY log_date DESC
            `, [currentUser.id, weekStart.toISOString().split('T')[0]]);

            return this.organizeStatsByDay(stats);
        } catch (error) {
            console.error('Failed to get weekly stats:', error);
            return null;
        }
    }

    organizeStatsByDay(stats) {
        const organized = {};
        
        stats.forEach(stat => {
            if (!organized[stat.log_date]) {
                organized[stat.log_date] = {
                    work: 0,
                    break: 0,
                    idle: 0,
                    sessions: 0
                };
            }
            
            organized[stat.log_date][stat.log_type] = stat.total_duration;
            organized[stat.log_date].sessions += stat.session_count;
        });
        
        return organized;
    }

    // Utility methods
    getCurrentSession() {
        return this.currentSession;
    }

    getDailyStats() {
        return { ...this.dailyStats };
    }

    isWorkingHours() {
        const now = new Date();
        const currentTime = now.getHours() * 60 + now.getMinutes();
        const startTime = this.parseTime(this.config.workDayStart);
        const endTime = this.parseTime(this.config.workDayEnd);
        
        return currentTime >= startTime && currentTime <= endTime;
    }

    parseTime(timeString) {
        const [hours, minutes] = timeString.split(':').map(Number);
        return hours * 60 + minutes;
    }

    formatDuration(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;
        
        if (hours > 0) {
            return `${hours}h ${minutes}m ${secs}s`;
        } else if (minutes > 0) {
            return `${minutes}m ${secs}s`;
        } else {
            return `${secs}s`;
        }
    }

    async getDeviceId() {
        try {
            const { machineId } = require('node-machine-id');
            return await machineId();
        } catch (error) {
            return 'unknown-device';
        }
    }

    // Configuration methods
    updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
        console.log('⚙️ Time logger configuration updated');
    }

    getConfig() {
        return { ...this.config };
    }
}

module.exports = TimeLogger;
