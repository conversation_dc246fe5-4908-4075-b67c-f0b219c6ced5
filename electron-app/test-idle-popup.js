const { app, BrowserWindow } = require('electron');
const path = require('path');
const IdleWarningPopup = require('./src/idle-warning-popup');

// Test the idle warning popup
async function testIdlePopup() {
    console.log('🧪 Testing Idle Warning Popup...');
    
    const popup = new IdleWarningPopup();
    popup.setupIpcHandlers();
    
    // Show the popup for testing
    popup.show(
        5, // 5 minutes idle
        () => {
            console.log('✅ Timeout callback triggered');
            app.quit();
        },
        () => {
            console.log('✅ Resume callback triggered');
            app.quit();
        }
    );
    
    console.log('🚨 Idle warning popup should be visible now');
    console.log('💡 Click "I\'m Still Here" or wait for timeout to test');
}

app.whenReady().then(() => {
    testIdlePopup();
});

app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
        testIdlePopup();
    }
});
