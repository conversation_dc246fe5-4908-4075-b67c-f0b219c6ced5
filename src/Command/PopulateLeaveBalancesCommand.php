<?php

namespace App\Command;

use App\Service\Admin\LeaveBalanceManagerService;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Attribute\AsCommand;

#[AsCommand(name: 'app:populate-leave-balances', description: 'Populates leave balances for all employees and leave types.')]
class PopulateLeaveBalancesCommand extends Command
{
    public function __construct(
        private readonly LeaveBalanceManagerService $leaveBalanceManager
    ) {
        parent::__construct();
    }

    /**
     * @throws \DateMalformedStringException
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $createdCount = $this->leaveBalanceManager->populateAllLeaveBalances();
        $output->writeln(sprintf('Successfully populated %d leave balance records.', $createdCount));

        return Command::SUCCESS;
    }
}