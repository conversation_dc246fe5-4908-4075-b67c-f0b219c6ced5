<div class="content">
        <div class="animated fadeIn">
            <div class="row">
                <div class="col-lg-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <strong class="card-title">
                                {{ mode == 'view' ? 'View Task' : (mode == 'edit' ? 'Edit Task' : 'Create Task') }}
                            </strong>
                            <div class="d-flex align-items-center">
                                {% if is_granted('ROLE_ADMIN') %}
                                <a href="{{ path('project_task', { 'project_id': project.id }) }}" class="btn btn-sm btn-outline-secondary mr-2">
                                    <i class="fa fa-list mr-1"></i>Back to List
                                </a>
                                {% elseif is_granted('ROLE_EMPLOYEE') %}
                                <a href="{{ path('employee_project_task', { 'project_id': project.id }) }}" class="btn btn-sm btn-outline-secondary mr-2">
                                    <i class="fa fa-list mr-1"></i>Back to List
                                </a>
                                {% endif %}
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="task-form">
                                {% if form is defined %}
                                    {{ form_start(form, { 'attr': { 'class': 'needs-validation use-loader', 'novalidate': 'novalidate' } }) }}
                                    <div class="form-section">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.title, 'Task Title', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-tasks"></i></span>
                                                        </div>
                                                        {{ form_widget(form.title, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter task title', 'required': 'required' } }) }}
                                                    </div>
                                                    {{ form_errors(form.title) }}
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.estimatedHours, 'Estimated Hours', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-clock-o"></i></span>
                                                        </div>
                                                        {{ form_widget(form.estimatedHours, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter estimated hours' } }) }}
                                                    </div>
                                                    {{ form_errors(form.estimatedHours) }}
                                                    <small class="form-text text-muted">Enter the estimated hours for task completion</small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.priority, 'Priority', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-exclamation-circle"></i></span>
                                                        </div>
                                                        {{ form_widget(form.priority, { 'attr': { 'class': 'form-control', 'aria-label': 'Select Priority' } }) }}
                                                    </div>
                                                    {{ form_errors(form.priority) }}
                                                    <small class="form-text text-muted">Select the task priority (Low/Medium/High)</small>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.status, 'Status', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-toggle-on"></i></span>
                                                        </div>
                                                        {{ form_widget(form.status, { 'attr': { 'class': 'form-control', 'aria-label': 'Select Status' } }) }}
                                                    </div>
                                                    {{ form_errors(form.status) }}
                                                    <small class="form-text text-muted">Select the task status (Open/In Progress/Completed)</small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.assignedTo, 'Assigned To', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-user"></i></span>
                                                        </div>
                                                        {{ form_widget(form.assignedTo, { 'attr': { 'class': 'form-control', 'aria-label': 'Select Employee' } }) }}
                                                    </div>
                                                    {{ form_errors(form.assignedTo) }}
                                                    <small class="form-text text-muted">Select the employee assigned to this task</small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-12">
                                                    <div class="mb-3">
                                                        {{ form_label(form.description, 'Description', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                                        {{ form_widget(form.description, { 'attr': { 'class': 'form-control', 'required': 'required' } }) }}
                                                        {{ form_errors(form.description) }}
                                                        <small class="form-text text-muted">Provide a brief description of the task</small>
                                                    </div>
                                                </div>
                                        </div>
                                    </div>
                                    <div class="action-footer mt-4">
                                        {% if is_granted('ROLE_ADMIN') %}
                                        <a href="{{ path('project_task', { 'project_id': project.id }) }}" class="btn btn-secondary mr-2">
                                            Cancel
                                        </a>
                                        {% elseif is_granted('ROLE_EMPLOYEE') %}
                                        <a href="{{ path('employee_project_task', { 'project_id': project.id }) }}" class="btn btn-secondary mr-2">
                                            Cancel
                                        </a>
                                        {% endif %}
                                        <button type="submit" class="btn btn-primary pr-3 pl-3"  id="submitButton" data-update="{{ task.id is defined ? 'true' : 'false' }}">
                                            {{ task.id is defined ? 'Update Task' : 'Create Task' }}
                                        </button>
                                    </div>
                                    {{ form_end(form) }}
                                {% else %}
                                    <div class="form-section employee-details-section">
                                        <div class="details-section">
                                            <div class="details-container">
                                                <div class="detail-row">
                                                    <div class="detail-item col-md-4"><strong class="detail-label">Task Title:</strong></div>
                                                    <div class="detail-item col-md-8">{{ task.title ?: 'N/A' }}</div>
                                                </div>
                                                <div class="detail-row">
                                                    <div class="detail-item col-md-4"><strong class="detail-label">Priority:</strong></div>
                                                    <div class="detail-item col-md-8">{{ task.priority ?: 'N/A' }}</div>
                                                </div>
                                                <div class="detail-row">
                                                    <div class="detail-item col-md-4"><strong class="detail-label">Status:</strong></div>
                                                    <div class="detail-item col-md-8">{{ task.status ?: 'N/A' }}</div>
                                                </div>
                                                <div class="detail-row">
                                                    <div class="detail-item col-md-4"><strong class="detail-label">Assigned To:</strong></div>
                                                    <div class="detail-item col-md-8">{{ task.assignedTo ? task.assignedTo.name : 'N/A' }}</div>
                                                </div>
                                                <div class="detail-row">
                                                    <div class="detail-item col-md-4"><strong class="detail-label">Assigned By:</strong></div>
                                                    <div class="detail-item col-md-8">
                                                        {{ task.assignedByAdmin ? task.assignedByAdmin.username : (task.assignedBy ? task.assignedBy.name : 'N/A') }}
                                                    </div>
                                                </div>
                                                <div class="detail-row">
                                                    <div class="detail-item col-md-4"><strong class="detail-label">Estimated Hours:</strong></div>
                                                    <div class="detail-item col-md-8">
                                                        {% if task.estimatedHours is not null %}
                                                            {% set hours = task.estimatedHours|number_format(2, '.', '')|split('.') %}
                                                            {% set hr = hours[0]|number_format %}
                                                            {% set min = hours[1]|number_format %}
                                                            {% set formatted = '' %}
                                                            {% if hr|number_format > 0 %}
                                                                {% set formatted = hr ~ ' hr' %}
                                                            {% endif %}
                                                            {% if min|number_format > 0 %}
                                                                {% set formatted = formatted ~ (hr|number_format > 0 ? ' ' : '') ~ min ~ ' min' %}
                                                            {% endif %}
                                                            {{ formatted ?: '0 hr' }}
                                                        {% else %}
                                                            N/A
                                                        {% endif %}
                                                    </div>
                                                </div>
                                                <div class="detail-row">
                                                    <div class="detail-item col-md-4"><strong class="detail-label">Completion Hours:</strong></div>
                                                    <div class="detail-item col-md-8">{{ task.actualHours | format_duration }}</div>
                                                </div>
                                                <div class="detail-row">
                                                    <div class="detail-item col-md-4"><strong class="detail-label">Assigned At:</strong></div>
                                                    <div class="detail-item col-md-8">{{ task.assignedAt ? task.assignedAt|custom_date : 'N/A' }}</div>
                                                </div>
                                                <div class="detail-row">
                                                    <div class="detail-item col-md-4"><strong class="detail-label">Created At:</strong></div>
                                                    <div class="detail-item col-md-8">{{ task.createdAt ? task.createdAt|custom_date : 'N/A' }}</div>
                                                </div>
                                                <div class="detail-row">
                                                    <div class="detail-item col-md-4"><strong class="detail-label">Updated At:</strong></div>
                                                    <div class="detail-item col-md-8">{{ task.updatedAt ? task.updatedAt|custom_date : 'N/A' }}</div>
                                                </div>
                                            </div>
                                        </div>
                                        {% if mode == 'view' %}
                                            <!-- Time Logs Section -->
                                            <div class="mt-4">
                                                <h5 class="font-weight-bold m-2">Time Logs</h5>
                                                <table class="table table-bordered table-striped">
                                                    <thead>
                                                    <tr>
                                                        <th>Start Time</th>
                                                        <th>End Time</th>
                                                        <th>Duration (Minutes)</th>
                                                    </tr>
                                                    </thead>
                                                    <tbody>
                                                    {% if timeLogs is empty %}
                                                        <tr>
                                                            <td colspan="3">No time logs available</td>
                                                        </tr>
                                                    {% else %}
                                                        {% for log in timeLogs %}
                                                            <tr>
                                                                <td>{{ log.startTime ? log.startTime|custom_dateTime : 'N/A' }}</td>
                                                                <td>{{ log.endTime ? log.endTime|custom_dateTime : 'N/A' }}</td>
                                                                <td>
                                                                    {% if log.duration is not null %}
                                                                        {{ (log.duration * 60)|number_format(1) }}
                                                                    {% else %}
                                                                        N/A
                                                                    {% endif %}
                                                                </td>
                                                            </tr>
                                                        {% endfor %}
                                                    {% endif %}
                                                    </tbody>
                                                    <tfoot>
                                                    <tr>
                                                        <td colspan="2"><strong>Total:</strong></td>
                                                        <td>
                                                            <strong>{{ task.actualHours | format_duration}}</strong>
                                                        </td>
                                                    </tr>
                                                    </tfoot>
                                                </table>
                                            </div>
                                            <!-- Status Logs Section -->
                                            <div class="mt-4">
                                                <h5 class="font-weight-bold m-2">Status Change History</h5>
                                                <table class="table table-bordered table-striped">
                                                    <thead>
                                                    <tr>
                                                        <th>Changed By</th>
                                                        <th>Old Status</th>
                                                        <th>New Status</th>
                                                        <th>Changed At</th>
                                                    </tr>
                                                    </thead>
                                                    <tbody>
                                                    {% if statusHistories is empty %}
                                                        <tr>
                                                            <td colspan="4">No status history available</td>
                                                        </tr>
                                                    {% else %}
                                                        {% for history in statusHistories %}
                                                            <tr>
                                                                <td>{{ history.changedByAdmin ? history.changedByAdmin.username : (history.changedBy ? history.changedBy.name : 'Unknown') }}</td>
                                                                <td>{{ history.oldStatus }}</td>
                                                                <td>{{ history.newStatus }}</td>
                                                                <td>{{ history.changedAt ? history.changedAt|custom_dateTime : 'N/A' }}</td>
                                                            </tr>
                                                        {% endfor %}
                                                    {% endif %}
                                                    </tbody>
                                                </table>
                                            </div>
                                        {% endif %}
                                        <div class="action-footer mt-4">
                                            {% if is_granted('ROLE_ADMIN') %}
                                            <a href="{{ path('project_task', { 'project_id': project.id }) }}" class="btn btn-secondary mr-2">
                                                Back to List
                                            </a>
                                            <a href="{{ path('task_edit', { 'id': task.id, 'project_id': project.id }) }}" class="btn btn-primary pr-3 pl-3" style="padding-top: 10px;">
                                                Edit Task
                                            </a>
                                            {% elseif is_granted('ROLE_EMPLOYEE') %}
                                            <a href="{{ path('employee_project_task', { 'project_id': project.id }) }}" class="btn btn-secondary mr-2">
                                                Back to List
                                            </a>
                                            <a href="{{ path('employee_task_edit', { 'id': task.id, 'project_id': project.id }) }}" class="btn btn-primary pr-3 pl-3" style="padding-top: 10px;">
                                                Edit Task
                                            </a>
                                            {% endif %}
                                        </div>
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="clearfix"></div>
    <link rel="stylesheet" href="{{ asset('assets/css/Error/error.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/AdminForm/Form.css') }}">
    <script src="{{ asset('assets/js/admin/task.js') }}"></script>