<?php

namespace App\Service\Admin;

use App\Repository\AttendanceRepository;
use App\Repository\HolidayRepository;
use App\Repository\MasterEmployeeRepository;

readonly class AttendanceAdminService
{
    public function __construct(
        private AttendanceRepository $attendanceRepository,
        private MasterEmployeeRepository $employeeRepository,
        private HolidayRepository       $holidayRepository,
    ) {
    }

    public function getAttendanceCalendarData(?\DateTime $startDate = null, ?\DateTime $endDate = null): array
    {
        if (!$startDate) {
            $startDate = new \DateTime('first day of this month 00:00:00');
        }
        if (!$endDate) {
            $endDate = new \DateTime('last day of this month 23:59:59');
        }

        $today = new \DateTime('today');
        $events = [];

        $employees = $this->employeeRepository->findBy(['isDelete' => false]);
        $attendanceByDate = [];
        $holidayDates = [];
        $holidays = $this->holidayRepository->findByDateRange($startDate, $endDate);
        foreach ($holidays as $holiday) {
            $dateStr = $holiday->getDate()->format('Y-m-d');
            $holidayDates[] = $dateStr;

            $events[] = [
                'id' => 'holiday_' . $holiday->getId(),
                'title' => $holiday->getName(),
                'start' => $dateStr,
                'color' => '#f78739',
                'extendedProps' => [
                    'isHoliday' => true,
                    'description' => $holiday->getDescription(),
                    'attendanceCount' => 0,
                    'status' => 'Holiday',
                    'hoursWorked' => 0,
                    'hasBreaks' => false
                ]
            ];
        }

        foreach ($employees as $employee) {
            $attendanceRecords = $this->attendanceRepository->findByEmployeeAndDateRange($employee, $startDate, $endDate);
            foreach ($attendanceRecords as $record) {
                $status = strtolower($record->getStatus());
                if ($status === 'office in') {
                    $date = $record->getTimestamp()->format('Y-m-d');
                    if (!in_array($employee->getId(), $attendanceByDate[$date]['present'] ?? [], true)) {
                        $attendanceByDate[$date]['present'][] = $employee->getId();
                    }
                }
            }
        }

        $currentDate = clone $startDate;

        while ($currentDate <= $endDate) {
            if ($currentDate > $today) {
                break;
            }
            $dateStr = $currentDate->format('Y-m-d');

            if (in_array($dateStr, $holidayDates, true)) {
                $currentDate->add(new \DateInterval('P1D'));
                continue;
            }

            $dayOfWeek = (int) $currentDate->format('N');
            $hasAttendanceData = isset($attendanceByDate[$dateStr]);

            if ($dayOfWeek <= 5 || $hasAttendanceData) {
                $presentList = $attendanceByDate[$dateStr]['present'] ?? [];
                $presentCount = count($presentList);
                $absentCount = count($employees) - $presentCount;

                if ($presentCount > 0) {
                    $events[] = [
                        'id' => $dateStr . '-present',
                        'title' => $presentCount . ' P',
                        'start' => $dateStr,
                        'color' => '#28a745',
                        'extendedProps' => [
                            'isHoliday' => false,
                            'attendanceCount' => $presentCount + $absentCount
                        ]
                    ];
                }

                if ($absentCount > 0) {
                    $events[] = [
                        'id' => $dateStr . '-absent',
                        'title' => $absentCount . ' A',
                        'start' => $dateStr,
                        'color' => '#dc3545',
                        'extendedProps' => [
                            'isHoliday' => false,
                            'attendanceCount' => $presentCount + $absentCount
                        ]
                    ];
                }
            }

            $currentDate->add(new \DateInterval('P1D'));
        }

        return ['events' => $events];
    }

    public function getDateAttendanceDetails(string $date): array
    {
        $dateStart = new \DateTime($date . ' 00:00:00');
        $dateEnd = new \DateTime($date . ' 23:59:59');

        $employees = $this->employeeRepository->findBy(['isDelete' => false]);
        $presentEmployees = [];
        $absentEmployees = [];

        foreach ($employees as $employee) {
            $attendanceRecords = $this->attendanceRepository->findAttendanceByEmployeeAndDateRange($employee, $dateStart, $dateEnd);

            if (!empty($attendanceRecords)) {
                $presentEmployees[] = ['name' => $employee->getName()];
            } else {
                $absentEmployees[] = ['name' => $employee->getName()];
            }
        }

        $presentCount = count($presentEmployees);
        $absentCount = count($absentEmployees);

        return [
            'date' => $date,
            'presentCount' => $presentCount,
            'absentCount' => $absentCount,
            'presentEmployees' => $presentEmployees,
            'absentEmployees' => $absentEmployees,
        ];
    }
}