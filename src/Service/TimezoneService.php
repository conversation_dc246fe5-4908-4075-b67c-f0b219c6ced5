<?php

namespace App\Service;

use DateTime;
use DateTimeInterface;
use DateTimeZone;

class TimezoneService
{
    private DateTimeZone $targetTimezone;

    public function __construct(string $timezone = 'Asia/Kolkata')
    {
        $this->targetTimezone = new DateTimeZone($timezone);
    }

    public function convertToLocalTime(?DateTimeInterface $dateTime): ?DateTime
    {
        if ($dateTime === null) {
            return null;
        }

        $datetime = DateTime::createFromInterface($dateTime);
        $datetime->setTimezone($this->targetTimezone);
        return $datetime;
    }

    public function getCurrentDateTime(): DateTime
    {
        return new DateTime('now', $this->targetTimezone);
    }

    public function formatDateTime(?DateTimeInterface $dateTime, string $format = 'Y-m-d H:i:s'): ?string
    {
        if ($dateTime === null) {
            return null;
        }

        return $this->convertToLocalTime($dateTime)->format($format);
    }
}