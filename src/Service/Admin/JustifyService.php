<?php

namespace App\Service\Admin;

use App\Entity\EmployeeNotification;
use Doctrine\ORM\QueryBuilder;
use Omines\DataTablesBundle\Adapter\Doctrine\ORMAdapter;
use Omines\DataTablesBundle\Column\BoolColumn;
use Omines\DataTablesBundle\Column\DateTimeColumn;
use Omines\DataTablesBundle\Column\TextColumn;
use Omines\DataTablesBundle\DataTable;
use Omines\DataTablesBundle\DataTableFactory;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Routing\RouterInterface;
use App\Traits\ExceptionLoggerTrait;
use App\Helper\TimeHelper;
use App\Helper\SettingHelper;
use App\Helper\ModalboxHelper;

class JustifyService
{
    use ExceptionLoggerTrait;

    private RouterInterface $router;

    public function __construct(
        private readonly DataTableFactory $dataTableFactory,
        ParameterBagInterface $parameterBag,
        private readonly RequestStack $requestStack,
        private readonly TimeHelper $timeHelper,
        private readonly SettingHelper $settingHelper,
        RouterInterface $router
    )
    {
        $this->router = $router;
        $this->initializeLogger($parameterBag, 'employee_notification_table_service');
    }

    public function createEmployeeNotificationTable(Request $request): DataTable
    {
        $dataTable = $this->dataTableFactory->create();

        foreach ($this->getColumns() as $key => $column) {
            $dataTable->add($key, $column['className'], array_filter($column, fn($k) => $k !== 'className'));
        }

        return $dataTable->createAdapter(ORMAdapter::class, [
            'entity' => EmployeeNotification::class,
            'query' => fn(QueryBuilder $qb) => $this->buildQuery($qb),
        ])->handleRequest($request);
    }

    private function getColumns(): array
    {
        return [
            'employee' => [
                'label' => 'Employee',
                'field' => 'em.name',
                'orderable' => true,
                'className' => TextColumn::class,
            ],
            'employeeHours' => [
                'label' => 'Log Hours',
                'field' => 'eh.totalHours',
                'className' => TextColumn::class,
                'render' => fn($value) => $this->timeHelper->convertDecimalToTime($value),
            ],
            'reportDate' => [
                'label' => 'Log Date',
                'field' => 'eh.reportDate',
                'className' => DateTimeColumn::class,
                'format' => 'd-m-Y',
                'searchable' => false,
            ],
            'sentAt' => [
                'label' => 'Sent ON',
                'field' => 'e.sentAt',
                'className' => DateTimeColumn::class,
                'format' => 'd-m-Y',
                'searchable' => false,
            ],
//            'status' => [
//                'label' => 'Status',
//                'field' => 'e.status',
//                'className' => BoolColumn::class,
//                'trueValue' => 'Justified',
//                'falseValue' => 'Pending',
//            ],
            'justifiedAt' => [
                'label' => 'Reviewed On',
                'field' => 'e.justifiedAt',
                'className' => DateTimeColumn::class,
                'format' => 'd-m-Y',
                'searchable' => false,
            ],
            'justification' => [
                'label' => 'Justification',
                'field' => 'e.justification',
                'orderable' => false,
                'className' => TextColumn::class,
                'render' => fn($value, $context) => $this->renderJustification($value, $context),
            ],
            'isApproved' => [
                'label' => 'Action',
                'field' => 'e.isApproved',
                'orderable' => false,
                'className' => TextColumn::class,
                'render' => fn($value, $context) => $this->renderActionColumn($value, $context),
            ],
        ];
    }

    private function renderJustification($value, $context): string
    {
        if (empty($value)) return '';

        $escapedValue = $value;
        $entityId = $context->getId();

        $viewButton = sprintf(
            '<button type="button" class="bs-btn-main btn btn-primary btn-sm" title="Justification" data-toggle="modal" data-target="#justificationModal%s">
                <i class="bs-fa-main fa fa-file-text"></i>
            </button>',
            $entityId
        );

        $modalBody = sprintf('<p style="color: #0b2e13; font-weight: bold;">%s</p>', $escapedValue);
        $footerButtons = '<button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>';

        $modalHtml = ModalBoxHelper::renderModal(
            "justificationModal{$entityId}",
            'Justification Details',
            $modalBody,
            $footerButtons
        );

        return $viewButton . $modalHtml;
    }

    private function renderActionColumn($value, $context): string
    {
        $justification = $context->getJustification();
        if (empty($justification)) return '';

        $entityId = $context->getId();
        $justificationCount = $context->getJustificationCount();
        $remainingJustification = (int)$this->settingHelper->getSettingConfiguration()['remain'];

        $unreadCount = $context->getChats()->filter(function($chat) {
            return $chat->isFromEmployee() && $chat->isUnread();
        })->count();

        // Generate the View Chat Button
        $viewChatsButton = sprintf(
            '<div class="position-relative d-inline-block">
                <a href="%s" class="bs-btn-main btn btn-primary btn-sm bs-m" title="Message">
                    <i class="bs-fa-main fa fa-comment"></i>
                </a>%s
            </div>',
            $this->router->generate('admin_notification_chats', ['id' => $entityId]),
            $unreadCount > 0 ? sprintf(
                '<span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger bs-message-count">%d</span>',
                $unreadCount
            ) : ''
        );

        // Handle Disapproved - Limit Exceeded Case
        if ($justificationCount === $remainingJustification && !$value) {
            return '<span class="text-danger fa-lg m-2 " title="Disapproved - Limit Exceeded"><i class="fa fa-times"></i></span>' .$viewChatsButton;
        }

        // Handle Approved Case
        if ($value) {
            return '<span class="text-success fa-lg m-2" title="Approved"><i class="fa fa-check"></i></span>' .$viewChatsButton;
        }

        $escapedEntityId = htmlspecialchars($entityId, ENT_QUOTES, 'UTF-8');
        $defaultReason = $this->settingHelper->getSettingConfiguration()['reason'];

        $approveButton = sprintf(
            '<button id="approveButton%d" type="button" class="bs-btn-main btn btn-success btn-sm bs-m" title="Approve" onclick="handleApproval(%d)">
                <i class="bs-fa-main fa fa-check"></i>
            </button>',
            $entityId,
            $entityId
        );

        $disapproveButton = sprintf(
            '<button id="disapproveButton%d" type="button" class="bs-btn-main btn btn-danger btn-sm bs-m" title="Disapprove" data-toggle="modal" data-target="#disapproveModal%s">
                <i class="bs-fa-main fa fa-times"></i>
            </button>',
            $entityId,
            $escapedEntityId
        );

        $reasonOptions = $this->generateReasonOptions($defaultReason);
        $modalBody = $this->generateModalBody($escapedEntityId, $reasonOptions, $remainingJustification);
        $footerButtons = sprintf(
            '<button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
             <button type="button" class="btn btn-primary" onclick="submitDisapproval(%d)">Submit</button>',
            $entityId
        );

        $modalHtml = ModalBoxHelper::renderModal(
            "disapproveModal{$escapedEntityId}",
            'Disapproval Reason',
            $modalBody,
            $footerButtons
        );

        return $approveButton . $disapproveButton . $viewChatsButton . $modalHtml;
    }

    private function generateReasonOptions(?string $defaultReason): string
    {
        $reasonOptions = '<option value="">Select a reason</option>';
        if (!empty($defaultReason)) {
            $escapedDefaultReason = htmlspecialchars($defaultReason, ENT_QUOTES, 'UTF-8');
            $reasonOptions .= sprintf('<option value="%s">%s</option>', $escapedDefaultReason, $escapedDefaultReason);
        }
        $reasonOptions .= '<option value="other">Other</option>';
        return $reasonOptions;
    }

    private function generateModalBody(string $entityId, string $reasonOptions, int $remainingJustification): string
    {
        return sprintf('
            <div class="form-group">
                <select class="form-control" id="reasonSelect%s" onchange="toggleCustomReason(%s)">
                    %s
                </select>
            </div>
            <div class="form-group mt-3" id="customReasonDiv%s" style="display: none;">
                <textarea class="form-control" id="disapprovalReason%s" rows="3" placeholder="Enter custom reason for disapproval"></textarea>
            </div>
            <div class="form-group mt-2">
                <small class="text-muted">Remaining justifications: %d</small>
            </div>',
            $entityId,
            $entityId,
            $reasonOptions,
            $entityId,
            $entityId,
            $remainingJustification
        );
    }

    private function buildQuery(QueryBuilder $qb): void
    {
        $session = $this->requestStack->getSession();
        $filters = $session->get('report_filters', []);
        $currentDate = new \DateTime();
        $currentDate->setTime(0, 0, 0);

        $qb->select('e', 'emp', 'eh','em')
            ->from(EmployeeNotification::class, 'e')
            ->leftJoin('e.employee', 'emp')
            ->leftJoin('emp.masterEmployee', 'em')
            ->leftJoin('e.employeeHours', 'eh');

        if (empty($filters)) {
            $qb->andWhere('e.sentAt >= :currentDate')
                ->setParameter('currentDate', $currentDate->format('Y-m-d'));
        } else {
            if (isset($filters['startDate'], $filters['endDate'])) {
                $qb->andWhere('e.sentAt BETWEEN :startDate AND :endDate')
                    ->setParameter('startDate', $filters['startDate'])
                    ->setParameter('endDate', $filters['endDate']);
            }

            if (!empty($filters['employeeId'])) {
                $qb->andWhere('e.employee = :employeeId')
                    ->setParameter('employeeId', $filters['employeeId']);
            }
        }
    }
}