{% extends 'Front/dashboard/base.html.twig' %}
{% block body %}
    {% include 'Front/partials/breadcrumbs.html.twig' with {
        breadcrumbs: [
            { 'label': 'Dashboard', 'url': url('dashboard_employee') },
            { 'label': 'My Tasks', 'url': path('employee_task') },
        ],
        active: 'Create Task'
    } %}
    <div class="content">
        <div class="animated fadeIn">
            <div class="row">
                <div class="col-lg-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <strong class="card-title">
                               Create Task
                            </strong>
                            <div class="d-flex align-items-center">
                                    <a href="{{ path('employee_task') }}" class="btn btn-sm btn-outline-secondary mr-2">
                                        <i class="fa fa-list mr-1"></i>Back to List
                                    </a>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="task-form">
                                    {{ form_start(form, { 'attr': { 'class': 'needs-validation use-loader', 'novalidate': 'novalidate' , 'data-turbo': 'false'} }) }}
                                    <div class="form-section">
                                        <div class="row">
                                            <div class="col-md-12">
                                                <div class="mb-3">
                                                    {{ form_label(form.project, 'Project', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-product-hunt"></i></span>
                                                        </div>
                                                        {{ form_widget(form.project, { 'attr': { 'class': 'form-control', 'aria-label': 'Select Employee' } }) }}
                                                    </div>
                                                    {{ form_errors(form.project) }}
                                                    <small class="form-text text-muted">Select the project</small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.title, 'Task Title', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-tasks"></i></span>
                                                        </div>
                                                        {{ form_widget(form.title, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter task title', 'required': 'required' } }) }}
                                                    </div>
                                                    {{ form_errors(form.title) }}
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.estimatedHours, 'Estimated Hours', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-clock-o"></i></span>
                                                        </div>
                                                        {{ form_widget(form.estimatedHours, { 'attr': { 'class': 'form-control', 'placeholder': 'Enter estimated hours' } }) }}
                                                    </div>
                                                    {{ form_errors(form.estimatedHours) }}
                                                    <small class="form-text text-muted">Enter the estimated hours for task completion</small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.priority, 'Priority', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-exclamation-circle"></i></span>
                                                        </div>
                                                        {{ form_widget(form.priority, { 'attr': { 'class': 'form-control', 'aria-label': 'Select Priority' } }) }}
                                                    </div>
                                                    {{ form_errors(form.priority) }}
                                                    <small class="form-text text-muted">Select the task priority (Low/Medium/High)</small>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    {{ form_label(form.status, 'Status', { 'label_attr': { 'class': 'form-label control-label mb-1' } }) }}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend">
                                                            <span class="input-group-text"><i class="fa fa-toggle-on"></i></span>
                                                        </div>
                                                        {{ form_widget(form.status, { 'attr': { 'class': 'form-control', 'aria-label': 'Select Status' } }) }}
                                                    </div>
                                                    {{ form_errors(form.status) }}
                                                    <small class="form-text text-muted">Select the task status (Open/In Progress/Completed)</small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-12">
                                                <div class="mb-3">
                                                    {{ form_label(form.description, 'Description', { 'label_attr': { 'class': 'form-label control-label mb-1 required-field' } }) }}
                                                    {{ form_widget(form.description, { 'attr': { 'class': 'form-control', 'required': 'required' } }) }}
                                                    {{ form_errors(form.description) }}
                                                    <small class="form-text text-muted">Provide a brief description of the task</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="action-footer mt-4">
                                            <a href="{{ path('employee_task') }}" class="btn btn-secondary mr-2">
                                                Cancel
                                            </a>
                                        <button type="submit" class="btn btn-primary pr-3 pl-3"  id="submitButton" data-update="{{ task.id is defined ? 'true' : 'false' }}">
                                            {{ task.id is defined ? 'Update Task' : 'Create Task' }}
                                        </button>
                                    </div>
                                    {{ form_end(form) }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="clearfix"></div>
    <link rel="stylesheet" href="{{ asset('assets/css/Error/error.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/AdminForm/Form.css') }}">
    <script src="{{ asset('assets/js/admin/task.js') }}"></script>
{% endblock %}