<?php

namespace App\Controller\Api;

use App\Entity\TimeLog;
use App\Repository\TimeLogRepository;
use App\Repository\MasterEmployeeRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

#[Route('/api/time-logs', name: 'api_time_logs_')]
class TimeLogController extends AbstractController
{
    public function __construct(
        private TimeLogRepository $timeLogRepository,
        private MasterEmployeeRepository $employeeRepository,
        private EntityManagerInterface $entityManager
    ) {}

    #[Route('/daily-stats', name: 'daily_stats', methods: ['GET'])]
    public function getDailyStats(Request $request): JsonResponse
    {
        try {
            $employeeId = $request->query->get('employee_id');
            $date = $request->query->get('date', date('Y-m-d'));
            
            if (!$employeeId) {
                return new JsonResponse([
                    'success' => false,
                    'error' => 'Employee ID is required'
                ], 400);
            }

            $dateObj = new \DateTime($date);
            $stats = $this->timeLogRepository->getDailyStats($employeeId, $dateObj);
            
            // Convert to format expected by desktop app
            $formattedStats = [];
            foreach ($stats as $type => $data) {
                $formattedStats[] = [
                    'log_type' => $type,
                    'total_duration' => $data['duration'],
                    'session_count' => $data['sessions']
                ];
            }
            
            return new JsonResponse([
                'success' => true,
                'data' => $formattedStats
            ]);
            
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => 'Failed to get daily stats',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    #[Route('/active-session', name: 'active_session', methods: ['GET'])]
    public function getActiveSession(Request $request): JsonResponse
    {
        try {
            $employeeId = $request->query->get('employee_id');
            
            if (!$employeeId) {
                return new JsonResponse([
                    'success' => false,
                    'error' => 'Employee ID is required'
                ], 400);
            }

            $activeSession = $this->timeLogRepository->findActiveSession($employeeId);
            
            if ($activeSession) {
                return new JsonResponse([
                    'success' => true,
                    'data' => [
                        'id' => $activeSession->getId(),
                        'employee_id' => $activeSession->getEmployee()->getId(),
                        'log_date' => $activeSession->getLogDate(),
                        'start_time' => $activeSession->getStartTime()->format('c'),
                        'log_type' => $activeSession->getLogType(),
                        'description' => $activeSession->getDescription(),
                        'device_id' => $activeSession->getDeviceId(),
                        'metadata' => $activeSession->getMetadata()
                    ]
                ]);
            } else {
                return new JsonResponse([
                    'success' => true,
                    'data' => null
                ]);
            }
            
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => 'Failed to get active session',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    #[Route('/weekly-stats', name: 'weekly_stats', methods: ['GET'])]
    public function getWeeklyStats(Request $request): JsonResponse
    {
        try {
            $employeeId = $request->query->get('employee_id');
            $weekStart = $request->query->get('week_start', date('Y-m-d', strtotime('monday this week')));
            
            if (!$employeeId) {
                return new JsonResponse([
                    'success' => false,
                    'error' => 'Employee ID is required'
                ], 400);
            }

            $weekStartObj = new \DateTime($weekStart);
            $stats = $this->timeLogRepository->getWeeklyStats($employeeId, $weekStartObj);
            
            return new JsonResponse([
                'success' => true,
                'data' => $stats
            ]);
            
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => 'Failed to get weekly stats',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    #[Route('/sync', name: 'sync', methods: ['POST'])]
    public function syncTimeLogs(Request $request): JsonResponse
    {
        try {
            $data = json_decode($request->getContent(), true);

            if (!$data || !isset($data['logs'])) {
                return new JsonResponse([
                    'success' => false,
                    'error' => 'Invalid data format'
                ], 400);
            }

            $syncedCount = 0;
            $errors = [];

            foreach ($data['logs'] as $logData) {
                try {
                    // Validate required fields
                    if (!isset($logData['employee_id'], $logData['log_date'], $logData['start_time'], $logData['log_type'])) {
                        $errors[] = 'Missing required fields in log data';
                        continue;
                    }

                    // Find the employee
                    $employee = $this->employeeRepository->find($logData['employee_id']);
                    if (!$employee) {
                        $errors[] = "Employee not found: {$logData['employee_id']}";
                        continue;
                    }

                    // Create new TimeLog entity
                    $timeLog = new TimeLog();
                    $timeLog->setEmployee($employee);
                    $timeLog->setLogDate(new \DateTime($logData['log_date']));
                    $timeLog->setStartTime(new \DateTime($logData['start_time']));

                    if (isset($logData['end_time']) && $logData['end_time']) {
                        $timeLog->setEndTime(new \DateTime($logData['end_time']));
                    }

                    $timeLog->setLogType($logData['log_type']);

                    if (isset($logData['duration'])) {
                        $timeLog->setDuration($logData['duration']);
                    }

                    if (isset($logData['description'])) {
                        $timeLog->setDescription($logData['description']);
                    }

                    if (isset($logData['device_id'])) {
                        $timeLog->setDeviceId($logData['device_id']);
                    }

                    if (isset($logData['metadata'])) {
                        $timeLog->setMetadata($logData['metadata']);
                    }

                    $timeLog->setIsSynced(true);
                    $timeLog->setCreatedAt(new \DateTime());
                    $timeLog->setUpdatedAt(new \DateTime());

                    // Persist to database
                    $this->entityManager->persist($timeLog);
                    $syncedCount++;

                } catch (\Exception $e) {
                    $errors[] = $e->getMessage();
                }
            }

            // Flush all changes to database
            if ($syncedCount > 0) {
                $this->entityManager->flush();
            }

            return new JsonResponse([
                'success' => true,
                'data' => [
                    'synced_count' => $syncedCount,
                    'errors' => $errors
                ]
            ]);

        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => 'Failed to sync time logs',
                'message' => $e->getMessage()
            ], 500);
        }
    }
}
