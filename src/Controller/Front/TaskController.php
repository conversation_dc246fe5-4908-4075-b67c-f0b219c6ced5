<?php

namespace App\Controller\Front;

use App\Entity\EmployeeReportsTo;
use App\Entity\MasterEmployee;
use App\Entity\ProjectTeam;
use App\Entity\Task;
use App\Entity\TaskAssignment;
use App\Entity\TaskAssignmentStatusHistory;
use App\Entity\TaskAssignmentTimeLog;
use App\Entity\TaskProgress;
use App\Entity\TaskStatusHistory;
use App\Entity\User;
use App\Form\TaskAssignmentType;
use App\Form\TaskProgressType;
use App\Form\TaskType;
use App\Message\TaskAndProjectNotificationMessage;
use App\Service\Front\EmployeeTaskService;
use App\Service\Front\TaskAssignmentService;
use App\Service\Front\TaskProgressService;
use App\Traits\ExceptionLoggerTrait;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Messenger\Exception\ExceptionInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Csrf\CsrfToken;
use Symfony\Component\Security\Csrf\CsrfTokenManagerInterface;

#[Route('/employee')]
class TaskController extends AbstractController
{
    use ExceptionLoggerTrait;

    public function __construct(
        private readonly EntityManagerInterface    $entityManager,
        private readonly EmployeeTaskService     $employeeTaskService,
        private readonly CsrfTokenManagerInterface $csrfTokenManager,
        private readonly TaskProgressService     $taskProgressService,
        private readonly TaskAssignmentService     $taskAssignmentService,
        private readonly MessageBusInterface       $messageBus,
    )
    {
    }

    #[Route('/task', name: 'employee_task', methods: ['GET', 'POST'])]
    public function index(Request $request): Response
    {
        $employee = $this->getUser();
        if (!$employee instanceof MasterEmployee) {
            throw $this->createAccessDeniedException('This can only be accessed by an employee.');
        }
        $report = $this->entityManager->getRepository(EmployeeReportsTo::class)
            ->findOneBy(['employee' => $employee]);
        $projects = $this->entityManager->getRepository(ProjectTeam::class)
            ->findBy(['teamMember' => $employee]);

        $hasProjects = !empty($projects);
        $teamLeader = $report?->getTeamLeader();
        $allowSelfTask = $teamLeader?->isAllowSelfTask() ?? false;
        $table = $this->employeeTaskService->createEmployeeTaskTable($request, $employee);
        if ($table->isCallback()) {
            return $table->getResponse();
        }
        return $this->render('Front/EmployeeTask/index.html.twig', [
            'datatable' => $table,
            'isHistory' => false,
            'allowSelfTask' => $allowSelfTask,
            'hasProjects' => $hasProjects,
        ]);
    }

    #[Route('/task/history', name: 'employee_task_history', methods: ['GET', 'POST'])]
    public function taskHistory(Request $request): Response
    {
        $employee = $this->getUser();
        if (!$employee instanceof MasterEmployee) {
            throw $this->createAccessDeniedException('This can only be accessed by an employee.');
        }
        $table = $this->employeeTaskService->createEmployeeTaskHistoryTable($request, $employee);

        if ($table->isCallback()) {
            return $table->getResponse();
        }
        return $this->render('Front/EmployeeTask/index.html.twig', [
            'datatable' => $table,
            'isHistory' => true,
            'allowSelfTask' => false,
            'hasProjects' => false,
        ]);
    }

    #[Route('/task/{id}/update-status', name: 'employee_task_update_status', methods: ['POST'])]
    public function updateStatus(Request $request, Task $task): JsonResponse
    {
        $employee = $this->getUser();
        if (!$employee instanceof MasterEmployee) {
            return new JsonResponse(['success' => false, 'error' => 'Unauthorized'], Response::HTTP_FORBIDDEN);
        }

        if ($task->getAssignedTo() !== $employee) {
            return new JsonResponse(['success' => false, 'error' => 'You are not assigned to this task'], Response::HTTP_FORBIDDEN);
        }

        $token = new CsrfToken('update_status_' . $task->getId(), $request->request->get('_csrf_token'));
        if (!$this->csrfTokenManager->isTokenValid($token)) {
            return new JsonResponse(['success' => false, 'error' => 'Invalid CSRF token'], Response::HTTP_BAD_REQUEST);
        }

        $status = $request->request->get('status');
        $validStatuses = ['NEW', 'IN_PROGRESS', 'COMPLETED'];
        if (!in_array($status, $validStatuses, true)) {
            return new JsonResponse(['success' => false, 'error' => 'Invalid status'], Response::HTTP_BAD_REQUEST);
        }
        $oldStatus = $task->getStatus();
        if ($oldStatus !== $status) {
            $statusHistory = new TaskStatusHistory();
            $statusHistory->setTask($task);
            $statusHistory->setChangedBy($employee);
            $statusHistory->setChangedByAdmin(null);
            $statusHistory->setOldStatus($oldStatus);
            $statusHistory->setNewStatus($status);
            $statusHistory->setChangedAt(new \DateTime());
            $this->entityManager->persist($statusHistory);
        }
        $task->setStatus($status);
        $task->setUpdatedAt(new \DateTime());
        $this->entityManager->persist($task);
        $this->entityManager->flush();

        return new JsonResponse([
            'success' => true,
            'message' => 'Task status updated successfully.'
        ], Response::HTTP_OK);
    }

    #[Route('/task/{task_id}/progress', name: 'employee_task_progress', methods: ['GET', 'POST'])]
    public function viewProgress(Request $request, int $task_id): Response
    {
        $employee = $this->getUser();
        if (!$employee instanceof MasterEmployee) {
            throw $this->createAccessDeniedException('This can only be accessed by an employee.');
        }

        $task = $this->entityManager->getRepository(Task::class)->find($task_id);
        if (!$task) {
            throw new NotFoundHttpException('Task not found');
        }

        if ($task->getAssignedTo() !== $employee) {
            throw $this->createAccessDeniedException('You are not assigned to this task.');
        }
        $isHistory = $task->getStatus() === 'COMPLETED';
        $table = $this->taskProgressService->createTaskProgressTable($request, $task_id, $employee);
        if ($table->isCallback()) {
            return $table->getResponse();
        }
        return $this->render('Front/EmployeeTask/taskProgressindex.html.twig', [
            'datatable' => $table,
            'task' => $task,
            'isHistory' => $isHistory,
        ]);
    }


    #[Route('/task/{task_id}/progress/create', name: 'employee_task_progress_create', methods: ['GET', 'POST'])]
    public function createProgress(Request $request, int $task_id): Response
    {
        $employee = $this->getUser();
        if (!$employee instanceof MasterEmployee) {
            throw $this->createAccessDeniedException('This can only be accessed by an employee.');
        }

        $task = $this->entityManager->getRepository(Task::class)->find($task_id);
        if (!$task) {
            throw new NotFoundHttpException('Task not found');
        }

        if ($task->getAssignedTo() !== $employee) {
            throw $this->createAccessDeniedException('You are not assigned to this task.');
        }

        $taskProgress = new TaskProgress();
        $taskProgress->setTask($task);
        $taskProgress->setEmployee($employee);
        $taskProgress->setProgressDate(new \DateTime());

        $form = $this->createForm(TaskProgressType::class, $taskProgress);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $taskProgress->setCreatedAt(new \DateTime());
            $this->entityManager->persist($taskProgress);
            $this->entityManager->flush();

            $this->addFlash('success', 'Task progress added successfully.');
            return $this->redirectToRoute('employee_task_progress',['task_id' => $task->getId()]);
        }
        $isHistory = $task->getStatus() === 'COMPLETED';
        return $this->render('Front/EmployeeTask/new.html.twig', [
            'form' => $form->createView(),
            'task' => $task,
            'mode' => 'create',
            'isHistory' => $isHistory,
        ]);
    }

    #[Route('/task/{task_id}/progress/{id}/edit', name: 'employee_task_progress_edit', methods: ['GET', 'POST'])]
    public function editProgress(Request $request, int $task_id, int $id): Response
    {
        $employee = $this->getUser();
        if (!$employee instanceof MasterEmployee) {
            throw $this->createAccessDeniedException('This can only be accessed by an employee.');
        }

        $task = $this->entityManager->getRepository(Task::class)->find($task_id);
        if (!$task) {
            throw new NotFoundHttpException('Task not found');
        }

        if ($task->getAssignedTo() !== $employee) {
            throw $this->createAccessDeniedException('You are not assigned to this task.');
        }

        $taskProgress = $this->entityManager->getRepository(TaskProgress::class)->find($id);
        if (!$taskProgress) {
            throw new NotFoundHttpException('Task progress not found');
        }

        if ($taskProgress->getEmployee() !== $employee || $taskProgress->getTask() !== $task) {
            throw $this->createAccessDeniedException('You are not authorized to edit this progress entry.');
        }

        $today = new \DateTime('today');
        if ($taskProgress->getCreatedAt() < $today) {
            throw $this->createAccessDeniedException('This progress entry cannot be edited after its creation date.');
        }

        $form = $this->createForm(TaskProgressType::class, $taskProgress);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $this->entityManager->persist($taskProgress);
            $this->entityManager->flush();

            $this->addFlash('success', 'Task progress updated successfully.');
            return $this->redirectToRoute('employee_task_progress', ['task_id' => $task_id]);
        }
        $isHistory = $task->getStatus() === 'COMPLETED';
        return $this->render('Front/EmployeeTask/new.html.twig', [
            'form' => $form->createView(),
            'task' => $task,
            'project' => $task->getProject(),
            'mode' => 'edit',
            'isHistory' => $isHistory,
        ]);
    }

    #[Route('/task/{task_id}/progress/delete/{id}', name: 'employee_task_progress_delete', methods: ['POST'])]
    public function deleteProgress(Request $request, int $task_id, TaskProgress $taskProgress): JsonResponse
    {
        $employee = $this->getUser();
        if (!$employee instanceof MasterEmployee) {
            return $this->json([
                'success' => false,
                'message' => 'This can only be accessed by an employee.'
            ], Response::HTTP_FORBIDDEN);
        }

        $task = $this->entityManager->getRepository(Task::class)->find($task_id);
        if (!$task) {
            return $this->json([
                'success' => false,
                'message' => 'Task not found.'
            ], Response::HTTP_NOT_FOUND);
        }

        if ($task->getAssignedTo() !== $employee) {
            return $this->json([
                'success' => false,
                'message' => 'You are not assigned to this task.'
            ], Response::HTTP_FORBIDDEN);
        }

        if ($taskProgress->getTask()->getId() !== $task_id || $taskProgress->getEmployee() !== $employee) {
            return $this->json([
                'success' => false,
                'message' => 'You are not authorized to delete this progress entry.'
            ], Response::HTTP_FORBIDDEN);
        }

        $today = new \DateTime('today');
        if ($taskProgress->getCreatedAt() < $today) {
            return $this->json([
                'success' => false,
                'message' => 'This progress entry cannot be deleted after its creation date.'
            ], Response::HTTP_FORBIDDEN);
        }

        $token = $request->request->get('_token');
        if (!$this->csrfTokenManager->isTokenValid(new CsrfToken('delete' . $taskProgress->getId(), $token))) {
            return $this->json([
                'success' => false,
                'message' => 'Invalid CSRF token.'
            ], Response::HTTP_FORBIDDEN);
        }

        $this->entityManager->remove($taskProgress);
        $this->entityManager->flush();

        return $this->json([
            'success' => true,
            'message' => 'Task progress has been deleted.'
        ], Response::HTTP_OK);
    }

    #[Route('/task/{task_id}/assignment', name: 'employee_task_assignment', methods: ['GET', 'POST'])]
    public function viewAssignments(Request $request, int $task_id): Response
    {
        $employee = $this->getUser();
        if (!$employee instanceof MasterEmployee) {
            throw $this->createAccessDeniedException('This can only be accessed by an employee.');
        }

        $task = $this->entityManager->getRepository(Task::class)->find($task_id);
        if (!$task) {
            throw new NotFoundHttpException('Task not found');
        }

        if ($task->getAssignedTo() !== $employee && $task->getAssignedBy() !== $employee) {
            throw $this->createAccessDeniedException('You are not authorized to view assignments for this task.');
        }

        $table = $this->taskAssignmentService->createTaskAssignmentTable($request, $task, $employee);
        if ($table->isCallback()) {
            return $table->getResponse();
        }
        $isHistory = $task->getStatus() === 'COMPLETED';
        return $this->render('Front/task/Sub_Task/index.html.twig', [
            'datatable' => $table,
            'task' => $task,
            'isHistory' => $isHistory,
        ]);
    }

    /**
     * @throws ExceptionInterface
     */
    #[Route('/task/{task_id}/assignment/create', name: 'employee_task_assignment_create', methods: ['GET', 'POST'])]
    public function createAssignment(Request $request, int $task_id): Response
    {
        $employee = $this->getUser();
        if (!$employee instanceof MasterEmployee) {
            throw $this->createAccessDeniedException('This can only be accessed by an employee.');
        }

        $task = $this->entityManager->getRepository(Task::class)->find($task_id);
        if (!$task) {
            throw new NotFoundHttpException('Task not found');
        }

        if ($task->getAssignedTo() !== $employee && $task->getAssignedBy() !== $employee) {
            throw $this->createAccessDeniedException('You are not authorized to create assignments for this task.');
        }

        $taskAssignment = new TaskAssignment();
        $taskAssignment->setTask($task);
        $taskAssignment->setAssignedBy($employee);
        $taskAssignment->setCreatedAt(new \DateTime());
        $taskAssignment->setUpdatedAt(new \DateTime());

        $form = $this->createForm(TaskAssignmentType::class, $taskAssignment);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $this->entityManager->persist($taskAssignment);
            $this->entityManager->flush();

            $statusHistory = new TaskAssignmentStatusHistory();
            $statusHistory->setTaskAssignment($taskAssignment);
            $statusHistory->setChangedBy($employee);
            $statusHistory->setOldStatus('N/A');
            $statusHistory->setNewStatus($taskAssignment->getStatus());
            $statusHistory->setChangedAt(new \DateTime());
            $this->entityManager->persist($statusHistory);
            $this->entityManager->flush();


            if ($taskAssignment->getAssignedTo()) {
                try {
                    $message = new TaskAndProjectNotificationMessage('task_assignment', $taskAssignment->getId(), 'create', []);
                    $this->messageBus->dispatch($message);
                }catch (\Throwable $e) {
                    $this->log('Notification dispatch failed: ' . $e->getMessage());
                }
            }
            $this->addFlash('success', 'Task assignment created successfully.');
            return $this->redirectToRoute('employee_task_assignment', ['task_id' => $task->getId()]);
        }
        $isHistory = $task->getStatus() === 'COMPLETED';
        return $this->render('Front/task/Sub_Task/new.html.twig', [
            'form' => $form->createView(),
            'task' => $task,
            'mode' => 'create',
            'isHistory' => $isHistory,
        ]);
    }

    /**
     * @throws ExceptionInterface
     */
    #[Route('/task/{task_id}/assignment/{id}/edit', name: 'employee_task_assignment_edit', methods: ['GET', 'POST'])]
    public function editAssignment(Request $request, int $task_id, int $id): Response
    {
        $employee = $this->getUser();
        if (!$employee instanceof MasterEmployee) {
            throw $this->createAccessDeniedException('This can only be accessed by an employee.');
        }

        $task = $this->entityManager->getRepository(Task::class)->find($task_id);
        if (!$task) {
            throw $this->createNotFoundException('Task not found.');
        }

        $taskAssignment = $this->entityManager->getRepository(TaskAssignment::class)->find($id);
        if (!$taskAssignment) {
            throw $this->createNotFoundException('Task assignment not found.');
        }

        if ($taskAssignment->getTask()->getId() !== $task->getId() ||
            $taskAssignment->getAssignedBy()->getId() !== $employee->getId()) {
            throw $this->createAccessDeniedException('You are not authorized to edit this assignment.');
        }

        $originalAssignedTo = $taskAssignment->getAssignedTo();
        $originalDescription = $taskAssignment->getDescription();
        $originalStatus = $taskAssignment->getStatus();

        $form = $this->createForm(TaskAssignmentType::class, $taskAssignment);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $newAssignedTo = $taskAssignment->getAssignedTo();
            $newDescription = $taskAssignment->getDescription();
            $newStatus = $taskAssignment->getStatus();

            if ($originalStatus !== $newStatus) {
                $statusHistory = new TaskAssignmentStatusHistory();
                $statusHistory->setTaskAssignment($taskAssignment);
                $statusHistory->setChangedBy($employee);
                $statusHistory->setOldStatus($originalStatus);
                $statusHistory->setNewStatus($newStatus);
                $statusHistory->setChangedAt(new \DateTime());
                $this->entityManager->persist($statusHistory);
            }

            $taskAssignment->setUpdatedAt(new \DateTime());
            $this->entityManager->flush();

            if ($taskAssignment->getAssignedTo()) {
                $changes = [];
                if ($originalAssignedTo !== $newAssignedTo) {
                    $changes[] = 'assignment';
                }
                if ($originalDescription !== $newDescription) {
                    $changes[] = 'description';
                }
                if ($originalStatus !== $newStatus) {
                    $changes[] = 'status';
                }
                $action = !empty($changes) ? 'update' : 'no-change';
                try {
                    $message = new TaskAndProjectNotificationMessage('task_assignment', $taskAssignment->getId(), $action, $changes);
                    $this->messageBus->dispatch($message);
                }catch (\Throwable $e) {
                    $this->log('Notification dispatch failed: ' . $e->getMessage());
                }
            }

            $this->addFlash('success', 'Task assignment updated successfully.');
            return $this->redirectToRoute('employee_task_assignment', ['task_id' => $task_id]);
        }
        $isHistory = $task->getStatus() === 'COMPLETED';
        return $this->render('Front/task/Sub_Task/new.html.twig', [
            'form' => $form->createView(),
            'task' => $task,
            'mode' => 'edit',
            'isHistory' => $isHistory,
        ]);
    }

    #[Route('/task/{task_id}/assignment/{id}/view', name: 'employee_taskAssignment_view', methods: ['GET'])]
    public function view(int $task_id, TaskAssignment $taskAssignment): Response
    {
        $employee = $this->getUser();
        if (!$employee instanceof MasterEmployee) {
            throw $this->createAccessDeniedException('This can only be accessed by an employee.');
        }

        $task = $this->entityManager->getRepository(Task::class)->find($task_id);
        if (!$task) {
            throw new NotFoundHttpException('Task not found');
        }
        /** @phpstan-ignore-next-line */
        if ($task->getAssignedTo() !== $employee) {
            throw $this->createAccessDeniedException('You are not authorized to view this task assignment.');
        }
        if ($taskAssignment->getTask()->getId() !== $task_id) {
            throw new NotFoundHttpException('Task assignment does not belong to the specified task.');
        }
        $timeLogs = $this->entityManager->getRepository(TaskAssignmentTimeLog::class)->findBy(
            ['taskAssignment' => $taskAssignment],
            ['createdAt' => 'DESC']
        );
        $statusHistories = $taskAssignment->getStatusHistories()->toArray();
        $isHistory = $task->getStatus() === 'COMPLETED';
        return $this->render('Front/task/Sub_Task/new.html.twig', [
            'task' => $task,
            'taskAssignment' => $taskAssignment,
            'mode' => 'view',
            'timeLogs' => $timeLogs,
            'statusHistories' => $statusHistories,
            'isHistory' => $isHistory,
        ]);
    }
    #[Route('/task/{task_id}/assignment/delete/{id}', name: 'employee_task_assignment_delete', methods: ['POST'])]
    public function deleteAssignment(Request $request, int $task_id, TaskAssignment $taskAssignment): JsonResponse
    {
        $employee = $this->getUser();
        if (!$employee instanceof MasterEmployee) {
            return $this->json([
                'success' => false,
                'message' => 'This can only be accessed by an employee.'
            ], Response::HTTP_FORBIDDEN);
        }

        $task = $this->entityManager->getRepository(Task::class)->find($task_id);
        if (!$task) {
            return $this->json([
                'success' => false,
                'message' => 'Task not found.'
            ], Response::HTTP_NOT_FOUND);
        }

        if ($taskAssignment->getTask()->getId() !== $task_id || $taskAssignment->getAssignedBy() !== $employee) {
            return $this->json([
                'success' => false,
                'message' => 'You are not authorized to delete this assignment.'
            ], Response::HTTP_FORBIDDEN);
        }

        $token = $request->request->get('_token');
        if (!$this->csrfTokenManager->isTokenValid(new CsrfToken('delete' . $taskAssignment->getId(), $token))) {
            return $this->json([
                'success' => false,
                'message' => 'Invalid CSRF token.'
            ], Response::HTTP_FORBIDDEN);
        }

        $this->entityManager->remove($taskAssignment);
        $this->entityManager->flush();

        return $this->json([
            'success' => true,
            'message' => 'Task assignment has been deleted.'
        ], Response::HTTP_OK);
    }

    /**
     * @throws ExceptionInterface
     */
    #[Route('/task/create', name: 'employee_task_create_self', methods: ['GET', 'POST'])]
    public function create(Request $request): Response
    {
        $employee = $this->getUser();
        if (!$employee instanceof MasterEmployee) {
            throw $this->createAccessDeniedException('This can only be accessed by a employee.');
        }

        $task = new Task();
        $task->setassignedTo($employee);
        $form = $this->createForm(TaskType::class, $task, [
            'self_assignment' => true,
            'employee' => $employee,
        ]);


        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $task->setAssignedBy($employee);
            $task->setAssignedByAdmin(null);

            if ($task->getAssignedTo()) {
                $task->setAssignedAt(new \DateTime());
            }

            $task->setCreatedAt(new \DateTime());
            $task->setUpdatedAt(new \DateTime());

            $this->entityManager->persist($task);
            $this->entityManager->flush();
            $statusHistory = new TaskStatusHistory();
            $statusHistory->setTask($task);
            /** @phpstan-ignore-next-line */
            $statusHistory->setChangedBy($employee instanceof MasterEmployee ? $employee : null);
            /** @phpstan-ignore-next-line */
            $statusHistory->setChangedByAdmin($employee instanceof User ? $employee : null);
            $statusHistory->setOldStatus('N/A');
            $statusHistory->setNewStatus($task->getStatus());
            $statusHistory->setChangedAt(new \DateTime());
            $this->entityManager->persist($statusHistory);
            $this->entityManager->flush();
            $this->addFlash('success', 'Task created successfully.');
            return $this->redirectToRoute('employee_task');
        }

        return $this->render('Front/task/selt-task.html.twig', [
            'form' => $form->createView(),
            'mode' => 'create',
        ]);
    }
}