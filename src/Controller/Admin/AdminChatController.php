<?php
namespace App\Controller\Admin;

use App\Entity\EmployeeNotification;
use App\Service\Admin\ChatService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

#[Route('/admin')]
class AdminChatController extends AbstractController
{
    public function __construct(
        private readonly ChatService $chatService
    ) {}

    #[Route('/notification/{id}/chats', name: 'admin_notification_chats', methods: ['GET'])]
    public function notificationChats(EmployeeNotification $notification): Response
    {
        $this->chatService->markEmployeeChatsAsRead($notification);

        $chats = $this->chatService->formatChatsForResponse($notification);

        return $this->render('admin/chat/employee_chats.html.twig', [
            'employee' => $notification->getEmployee(),
            'notification' => $notification,
            'chats' => $chats,
        ]);
    }

    /**
     * @throws \JsonException
     */
    #[Route('/notification/{id}/chats/send', name: 'admin_send_notification_chat', methods: ['POST'])]
    public function sendChat(Request $request, EmployeeNotification $notification): JsonResponse
    {
        $data = json_decode($request->getContent(), true, 512, JSON_THROW_ON_ERROR);
        $message = $data['message'] ?? '';

        if (empty($message)) {
            return new JsonResponse(['error' => 'Message is required'], Response::HTTP_BAD_REQUEST);
        }

        $chat = $this->chatService->sendChat($notification, $message);

        return new JsonResponse([
            'success' => true,
            'chat' => [
                'id' => $chat->getId(),
                'message' => $chat->getMessage(),
                'sentAt' => $chat->getCreatedAt()->format('d-m-Y  H:i:s'),
                'isFromEmployee' => false,
            ]
        ]);
    }

    #[Route('/notification/{id}/chats/refresh', name: 'admin_refresh_notification_chats', methods: ['GET'])]
    public function refreshChats(EmployeeNotification $notification): JsonResponse
    {
        $chats = $this->chatService->formatChatsForResponse($notification);

        return new JsonResponse($chats);
    }
}