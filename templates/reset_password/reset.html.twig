<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset your password</title>
    <link rel="stylesheet" href="{{ asset('assets/css/bootstrap.min.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/forgot/reset.css') }}">
</head>
<body>
<div class="reset-container">
    <div class="reset-card">
        <div class="row g-0">
            <div class="col-md-6">
                <img src="{{ asset('images/login-image.png') }}" alt="Person pointing at floating images of people" class="reset-image">
            </div>
            <div class="col-md-6 p-4 p-md-5">
                <div class="logo-container">
                    <img src="{{ asset('images/GetMedia.png') }}" alt="BrainStream logo">
                </div>

                <h2 class="fw-bold mb-2">Reset your password</h2>
                <p class="text-secondary mb-4">Enter your new password below</p>

                {{ form_start(resetForm) }}
                <div class="mb-3">
                    <label class="form-label">New Password</label>
                    {{ form_widget(resetForm.plainPassword.first, {
                        'attr': {
                            'class': 'form-control',
                            'placeholder': 'New password'
                        },'required':false
                    }) }}
                </div>

                <div class="mb-3">
                    <label class="form-label">Repeat Password</label>
                    {{ form_widget(resetForm.plainPassword.second, {
                        'attr': {
                            'class': 'form-control',
                            'placeholder': 'Repeat password'
                        },'required':false
                    }) }}

                </div>
                <div class="form-hint">
                    Password must be at least 8 characters long and contain letters and numbers.
                </div>
                <button type="submit" class="btn btn-orange">Reset Password</button>
                {{ form_end(resetForm) }}
                <div class="text-center">
                <a href="{{ path('app_login_admin') }}" class="back-link">Back to Login</a>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="{{ asset('assets/js/bootstrap.bundle.min.js') }}"></script>
<script src="{{ asset('assets/js/admin/ResetPasswordValidation.js') }}"></script>
</body>
</html>