<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Brainee HRM - Employee Dashboard</title>
    <link rel="stylesheet" href="css/dashboard.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="app-header">
            <div class="header-left">
                <div class="logo">
                    <i class="fas fa-users"></i>
                    <span>Brainee HRM</span>
                </div>
            </div>
            
            <div class="header-center">
                <h1 id="welcomeMessage">Welcome back!</h1>
            </div>
            
            <div class="header-right">
                <div class="user-info">
                    <span id="userName">Loading...</span>
                    <div class="user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                </div>
                

            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Status Cards -->
            <div class="status-cards">
                <div class="status-card online" id="connectionCard">
                    <div class="card-icon">
                        <i class="fas fa-wifi"></i>
                    </div>
                    <div class="card-content">
                        <h3>Connection Status</h3>
                        <p id="connectionStatus">Checking...</p>
                    </div>
                </div>
                
                <div class="status-card" id="statusCard">
                    <div class="card-icon" id="statusIcon">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="card-content">
                        <h3>Employee Status</h3>
                        <p id="employeeStatus">Active</p>
                    </div>
                </div>

                <div class="status-card">
                    <div class="card-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="card-content">
                        <h3>Session Time</h3>
                        <p id="sessionTime">00:00:00</p>
                    </div>
                </div>
                
                <div class="status-card" id="trackingCard">
                    <div class="card-icon" id="trackingIcon">
                        <i class="fas fa-play-circle"></i>
                    </div>
                    <div class="card-content">
                        <h3>Time Tracking</h3>
                        <p id="trackingStatus">Starting...</p>
                    </div>
                </div>

                <div class="status-card">
                    <div class="card-icon">
                        <i class="fas fa-calendar-day"></i>
                    </div>
                    <div class="card-content">
                        <h3>Today's Date</h3>
                        <p id="currentDate">Loading...</p>
                    </div>
                </div>
            </div>

            <!-- Time Tracking Details -->
            <div class="time-tracking-section">
                <h2>Today's Time Tracking</h2>
                <div class="time-stats-grid">
                    <div class="time-stat-card work">
                        <div class="stat-icon">
                            <i class="fas fa-briefcase"></i>
                        </div>
                        <div class="stat-content">
                            <h3>Work Time</h3>
                            <p class="stat-value" id="workTime">0h 0m</p>
                        </div>
                    </div>

                    <div class="time-stat-card break">
                        <div class="stat-icon">
                            <i class="fas fa-coffee"></i>
                        </div>
                        <div class="stat-content">
                            <h3>Break Time</h3>
                            <p class="stat-value" id="breakTime">0h 0m</p>
                        </div>
                    </div>

                    <div class="time-stat-card idle">
                        <div class="stat-icon">
                            <i class="fas fa-pause-circle"></i>
                        </div>
                        <div class="stat-content">
                            <h3>Idle Time</h3>
                            <p class="stat-value" id="idleTime">0h 0m</p>
                        </div>
                    </div>

                    <div class="time-stat-card sessions">
                        <div class="stat-icon">
                            <i class="fas fa-list"></i>
                        </div>
                        <div class="stat-content">
                            <h3>Sessions</h3>
                            <p class="stat-value" id="totalSessions">0</p>
                        </div>
                    </div>
                </div>

                <div class="current-session-info" id="currentSessionInfo">
                    <h3>Current Session</h3>
                    <div class="session-details">
                        <span class="session-type" id="currentSessionType">No active session</span>
                        <span class="session-duration" id="currentSessionDuration">--:--</span>
                    </div>
                </div>
            </div>

            <!-- Activity Log -->
            <div class="activity-section">
                <h2>Recent Sessions</h2>
                <div class="activity-log" id="activityLog">
                    <!-- Content will be loaded dynamically by JavaScript -->
                </div>
            </div>
            <!-- Quick Actions -->
            <div class="quick-actions">
                <h2>Quick Actions</h2>
                <div class="action-buttons">
                    <button class="action-btn" id="syncDataBtn">
                        <i class="fas fa-sync-alt"></i>
                        <span>Sync Data</span>
                    </button>

                    <button class="action-btn" id="viewProfileBtn">
                        <i class="fas fa-user-circle"></i>
                        <span>View Profile</span>
                    </button>

                    <button class="action-btn" id="settingsBtn">
                        <i class="fas fa-cog"></i>
                        <span>Settings</span>
                    </button>
                </div>
            </div>



            <!-- System Information -->
            <div class="system-info">
                <h2>System Information</h2>
                <div class="info-grid">
                    <div class="info-item">
                        <label>App Version:</label>
                        <span id="appVersion">v1.0.0</span>
                    </div>
                    <div class="info-item">
                        <label>Device ID:</label>
                        <span id="deviceId">Loading...</span>
                    </div>
                    <div class="info-item">
                        <label>Last Sync:</label>
                        <span id="lastSync">Never</span>
                    </div>
                    <div class="info-item">
                        <label>Storage:</label>
                        <span id="storageInfo">Local SQLite</span>
                    </div>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="app-footer">
            <div class="footer-content">
                <p>&copy; 2025 Brainee HRM. All rights reserved.</p>
                <div class="footer-status">
                    <span class="status-dot" id="statusDot"></span>
                    <span id="footerStatus">Application running</span>
                </div>
            </div>
        </footer>
    </div>

    <!-- Notification Toast -->
    <div class="toast" id="toast" style="display: none;">
        <div class="toast-content">
            <i class="toast-icon"></i>
            <span class="toast-message"></span>
        </div>
        <button class="toast-close" onclick="hideToast()">
            <i class="fas fa-times"></i>
        </button>
    </div>



    <script src="js/dashboard.js"></script>
</body>
</html>
