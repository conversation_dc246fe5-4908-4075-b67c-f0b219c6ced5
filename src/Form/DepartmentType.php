<?php

namespace App\Form;

use App\Entity\Department;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\Length;
use Symfony\Component\Validator\Constraints\NotBlank;

class DepartmentType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('depName', TextType::class, [
                'label' => 'Department Name',
                'required' => true,
                'constraints' => [
                    new NotBlank([
                        'message' => 'Field label is required.',
                    ]),
                    new Length([
                        'min' => 2,
                        'max' => 255,
                        'minMessage' => 'Department name must be at least {{ limit }} characters long',
                        'maxMessage' => 'Department name cannot be longer than {{ limit }} characters',
                    ]),
                ],
                'attr' => [
                    'maxlength' => 255,
                    'class' => 'form-control',
                    'data-validation' => 'required|min:2|max:255',
                    'placeholder' => 'Enter department name'
                ],
            ])
            ->add('depDescription', TextareaType::class, [
                'label' => 'Description',
                'required' => false,
                'constraints' => [
                    new Length([
                        'min' => 2,
                        'max' => 255,
                        'minMessage' => 'Department description must be at least {{ limit }} characters long',
                        'maxMessage' => 'Department description cannot be longer than {{ limit }} characters',
                    ]),
                ],
                'attr' => [
                    'maxlength' => 255,
                    'rows' => 1,
                    'class' => 'form-control',
                    'data-validation' => 'min:2|max:255',
                    'placeholder' => 'Enter department description'
                ],
            ])
            ->add('depStatus', ChoiceType::class, [
                'label' => 'Active',
                'choices' => [
                    'Active' => true,
                    'Inactive' => false,
                ],
                'required' => true,
            ]);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => Department::class,
        ]);
    }
}