<div class="content">
    <div class="animated fadeIn">
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <strong class="card-title">Change Password</strong>
                    </div>
                    <div id="pay-invoice">
                        <div class="card-body">
                            {{ form_start(resetForm) }}
                            <div class="form-group">
                                {{ form_label(resetForm.oldPassword, 'Current Password:', {'label_attr': {'class': 'form-label'}}) }}
                                {{ form_widget(resetForm.oldPassword, {'attr': {'class': 'form-control'}, 'required': false}) }}
                                {% if form_errors(resetForm.oldPassword) %}
                                    <div class="error-message text-danger mt-1">{{ form_errors(resetForm.oldPassword) }}</div>
                                {% endif %}
                            </div>
                            <div class="form-group">
                                {{ form_label(resetForm.plainPassword.first, 'New Password:', {'label_attr': {'class': 'form-label'}}) }}
                                {{ form_widget(resetForm.plainPassword.first, {'attr': {'class': 'form-control'}, 'required': false}) }}
                                {% if form_errors(resetForm.plainPassword.first) %}
                                    <div class="error-message text-danger mt-1">{{ form_errors(resetForm.plainPassword.first) }}</div>
                                {% endif %}
                            </div>
                            <div class="form-group">
                                {{ form_label(resetForm.plainPassword.second, 'Confirm Password:', {'label_attr': {'class': 'form-label'}}) }}
                                {{ form_widget(resetForm.plainPassword.second, {'attr': {'class': 'form-control'}, 'required': false}) }}
                                {% if form_errors(resetForm.plainPassword.second) %}
                                    <div class="error-message text-danger mt-1">{{ form_errors(resetForm.plainPassword.second) }}</div>
                                {% endif %}
                            </div>
                            <div>
                                <button type="submit" class="btn btn-block btn-primary" id="payment-button">
                                    Change Password
                                </button>
                            </div>
                            {{ form_end(resetForm) }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="clearfix"></div>
<script src="{{ asset('assets/js/admin/passwordform_validation.js') }}"></script>