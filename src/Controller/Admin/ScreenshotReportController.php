<?php

namespace App\Controller\Admin;

use App\Entity\User;
use App\Repository\MasterEmployeeRepository;
use App\Repository\ScreenshotLogRepository;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/admin/screenshots')]
class ScreenshotReportController extends AbstractController
{
    public function __construct(
        private readonly ScreenshotLogRepository $screenshotLogRepository,
        private readonly MasterEmployeeRepository $masterEmployeeRepository,
    ) {
    }

    #[Route('/', name: 'admin_screenshot_report', methods: ['GET', 'POST'])]
    public function index(Request $request): Response
    {
        $user = $this->getUser();
        if (!$user instanceof User) {
            throw $this->createAccessDeniedException('This can only be accessed by an admin.');
        }

        // Get all employees for dropdown
        $employees = $this->masterEmployeeRepository->findAll();
        
        // Get form data
        $selectedEmployeeId = $request->get('employee_id');
        $selectedDate = $request->get('date', date('Y-m-d'));
        
        $screenshots = [];
        $selectedEmployee = null;
        
        if ($selectedEmployeeId) {
            $selectedEmployee = $this->masterEmployeeRepository->find($selectedEmployeeId);
            
            if ($selectedEmployee) {
                // Get screenshots for selected employee and date
                $screenshots = $this->screenshotLogRepository->findByEmployeeAndDate(
                    $selectedEmployee,
                    new \DateTime($selectedDate)
                );
            }
        }

        return $this->render('admin/screenshots/index.html.twig', [
            'employees' => $employees,
            'selectedEmployeeId' => $selectedEmployeeId,
            'selectedDate' => $selectedDate,
            'selectedEmployee' => $selectedEmployee,
            'screenshots' => $screenshots,
        ]);
    }

    #[Route('/view/{id}', name: 'admin_screenshot_view', methods: ['GET'])]
    public function viewScreenshot(int $id): Response
    {
        $user = $this->getUser();
        if (!$user instanceof User) {
            throw $this->createAccessDeniedException('This can only be accessed by an admin.');
        }

        $screenshot = $this->screenshotLogRepository->find($id);
        
        if (!$screenshot) {
            throw $this->createNotFoundException('Screenshot not found.');
        }

        return $this->render('admin/screenshots/view.html.twig', [
            'screenshot' => $screenshot,
        ]);
    }

    #[Route('/report/{employeeId}/{date}', name: 'admin_screenshot_full_report', methods: ['GET'])]
    public function fullReport(int $employeeId, string $date): Response
    {
        $user = $this->getUser();
        if (!$user instanceof User) {
            throw $this->createAccessDeniedException('This can only be accessed by an admin.');
        }

        $employee = $this->masterEmployeeRepository->find($employeeId);
        
        if (!$employee) {
            throw $this->createNotFoundException('Employee not found.');
        }

        $screenshots = $this->screenshotLogRepository->findByEmployeeAndDate(
            $employee,
            new \DateTime($date)
        );

        // Group screenshots by hour for better organization
        $screenshotsByHour = [];
        foreach ($screenshots as $screenshot) {
            $hour = $screenshot->getCapturedAt()->format('H:00');
            $screenshotsByHour[$hour][] = $screenshot;
        }

        return $this->render('admin/screenshots/full_report.html.twig', [
            'employee' => $employee,
            'date' => $date,
            'screenshots' => $screenshots,
            'screenshotsByHour' => $screenshotsByHour,
            'totalScreenshots' => count($screenshots),
        ]);
    }
}
