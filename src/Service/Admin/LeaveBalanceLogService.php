<?php

namespace App\Service\Admin;

use App\Entity\LeaveBalance;
use Doctrine\ORM\QueryBuilder;
use Omines\DataTablesBundle\Adapter\Doctrine\ORMAdapter;
use Omines\DataTablesBundle\Column\TextColumn;
use Omines\DataTablesBundle\DataTable;
use Omines\DataTablesBundle\DataTableFactory;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Request;
use App\Traits\ExceptionLoggerTrait;

class LeaveBalanceLogService
{
    use ExceptionLoggerTrait;

    public function __construct(
        private readonly DataTableFactory          $dataTableFactory,
        ParameterBagInterface                      $parameterBag,
    ) {
        $this->initializeLogger($parameterBag, 'LeaveBalance_Table_service');
    }

    public function createLeaveBalanceTable(Request $request): DataTable
    {
        $dataTable = $this->dataTableFactory->create();

        foreach ($this->getColumns() as $key => $column) {
            $dataTable->add($key, $column['className'], array_filter($column, fn($k) => $k !== 'className', ARRAY_FILTER_USE_KEY));
        }

        return $dataTable->createAdapter(ORMAdapter::class, [
            'entity' => LeaveBalance::class,
            'query' => fn(QueryBuilder $qb) => $this->buildQuery($qb),
        ])
            ->addOrderBy('year', 'desc')
            ->handleRequest($request);
    }

    private function getColumns(): array
    {
        return [
            'employee' => [
                'label' => 'Employee',
                'field' => 'e.name',
                'className' => TextColumn::class,
                'orderable' => true,
            ],
            'leaveType' => [
                'label' => 'Leave Type',
                'field' => 'lt.name',
                'className' => TextColumn::class,
                'orderable' => true,
            ],
            'totalDays' => [
                'label' => 'Total Days',
                'field' => 'lb.totalDays',
                'className' => TextColumn::class,
                'searchable' => false,
                'render' => fn($value, $context) => number_format($context->getTotalDays(), 1),
            ],
            'usedDays' => [
                'label' => 'Used Days',
                'field' => 'lb.usedDays',
                'className' => TextColumn::class,
                'searchable' => false,
                'render' => fn($value, $context) => number_format($context->getUsedDays(), 1),
            ],
            'remainingDays' => [
                'label' => 'Remaining Days',
                'field' => 'lb.remainingDays',
                'className' => TextColumn::class,
                'searchable' => false,
                'render' => fn($value, $context) => number_format($context->getRemainingDays(), 1),
            ],
            'penaltyDays' => [
                'label' => 'Penalty Days',
                'field' => 'lb.penaltyDays',
                'className' => TextColumn::class,
                'searchable' => false,
                'render' => fn($value, $context) => number_format($context->getPenaltyDays(), 1),
            ],
            'year' => [
                'label' => 'Year',
                'field' => 'lb.year',
                'className' => TextColumn::class,
                'searchable' => false,
            ],
        ];
    }
    private function buildQuery(QueryBuilder $qb): QueryBuilder
    {
        return $qb->select('lb')
            ->from(LeaveBalance::class, 'lb')
            ->leftJoin('lb.employee', 'e')
            ->leftJoin('lb.leaveType', 'lt')
            ->select('lb, e, lt');
    }
}