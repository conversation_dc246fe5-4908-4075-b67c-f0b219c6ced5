<?php

namespace App\Form;

use App\Entity\Holiday;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\CallbackTransformer;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\Length;
use Symfony\Component\Validator\Constraints\NotBlank;

class HolidayType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('name', TextType::class, [
                'label' => 'Holiday Name',
                'attr' => [
                    'placeholder' => 'Enter holiday name',
                    'class' => 'form-control',
                    'maxlength' => 255,
                    'data-validation' => 'max:255'
                ],
                'constraints' => [
                    new NotBlank([
                        'message' => 'Holiday name cannot be blank',
                    ]),
                    new Length([
                        'min' => 2,
                        'max' => 255,
                        'minMessage' => 'Holiday name must be at least {{ limit }} characters long',
                        'maxMessage' => 'Holiday name cannot be longer than {{ limit }} characters',
                    ]),
                ],
            ])
            ->add('description', TextareaType::class, [
                'label' => 'Description',
                'required' => false,
                'attr' => [
                    'placeholder' => 'Enter holiday description (optional)',
                    'class' => 'form-control',
                    'rows' => 1,
                    'maxlength' => 255,
                    'data-validation' => 'max:255'
                ],
                'constraints' => [
                    new Length([
                        'max' => 255,
                        'maxMessage' => 'Description cannot be longer than {{ limit }} characters',
                    ]),
                ],
            ])
            ->add('date', TextType::class, [
                'label' => 'Date',
                'attr' => [
                    'class' => 'form-control datepicker',
                    'placeholder' => 'Select date',
                    'required' => 'required',
                ],
            ])
            ->add('isPublicHoliday', CheckboxType::class, [
                'label' => 'Public Holiday',
                'required' => false,
                'attr' => [
                    'class' => 'form-check-input'
                ],
                'label_attr' => [
                    'class' => 'form-check-label',
                ],
            ]);

        $builder->get('date')
            ->addModelTransformer(new CallbackTransformer(
                function ($dateAsObject) {
                    return $dateAsObject instanceof \DateTimeInterface ? $dateAsObject->format('d-m-Y') : '';
                },
                function ($dateAsString) {
                    return $dateAsString ? \DateTime::createFromFormat('d-m-Y', $dateAsString) : null;
                }
            ));
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => Holiday::class,
        ]);
    }
}