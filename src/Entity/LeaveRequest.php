<?php

namespace App\Entity;

use App\Repository\LeaveRequestRepository;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Context\ExecutionContextInterface;

#[ORM\Entity(repositoryClass: LeaveRequestRepository::class)]
#[ORM\Table(name: "leave_requests")]
#[ORM\HasLifecycleCallbacks]
class LeaveRequest
{
    public const STATUS_PENDING = 'PENDING';
    public const STATUS_APPROVED = 'APPROVED';
    public const STATUS_REJECTED = 'REJECTED';
    public const HALF_DAY_MORNING = 'MORNING';
    public const HALF_DAY_AFTERNOON = 'AFTERNOON';

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: "integer")]
    /** @phpstan-ignore-next-line */
    private ?int $id = null;

    #[ORM\ManyToOne(targetEntity: MasterEmployee::class, inversedBy: "leaveRequests")]
    #[ORM\JoinColumn(name: "employee_id", referencedColumnName: "id", nullable: false)]
    #[Assert\NotBlank(message: "Employee cannot be blank")]
    private ?MasterEmployee $employee = null;

    #[ORM\ManyToOne(targetEntity: LeaveBalance::class)]
    #[ORM\JoinColumn(name: "leave_type_id", referencedColumnName: "id", nullable: false)]
    #[Assert\NotBlank(message: "Leave type cannot be blank")]
    private ?LeaveBalance $leaveType = null;

    #[ORM\Column(type: "date")]
    #[Assert\NotBlank(message: "Start date cannot be blank")]
    #[Assert\LessThanOrEqual(propertyPath: "endDate", message: "Start date must be before or equal to end date")]
    private ?\DateTimeInterface $startDate = null;

    #[ORM\Column(type: "string", nullable: true)]
    #[Assert\Choice(choices: [self::HALF_DAY_MORNING, self::HALF_DAY_AFTERNOON], message: "Invalid half-day value for start date")]
    private ?string $startHalfDay = null;

    #[ORM\Column(type: "date")]
    #[Assert\NotBlank(message: "End date cannot be blank")]
    #[Assert\GreaterThanOrEqual(propertyPath: "startDate", message: "End date must be after or equal to start date")]
    private ?\DateTimeInterface $endDate = null;

    #[ORM\Column(type: "string", nullable: true)]
    #[Assert\Choice(choices: [self::HALF_DAY_MORNING, self::HALF_DAY_AFTERNOON], message: "Invalid half-day value for end date")]
    private ?string $endHalfDay = null;

    #[ORM\Column(type: "string", options: ["default" => self::STATUS_PENDING])]
    #[Assert\Choice(choices: [self::STATUS_PENDING, self::STATUS_APPROVED, self::STATUS_REJECTED], message: "Invalid status value")]
    private string $status = self::STATUS_PENDING;

    #[ORM\Column(type: "text" , length: 255)]
    #[Assert\NotBlank(message: "Reason cannot be blank")]
    private ?string $reason = null;

    #[ORM\Column(type: "datetime", options: ["default" => "CURRENT_TIMESTAMP"])]
    private ?\DateTimeInterface $appliedOn = null;

    #[ORM\ManyToOne(targetEntity: MasterEmployee::class)]
    #[ORM\JoinColumn(name: "hr_approved_by", referencedColumnName: "id", nullable: true)]
    private ?MasterEmployee $hrApprovedBy = null;

    #[ORM\Column(type: "datetime", nullable: true)]
    private ?\DateTimeInterface $hrApprovedOn = null;

    #[ORM\ManyToOne(targetEntity: MasterEmployee::class)]
    #[ORM\JoinColumn(name: "team_lead_approved_by", referencedColumnName: "id", nullable: true)]
    private ?MasterEmployee $teamLeadApprovedBy = null;

    #[ORM\Column(type: "datetime", nullable: true)]
    private ?\DateTimeInterface $teamLeadApprovedOn = null;

    #[ORM\ManyToOne(targetEntity: User::class)]
    #[ORM\JoinColumn(name: "admin_approved_by", referencedColumnName: "id", nullable: true)]
    private ?User $adminApprovedBy = null;

    #[ORM\Column(type: "datetime", nullable: true)]
    private ?\DateTimeInterface $adminApprovedOn = null;

    #[ORM\Column(type: "float", nullable: false, options: ["default" => 0.0])]
    private float $daysRequested = 0.0;

    #[ORM\Column(type: "float", nullable: false, options: ["default" => 0.0])]
    private float $penaltyDays = 0.0;

    #[ORM\Column(type: "boolean", nullable: false, options: ["default" => false])]
    private bool $penaltyApplied = false;

    public function __construct()
    {
        $this->appliedOn = new \DateTime();
    }
    public function getStartHalfDay(): ?string
    {
        return $this->startHalfDay;
    }

    public function setStartHalfDay(?string $startHalfDay): self
    {
        $this->startHalfDay = $startHalfDay;
        return $this;
    }

    public function getEndHalfDay(): ?string
    {
        return $this->endHalfDay;
    }

    public function setEndHalfDay(?string $endHalfDay): self
    {
        $this->endHalfDay = $endHalfDay;
        return $this;
    }

    public function getDaysRequested(): float
    {
        return $this->daysRequested;
    }

    public function setDaysRequested(float $daysRequested): void
    {
        $this->daysRequested = $daysRequested;
    }
    #[Assert\Callback]
    public function validate(ExecutionContextInterface $context): void
    {
//        if ($this->startHalfDay && $this->endHalfDay && $this->startDate !== $this->endDate) {
//            $context->buildViolation('Half-day settings are only valid for single-day requests')
//                ->atPath('startHalfDay')
//                ->addViolation();
//        }

        if ($this->status === self::STATUS_APPROVED &&
            !$this->hrApprovedBy &&
            !$this->teamLeadApprovedBy &&
            !$this->adminApprovedBy) {
            $context->buildViolation('Approved leave request must have at least one approver')
                ->atPath('status')
                ->addViolation();
        }

        if ($this->hrApprovedBy && !$this->hrApprovedOn) {
            $context->buildViolation('HR approval date is required when HR approver is set')
                ->atPath('hrApprovedOn')
                ->addViolation();
        }

        if ($this->teamLeadApprovedBy && !$this->teamLeadApprovedOn) {
            $context->buildViolation('Team Lead approval date is required when Team Lead approver is set')
                ->atPath('teamLeadApprovedOn')
                ->addViolation();
        }

        if ($this->adminApprovedBy && !$this->adminApprovedOn) {
            $context->buildViolation('Admin approval date is required when Admin approver is set')
                ->atPath('adminApprovedOn')
                ->addViolation();
        }
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getEmployee(): ?MasterEmployee
    {
        return $this->employee;
    }

    public function setEmployee(?MasterEmployee $employee): void
    {
        $this->employee = $employee;
    }

    public function getLeaveType(): ?LeaveBalance
    {
        return $this->leaveType;
    }

    public function setLeaveType(?LeaveBalance $leaveType): void
    {
        $this->leaveType = $leaveType;
    }

    public function getStartDate(): ?\DateTimeInterface
    {
        return $this->startDate;
    }

    public function setStartDate(?\DateTimeInterface $startDate): void
    {
        $this->startDate = $startDate;
    }

    public function getEndDate(): ?\DateTimeInterface
    {
        return $this->endDate;
    }

    public function setEndDate(?\DateTimeInterface $endDate): void
    {
        $this->endDate = $endDate;
    }

    public function getStatus(): string
    {
        return $this->status;
    }

    public function setStatus(string $status): void
    {
        $this->status = $status;
    }

    public function getReason(): ?string
    {
        return $this->reason;
    }

    public function setReason(?string $reason): void
    {
        $this->reason = $reason;
    }

    public function getAppliedOn(): ?\DateTimeInterface
    {
        return $this->appliedOn;
    }

    public function setAppliedOn(?\DateTimeInterface $appliedOn): void
    {
        $this->appliedOn = $appliedOn;
    }

    public function getHrApprovedBy(): ?MasterEmployee
    {
        return $this->hrApprovedBy;
    }

    public function setHrApprovedBy(?MasterEmployee $hrApprovedBy): void
    {
        $this->hrApprovedBy = $hrApprovedBy;
    }

    public function getHrApprovedOn(): ?\DateTimeInterface
    {
        return $this->hrApprovedOn;
    }

    public function setHrApprovedOn(?\DateTimeInterface $hrApprovedOn): void
    {
        $this->hrApprovedOn = $hrApprovedOn;
    }

    public function getTeamLeadApprovedBy(): ?MasterEmployee
    {
        return $this->teamLeadApprovedBy;
    }

    public function setTeamLeadApprovedBy(?MasterEmployee $teamLeadApprovedBy): void
    {
        $this->teamLeadApprovedBy = $teamLeadApprovedBy;
    }

    public function getTeamLeadApprovedOn(): ?\DateTimeInterface
    {
        return $this->teamLeadApprovedOn;
    }

    public function setTeamLeadApprovedOn(?\DateTimeInterface $teamLeadApprovedOn): void
    {
        $this->teamLeadApprovedOn = $teamLeadApprovedOn;
    }

    public function getAdminApprovedBy(): ?User
    {
        return $this->adminApprovedBy;
    }

    public function setAdminApprovedBy(?User $adminApprovedBy): void
    {
        $this->adminApprovedBy = $adminApprovedBy;
    }

    public function getAdminApprovedOn(): ?\DateTimeInterface
    {
        return $this->adminApprovedOn;
    }

    public function setAdminApprovedOn(?\DateTimeInterface $adminApprovedOn): void
    {
        $this->adminApprovedOn = $adminApprovedOn;
    }
    #[ORM\PrePersist]
    #[ORM\PreUpdate]
    public function setTimestamps(): void
    {
        if ($this->hrApprovedBy && !$this->hrApprovedOn) {
            $this->hrApprovedOn = new \DateTime();
        }
        if ($this->teamLeadApprovedBy && !$this->teamLeadApprovedOn) {
            $this->teamLeadApprovedOn = new \DateTime();
        }
        if ($this->adminApprovedBy && !$this->adminApprovedOn) {
            $this->adminApprovedOn = new \DateTime();
        }
    }
    public function getPenaltyDays(): float
    {
        return $this->penaltyDays;
    }

    public function setPenaltyDays(float $penaltyDays): void
    {
        $this->penaltyDays = $penaltyDays;
    }

    public function isPenaltyApplied(): bool
    {
        return $this->penaltyApplied;
    }

    public function setPenaltyApplied(bool $penaltyApplied): void
    {
        $this->penaltyApplied = $penaltyApplied;
    }

}