{% extends '/admin/dashboard/base.html.twig' %}

{% block title %}Tasks - {{ project.name }}{% endblock %}
{% block body %}
    {% include '/admin/partials/breadcrumbs.html.twig' with {
        title: 'Tasks',
        breadcrumbs: [
            { 'label': 'Dashboard', 'url': url('admin_dashboard') },
            { 'label': 'Projects', 'url': path('project') },
            { 'label': project.name ~ ' Project', 'url': path('project_task', { 'project_id': project.id }) }
        ],
        active: mode == 'view' ? 'View Task' : (mode == 'edit' ? 'Edit Task' : 'Create Task')
    } %}
    {% include 'CommonTemplate/task/new.html.twig' %}
{% endblock %}