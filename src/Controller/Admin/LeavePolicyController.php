<?php
namespace App\Controller\Admin;

use App\Service\Admin\LeaveConfigurationService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\Console\Input\ArrayInput;
use Symfony\Component\Console\Output\BufferedOutput;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\KernelInterface;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Csrf\CsrfTokenManagerInterface;
use App\Traits\ExceptionLoggerTrait;
use Symfony\Bundle\FrameworkBundle\Console\Application;

#[Route('/admin')]
class LeavePolicyController extends AbstractController
{
    use ExceptionLoggerTrait;
    private const FIELD_LEAVE_RENEWAL_MONTH = 'LEAVE_RENEWAL_MONTH';

    public function __construct(
        private readonly LeaveConfigurationService $leaveConfigurationService,
        private readonly CsrfTokenManagerInterface $csrfTokenManager,
        ParameterBagInterface $parameterBag,
    ) {
        $this->initializeLogger($parameterBag, 'leave_controller');
    }

    #[Route('/leave/policy', name: 'leave_policy', methods: ['GET'])]
    public function reportConfigPage(): Response
    {
        $fields = [
            'LeaveRenewalMonth' => self::FIELD_LEAVE_RENEWAL_MONTH,
        ];

        $settings = $this->leaveConfigurationService->getReportSettings($fields);

        return $this->render('admin/leave_config.html.twig', [
            'settings' => $settings,
        ]);
    }

    #[Route('/leave/policy/update', name: 'leave_policy_update', methods: ['POST'])]
    public function update(Request $request): JsonResponse
    {
        $token = $request->headers->get('X-CSRF-Token');
        if (!$this->csrfTokenManager->isTokenValid(new \Symfony\Component\Security\Csrf\CsrfToken('setting_update', $token))) {
            return new JsonResponse(['message' => 'Invalid CSRF token'], Response::HTTP_FORBIDDEN);
        }

        try {
            $data = json_decode($request->getContent(), true, 512, JSON_THROW_ON_ERROR);

            $fields = [
                'LeaveRenewalMonth' => self::FIELD_LEAVE_RENEWAL_MONTH,
            ];

            foreach ($fields as $key => $fieldName) {
                if (isset($data[$key])) {
                    $this->leaveConfigurationService->updateConfiguration($fieldName, $data[$key]);
                }
            }

            return new JsonResponse(['message' => 'Report settings updated successfully']);
        } catch (\JsonException $e) {
            return new JsonResponse(['message' => 'Invalid data'], Response::HTTP_BAD_REQUEST);
        } catch (\Exception $e) {
            $this->leaveConfigurationService->log('Error updating settings', ['exception' => $e]);
            return new JsonResponse(['message' => 'Error updating settings: ' . $e->getMessage()], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    #[Route(path: '/leave-balance/populate', name: 'leave_balance_populate', methods: ['POST'])]
    public function populateLeaveBalances(KernelInterface $kernel): JsonResponse
    {
        try {
            $application = new Application($kernel);
            $application->setAutoExit(false);

            $input = new ArrayInput([
                'command' => 'app:populate-leave-balances'
            ]);
            $output = new BufferedOutput();
            $exitCode = $application->run($input, $output);

            if ($exitCode !== 0) {
                throw new \RuntimeException('Command failed with exit code: ' . $exitCode);
            }

            $commandOutput = $output->fetch();

            return new JsonResponse([
                'success' => true,
                'message' => $commandOutput ?: 'Leave balances populated successfully'
            ], Response::HTTP_OK);
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'message' => 'Failed to populate leave balances: ' . $e->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}