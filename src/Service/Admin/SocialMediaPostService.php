<?php

namespace App\Service\Admin;

use App\Entity\SocialMediaPost;
use App\Helper\ModalboxHelper;
use Doctrine\ORM\QueryBuilder;
use Omines\DataTablesBundle\Adapter\Doctrine\ORMAdapter;
use Omines\DataTablesBundle\Column\DateTimeColumn;
use Omines\DataTablesBundle\Column\TextColumn;
use Omines\DataTablesBundle\DataTable;
use Omines\DataTablesBundle\DataTableFactory;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Request;
use App\Traits\ExceptionLoggerTrait;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Security\Csrf\CsrfTokenManagerInterface;

class SocialMediaPostService
{
    use ExceptionLoggerTrait;

    public function __construct(
        private readonly DataTableFactory          $dataTableFactory,
        private readonly RouterInterface           $router,
        private readonly ParameterBagInterface     $parameterBag,
        private readonly CsrfTokenManagerInterface $csrfTokenManager,
        private readonly ModalboxHelper            $modalboxHelper
    )
    {
        $this->initializeLogger($parameterBag, 'SocialMediaPost_Table_service');
    }

    public function createSocialMediaPostTable(Request $request): DataTable
    {
        $dataTable = $this->dataTableFactory->create();

        foreach ($this->getColumns() as $key => $column) {
            $dataTable->add($key, $column['className'], array_filter($column, fn($k) => $k !== 'className', ARRAY_FILTER_USE_KEY));
        }

        return $dataTable->createAdapter(ORMAdapter::class, [
            'entity' => SocialMediaPost::class,
            'query' => fn(QueryBuilder $qb) => $this->buildQuery($qb),
        ])
            ->addOrderBy('postDate', 'asc')
            ->handleRequest($request);
    }

    private function getColumns(): array
    {
        return [
            'title' => [
                'label' => 'Post Title',
                'field' => 's.title',
                'className' => TextColumn::class,
                'orderable' => true,
            ],
            'description' => [
                'label' => 'Description',
                'field' => 's.description',
                'className' => TextColumn::class,
                'orderable' => false,
                'render' => fn($value) => $value ?: '-',
            ],
            'postDate' => [
                'label' => 'Post Date',
                'field' => 's.postDate',
                'className' => DateTimeColumn::class,
                'format' => 'd-m-Y',
                'nullValue' => '-',
                'searchable' => false,
            ],
            'mediaType' => [
                'label' => 'Media Type',
                'field' => 's.mediaType',
                'className' => TextColumn::class,
                'orderable' => true,
                'render' => fn($value) => ucfirst($value),
            ],
            'actions' => [
                'label' => 'Actions',
                'field' => 's.id',
                'className' => TextColumn::class,
                'orderable' => false,
                'render' => fn($value, $context) => $this->renderActionColumn($context),
            ],
        ];
    }

    private function renderActionColumn(SocialMediaPost $socialMediaPost): string
    {
        $entityId = $socialMediaPost->getId();
        $editUrl = $this->router->generate('social_media_post_edit');
        $deleteUrl = $this->router->generate('social_media_post_delete', ['id' => $entityId]);
        $csrfToken = $this->csrfTokenManager->getToken('delete' . $entityId)->getValue();

        $modalId = 'deleteModal_' . $entityId;
        $modalBody = sprintf(
            'Are you sure you want to delete the social media post "%s" on %s?<br><br>' .
            '<strong>Warning:</strong> This action cannot be undone.',
            htmlspecialchars($socialMediaPost->getTitle()),
            $socialMediaPost->getPostDate()->format('d-m-Y')
        );

        $footerButtons = sprintf(
            '<button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>' .
            '<button type="button" class="btn btn-primary delete-social-media-post-btn" data-delete="true" data-url="%s" data-token="%s">Delete</button>',
            $deleteUrl,
            $csrfToken
        );

        $deleteModal = $this->modalboxHelper->renderModal(
            $modalId,
            'Confirm Social Media Post Deletion',
            $modalBody,
            $footerButtons
        );

        return sprintf(
            '<div class="d-inline-flex gap-2">
            <form action="%s" method="POST" class="d-inline">
                <input type="hidden" name="id" value="%s">
                <input type="hidden" name="_token" value="%s">
                <button type="submit" class="bs-btn btn btn-primary btn-sm bs-m" data-turbo="false" title="Edit">
                    <i class="bs-fa fa fa-edit"></i>
                </button>
            </form>
            <button type="button" class="bs-btn btn btn-primary btn-sm bs-m open-delete-social-media-post-modal" data-toggle="modal" data-target="#%s" title="Delete">
                <i class="bs-fa fa fa-trash"></i>
            </button>
        </div>%s',
            $editUrl,
            $entityId,
            $this->csrfTokenManager->getToken('edit' . $entityId)->getValue(),
            $modalId,
            $deleteModal
        );
    }

    private function buildQuery(QueryBuilder $qb): QueryBuilder
    {
        return $qb->select('s')
            ->from(SocialMediaPost::class, 's');
    }
}