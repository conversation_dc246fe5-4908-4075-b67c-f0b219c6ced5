<?php

namespace App\Form;

use App\Entity\FormField;
use App\Form\DataTransformer\JsonToArrayTransformer;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormEvent;
use Symfony\Component\Form\FormEvents;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\Length;
use Symfony\Component\Validator\Constraints\PositiveOrZero;

class FormFieldType extends AbstractType
{
    private JsonToArrayTransformer $jsonToArrayTransformer;

    public function __construct(JsonToArrayTransformer $jsonToArrayTransformer)
    {
        $this->jsonToArrayTransformer = $jsonToArrayTransformer;
    }
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('fieldLabel', TextType::class, [
                'label' => 'Field Label',
                'required' => true,
                'constraints' => [
                    new NotBlank([
                        'message' => 'Field label is required.',
                    ]),
                    new Length([
                        'min' => 2,
                        'max' => 255,
                        'minMessage' => 'Field label must be at least {{ limit }} characters long',
                        'maxMessage' => 'Field label cannot be longer than {{ limit }} characters',
                    ]),
                ],
                'attr' => [
                    'maxlength' => 255,
                    'class' => 'form-control',
                    'data-validation' => 'required|min:2|max:255'
                ],
            ])
            ->add('fieldType', ChoiceType::class, [
                'label' => 'Field Type',
                'choices' => [
                    'Text' => 'text',
                    'Rating' => 'rating',
                    'Textarea' => 'textarea',
                    'Number' => 'number',
                    'Date' => 'date',
                    'Checkbox' => 'checkbox',
                    'Select' => 'select',
                    'Radio' => 'radio',
                    'Email' => 'email',
                    'Password' => 'password',
                ],
                'required' => true,
                'constraints' => [
                    new NotBlank([
                        'message' => 'Field type is required.',
                    ]),
                ],
                'attr' => [
                    'class' => 'form-control',
                    'data-validation' => 'required'
                ],
            ])
            ->add('fieldOrder', IntegerType::class, [
                'label' => 'Field Order',
                'required' => true,
                'constraints' => [
                    new NotBlank([
                        'message' => 'Field order is required.',
                    ]),
                    new PositiveOrZero([
                        'message' => 'Field order must be zero or positive.',
                    ]),
                ],
                'attr' => [
                    'class' => 'form-control',
                    'data-validation' => 'required|numeric|min:0',
                ],
            ])
            ->add('placeholder', TextType::class, [
                'label' => 'Placeholder',
                'required' => false,
                'constraints' => [
                    new Length([
                        'max' => 255,
                        'maxMessage' => 'Placeholder cannot be longer than {{ limit }} characters',
                    ]),
                ],
                'attr' => [
                    'maxlength' => 255,
                    'class' => 'form-control',
                    'data-validation' => 'max:255'
                ],
            ])
            ->add('helpText', TextareaType::class, [
                'label' => 'Help Text',
                'required' => false,
                'constraints' => [
                    new Length([
                        'max' => 255,
                        'maxMessage' => 'helpText cannot be longer than {{ limit }} characters',
                    ]),
                ],
                'attr' => [
                    'maxlength' => 255,
                    'class' => 'form-control',
                    'rows' => 1,
                    'data-validation' => 'max:255'
                ],
            ])
            ->add('defaultValue', TextareaType::class, [
                'label' => 'Default Value',
                'required' => false,
                'constraints' => [
                    new Length([
                        'max' => 255,
                        'maxMessage' => 'defaultValue cannot be longer than {{ limit }} characters',
                    ]),
                ],
                'attr' => [
                    'maxlength' => 255,
                    'class' => 'form-control',
                    'rows' => 1,
                    'data-validation' => 'max:255'
                ],
            ])
            ->add('fieldOptions', TextareaType::class, [
                'label' => 'Field Options (JSON)',
                'required' => false,
                'attr' => [
                    'placeholder' => '{"key": "value"}',
                    'class' => 'form-control',
                    'rows' => 3,
                    'data-validation' => 'json',
                    'style' => 'display: none;',
                ],
            ])
            ->add('isRequired', CheckboxType::class, [
                'label' => 'Is Required',
                'required' => false,
                'attr' => [
                    'class' => 'form-check-input'
                ],
            ])
            ->add('isVisible', CheckboxType::class, [
                'label' => 'Is Active',
                'required' => false,
                'attr' => [
                    'class' => 'form-check-input'
                ],
            ]);
        $builder->get('fieldOptions')->addModelTransformer($this->jsonToArrayTransformer);
        $builder->addEventListener(FormEvents::PRE_SET_DATA, function (FormEvent $event) {
            $data = $event->getData();

            if (!$data instanceof FormField) {
                return;
            }

            if (!empty($data->getFieldOptions()) && is_string($data->getFieldOptions())) {
                $decodedOptions = json_decode($data->getFieldOptions(), true, 512, JSON_THROW_ON_ERROR);
                $data->setFieldOptions($decodedOptions);
            }
        });

        $builder->addEventListener(FormEvents::PRE_SUBMIT, function (FormEvent $event) {
            $data = $event->getData();

            if (!empty($data['fieldOptions']) && is_array($data['fieldOptions'])) {
                $data['fieldOptions'] = json_encode($data['fieldOptions'], JSON_THROW_ON_ERROR);
            }

            $event->setData($data);
        });
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => FormField::class,
            'attr' => [
                'novalidate' => 'novalidate',
            ],
        ]);
    }
}